package com.ruoyi.crm.controller;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.crm.service.ICrmContactResponsibleRelationService;
import com.ruoyi.crm.domain.vo.PoolQueryRequest;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.domain.entity.CrmContactResponsibleRelation;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;

/**
 * 联系人公海管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/crm/contact-pool")
public class CrmContactPoolController extends BaseController {
    
    @Autowired
    private ICrmContactResponsibleRelationService contactResponsibleRelationService;
    
    /**
     * 查询公海联系人列表
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('crm:pool:list')")
    public TableDataInfo list(PoolQueryRequest request) {
        try {
            // 设置默认值
            if (request.getUserId() == null) {
                request.setUserId(SecurityUtils.getUserId());
            }
            if (request.getQueryType() == null) {
                request.setQueryType(PoolQueryRequest.QueryType.PERSONAL);
            }
            
            startPage();
            List<CrmContacts> list = contactResponsibleRelationService.selectPoolContacts(request);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("查询公海联系人失败", e);
            return new TableDataInfo();
        }
    }
    
    /**
     * 认领公海联系人
     */
    @PostMapping("/claim")
    @PreAuthorize("@ss.hasPermi('crm:pool:claim')")
    @Log(title = "认领公海联系人", businessType = BusinessType.UPDATE)
    public AjaxResult claimContacts(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> contactIdInts = (List<Integer>) params.get("contactIds");
            Long[] contactIds = contactIdInts.stream().map(Long::valueOf).toArray(Long[]::new);
            String businessType = (String) params.get("businessType");
            
            if (businessType == null || businessType.isEmpty()) {
                businessType = CrmContactResponsibleRelation.BusinessType.GENERAL;
            }
            
            int result = contactResponsibleRelationService.claimContactsFromPool(contactIds, businessType);
            return AjaxResult.success("成功认领 " + result + " 个联系人");
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            logger.error("认领公海联系人失败", e);
            return AjaxResult.error("认领失败：" + e.getMessage());
        }
    }
    
    /**
     * 退回联系人到公海
     */
    @PostMapping("/return")
    @PreAuthorize("@ss.hasPermi('crm:pool:return')")
    @Log(title = "退回联系人到公海", businessType = BusinessType.UPDATE)
    public AjaxResult returnContacts(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> contactIdInts = (List<Integer>) params.get("contactIds");
            Long[] contactIds = contactIdInts.stream().map(Long::valueOf).toArray(Long[]::new);
            String reason = (String) params.get("reason");
            Long responsiblePersonId = params.get("responsiblePersonId") != null ? 
                Long.valueOf(params.get("responsiblePersonId").toString()) : null;
            
            int result = contactResponsibleRelationService.returnContactsToPool(
                contactIds, responsiblePersonId, reason);
            return AjaxResult.success("成功退回 " + result + " 个联系人关系");
        } catch (Exception e) {
            logger.error("退回联系人到公海失败", e);
            return AjaxResult.error("退回失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量转移联系人
     */
    @PostMapping("/transfer")
    @PreAuthorize("@ss.hasPermi('crm:pool:transfer')")
    @Log(title = "转移联系人负责人", businessType = BusinessType.UPDATE)
    public AjaxResult transferContacts(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> contactIdInts = (List<Integer>) params.get("contactIds");
            Long[] contactIds = contactIdInts.stream().map(Long::valueOf).toArray(Long[]::new);
            Long toUserId = Long.valueOf(params.get("toUserId").toString());
            String businessType = (String) params.get("businessType");
            String remark = (String) params.get("remark");
            
            int result = contactResponsibleRelationService.transferContacts(
                contactIds, SecurityUtils.getUserId(), toUserId, businessType, remark);
            return AjaxResult.success("成功转移 " + result + " 个联系人");
        } catch (Exception e) {
            logger.error("转移联系人失败", e);
            return AjaxResult.error("转移失败：" + e.getMessage());
        }
    }

    /**
     * 获取我的联系人列表（按业务类型）
     */
    @GetMapping("/my-contacts")
    @PreAuthorize("@ss.hasPermi('crm:pool:list')")
    public TableDataInfo getMyContacts(@RequestParam(required = false) String businessType) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            startPage();
            List<CrmContactResponsibleRelation> list = 
                contactResponsibleRelationService.getMyContacts(currentUserId, businessType);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("获取我的联系人失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 查询联系人负责人关系列表
     */
    @GetMapping("/relations")
    @PreAuthorize("@ss.hasPermi('crm:pool:list')")
    public TableDataInfo getContactResponsibleRelations(CrmContactResponsibleRelation relation) {
        try {
            startPage();
            List<CrmContactResponsibleRelation> list = 
                contactResponsibleRelationService.selectCrmContactResponsibleRelationList(relation);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("查询联系人负责人关系失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 批量修改联系人负责人关系状态
     */
    @PutMapping("/relations/status")
    @PreAuthorize("@ss.hasPermi('crm:pool:edit')")
    @Log(title = "修改联系人负责人关系状态", businessType = BusinessType.UPDATE)
    public AjaxResult updateRelationStatus(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> relationIdInts = (List<Integer>) params.get("relationIds");
            Long[] relationIds = relationIdInts.stream().map(Long::valueOf).toArray(Long[]::new);
            String newStatus = (String) params.get("newStatus");
            
            int result = 0;
            for (Long relationId : relationIds) {
                CrmContactResponsibleRelation relation = 
                    contactResponsibleRelationService.selectCrmContactResponsibleRelationById(relationId);
                if (relation != null) {
                    relation.setRelationStatus(newStatus);
                    if ("INACTIVE".equals(newStatus) || "TRANSFERRED".equals(newStatus)) {
                        relation.setEndDate(new java.util.Date());
                    }
                    result += contactResponsibleRelationService.updateCrmContactResponsibleRelation(relation);
                }
            }
            
            return AjaxResult.success("成功更新 " + result + " 个关系状态");
        } catch (Exception e) {
            logger.error("更新关系状态失败", e);
            return AjaxResult.error("更新关系状态失败：" + e.getMessage());
        }
    }
}