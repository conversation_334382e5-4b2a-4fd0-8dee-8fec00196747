@echo off
echo 正在设置客户公海管理功能...

echo.
echo 1. 执行数据库脚本...
mysql -h localhost -u mycrm41 -pmycrm41 crm41 < ../sql/customer_pool_tables.sql

if %errorlevel% neq 0 (
    echo 数据库脚本执行失败！
    pause
    exit /b 1
)

echo.
echo 2. 编译项目...
cd ..
mvn clean compile -DskipTests

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 3. 运行测试...
mvn test -Dtest=CrmCustomerPoolControllerTest

echo.
echo 客户公海管理功能设置完成！
echo.
echo 接下来请：
echo 1. 启动后端服务：cd ruoyi-admin && mvn spring-boot:run
echo 2. 启动前端服务：cd frontend && npm run dev
echo 3. 访问 http://localhost:3000 测试功能
echo.
pause