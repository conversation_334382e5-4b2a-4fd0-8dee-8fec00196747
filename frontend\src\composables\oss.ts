export interface OSSPolicy {
    accessKeyId: string;
    policy: string;
    signature: string;
    dir: string;
    host: string;
    expire: number;
    fileName: string;
}

/**
 * 上传文件到OSS
 * @param file 要上传的文件
 * @param policy OSS上传策略
 * @returns 上传成功后的文件URL
 */
export async function uploadToOSS(file: File, policy: OSSPolicy): Promise<string> {
    return new Promise((resolve, reject) => {
        if (!file) {
            return reject(new Error('文件对象不能为空'));
        }

        if (!policy || !policy.host || !policy.accessKeyId || !policy.policy || !policy.signature) {
            return reject(new Error('OSS策略配置不完整'));
        }

        console.log('开始上传文件到OSS:', file.name);
        console.log('OSS策略:', policy);

        try {
            const fileExtension = file.name.split('.').pop() || '';
            const uniqueFileName = policy.fileName || new Date().getTime() + '-' + Math.floor(Math.random() * 1000);
            const objectKey = `${policy.dir}${uniqueFileName}.${fileExtension}`;

            const formData = new FormData();
            formData.append('key', objectKey);
            formData.append('policy', policy.policy);
            formData.append('OSSAccessKeyId', policy.accessKeyId);
            formData.append('success_action_status', '200');
            formData.append('signature', policy.signature);
            formData.append('file', file);

            const uploadUrl = policy.host;

            console.log('上传URL:', uploadUrl);
            console.log('对象键:', objectKey);

            fetch(uploadUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('上传响应状态:', response.status);
                if (response.ok) {
                    const fileUrl = `${uploadUrl}/${objectKey}`;
                    console.log('文件上传成功，URL:', fileUrl);
                    resolve(fileUrl);
                } else {
                    response.text().then(text => {
                        console.error('OSS上传失败响应:', text);
                        reject(new Error(`上传失败，状态码: ${response.status}, 响应: ${text}`));
                    }).catch(() => {
                        reject(new Error(`上传失败，状态码: ${response.status}`));
                    });
                }
            })
            .catch(error => {
                console.error('OSS上传fetch失败:', error);
                reject(error);
            });

        } catch (error) {
            console.error('OSS上传异常:', error);
            reject(error);
        }
    });
}
