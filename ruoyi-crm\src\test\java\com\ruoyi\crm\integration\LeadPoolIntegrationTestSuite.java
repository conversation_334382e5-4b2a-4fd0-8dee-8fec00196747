package com.ruoyi.crm.integration;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord;
import com.ruoyi.common.domain.entity.CrmLeadPool;
import com.ruoyi.common.domain.entity.CrmLeads;
import com.ruoyi.common.mapper.CrmLeadAssignmentRecordMapper;
import com.ruoyi.common.mapper.CrmLeadPoolMapper;
import com.ruoyi.common.mapper.CrmLeadsMapper;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmLeadAssignmentRecordService;
import com.ruoyi.crm.service.ICrmLeadPoolService;

/**
 * 线索池功能完整业务流程集成测试套件
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */

@AutoConfigureWebMvc
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("线索池功能完整业务流程集成测试套件")
public class LeadPoolIntegrationTestSuite extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmLeadPoolService leadPoolService;

    @Autowired
    private ICrmLeadAssignmentRecordService assignmentRecordService;

    @Autowired
    private CrmLeadPoolMapper leadPoolMapper;

    @Autowired
    private CrmLeadAssignmentRecordMapper assignmentRecordMapper;

    @Autowired
    private CrmLeadsMapper leadsMapper;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    // 测试数据ID
    private static Long testLeadId = 5001L;
    private static Long testPoolId;
    private static Long testUserId = 501L;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @Order(1)
    @DisplayName("1. 完整业务流程：线索入池")
    void testCompleteWorkflow_AddLeadToPool() throws Exception {
        // 1. 创建一个线索
        CrmLeads testLead = new CrmLeads();
        testLead.setId(testLeadId);
        testLead.setLeadName("测试线索-集成测试");
        testLead.setCustomerName("测试客户公司");
        testLead.setMobile("13800138000");
        testLead.setEmail("<EMAIL>");
        testLead.setAddress("北京市朝阳区");
        testLead.setCustomerIndustry("IT");
        testLead.setLeadSource("网站");
        testLead.setCreateBy("testUser");
        testLead.setCreateTime(new Date());
        leadsMapper.insertCrmLeads(testLead);

        // 2. 将线索添加到线索池
        int addResult = leadPoolService.addToPool(
            testLeadId, "A", 9, "北京", "IT", "80000", "高质量线索，优先处理");
        assertEquals(1, addResult);

        // 3. 验证线索池中的数据
        CrmLeadPool pool = leadPoolService.getLeadPoolByLeadId(testLeadId);
        assertNotNull(pool);
        assertEquals("available", pool.getPoolStatus());
        assertEquals("A", pool.getQualityLevel());
        assertEquals(9, pool.getPriority());
        assertEquals("北京", pool.getRegion());
        assertEquals("IT", pool.getIndustry());
        assertEquals(new BigDecimal("80000"), pool.getEstimatedValue());

        testPoolId = pool.getId();

        // 4. 通过API验证
        mockMvc.perform(get("/front/crm/leadPool/check/" + testLeadId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));

        System.out.println("✅ 测试1完成：线索成功入池，ID=" + testPoolId);
    }

    @Test
    @Order(2)
    @DisplayName("2. 完整业务流程：手动分配线索")
    void testCompleteWorkflow_ManualAssignment() throws Exception {
        // 1. 手动分配线索
        String assignRequest = String.format("""
            {
                "poolIds": [%d],
                "toUserId": %d,
                "reason": "集成测试-手动分配给销售人员"
            }
            """, testPoolId, testUserId);

        mockMvc.perform(post("/front/crm/leadPool/assign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(assignRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 2. 验证线索池状态变更
        CrmLeadPool pool = leadPoolService.selectCrmLeadPoolById(testPoolId);
        assertEquals("assigned", pool.getPoolStatus());
        assertNotNull(pool.getLastAssignTime());
        assertTrue(pool.getAssignCount() > 0);

        // 3. 验证分配记录生成
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByLeadId(testLeadId);
        assertFalse(records.isEmpty());
        
        CrmLeadAssignmentRecord latestRecord = records.get(0);
        assertEquals("manual", latestRecord.getAssignmentType());
        assertEquals(testUserId, latestRecord.getToUserId());
        assertEquals("集成测试-手动分配给销售人员", latestRecord.getAssignmentReason());

        // 4. 验证线索表中的负责人更新
        CrmLeads lead = leadsMapper.selectCrmLeadsById(testLeadId);
        assertEquals(testUserId.toString(), lead.getResponsiblePersonId());

        System.out.println("✅ 测试2完成：线索成功分配给用户" + testUserId);
    }

    @Test
    @Order(3)
    @DisplayName("3. 完整业务流程：查询分配记录和统计")
    void testCompleteWorkflow_QueryRecordsAndStats() throws Exception {
        // 1. 查询分配记录列表
        mockMvc.perform(get("/front/crm/assignmentRecords/list")
                .param("leadId", testLeadId.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.rows[0].leadId").value(testLeadId))
                .andExpect(jsonPath("$.rows[0].assignmentType").value("manual"));

        // 2. 查询用户的分配记录
        mockMvc.perform(get("/front/crm/assignmentRecords/toUser/" + testUserId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 3. 获取线索池统计信息
        mockMvc.perform(get("/front/crm/leadPool/stats")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalCount").isNumber())
                .andExpect(jsonPath("$.data.assignedCount").isNumber());

        // 4. 获取分配记录统计信息
        mockMvc.perform(get("/front/crm/assignmentRecords/stats")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalCount").isNumber())
                .andExpect(jsonPath("$.data.manualCount").isNumber());

        // 5. 验证统计数据的准确性
        Map<String, Object> poolStats = leadPoolService.getLeadPoolStats();
        assertTrue((Integer) poolStats.get("totalCount") >= 1);
        assertTrue((Integer) poolStats.get("assignedCount") >= 1);

        Map<String, Object> recordStats = assignmentRecordService.getAssignmentRecordStats();
        assertTrue((Integer) recordStats.get("totalCount") >= 1);
        assertTrue((Integer) recordStats.get("manualCount") >= 1);

        System.out.println("✅ 测试3完成：查询和统计功能正常");
    }

    @Test
    @Order(4)
    @DisplayName("4. 完整业务流程：回收线索到池中")
    void testCompleteWorkflow_RecycleLead() throws Exception {
        // 1. 回收线索到池中
        String recycleRequest = String.format("""
            {
                "leadIds": [%d],
                "reason": "集成测试-线索回收，重新分配"
            }
            """, testLeadId);

        mockMvc.perform(post("/front/crm/leadPool/recycle")
                .contentType(MediaType.APPLICATION_JSON)
                .content(recycleRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 2. 验证线索池状态变更
        CrmLeadPool pool = leadPoolService.getLeadPoolByLeadId(testLeadId);
        assertEquals("available", pool.getPoolStatus());
        assertEquals("recycled", pool.getSourceType());

        // 3. 验证线索表中的负责人清空
        CrmLeads lead = leadsMapper.selectCrmLeadsById(testLeadId);
        assertTrue(lead.getResponsiblePersonId() == null || lead.getResponsiblePersonId().isEmpty());

        // 4. 验证回收记录生成
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByLeadId(testLeadId);
        boolean hasRecycleRecord = records.stream()
            .anyMatch(record -> "recycle".equals(record.getAssignmentType()));
        assertTrue(hasRecycleRecord);

        System.out.println("✅ 测试4完成：线索成功回收到池中");
    }

    @Test
    @Order(5)
    @DisplayName("5. 完整业务流程：抢单功能")
    void testCompleteWorkflow_GrabLead() throws Exception {
        Long grabUserId = 502L;
        
        // 1. 销售人员抢单
        String grabRequest = String.format("""
            {
                "userId": %d,
                "reason": "集成测试-销售人员主动抢单"
            }
            """, grabUserId);

        mockMvc.perform(post("/front/crm/leadPool/grab/" + testPoolId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(grabRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 2. 验证线索池状态变更
        CrmLeadPool pool = leadPoolService.selectCrmLeadPoolById(testPoolId);
        assertEquals("assigned", pool.getPoolStatus());
        assertTrue(pool.getAssignCount() >= 2); // 之前分配过一次，现在又分配一次

        // 3. 验证线索表中的负责人更新
        CrmLeads lead = leadsMapper.selectCrmLeadsById(testLeadId);
        assertEquals(grabUserId.toString(), lead.getResponsiblePersonId());

        // 4. 验证抢单记录生成
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByAssignmentType("grab");
        boolean hasGrabRecord = records.stream()
            .anyMatch(record -> record.getLeadId().equals(testLeadId) && record.getToUserId().equals(grabUserId));
        assertTrue(hasGrabRecord);

        System.out.println("✅ 测试5完成：抢单功能正常，用户" + grabUserId + "成功抢到线索");
    }

    @Test
    @Order(6)
    @DisplayName("6. 完整业务流程：批量操作测试")
    void testCompleteWorkflow_BatchOperations() throws Exception {
        // 1. 准备多个线索池数据
        Long[] additionalLeadIds = {5002L, 5003L};
        Long[] additionalPoolIds = new Long[2];
        
        for (int i = 0; i < additionalLeadIds.length; i++) {
            // 创建线索
            CrmLeads lead = new CrmLeads();
            lead.setId(additionalLeadIds[i]);
            lead.setLeadName("批量测试线索-" + (i + 1));
            lead.setCustomerName("批量测试客户-" + (i + 1));
            lead.setMobile("1380013800" + i);
            lead.setCreateBy("testUser");
            lead.setCreateTime(new Date());
            leadsMapper.insertCrmLeads(lead);
            
            // 添加到线索池
            leadPoolService.addToPool(additionalLeadIds[i], "B", 5, "上海", "教育", "30000", "批量测试线索");
            CrmLeadPool pool = leadPoolService.getLeadPoolByLeadId(additionalLeadIds[i]);
            additionalPoolIds[i] = pool.getId();
        }

        // 2. 批量分配测试
        String batchAssignRequest = String.format("""
            {
                "poolIds": [%d, %d],
                "userIds": [503, 504],
                "reason": "集成测试-批量分配"
            }
            """, additionalPoolIds[0], additionalPoolIds[1]);

        mockMvc.perform(post("/front/crm/leadPool/batchAssign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(batchAssignRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 验证批量分配结果
        for (Long poolId : additionalPoolIds) {
            CrmLeadPool pool = leadPoolService.selectCrmLeadPoolById(poolId);
            assertEquals("assigned", pool.getPoolStatus());
        }

        // 4. 批量回收测试
        String batchRecycleRequest = String.format("""
            {
                "leadIds": [%d, %d],
                "reason": "集成测试-批量回收"
            }
            """, additionalLeadIds[0], additionalLeadIds[1]);

        mockMvc.perform(post("/front/crm/leadPool/recycle")
                .contentType(MediaType.APPLICATION_JSON)
                .content(batchRecycleRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 5. 验证批量回收结果
        for (Long leadId : additionalLeadIds) {
            CrmLeadPool pool = leadPoolService.getLeadPoolByLeadId(leadId);
            assertEquals("available", pool.getPoolStatus());
            assertEquals("recycled", pool.getSourceType());
        }

        System.out.println("✅ 测试6完成：批量操作功能正常");
    }

    @Test
    @Order(7)
    @DisplayName("7. 完整业务流程：边界条件和异常处理")
    void testCompleteWorkflow_EdgeCasesAndExceptions() throws Exception {
        // 1. 测试重复分配（应该失败）
        String duplicateAssignRequest = String.format("""
            {
                "poolIds": [%d],
                "toUserId": 505,
                "reason": "重复分配测试"
            }
            """, testPoolId);

        // 线索已经被分配，再次分配应该返回0
        int result = leadPoolService.assignLeads(new Long[]{testPoolId}, 505L, "重复分配测试");
        assertEquals(0, result);

        // 2. 测试分配不存在的线索池
        int invalidResult = leadPoolService.assignLeads(new Long[]{99999L}, 505L, "无效ID测试");
        assertEquals(0, invalidResult);

        // 3. 测试查询不存在的记录
        mockMvc.perform(get("/front/crm/assignmentRecords/lead/99999")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isEmpty());

        // 4. 测试检查不存在的线索
        mockMvc.perform(get("/front/crm/leadPool/check/99999")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(false));

        System.out.println("✅ 测试7完成：边界条件和异常处理正常");
    }

    @Test
    @Order(8)
    @DisplayName("8. 完整业务流程：数据一致性验证")
    void testCompleteWorkflow_DataConsistency() throws Exception {
        // 1. 验证线索池和分配记录的数据一致性
        List<CrmLeadPool> allPools = leadPoolService.selectCrmLeadPoolList(new CrmLeadPool());
        List<CrmLeadAssignmentRecord> allRecords = assignmentRecordService.selectCrmLeadAssignmentRecordList(new CrmLeadAssignmentRecord());

        // 2. 验证每个已分配的线索池都有对应的分配记录
        for (CrmLeadPool pool : allPools) {
            if ("assigned".equals(pool.getPoolStatus()) && pool.getLeadId() != null) {
                List<CrmLeadAssignmentRecord> poolRecords = assignmentRecordService.getRecordsByLeadId(pool.getLeadId());
                assertFalse(poolRecords.isEmpty(), "已分配的线索池应该有分配记录");
            }
        }

        // 3. 验证分配次数的准确性
        for (CrmLeadPool pool : allPools) {
            if (pool.getLeadId() != null) {
                List<CrmLeadAssignmentRecord> poolRecords = assignmentRecordService.getRecordsByLeadId(pool.getLeadId());
                long assignmentCount = poolRecords.stream()
                    .filter(record -> !"recycle".equals(record.getAssignmentType()))
                    .count();
                assertTrue(pool.getAssignCount() >= assignmentCount, "分配次数应该大于等于实际分配记录数");
            }
        }

        // 4. 验证统计数据的准确性
        Map<String, Object> poolStats = leadPoolService.getLeadPoolStats();
        Map<String, Object> recordStats = assignmentRecordService.getAssignmentRecordStats();

        int totalPools = allPools.size();
        int totalRecords = allRecords.size();

        assertEquals(totalPools, (Integer) poolStats.get("totalCount"));
        assertEquals(totalRecords, (Integer) recordStats.get("totalCount"));

        System.out.println("✅ 测试8完成：数据一致性验证通过");
        System.out.println("📊 最终统计：线索池总数=" + totalPools + "，分配记录总数=" + totalRecords);
    }
}
