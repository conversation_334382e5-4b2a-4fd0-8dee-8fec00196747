package com.ruoyi.common.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.ruoyi.common.domain.entity.CrmOrderAssignmentLog;

/**
 * 订单分配历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Mapper
public interface CrmOrderAssignmentLogMapper {
    
    /**
     * 查询订单分配历史
     * 
     * @param id 订单分配历史主键
     * @return 订单分配历史
     */
    public CrmOrderAssignmentLog selectCrmOrderAssignmentLogById(Long id);

    /**
     * 查询订单分配历史列表
     * 
     * @param crmOrderAssignmentLog 订单分配历史
     * @return 订单分配历史集合
     */
    public List<CrmOrderAssignmentLog> selectCrmOrderAssignmentLogList(CrmOrderAssignmentLog crmOrderAssignmentLog);

    /**
     * 根据订单ID查询分配历史
     * 
     * @param orderId 订单ID
     * @return 分配历史列表
     */
    public List<CrmOrderAssignmentLog> selectCrmOrderAssignmentLogByOrderId(Long orderId);

    /**
     * 根据用户ID查询分配历史
     * 
     * @param userId 用户ID
     * @return 分配历史列表
     */
    public List<CrmOrderAssignmentLog> selectCrmOrderAssignmentLogByUserId(Long userId);

    /**
     * 根据操作类型查询分配历史
     * 
     * @param actionType 操作类型
     * @return 分配历史列表
     */
    public List<CrmOrderAssignmentLog> selectCrmOrderAssignmentLogByActionType(String actionType);

    /**
     * 查询用户的分配统计
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public List<CrmOrderAssignmentLog> selectAssignmentStatsByUser(@Param("userId") Long userId, 
                                                                   @Param("startTime") String startTime, 
                                                                   @Param("endTime") String endTime);

    /**
     * 查询订单的最新分配记录
     * 
     * @param orderId 订单ID
     * @return 最新分配记录
     */
    public CrmOrderAssignmentLog selectLatestAssignmentByOrderId(Long orderId);

    /**
     * 查询用户今日抢单次数
     * 
     * @param userId 用户ID
     * @return 抢单次数
     */
    public int countTodayGrabsByUserId(Long userId);

    /**
     * 查询订单分配次数
     * 
     * @param orderId 订单ID
     * @return 分配次数
     */
    public int countAssignmentsByOrderId(Long orderId);

    /**
     * 新增订单分配历史
     * 
     * @param crmOrderAssignmentLog 订单分配历史
     * @return 结果
     */
    public int insertCrmOrderAssignmentLog(CrmOrderAssignmentLog crmOrderAssignmentLog);

    /**
     * 修改订单分配历史
     * 
     * @param crmOrderAssignmentLog 订单分配历史
     * @return 结果
     */
    public int updateCrmOrderAssignmentLog(CrmOrderAssignmentLog crmOrderAssignmentLog);

    /**
     * 删除订单分配历史
     * 
     * @param id 订单分配历史主键
     * @return 结果
     */
    public int deleteCrmOrderAssignmentLogById(Long id);

    /**
     * 批量删除订单分配历史
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmOrderAssignmentLogByIds(Long[] ids);

    /**
     * 根据订单ID删除分配历史
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int deleteCrmOrderAssignmentLogByOrderId(Long orderId);

    /**
     * 批量插入分配历史
     * 
     * @param assignmentLogs 分配历史列表
     * @return 结果
     */
    public int batchInsertCrmOrderAssignmentLog(List<CrmOrderAssignmentLog> assignmentLogs);

    /**
     * 查询分配历史统计报表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param actionType 操作类型
     * @return 统计结果
     */
    public List<CrmOrderAssignmentLog> selectAssignmentStatistics(@Param("startTime") String startTime, 
                                                                  @Param("endTime") String endTime, 
                                                                  @Param("actionType") String actionType);

    /**
     * 查询用户工作量统计
     * 
     * @param userIds 用户ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工作量统计
     */
    public List<CrmOrderAssignmentLog> selectWorkloadStatistics(@Param("userIds") List<Long> userIds, 
                                                                @Param("startTime") String startTime, 
                                                                @Param("endTime") String endTime);
}
