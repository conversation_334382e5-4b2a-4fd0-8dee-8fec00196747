package com.ruoyi.common.mapper;

import com.ruoyi.common.domain.entity.CrmPoolRules;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 公海规则配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface CrmPoolRulesMapper {
    
    /**
     * 新增公海规则
     * 
     * @param rule 公海规则
     * @return 结果
     */
    int insert(CrmPoolRules rule);
    
    /**
     * 修改公海规则
     * 
     * @param rule 公海规则
     * @return 结果
     */
    int update(CrmPoolRules rule);
    
    /**
     * 查询公海规则列表
     * 
     * @param rule 查询条件
     * @return 公海规则列表
     */
    List<CrmPoolRules> selectList(CrmPoolRules rule);
    
    /**
     * 根据ID查询公海规则
     * 
     * @param id 主键ID
     * @return 公海规则
     */
    CrmPoolRules selectById(@Param("id") Long id);
    
    /**
     * 删除公海规则
     * 
     * @param id 主键ID
     * @return 结果
     */
    int deleteById(@Param("id") Long id);
    
}