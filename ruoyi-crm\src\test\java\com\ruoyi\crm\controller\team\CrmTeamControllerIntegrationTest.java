package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmTeam;
import com.ruoyi.common.domain.entity.CrmTeamMember;
import com.ruoyi.common.service.ICrmTeamService;
import com.ruoyi.crm.BaseTestCase;

/**
 * CrmTeamController 集成测试类
 * 使用真实的数据库和完整的Spring上下文进行测试
 * 
 * <AUTHOR>
 * @date 2024-08-16
 */

@AutoConfigureWebMvc
@DisplayName("团队管理控制器集成测试")
class CrmTeamControllerIntegrationTest  extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(CrmTeamControllerIntegrationTest.class);
    
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmTeamService crmTeamService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        // 关键：调用父类的setUpBase方法，它会设置默认的认证信息
        super.setUpBase(); 
        
        // 设置MockMvc，并应用Spring Security过滤器链
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .apply(springSecurity()) // 应用Spring Security
                .alwaysDo(print())
                .build();
    }

    @AfterEach
    void tearDown() {
        // BaseTestCase.tearDownBase() 会自动清理安全上下文
    }

    // 工具方法，创建测试团队
    private CrmTeam createTestTeam(String teamName, String description) {
        CrmTeam testTeam = new CrmTeam();
        testTeam.setTeamName(teamName);
        testTeam.setLeaderId(1L); // 默认使用admin用户作为团队负责人
        testTeam.setDescription(description);

        // 插入测试数据
        crmTeamService.insertCrmTeam(testTeam);
        assertNotNull(testTeam.getId(), "测试数据创建失败");
        return testTeam;
    }

    // 工具方法，清理测试团队
    private void cleanupTestTeam(Long teamId) {
        if (teamId != null) {
            try {
                crmTeamService.deleteCrmTeamById(teamId);
            } catch (Exception e) {
                // 忽略清理错误
                logger.warn("清理测试团队时发生错误: {}", e.getMessage());
            }
        }
    }

    @Nested
    @DisplayName("团队CRUD集成测试")
    class CrudIntegrationTests {

        @Test
        @DisplayName("团队创建时负责人自动成为成员测试")
        void testCreateTeamWithLeaderAutoMember() throws Exception {
            // 创建团队
            CrmTeam newTeam = new CrmTeam();
            newTeam.setTeamName("负责人自动成员测试团队");
            newTeam.setLeaderId(1L);
            newTeam.setDescription("测试负责人自动成为成员");

            MvcResult createResult = mockMvc.perform(post("/crm/team")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newTeam)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            // 获取创建的团队ID
            String createResponseContent = createResult.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdTeamId = null;
            
            if (rootNode.path("data").path("id").isNumber()) {
                createdTeamId = rootNode.path("data").path("id").asLong();
            } else {
                CrmTeam query = new CrmTeam();
                query.setTeamName("负责人自动成员测试团队");
                List<CrmTeam> teams = crmTeamService.selectCrmTeamList(query);
                if (!teams.isEmpty()) {
                    createdTeamId = teams.get(0).getId();
                }
            }
            
            assertNotNull(createdTeamId, "创建团队后应能获取到ID");
            
            try {
                // 验证负责人已自动成为团队成员
                MvcResult membersResult = mockMvc.perform(get("/crm/team/{id}/members", createdTeamId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200))
                        .andReturn();

                String membersResponseContent = membersResult.getResponse().getContentAsString();
                JsonNode membersRootNode = objectMapper.readTree(membersResponseContent);
                JsonNode membersDataNode = membersRootNode.path("data");
                
                // 验证成员列表中包含负责人
                boolean leaderFoundAsMember = false;
                for (JsonNode memberNode : membersDataNode) {
                    Long userId = memberNode.path("userId").asLong();
                    String role = memberNode.path("roleInTeam").asText();
                    if (userId == 1L && "owner".equals(role)) {
                        leaderFoundAsMember = true;
                        break;
                    }
                }
                
                assertTrue(leaderFoundAsMember, "负责人应自动成为团队成员，角色为owner");
                
            } finally {
                // 清理测试数据
                mockMvc.perform(delete("/crm/team/{ids}", createdTeamId))
                        .andExpect(status().isOk());
            }
        }

        @Test
        @DisplayName("完整的CRUD流程测试")
        void testFullCrudFlow() throws Exception {
            // 1. 创建团队
            CrmTeam newTeam = new CrmTeam();
            newTeam.setTeamName("CRUD测试团队");
            newTeam.setLeaderId(1L);
            newTeam.setDescription("CRUD测试描述");

            MvcResult createResult = mockMvc.perform(post("/crm/team")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newTeam)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            
            // 从响应中获取团队ID
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdTeamId = null;
            
            // 检查响应中是否包含data.id，如果不包含，则从service中查询
            if (rootNode.path("data").path("id").isNumber()) {
                createdTeamId = rootNode.path("data").path("id").asLong();
            } else {
                // 如果响应中没有返回ID，则通过查询获取
                CrmTeam query = new CrmTeam();
                query.setTeamName("CRUD测试团队");
                List<CrmTeam> teams = crmTeamService.selectCrmTeamList(query);
                if (!teams.isEmpty()) {
                    createdTeamId = teams.get(0).getId();
                }
            }
            
            assertNotNull(createdTeamId, "创建团队后应能获取到ID");
            logger.info("创建的团队ID: {}", createdTeamId);
            
            try {
                // 2. 查询单个团队
                mockMvc.perform(get("/crm/team/{id}", createdTeamId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.teamName").value("CRUD测试团队"))
                        .andExpect(jsonPath("$.data.description").value("CRUD测试描述"));

                // 3. 修改团队
                newTeam.setId(createdTeamId);
                newTeam.setTeamName("修改后的团队名称");
                newTeam.setDescription("修改后的描述");

                mockMvc.perform(put("/crm/team")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newTeam)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证修改结果
                mockMvc.perform(get("/crm/team/{id}", createdTeamId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.data.teamName").value("修改后的团队名称"))
                        .andExpect(jsonPath("$.data.description").value("修改后的描述"));

            } finally {
                // 5. 删除团队
                mockMvc.perform(delete("/crm/team/{ids}", createdTeamId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 6. 验证删除结果 - 查询应该返回null或错误
                MvcResult deleteVerifyResult = mockMvc.perform(get("/crm/team/{id}", createdTeamId))
                        .andExpect(status().isOk())
                        .andReturn();
                
                String deleteVerifyContent = deleteVerifyResult.getResponse().getContentAsString();
                AjaxResult deleteVerifyResponse = objectMapper.readValue(deleteVerifyContent, AjaxResult.class);
                // 删除后查询应该返回错误或null
                assertTrue(deleteVerifyResponse.get("data") == null || !deleteVerifyResponse.isSuccess(),
                        "删除后查询应返回null或错误");
            }
        }

        @Test
        @DisplayName("查询团队列表 - 带分页和筛选")
        void testGetTeamListWithFilters () throws Exception {
            // 创建测试数据
            CrmTeam testTeam = createTestTeam("列表测试团队", "列表测试描述");
            Long testTeamId = testTeam.getId();
            
            try {
                MvcResult result = mockMvc.perform(get("/crm/team/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .param("teamName", "列表测试"))
                        .andExpect(status().isOk())
                        .andReturn();

                String responseContent = result.getResponse().getContentAsString();
                TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
                
                // 打印响应内容
                logger.info("响应内容: {}", objectMapper.writeValueAsString(response));
                
                assertNotNull(response);
                assertEquals(200, response.getCode());
                assertNotNull(response.getRows());
                assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
            } finally {
                cleanupTestTeam(testTeamId);
            }
        }
    }

    @Nested
    @DisplayName("团队成员管理集成测试")
    class TeamMemberIntegrationTests {
        
        private CrmTeam testTeam;
        private Long testTeamId;
        
        @BeforeEach
        void setUpTeamMemberTests() {
            // 创建测试团队
            testTeam = createTestTeam("成员测试团队", "成员测试描述");
            testTeamId = testTeam.getId();
        }
        
        @AfterEach
        void tearDownTeamMemberTests() {
            cleanupTestTeam(testTeamId);
        }

        @Test
        @DisplayName("添加团队成员测试")
        void testAddTeamMember() throws Exception {
            // 创建团队成员
            CrmTeamMember member = new CrmTeamMember();
            member.setTeamId(testTeamId);
            member.setUserId(2L); // 使用ID为2的用户作为测试
            member.setRoleInTeam("member");

            // 添加成员
            mockMvc.perform(post("/crm/team/member")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(member)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            // 验证成员是否添加成功
            MvcResult result = mockMvc.perform(get("/crm/team/{id}/members", testTeamId))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(responseContent);
            JsonNode dataNode = rootNode.path("data");
            
            // 验证成员列表中包含新添加的成员
            boolean found = false;
            for (JsonNode memberNode : dataNode) {
                if (memberNode.path("userId").asLong() == 2L) {
                    found = true;
                    break;
                }
            }
            
            assertTrue(found, "成员列表中应包含新添加的成员");
        }

        @Test
        @DisplayName("移除团队成员测试")
        void testRemoveTeamMember() throws Exception {
            // 先添加成员
            CrmTeamMember member = new CrmTeamMember();
            member.setTeamId(testTeamId);
            member.setUserId(2L);
            member.setRoleInTeam("member");
            
            crmTeamService.addTeamMember(member);
            
            // 验证成员已添加
            List<CrmTeamMember> membersBeforeRemove = crmTeamService.selectTeamMembersByTeamId(testTeamId);
            boolean memberExists = membersBeforeRemove.stream()
                    .anyMatch(m -> m.getUserId().equals(2L));
            assertTrue(memberExists, "测试前成员应该存在");
            
            // 移除成员
            mockMvc.perform(delete("/crm/team/member/{teamId}/{userId}", testTeamId, 2L))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
            
            // 验证成员已移除
            List<CrmTeamMember> membersAfterRemove = crmTeamService.selectTeamMembersByTeamId(testTeamId);
            boolean memberRemoved = membersAfterRemove.stream()
                    .noneMatch(m -> m.getUserId().equals(2L));
            assertTrue(memberRemoved, "成员应该已被移除");
        }

        @Test
        @DisplayName("获取团队成员列表测试")
        void testListTeamMembers() throws Exception {
            // 添加两个成员
            CrmTeamMember member1 = new CrmTeamMember();
            member1.setTeamId(testTeamId);
            member1.setUserId(2L);
            member1.setRoleInTeam("member");
            
            CrmTeamMember member2 = new CrmTeamMember();
            member2.setTeamId(testTeamId);
            member2.setUserId(3L);
            member2.setRoleInTeam("member");
            
            crmTeamService.addTeamMember(member1);
            crmTeamService.addTeamMember(member2);
            
            // 获取成员列表
            MvcResult result = mockMvc.perform(get("/crm/team/{id}/members", testTeamId))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(responseContent);
            JsonNode dataNode = rootNode.path("data");
            
            // 验证成员列表包含两个成员
            assertTrue(dataNode.isArray(), "响应数据应该是数组");
            assertTrue(dataNode.size() >= 2, "成员列表应至少包含两个成员");
            
            // 验证成员ID
            boolean foundMember1 = false;
            boolean foundMember2 = false;
            
            for (JsonNode memberNode : dataNode) {
                Long userId = memberNode.path("userId").asLong();
                if (userId == 2L) foundMember1 = true;
                if (userId == 3L) foundMember2 = true;
            }
            
            assertTrue(foundMember1 && foundMember2, "成员列表应包含添加的两个成员");
        }
    
        @Test
        @DisplayName("修改团队成员角色测试")
        void testUpdateTeamMemberRole() throws Exception {
            // 1. 添加测试成员
            CrmTeamMember member = new CrmTeamMember();
            member.setTeamId(testTeamId);
            member.setUserId(2L);
            member.setRoleInTeam("member");
            mockMvc.perform(post("/crm/team/member")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(member)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
    
            // 2. 修改角色
            String requestBody = String.format("{\"teamId\":%d,\"userId\":2,\"roleInTeam\":\"admin\"}", testTeamId);
            mockMvc.perform(put("/crm/team/member/role")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
    
            // 3. 验证数据库中角色更新
            List<CrmTeamMember> members = crmTeamService.selectTeamMembersByTeamId(testTeamId);
            boolean roleUpdated = members.stream()
                    .anyMatch(m -> m.getUserId().equals(2L) && "admin".equals(m.getRoleInTeam()));
            assertTrue(roleUpdated, "团队成员角色应更新为admin");
        }
    }

    @Nested
    @DisplayName("边界测试和参数验证")
    class BoundaryAndValidationTests {
        
        @Test
        @DisplayName("创建团队 - 空团队名称")
        void testCreateTeamWithEmptyName() throws Exception {
            CrmTeam newTeam = new CrmTeam();
            newTeam.setTeamName("");  // 空名称
            newTeam.setLeaderId(1L);
            newTeam.setDescription("测试描述");

            mockMvc.perform(post("/crm/team")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newTeam)))
                    .andExpect(status().isBadRequest()); // 预期 400 Bad Request
        }
        
        @Test
        @DisplayName("创建团队 - 空负责人")
        void testCreateTeamWithoutLeader() throws Exception {
            CrmTeam newTeam = new CrmTeam();
            newTeam.setTeamName("无负责人团队");
            newTeam.setLeaderId(null);  // 无负责人
            newTeam.setDescription("测试描述");

            MvcResult result = mockMvc.perform(post("/crm/team")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newTeam)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();
            
            // 获取团队ID进行验证
            String responseContent = result.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(responseContent);
            Long createdTeamId = null;
            
            if (rootNode.path("data").path("id").isNumber()) {
                createdTeamId = rootNode.path("data").path("id").asLong();
            } else {
                CrmTeam query = new CrmTeam();
                query.setTeamName("无负责人团队");
                List<CrmTeam> teams = crmTeamService.selectCrmTeamList(query);
                if (!teams.isEmpty()) {
                    createdTeamId = teams.get(0).getId();
                }
            }
            
            if (createdTeamId != null) {
                try {
                    // 验证无负责人的团队成员列表应该为空
                    MvcResult membersResult = mockMvc.perform(get("/crm/team/{id}/members", createdTeamId))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.code").value(200))
                            .andReturn();

                    String membersResponseContent = membersResult.getResponse().getContentAsString();
                    JsonNode membersRootNode = objectMapper.readTree(membersResponseContent);
                    JsonNode membersDataNode = membersRootNode.path("data");
                    
                    assertTrue(membersDataNode.isArray(), "成员列表应该是数组");
                    assertEquals(0, membersDataNode.size(), "无负责人团队的成员列表应为空");
                } finally {
                    // 清理测试数据
                    mockMvc.perform(delete("/crm/team/{ids}", createdTeamId));
                }
            }
        }
        
        @Test
        @DisplayName("创建团队 - 超长团队名称")
        void testCreateTeamWithLongName() throws Exception {
            CrmTeam newTeam = new CrmTeam();
            newTeam.setTeamName("a".repeat(101));  // 超长名称
            newTeam.setLeaderId(1L);
            newTeam.setDescription("测试描述");

            mockMvc.perform(post("/crm/team")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newTeam)))
                    .andExpect(status().isBadRequest()); // 预期 400 Bad Request
        }
        
        @Test
        @DisplayName("添加团队成员 - 空参数")
        void testAddTeamMemberWithEmptyParams() throws Exception {
            CrmTeamMember member = new CrmTeamMember();
            // 不设置任何参数
            
            mockMvc.perform(post("/crm/team/member")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(member)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("团队ID不能为空"));
        }
        
        @Test
        @DisplayName("添加团队成员 - 只有团队ID无用户ID")
        void testAddTeamMemberWithoutUserId() throws Exception {
            CrmTeam testTeam = createTestTeam("边界测试团队", "边界测试描述");
            
            try {
                CrmTeamMember member = new CrmTeamMember();
                member.setTeamId(testTeam.getId());
                // 不设置用户ID
                
                mockMvc.perform(post("/crm/team/member")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(member)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(500))
                        .andExpect(jsonPath("$.msg").value("用户ID不能为空"));
            } finally {
                cleanupTestTeam(testTeam.getId());
            }
        }
        
        @Test
        @DisplayName("添加重复团队成员")
        void testAddDuplicateTeamMember() throws Exception {
            CrmTeam testTeam = createTestTeam("重复成员测试团队", "重复成员测试描述");
            
            try {
                CrmTeamMember member = new CrmTeamMember();
                member.setTeamId(testTeam.getId());
                member.setUserId(2L);
                member.setRoleInTeam("member");
                
                // 第一次添加
                mockMvc.perform(post("/crm/team/member")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(member)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));
                
                // 第二次添加相同成员 - 应该失败
                mockMvc.perform(post("/crm/team/member")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(member)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(500))
                        .andExpect(jsonPath("$.msg").value("添加失败，可能该用户已经是团队成员"));
            } finally {
                cleanupTestTeam(testTeam.getId());
            }
        }
        
        @Test
        @DisplayName("移除不存在的团队成员")
        void testRemoveNonExistentTeamMember() throws Exception {
            CrmTeam testTeam = createTestTeam("移除成员测试团队", "移除成员测试描述");
            
            try {
                // 尝试移除不存在的成员
                mockMvc.perform(delete("/crm/team/member/{teamId}/{userId}", testTeam.getId(), 999L))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(500))
                        .andExpect(jsonPath("$.msg").value("移除失败，可能该用户不是团队成员"));
            } finally {
                cleanupTestTeam(testTeam.getId());
            }
        }
        
        @Test
        @DisplayName("批量删除团队")
        void testBatchDeleteTeams() throws Exception {
            // 创建两个测试团队
            CrmTeam team1 = createTestTeam("批量删除测试团队1", "批量删除测试描述1");
            CrmTeam team2 = createTestTeam("批量删除测试团队2", "批量删除测试描述2");
            
            // 批量删除
            String ids = team1.getId() + "," + team2.getId();
            mockMvc.perform(delete("/crm/team/{ids}", ids))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
            
            // 验证删除结果
            mockMvc.perform(get("/crm/team/{id}", team1.getId()))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("团队不存在"));
            
            mockMvc.perform(get("/crm/team/{id}", team2.getId()))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("团队不存在"));
        }
    }

    @Nested
    @DisplayName("异常处理集成测试")
    class ExceptionIntegrationTests {
        
        @Test
        @DisplayName("获取不存在的团队")
        void testGetNonExistentTeam() throws Exception {
            mockMvc.perform(get("/crm/team/{id}", 99999L)) // 不存在的ID
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("团队不存在"));
        }
        
        @Test
        @DisplayName("向不存在的团队添加成员")
        void testAddMemberToNonExistentTeam() throws Exception {
            CrmTeamMember member = new CrmTeamMember();
            member.setTeamId(99999L); // 不存在的团队ID
            member.setUserId(2L);
            member.setRoleInTeam("member");
            
            mockMvc.perform(post("/crm/team/member")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(member)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("团队不存在"));
        }
        
        @Test
        @DisplayName("从不存在的团队移除成员")
        void testRemoveMemberFromNonExistentTeam() throws Exception {
            mockMvc.perform(delete("/crm/team/member/{teamId}/{userId}", 99999L, 2L))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("团队不存在"));
        }
        
        @Test
        @DisplayName("获取不存在团队的成员列表")
        void testListMembersOfNonExistentTeam() throws Exception {
            mockMvc.perform(get("/crm/team/{id}/members", 99999L))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("团队不存在"));
        }
    }
}
