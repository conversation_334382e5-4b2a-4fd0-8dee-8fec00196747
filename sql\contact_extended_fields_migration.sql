-- 为 crm_business_contacts 表添加扩展字段以支持更丰富的联系人信息
-- 执行时间: 2025-06-29

-- 添加固定电话字段（如果不存在）
ALTER TABLE crm_business_contacts 
ADD COLUMN IF NOT EXISTS telephone VARCHAR(20) COMMENT '固定电话' AFTER phone;

-- 添加生日字段（如果不存在）
ALTER TABLE crm_business_contacts 
ADD COLUMN IF NOT EXISTS birthday DATE COMMENT '生日' AFTER gender;

-- 添加部门字段（如果不存在）
ALTER TABLE crm_business_contacts 
ADD COLUMN IF NOT EXISTS department VARCHAR(100) COMMENT '部门' AFTER position;

-- 添加决策角色字段（如果不存在）
ALTER TABLE crm_business_contacts 
ADD COLUMN IF NOT EXISTS decision_role VARCHAR(50) COMMENT '决策角色(决策者/影响者/使用者/其他)' AFTER department;

-- 添加联系人级别字段（如果不存在）
ALTER TABLE crm_business_contacts 
ADD COLUMN IF NOT EXISTS contact_level VARCHAR(10) COMMENT '联系人级别(A/B/C/D)' AFTER decision_role;

-- 添加状态字段（如果不存在）
ALTER TABLE crm_business_contacts 
ADD COLUMN IF NOT EXISTS status CHAR(1) DEFAULT '0' COMMENT '状态(0有效 1无效)' AFTER contact_level;

-- 为新字段添加索引以提高查询性能
ALTER TABLE crm_business_contacts 
ADD INDEX IF NOT EXISTS idx_department (department);

ALTER TABLE crm_business_contacts 
ADD INDEX IF NOT EXISTS idx_decision_role (decision_role);

ALTER TABLE crm_business_contacts 
ADD INDEX IF NOT EXISTS idx_contact_level (contact_level);

ALTER TABLE crm_business_contacts 
ADD INDEX IF NOT EXISTS idx_status (status);

-- 验证表结构
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'crm41' AND TABLE_NAME = 'crm_business_contacts' 
-- ORDER BY ORDINAL_POSITION;
