-- 统一的业务操作日志表
CREATE TABLE `business_operation_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `business_type` varchar(50) NOT NULL COMMENT '业务类型',
    `business_id` bigint(20) NOT NULL COMMENT '业务ID',
    `business_name` varchar(200) DEFAULT NULL COMMENT '业务名称',
    `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
    `operation_desc` varchar(500) DEFAULT NULL COMMENT '操作描述',
    `field_changes` json DEFAULT NULL COMMENT '字段变更详情',
    `before_data` json DEFAULT NULL COMMENT '变更前数据',
    `after_data` json DEFAULT NULL COMMENT '变更后数据',
    `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
    `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
    `operation_time` datetime NOT NULL COMMENT '操作时间',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `extra_data` text DEFAULT NULL COMMENT '扩展数据',
    `success` tinyint(1) DEFAULT 1 COMMENT '是否成功',
    `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
    `method_name` varchar(200) DEFAULT NULL COMMENT '方法名',
    `request_params` text DEFAULT NULL COMMENT '请求参数',
    `response_result` text DEFAULT NULL COMMENT '响应结果',
    `execution_time` bigint(20) DEFAULT NULL COMMENT '执行时间（毫秒）',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_business` (`business_type`, `business_id`),
    KEY `idx_operation` (`operation_type`, `operation_time`),
    KEY `idx_operator` (`operator_id`, `operation_time`),
    KEY `idx_operation_time` (`operation_time`),
    KEY `idx_business_type` (`business_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='业务操作日志表';

-- 插入一些示例数据
INSERT INTO `business_operation_log` (`business_type`, `business_id`, `business_name`, `operation_type`, `operation_desc`, `operator_id`, `operator_name`, `operation_time`, `ip_address`, `success`) VALUES
('LEAD', 1, '测试线索', 'CREATE', '创建线索', 1, 'admin', NOW(), '127.0.0.1', 1),
('LEAD', 1, '测试线索', 'UPDATE', '更新线索', 1, 'admin', NOW(), '127.0.0.1', 1),
('CONTACT', 1, '测试联系人', 'CREATE', '创建联系人', 1, 'admin', NOW(), '127.0.0.1', 1);