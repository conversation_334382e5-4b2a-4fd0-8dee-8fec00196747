package com.ruoyi.crm.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.crm.domain.dto.ThreeDPrintingOrderCreateDTO;
import com.ruoyi.crm.domain.vo.ThreeDPrintingOrderResultVO;
import com.ruoyi.crm.service.IThreeDPrintingCRMIntegrationService;

/**
 * 3D打印CRM集成控制器
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Anonymous
@RestController
@RequestMapping("/front/crm/3d-printing")
public class ThreeDPrintingCRMController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(ThreeDPrintingCRMController.class);
    
    @Autowired
    private IThreeDPrintingCRMIntegrationService threeDPrintingCRMIntegrationService;
    
    /**
     * 创建3D打印订单并自动集成CRM
     * 包含：检查/创建客户、创建联系人、创建商机、创建订单
     */
    @Log(title = "3D打印订单", businessType = BusinessType.INSERT)
    @PostMapping("/create-order")
    public AjaxResult createOrderWithCRMIntegration(@RequestBody ThreeDPrintingOrderCreateDTO orderCreateDTO) {
        try {
            log.info("接收到3D打印订单创建请求，询价单号: {}", orderCreateDTO.getQuoteNo());
            
            // 参数验证
            if (orderCreateDTO.getCustomerInfo() == null) {
                return AjaxResult.error("客户信息不能为空");
            }
            
            if (orderCreateDTO.getCustomerInfo().getCompanyName() == null || 
                orderCreateDTO.getCustomerInfo().getCompanyName().trim().isEmpty()) {
                return AjaxResult.error("公司名称不能为空");
            }
            
            if (orderCreateDTO.getCustomerInfo().getContactPhone() == null || 
                orderCreateDTO.getCustomerInfo().getContactPhone().trim().isEmpty()) {
                return AjaxResult.error("联系人手机号不能为空");
            }
            
            if (orderCreateDTO.getItems() == null || orderCreateDTO.getItems().isEmpty()) {
                return AjaxResult.error("订单项目不能为空");
            }
            
            // 调用服务创建订单
            ThreeDPrintingOrderResultVO result = threeDPrintingCRMIntegrationService.createOrderWithCRMIntegration(orderCreateDTO);
            
            if (result.getSuccess()) {
                log.info("3D打印订单创建成功，订单号: {}, 客户ID: {}, 联系人ID: {}, 商机ID: {}", 
                        result.getOrderNo(), result.getCustomerId(), result.getContactId(), result.getOpportunityId());
                
                return AjaxResult.success("订单创建成功")
                    .put("orderNo", result.getOrderNo())
                    .put("orderId", result.getOrderId())
                    .put("customerId", result.getCustomerId())
                    .put("isNewCustomer", result.getIsNewCustomer())
                    .put("contactId", result.getContactId())
                    .put("isNewContact", result.getIsNewContact())
                    .put("opportunityId", result.getOpportunityId())
                    .put("message", result.getMessage());
            } else {
                return AjaxResult.error(result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("创建3D打印订单失败，询价单号: {}, 错误: {}", orderCreateDTO.getQuoteNo(), e.getMessage(), e);
            return AjaxResult.error("订单创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查客户是否存在（通过手机号）
     */
    @GetMapping("/check-customer")
    public AjaxResult checkCustomerExists(@RequestParam("mobile") String mobile) {
        try {
            if (mobile == null || mobile.trim().isEmpty()) {
                return AjaxResult.error("手机号不能为空");
            }
            
            Long customerId = threeDPrintingCRMIntegrationService.checkCustomerExistsByMobile(mobile.trim());
            
            if (customerId != null) {
                log.info("客户已存在，手机号: {}, 客户ID: {}", mobile, customerId);
                return AjaxResult.success()
                    .put("exists", true)
                    .put("customerId", customerId.toString());
            } else {
                log.info("客户不存在，手机号: {}", mobile);
                return AjaxResult.success()
                    .put("exists", false);
            }
        } catch (Exception e) {
            log.error("检查客户是否存在失败，手机号: {}, 错误: {}", mobile, e.getMessage(), e);
            return AjaxResult.error("检查客户存在性失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查联系人是否存在（通过客户ID和手机号）
     */
    @GetMapping("/check-contact")
    public AjaxResult checkContactExists(@RequestParam("customerId") Long customerId, 
                                        @RequestParam("mobile") String mobile) {
        try {
            if (customerId == null) {
                return AjaxResult.error("客户ID不能为空");
            }
            
            if (mobile == null || mobile.trim().isEmpty()) {
                return AjaxResult.error("手机号不能为空");
            }
            
            Long contactId = threeDPrintingCRMIntegrationService.checkContactExists(customerId, mobile.trim());
            
            if (contactId != null) {
                log.info("联系人已存在，客户ID: {}, 手机号: {}, 联系人ID: {}", customerId, mobile, contactId);
                return AjaxResult.success()
                    .put("exists", true)
                    .put("contactId", contactId.toString());
            } else {
                log.info("联系人不存在，客户ID: {}, 手机号: {}", customerId, mobile);
                return AjaxResult.success()
                    .put("exists", false);
            }
        } catch (Exception e) {
            log.error("检查联系人是否存在失败，客户ID: {}, 手机号: {}, 错误: {}", customerId, mobile, e.getMessage(), e);
            return AjaxResult.error("检查联系人存在性失败: " + e.getMessage());
        }
    }
}