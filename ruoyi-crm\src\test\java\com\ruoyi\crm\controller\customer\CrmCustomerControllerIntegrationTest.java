package com.ruoyi.crm.controller;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.service.ICrmCustomerService;
import com.ruoyi.crm.BaseTestCase;

/**
 * CrmCustomerController 集成测试类
 * 使用MockMvc测试控制器层的API接口
 * 
 * <AUTHOR>
 * @date 2024-08-16
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("客户管理控制器集成测试")
class CrmCustomerControllerIntegrationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(CrmCustomerControllerIntegrationTest.class);
    
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    private List<Long> testCustomerIds = new ArrayList<>();

    @BeforeEach
    void setUp() {
        // 设置MockMvc，不创建测试数据
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .alwaysDo(print())
                .build();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        for (Long customerId : testCustomerIds) {
            try {
                crmCustomerService.deleteCrmCustomerById(customerId);
            } catch (Exception e) {
                logger.error("清理测试客户失败: {}", customerId, e);
            }
        }
        testCustomerIds.clear();
    }

    /**
     * 创建测试客户
     */
    private CrmCustomer createTestCustomer(String customerName) throws Exception {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerName(customerName);
        customer.setResponsiblePersonId("1"); // 默认使用admin用户作为负责人
        customer.setCustomerSource("测试来源");
        customer.setMobile("13800138000");
        customer.setPhone("010-12345678");
        customer.setEmail("<EMAIL>");
        customer.setWebsite("http://www.example.com");
        customer.setCustomerIndustry("IT");
        customer.setCustomerLevel("A");
        customer.setCustomerAddress("北京市海淀区");
        customer.setDealStatus("未成交");
        customer.setStatus("正常");

        String requestBody = objectMapper.writeValueAsString(customer);

        MvcResult result = mockMvc.perform(post("/front/crm/customer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        AjaxResult ajaxResult = objectMapper.readValue(responseContent, AjaxResult.class);
        
        // 从响应中获取客户ID
        Object data = ajaxResult.get("data");
        if (data instanceof CrmCustomer) {
            customer = (CrmCustomer) data;
        } else {
            // 如果响应中没有返回完整客户对象，则通过查询获取
            CrmCustomer query = new CrmCustomer();
            query.setCustomerName(customerName);
            List<CrmCustomer> customers = crmCustomerService.selectCrmCustomerByName(customerName);
            if (!customers.isEmpty()) {
                customer = customers.get(0);
            }
        }

        assertNotNull(customer.getId(), "创建客户后应能获取到ID");
        testCustomerIds.add(customer.getId());

        return customer;
    }

    @Nested
    @DisplayName("客户CRUD集成测试")
    class CrudIntegrationTests {

        @Test
        @DisplayName("完整的CRUD流程测试")
        void testFullCrudFlow() throws Exception {
            // 1. 创建客户
            CrmCustomer newCustomer = new CrmCustomer();
            newCustomer.setCustomerName("CRUD测试客户");
            newCustomer.setResponsiblePersonId("1");
            newCustomer.setCustomerSource("测试来源");
            newCustomer.setMobile("13800138000");
            newCustomer.setEmail("<EMAIL>");
            newCustomer.setCustomerIndustry("IT");
            newCustomer.setCustomerLevel("A");

            MvcResult createResult = mockMvc.perform(post("/front/crm/customer")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newCustomer)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            
            // 从响应中获取客户ID
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdCustomerId = null;
            
            // 检查响应中是否包含data.id，如果不包含，则从service中查询
            if (rootNode.path("data").path("id").isNumber()) {
                createdCustomerId = rootNode.path("data").path("id").asLong();
            } else {
                // 如果响应中没有返回ID，则通过查询获取
                CrmCustomer query = new CrmCustomer();
                query.setCustomerName("CRUD测试客户");
                List<CrmCustomer> customers = crmCustomerService.selectCrmCustomerByName(query.getCustomerName());
                if (!customers.isEmpty()) {
                    createdCustomerId = customers.get(0).getId();
                }
            }
            
            assertNotNull(createdCustomerId, "创建客户后应能获取到ID");
            logger.info("创建的客户ID: {}", createdCustomerId);
            
            // 添加到清理列表
            testCustomerIds.add(createdCustomerId);
            
            try {
                // 2. 查询单个客户
                mockMvc.perform(get("/front/crm/customer/{id}", createdCustomerId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.customerName").value("CRUD测试客户"))
                        .andExpect(jsonPath("$.data.email").value("<EMAIL>"));

                // 3. 修改客户
                newCustomer.setId(createdCustomerId);
                newCustomer.setCustomerName("修改后的客户名称");
                newCustomer.setCustomerLevel("B");
                newCustomer.setEmail("<EMAIL>");

                mockMvc.perform(put("/front/crm/customer")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newCustomer)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证修改结果
                mockMvc.perform(get("/front/crm/customer/{id}", createdCustomerId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.data.customerName").value("修改后的客户名称"))
                        .andExpect(jsonPath("$.data.customerLevel").value("B"))
                        .andExpect(jsonPath("$.data.email").value("<EMAIL>"));

            } finally {
                // 5. 删除客户
                mockMvc.perform(delete("/front/crm/customer/{ids}", createdCustomerId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 6. 验证删除结果 - 查询应该返回null或错误
                MvcResult deleteVerifyResult = mockMvc.perform(get("/front/crm/customer/{id}", createdCustomerId))
                        .andExpect(status().isOk())
                        .andReturn();
                
                String deleteVerifyContent = deleteVerifyResult.getResponse().getContentAsString();
                AjaxResult deleteVerifyResponse = objectMapper.readValue(deleteVerifyContent, AjaxResult.class);
                // 删除后查询应该返回错误或null
                assertTrue(deleteVerifyResponse.get("data") == null || !deleteVerifyResponse.isSuccess(),
                        "删除后查询应返回null或错误");
                
                // 从清理列表中移除，因为已经删除了
                testCustomerIds.remove(createdCustomerId);
            }
        }

        @Test
        @DisplayName("查询客户列表 - 带分页和筛选")
        void testGetCustomerListWithFilters() throws Exception {
            // 创建测试数据
            CrmCustomer testCustomer = createTestCustomer("列表测试客户");
            Long testCustomerId = testCustomer.getId();
            
            try {
                MvcResult result = mockMvc.perform(get("/front/crm/customer/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .param("customerName", "列表测试"))
                        .andExpect(status().isOk())
                        .andReturn();

                String responseContent = result.getResponse().getContentAsString();
                TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
                
                // 打印响应内容
                logger.info("响应内容: {}", objectMapper.writeValueAsString(response));
                
                assertNotNull(response);
                assertEquals(200, response.getCode());
                assertNotNull(response.getRows());
                assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
            } finally {
                cleanupTestCustomer(testCustomerId);
            }
        }
    }

    @Nested
    @DisplayName("客户搜索集成测试")
    class SearchIntegrationTests {
        
        private CrmCustomer testCustomer;
        private Long testCustomerId;
        
        @BeforeEach
        void setUpSearchTests() throws Exception {
            // 创建测试客户
            testCustomer = createTestCustomer("搜索测试客户");
            testCustomerId = testCustomer.getId();
        }
        
        @AfterEach
        void tearDownSearchTests() {
            cleanupTestCustomer(testCustomerId);
        }

        @Test
        @DisplayName("按关键字搜索客户测试")
        void testSearchCustomersByKeyword() throws Exception {
            mockMvc.perform(get("/front/crm/customer/search")
                    .param("keyword", "搜索测试"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.rows").isArray())
                    .andExpect(jsonPath("$.total").value(greaterThanOrEqualTo(1)));
        }



        @Test
        @DisplayName("按电话搜索客户测试")
        void testSearchCustomersByPhone() throws Exception {
            mockMvc.perform(get("/front/crm/customer/search")
                    .param("keyword", "13800138000"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.rows").isArray())
                    .andExpect(jsonPath("$.total").value(greaterThanOrEqualTo(1)));
        }

        @Test
        @DisplayName("按邮箱搜索客户测试")
        void testSearchCustomersByEmail() throws Exception {
            mockMvc.perform(get("/front/crm/customer/search")
                    .param("keyword", "<EMAIL>"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.rows").isArray())
                    .andExpect(jsonPath("$.total").value(greaterThanOrEqualTo(1)));
        }
    }

    @Nested
    @DisplayName("异常处理集成测试")
    class ExceptionIntegrationTests {
        
        @Test
        @DisplayName("获取不存在的客户")
        void testGetNonExistentCustomer() throws Exception {
            mockMvc.perform(get("/front/crm/customer/{id}", 99999L)) // 不存在的ID
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.data").isEmpty());
        }
        
        @Test
        @DisplayName("删除不存在的客户")
        void testDeleteNonExistentCustomer() throws Exception {
            mockMvc.perform(delete("/front/crm/customer/{ids}", 99999L))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.msg").value("操作成功"));
        }
    }
    
    /**
     * 清理测试客户
     */
    private void cleanupTestCustomer(Long customerId) {
        if (customerId != null) {
            try {
                crmCustomerService.deleteCrmCustomerById(customerId);
                testCustomerIds.remove(customerId);
            } catch (Exception e) {
                logger.error("清理测试客户失败: {}", customerId, e);
            }
        }
    }
}