package com.ruoyi.crm.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;

import java.math.BigDecimal;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmPaymentPlan;
import com.ruoyi.common.domain.entity.CrmPayment;
import com.ruoyi.crm.service.ICrmPaymentPlanService;
import com.ruoyi.crm.service.ICrmPaymentService;
import com.ruoyi.crm.BaseTestCase;

@AutoConfigureWebMvc
@DisplayName("回款记录Controller集成测试")
class CrmPaymentRecordControllerIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ICrmPaymentPlanService paymentPlanService;

    @Autowired
    private ICrmPaymentService paymentService;

    private MockMvc mockMvc;

    private CrmPayment testPaymentRecord;
    private CrmPaymentPlan testPaymentPlan;

    @BeforeEach
    void setUp() {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 创建测试回款计划
        testPaymentPlan = new CrmPaymentPlan();
        testPaymentPlan.setPlanNumber("PLN20240724001");
        testPaymentPlan.setCustomerId(1L);
        testPaymentPlan.setCustomerName("测试客户A");
        testPaymentPlan.setResponsibleUserId(1L);
        testPaymentPlan.setResponsibleUserName("张三");
        testPaymentPlan.setTotalAmount(new BigDecimal("100000.00"));
        testPaymentPlan.setReceivedAmount(new BigDecimal("0.00"));
        testPaymentPlan.setRemainingAmount(new BigDecimal("100000.00"));
        testPaymentPlan.setPlanStatus("执行中");
        testPaymentPlan.setApprovalStatus("已通过");
        testPaymentPlan.setPaymentMethod("银行转账");
        testPaymentPlan.setCurrency("CNY");
        testPaymentPlan.setPlanType("普通");
        testPaymentPlan.setRiskLevel("低");
        testPaymentPlan.setCreateTime(new Date());
        testPaymentPlan.setCreateBy("test");
        testPaymentPlan.setDelFlag("0");

        // 创建测试回款记录
        testPaymentRecord = new CrmPayment();
        testPaymentRecord.setPaymentNo("REC20240724001");
        testPaymentRecord.setPaymentName("测试回款");
        testPaymentRecord.setPaymentAmount(new BigDecimal("50000.00"));
        testPaymentRecord.setPaymentDate(new Date());
        testPaymentRecord.setStatus("待确认");
        testPaymentRecord.setRemarks("首期回款");
        testPaymentRecord.setCreateTime(new Date());
        testPaymentRecord.setCreateBy("test");
        testPaymentRecord.setDelFlag("0");
    }

    @Test
    @DisplayName("测试回款记录列表查询")
    void testListPaymentRecord() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertPaymentPlan(testPaymentPlan);
        testPaymentRecord.setCustomerId(testPaymentPlan.getCustomerId());

        mockMvc.perform(get("/crm/payment/list")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @DisplayName("测试新增回款记录")
    void testAddPaymentRecord() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertPaymentPlan(testPaymentPlan);
        testPaymentRecord.setCustomerId(testPaymentPlan.getCustomerId());

        String jsonContent = objectMapper.writeValueAsString(testPaymentRecord);

        MvcResult result = mockMvc.perform(post("/crm/payment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"))
                .andReturn();

        String response = result.getResponse().getContentAsString();
        System.out.println("新增回款记录响应: " + response);
    }

    @Test
    @DisplayName("测试根据ID查询回款记录详情")
    void testGetPaymentRecord() throws Exception {
        // 先插入回款计划和记录
        paymentPlanService.insertPaymentPlan(testPaymentPlan);
        testPaymentRecord.setCustomerId(testPaymentPlan.getCustomerId());

        mockMvc.perform(get("/crm/payment/{id}", 1L))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @DisplayName("测试修改回款记录")
    void testUpdatePaymentRecord() throws Exception {
        // 先插入回款计划
        paymentPlanService.insertPaymentPlan(testPaymentPlan);
        testPaymentRecord.setCustomerId(testPaymentPlan.getCustomerId());
        testPaymentRecord.setId(1L);

        // 修改数据
        testPaymentRecord.setPaymentAmount(new BigDecimal("60000.00"));
        testPaymentRecord.setRemarks("修改后的备注");

        String jsonContent = objectMapper.writeValueAsString(testPaymentRecord);

        mockMvc.perform(put("/crm/payment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试删除回款记录")
    void testDeletePaymentRecord() throws Exception {
        mockMvc.perform(delete("/crm/payment/{ids}", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @DisplayName("测试导出回款记录")
    void testExportPaymentRecord() throws Exception {
        mockMvc.perform(get("/crm/payment/export")
                .param("customerName", "")
                .param("status", ""))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试回款记录参数验证")
    void testPaymentRecordValidation() throws Exception {
        // 测试必填字段验证
        CrmPayment invalidRecord = new CrmPayment();
        String jsonContent = objectMapper.writeValueAsString(invalidRecord);

        mockMvc.perform(post("/crm/payment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk()); // 基础的payment controller不做参数验证
    }

    @Test
    @DisplayName("测试按状态查询回款记录")
    void testGetRecordsByStatus() throws Exception {
        mockMvc.perform(get("/crm/payment/list")
                .param("status", "待确认"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @DisplayName("测试按日期范围查询回款记录")
    void testGetRecordsByDateRange() throws Exception {
        mockMvc.perform(get("/crm/payment/list")
                .param("startDate", "2024-07-01")
                .param("endDate", "2024-07-31"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }
}