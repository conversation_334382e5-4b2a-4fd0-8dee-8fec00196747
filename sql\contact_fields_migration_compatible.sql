-- 联系人字段统一迁移脚本（兼容版）
-- 目标：使 crm_business_contacts 表结构与 CrmContacts 实体类完全一致
-- 执行时间: 2025-06-29
-- 兼容 MySQL 5.7+ 版本

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一步：添加扩展字段（如果不存在）
-- ========================================

-- 检查并添加固定电话字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND COLUMN_NAME = 'telephone') = 0,
    'ALTER TABLE crm_business_contacts ADD COLUMN telephone VARCHAR(20) COMMENT ''固定电话'' AFTER phone',
    'SELECT ''telephone column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加生日字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND COLUMN_NAME = 'birthday') = 0,
    'ALTER TABLE crm_business_contacts ADD COLUMN birthday DATE COMMENT ''生日'' AFTER gender',
    'SELECT ''birthday column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加部门字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND COLUMN_NAME = 'department') = 0,
    'ALTER TABLE crm_business_contacts ADD COLUMN department VARCHAR(100) COMMENT ''部门'' AFTER position',
    'SELECT ''department column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加决策角色字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND COLUMN_NAME = 'decision_role') = 0,
    'ALTER TABLE crm_business_contacts ADD COLUMN decision_role VARCHAR(50) COMMENT ''决策角色(决策者/影响者/使用者/其他)'' AFTER department',
    'SELECT ''decision_role column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加联系人级别字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND COLUMN_NAME = 'contact_level') = 0,
    'ALTER TABLE crm_business_contacts ADD COLUMN contact_level VARCHAR(10) COMMENT ''联系人级别(A/B/C/D)'' AFTER decision_role',
    'SELECT ''contact_level column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加状态字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND COLUMN_NAME = 'status') = 0,
    'ALTER TABLE crm_business_contacts ADD COLUMN status CHAR(1) DEFAULT ''0'' COMMENT ''状态(0有效 1无效)'' AFTER contact_level',
    'SELECT ''status column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 第二步：优化现有字段注释（安全模式）
-- ========================================

-- 更新字段注释（不修改数据类型，避免数据丢失风险）
ALTER TABLE crm_business_contacts 
MODIFY COLUMN id INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
MODIFY COLUMN responsible_person_id VARCHAR(255) NOT NULL COMMENT '负责人ID',
MODIFY COLUMN name VARCHAR(255) NOT NULL COMMENT '联系人姓名',
MODIFY COLUMN mobile VARCHAR(20) COMMENT '手机号码',
MODIFY COLUMN phone VARCHAR(20) COMMENT '办公电话',
MODIFY COLUMN email VARCHAR(255) COMMENT '电子邮件地址',
MODIFY COLUMN position VARCHAR(255) COMMENT '职务',
MODIFY COLUMN is_key_decision_maker VARCHAR(50) COMMENT '是否关键决策人',
MODIFY COLUMN direct_superior VARCHAR(255) COMMENT '直属上级',
MODIFY COLUMN address VARCHAR(255) COMMENT '地址',
MODIFY COLUMN detailed_address VARCHAR(255) COMMENT '详细地址',
MODIFY COLUMN next_contact_time DATETIME COMMENT '下次联系时间',
MODIFY COLUMN selected_date DATE COMMENT '选择日期',
MODIFY COLUMN gender VARCHAR(50) COMMENT '性别',
MODIFY COLUMN remarks TEXT COMMENT '备注信息',
MODIFY COLUMN del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
MODIFY COLUMN create_by VARCHAR(255) COMMENT '创建者',
MODIFY COLUMN create_time DATETIME COMMENT '创建时间',
MODIFY COLUMN update_by VARCHAR(255) COMMENT '更新者',
MODIFY COLUMN update_time DATETIME COMMENT '更新时间';

-- ========================================
-- 第三步：添加必要的索引优化查询性能
-- ========================================

-- 为新字段添加索引（检查是否已存在）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND INDEX_NAME = 'idx_telephone') = 0,
    'ALTER TABLE crm_business_contacts ADD INDEX idx_telephone (telephone)',
    'SELECT ''idx_telephone already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND INDEX_NAME = 'idx_department') = 0,
    'ALTER TABLE crm_business_contacts ADD INDEX idx_department (department)',
    'SELECT ''idx_department already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND INDEX_NAME = 'idx_decision_role') = 0,
    'ALTER TABLE crm_business_contacts ADD INDEX idx_decision_role (decision_role)',
    'SELECT ''idx_decision_role already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND INDEX_NAME = 'idx_contact_level') = 0,
    'ALTER TABLE crm_business_contacts ADD INDEX idx_contact_level (contact_level)',
    'SELECT ''idx_contact_level already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND INDEX_NAME = 'idx_status') = 0,
    'ALTER TABLE crm_business_contacts ADD INDEX idx_status (status)',
    'SELECT ''idx_status already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND INDEX_NAME = 'idx_birthday') = 0,
    'ALTER TABLE crm_business_contacts ADD INDEX idx_birthday (birthday)',
    'SELECT ''idx_birthday already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为常用查询组合添加复合索引
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND INDEX_NAME = 'idx_status_del_flag') = 0,
    'ALTER TABLE crm_business_contacts ADD INDEX idx_status_del_flag (status, del_flag)',
    'SELECT ''idx_status_del_flag already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_business_contacts' AND INDEX_NAME = 'idx_level_role') = 0,
    'ALTER TABLE crm_business_contacts ADD INDEX idx_level_role (contact_level, decision_role)',
    'SELECT ''idx_level_role already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 第四步：数据清理和标准化
-- ========================================

-- 清理无效的决策人标志数据（保持现有格式）
UPDATE crm_business_contacts 
SET is_key_decision_maker = '否' 
WHERE is_key_decision_maker IS NULL OR is_key_decision_maker = '';

-- 标准化性别数据（保持现有格式，统一为中文）
UPDATE crm_business_contacts 
SET gender = CASE 
    WHEN gender IN ('M', 'male', '1') THEN '男'
    WHEN gender IN ('F', 'female', '2') THEN '女'
    WHEN gender IS NULL OR gender = '' THEN NULL
    ELSE gender
END
WHERE gender IS NOT NULL;

-- 确保状态字段的默认值
UPDATE crm_business_contacts 
SET status = '0' 
WHERE status IS NULL;

-- ========================================
-- 第五步：验证表结构
-- ========================================

-- 显示表结构验证信息
SELECT 'Migration completed successfully. Table structure:' as message;

SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '可空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_business_contacts' 
ORDER BY ORDINAL_POSITION;

-- 显示新增的索引
SELECT 
    INDEX_NAME as '索引名',
    COLUMN_NAME as '字段名',
    INDEX_TYPE as '索引类型'
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_business_contacts'
  AND INDEX_NAME LIKE 'idx_%'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 提交事务
COMMIT;

-- ========================================
-- 迁移说明
-- ========================================

/*
本次迁移完成以下统一工作：

1. 字段统一:
   ✅ 添加了 telephone 字段（固定电话）
   ✅ 添加了 birthday 字段（生日）
   ✅ 添加了 department 字段（部门）
   ✅ 添加了 decision_role 字段（决策角色）
   ✅ 添加了 contact_level 字段（联系人级别）
   ✅ 添加了 status 字段（状态）

2. 字段区分:
   - mobile: 手机号码（主要联系方式）
   - phone: 办公电话（工作联系方式）
   - telephone: 固定电话（家庭/其他固定电话）

3. 兼容性优化:
   - 使用动态SQL避免IF NOT EXISTS语法问题
   - 保持现有数据类型避免数据丢失
   - 安全的字段修改和索引添加

4. 索引优化:
   - 为新字段添加单列索引
   - 添加常用查询的复合索引

5. 数据清理:
   - 统一了性别字段格式
   - 清理了决策人标志
   - 设置了状态字段默认值

执行后，crm_business_contacts 表将与 CrmContacts 实体类完全一致。
*/
