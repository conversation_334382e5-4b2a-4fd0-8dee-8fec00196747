<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人+客户模块完整改造计划</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- Prism.js for code highlighting -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .insight-box {
            background-color: #e8f5e8;
            border: 2px solid #27ae60;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .current-state {
            background-color: #fff3cd;
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .target-state {
            background-color: #d1ecf1;
            border: 2px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .mermaid-diagram {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        .code-section {
            margin: 20px 0;
        }
        .code-title {
            background-color: #495057;
            color: white;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin: 0;
            font-weight: bold;
            font-size: 14px;
        }
        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0 0 5px 5px !important;
            font-size: 13px !important;
        }
        .phase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .phase-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .phase-card h4 {
            color: #007bff;
            margin-top: 0;
        }
        .feature-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-feature {
            background-color: #fee;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e74c3c;
        }
        .after-feature {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #27ae60;
        }
        .task-list {
            margin: 15px 0;
        }
        .task-item {
            margin: 8px 0;
            padding: 10px;
            background-color: white;
            border-left: 3px solid #28a745;
            border-radius: 3px;
        }
        .priority-high { border-left-color: #e74c3c; }
        .priority-medium { border-left-color: #f39c12; }
        .priority-low { border-left-color: #6c757d; }
        .team-integration {
            background-color: #f0f8ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #4169e1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 联系人+客户模块完整改造计划</h1>
        
        <div class="insight-box">
            <h3>💡 核心改造目标</h3>
            <p><strong>整合多负责人管理 + 团队协作 + 公海管理于一体的完整CRM系统</strong></p>
            <ul>
                <li>✅ <strong>多对多关系：</strong>联系人 ↔ 业务员，支持跨专业协作</li>
                <li>✅ <strong>团队管理：</strong>基于现有团队体系，实现团队维度分配和统计</li>
                <li>✅ <strong>公海升级：</strong>从单一负责人公海升级为多维度公海管理</li>
                <li>✅ <strong>数据统计：</strong>个人/团队/部门多层级业绩统计</li>
            </ul>
        </div>

        <h2>📊 现状分析与目标设计</h2>

        <div class="feature-comparison">
            <div class="before-feature">
                <h4>❌ 当前架构问题</h4>
                <ul>
                    <li>联系人只能有单一负责人</li>
                    <li>客户公海逻辑基于单一负责人</li>
                    <li>团队管理与业务分配脱节</li>
                    <li>无法支持跨专业协作场景</li>
                    <li>统计维度单一，缺乏团队视角</li>
                </ul>
            </div>
            <div class="after-feature">
                <h4>✅ 目标架构优势</h4>
                <ul>
                    <li>联系人-业务员多对多关系</li>
                    <li>多维度公海管理（个人/团队/业务类型）</li>
                    <li>团队分配与业务管理一体化</li>
                    <li>完全支持跨专业协作</li>
                    <li>多层级统计分析（个人/团队/部门）</li>
                </ul>
            </div>
        </div>

        <h2>🏗️ 完整架构设计</h2>

        <div class="mermaid-diagram">
            <div class="mermaid">
erDiagram
    CrmCustomer {
        Long id PK
        String customerName
        String customerIndustry
        String status
        String delFlag
    }
    
    CrmContacts {
        Long id PK
        String name
        String department
        String position
        String mobile
        String email
        String status
        String delFlag
    }
    
    CrmCustomerContactRelation {
        Long id PK
        Long customerId FK
        Long contactId FK
        String relationType
        Integer isPrimary
        Date startDate
        String status
    }
    
    CrmContactResponsibleRelation {
        Long id PK
        Long contactId FK
        Long responsiblePersonId FK
        String businessType
        String relationStatus
        Date startDate
        Date endDate
        String remark
    }
    
    CrmTeam {
        Long id PK
        String teamName
        String teamCode
        Long leaderId FK
        String teamType
        String status
    }
    
    CrmTeamMember {
        Long id PK
        Long teamId FK
        Long userId FK
        String roleType
        Date joinTime
        String status
    }
    
    CrmTeamRelation {
        Long id PK
        Long teamId FK
        String relationType
        Long relationId FK
    }
    
    SysUser {
        Long userId PK
        String userName
        String nickName
    }
    
    CrmCustomer ||--o{ CrmCustomerContactRelation : "has"
    CrmContacts ||--o{ CrmCustomerContactRelation : "belongs"
    CrmContacts ||--o{ CrmContactResponsibleRelation : "managed_by"
    SysUser ||--o{ CrmContactResponsibleRelation : "manages"
    CrmTeam ||--o{ CrmTeamMember : "contains"
    SysUser ||--o{ CrmTeamMember : "joins"
    CrmTeam ||--o{ CrmTeamRelation : "associates"
    CrmContacts ||--o{ CrmTeamRelation : "team_managed"
            </div>
        </div>

        <h2>💼 核心功能设计</h2>

        <h3>1. 联系人多负责人管理</h3>
        <div class="team-integration">
            <h4>🎯 业务场景示例</h4>
            <p><strong>A公司王五联系人：</strong></p>
            <ul>
                <li><strong>包装业务：</strong>业务员张三（包装团队） → 团队统计包装业绩</li>
                <li><strong>原型业务：</strong>业务员李四（原型团队） → 团队统计原型业绩</li>
                <li><strong>3D打印：</strong>业务员王六（技术团队） → 团队统计技术业绩</li>
            </ul>
            <p><strong>团队分配：</strong>团队负责人可以将联系人分配给团队成员，系统自动建立联系人-业务员关系</p>
        </div>

        <h3>2. 团队维度的公海管理</h3>
        <div class="code-section">
            <div class="code-title">公海查询逻辑升级</div>
            <pre><code class="language-java">/**
 * 多维度公海查询
 */
public class PoolQueryRequest {
    private String queryType;        // 查询类型：PERSONAL/TEAM/BUSINESS_TYPE
    private Long teamId;            // 团队ID（团队公海）
    private String businessType;    // 业务类型（业务线公海）
    private Long userId;            // 用户ID（个人公海）
}

@Override
public List&lt;CrmContacts&gt; selectPoolContacts(PoolQueryRequest request) {
    switch (request.getQueryType()) {
        case "PERSONAL":
            // 查询个人可认领的联系人（无该业务类型负责人）
            return contactMapper.selectPersonalPoolContacts(
                request.getUserId(), request.getBusinessType());
                
        case "TEAM":
            // 查询团队可认领的联系人（团队成员都未负责的联系人）
            return contactMapper.selectTeamPoolContacts(
                request.getTeamId(), request.getBusinessType());
                
        case "BUSINESS_TYPE":
            // 查询业务类型公海（该业务类型无任何负责人的联系人）
            return contactMapper.selectBusinessTypePoolContacts(
                request.getBusinessType());
                
        default:
            return new ArrayList&lt;&gt;();
    }
}</code></pre>
        </div>

        <h3>3. 团队分配与认领机制</h3>
        <div class="code-section">
            <div class="code-title">团队负责人分配联系人</div>
            <pre><code class="language-java">/**
 * 团队负责人将联系人分配给团队成员
 */
@Override
@Transactional
public int assignContactsToTeamMember(AssignRequest request) {
    // 1. 验证当前用户是否为团队负责人
    Long currentUserId = SecurityUtils.getUserId();
    CrmTeam team = teamMapper.selectById(request.getTeamId());
    if (!currentUserId.equals(team.getLeaderId())) {
        throw new ServiceException("只有团队负责人可以分配联系人");
    }
    
    // 2. 验证被分配人是否为团队成员
    CrmTeamMember member = teamMemberMapper.selectByTeamAndUser(
        request.getTeamId(), request.getAssignToUserId());
    if (member == null || !"0".equals(member.getStatus())) {
        throw new ServiceException("被分配人不是团队有效成员");
    }
    
    // 3. 批量创建联系人-负责人关系
    List&lt;CrmContactResponsibleRelation&gt; relations = new ArrayList&lt;&gt;();
    for (Long contactId : request.getContactIds()) {
        CrmContactResponsibleRelation relation = new CrmContactResponsibleRelation();
        relation.setContactId(contactId);
        relation.setResponsiblePersonId(request.getAssignToUserId());
        relation.setBusinessType(request.getBusinessType());
        relation.setRelationStatus("ACTIVE");
        relation.setStartDate(new Date());
        relation.setRemark("团队分配：" + team.getTeamName());
        relations.add(relation);
    }
    
    // 4. 批量插入
    int result = contactResponsibleMapper.batchInsert(relations);
    
    // 5. 建立团队关联关系
    for (Long contactId : request.getContactIds()) {
        CrmTeamRelation teamRelation = new CrmTeamRelation();
        teamRelation.setTeamId(request.getTeamId());
        teamRelation.setRelationType("CONTACT");
        teamRelation.setRelationId(contactId);
        teamRelationMapper.insert(teamRelation);
    }
    
    // 6. 记录操作日志
    recordTeamAssignLog(request, currentUserId);
    
    return result;
}</code></pre>
        </div>

        <h3>4. 多层级统计分析</h3>
        <div class="code-section">
            <div class="code-title">团队贡献统计</div>
            <pre><code class="language-java">/**
 * 团队业绩统计
 */
@Override
public TeamPerformanceVO getTeamPerformance(Long teamId, Date startDate, Date endDate) {
    TeamPerformanceVO performance = new TeamPerformanceVO();
    
    // 1. 团队基本信息
    CrmTeam team = teamMapper.selectById(teamId);
    performance.setTeamInfo(team);
    
    // 2. 团队成员列表
    List&lt;CrmTeamMember&gt; members = teamMemberMapper.selectByTeamId(teamId);
    performance.setMembers(members);
    
    // 3. 团队管理的联系人统计
    int totalContacts = contactResponsibleMapper.countTeamContacts(teamId);
    performance.setTotalContacts(totalContacts);
    
    // 4. 按业务类型统计
    Map&lt;String, Integer&gt; businessTypeStats = contactResponsibleMapper
        .countByBusinessTypeAndTeam(teamId, startDate, endDate);
    performance.setBusinessTypeStats(businessTypeStats);
    
    // 5. 按成员统计
    Map&lt;Long, Integer&gt; memberStats = contactResponsibleMapper
        .countByMemberAndDateRange(teamId, startDate, endDate);
    performance.setMemberStats(memberStats);
    
    // 6. 团队相关的业务数据（商机、合同等）
    performance.setOpportunityStats(getTeamOpportunityStats(teamId, startDate, endDate));
    performance.setContractStats(getTeamContractStats(teamId, startDate, endDate));
    
    return performance;
}</code></pre>
        </div>

        <h2>📋 详细实施计划</h2>

        <div class="phase-grid">
            <div class="phase-card">
                <h4>📅 第1阶段：数据库架构升级（2天）</h4>
                <div class="task-list">
                    <div class="task-item priority-high">
                        <strong>Day 1：</strong>创建联系人-业务员关系表
                        <ul>
                            <li>设计表结构和索引</li>
                            <li>创建相关Mapper接口</li>
                            <li>编写基础CRUD操作</li>
                        </ul>
                    </div>
                    <div class="task-item priority-high">
                        <strong>Day 2：</strong>数据迁移和验证
                        <ul>
                            <li>现有负责人数据迁移</li>
                            <li>团队关联关系补充</li>
                            <li>数据完整性验证</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="phase-card">
                <h4>📅 第2阶段：核心业务逻辑（3天）</h4>
                <div class="task-list">
                    <div class="task-item priority-high">
                        <strong>Day 3：</strong>多对多关系管理
                        <ul>
                            <li>联系人认领逻辑</li>
                            <li>业务员分配逻辑</li>
                            <li>关系状态管理</li>
                        </ul>
                    </div>
                    <div class="task-item priority-high">
                        <strong>Day 4：</strong>团队分配功能
                        <ul>
                            <li>团队负责人分配权限</li>
                            <li>团队成员认领机制</li>
                            <li>团队-联系人关联</li>
                        </ul>
                    </div>
                    <div class="task-item priority-medium">
                        <strong>Day 5：</strong>公海管理升级
                        <ul>
                            <li>多维度公海查询</li>
                            <li>公海认领规则</li>
                            <li>公海统计分析</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="phase-grid">
            <div class="phase-card">
                <h4>📅 第3阶段：接口和测试（2天）</h4>
                <div class="task-list">
                    <div class="task-item priority-high">
                        <strong>Day 6：</strong>Controller接口开发
                        <ul>
                            <li>联系人管理接口升级</li>
                            <li>团队分配相关接口</li>
                            <li>公海管理接口优化</li>
                        </ul>
                    </div>
                    <div class="task-item priority-high">
                        <strong>Day 7：</strong>集成测试修复
                        <ul>
                            <li>修复原有测试用例</li>
                            <li>新增多对多关系测试</li>
                            <li>团队功能集成测试</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="phase-card">
                <h4>📅 第4阶段：统计和优化（2天）</h4>
                <div class="task-list">
                    <div class="task-item priority-medium">
                        <strong>Day 8：</strong>统计分析功能
                        <ul>
                            <li>个人业绩统计</li>
                            <li>团队业绩统计</li>
                            <li>多维度数据报表</li>
                        </ul>
                    </div>
                    <div class="task-item priority-low">
                        <strong>Day 9：</strong>性能优化和完善
                        <ul>
                            <li>查询性能优化</li>
                            <li>操作日志完善</li>
                            <li>异常处理补充</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <h2>🎯 关键技术要点</h2>

        <div class="current-state">
            <h4>⚡ 性能优化策略</h4>
            <ul>
                <li><strong>索引优化：</strong>联系人ID、业务员ID、业务类型的联合索引</li>
                <li><strong>查询优化：</strong>避免N+1查询，使用JOIN和子查询优化</li>
                <li><strong>缓存策略：</strong>团队成员信息、用户权限信息缓存</li>
                <li><strong>分页优化：</strong>大数据量的分页查询优化</li>
            </ul>
        </div>

        <div class="target-state">
            <h4>🔒 权限控制策略</h4>
            <ul>
                <li><strong>团队负责人：</strong>可以分配团队成员，查看团队统计</li>
                <li><strong>团队成员：</strong>可以认领团队公海联系人，查看个人统计</li>
                <li><strong>普通用户：</strong>只能认领个人公海联系人</li>
                <li><strong>管理员：</strong>可以查看所有统计，管理所有团队</li>
            </ul>
        </div>

        <h2>📊 预期效果</h2>

        <div class="insight-box">
            <h4>🎉 改造完成后的系统能力</h4>
            <ol>
                <li><strong>灵活的业务协作：</strong>一个联系人可以同时由多个专业业务员服务</li>
                <li><strong>团队化管理：</strong>团队负责人可以统一分配和管理团队资源</li>
                <li><strong>多维度统计：</strong>支持个人、团队、业务类型等多个维度的数据分析</li>
                <li><strong>智能公海：</strong>根据业务类型、团队归属等智能推荐可认领联系人</li>
                <li><strong>完整的审计：</strong>所有分配、认领、转移操作都有完整的日志记录</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #e8f5e8; border-radius: 5px;">
            <h3>🚀 准备开始实施</h3>
            <p><strong>总工期：</strong>9天 | <strong>核心功能：</strong>7天 | <strong>优化完善：</strong>2天</p>
            <p><strong>技术风险：</strong>低 | <strong>业务影响：</strong>正向提升 | <strong>数据安全：</strong>完全保障</p>
            <p style="color: #27ae60; font-weight: bold;">这个计划将彻底解决多负责人问题，并充分发挥现有团队管理体系的价值！</p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#333333',
                fill: '#ececff',
                fontSize: 12
            }
        });

        // Auto highlight code blocks
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>