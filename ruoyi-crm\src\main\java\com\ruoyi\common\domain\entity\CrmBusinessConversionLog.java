package com.ruoyi.common.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务转化日志实体类
 * 对应数据库表：crm_business_conversion_log
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmBusinessConversionLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 转化类型 */
    @Excel(name = "转化类型", readConverterExp = "LEAD_TO_CUSTOMER=线索转客户,LEAD_TO_OPPORTUNITY=线索转商机,OPPORTUNITY_TO_ORDER=商机转订单,ORDER_TO_CONTRACT=订单转合同")
    private String conversionType;

    /** 源实体类型 */
    @Excel(name = "源实体类型", readConverterExp = "LEAD=线索,CUSTOMER=客户,OPPORTUNITY=商机,ORDER=订单")
    private String sourceType;

    /** 源实体ID */
    private Long sourceId;

    /** 源实体名称 */
    @Excel(name = "源实体名称")
    private String sourceName;

    /** 目标实体类型 */
    @Excel(name = "目标实体类型", readConverterExp = "CUSTOMER=客户,OPPORTUNITY=商机,ORDER=订单,CONTRACT=合同")
    private String targetType;

    /** 目标实体ID */
    private Long targetId;

    /** 目标实体名称 */
    @Excel(name = "目标实体名称")
    private String targetName;

    /** 转化详情数据（JSON格式） */
    private String conversionData;

    /** 转化金额 */
    @Excel(name = "转化金额")
    private BigDecimal conversionAmount;

    /** 转化成功标志 */
    @Excel(name = "转化结果", readConverterExp = "0=失败,1=成功")
    private Integer successFlag;

    /** 操作人ID */
    private Long operatorId;

    /** 操作人姓名 */
    @Excel(name = "操作人")
    private String operatorName;

    /** 转化时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "转化时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date conversionTime;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    // ========== 关联对象 ==========
    
    /** 操作人对象 */
    private Object operator;

    // ========== 查询条件字段 ==========
    
    /** 开始时间（查询用） */
    private String startTime;

    /** 结束时间（查询用） */
    private String endTime;

    /** 最小金额（查询用） */
    private BigDecimal minAmount;

    /** 最大金额（查询用） */
    private BigDecimal maxAmount;

    // ========== 常量定义 ==========
    
    /** 转化类型：线索转客户 */
    public static final String CONVERSION_TYPE_LEAD_TO_CUSTOMER = "LEAD_TO_CUSTOMER";
    
    /** 转化类型：线索转商机 */
    public static final String CONVERSION_TYPE_LEAD_TO_OPPORTUNITY = "LEAD_TO_OPPORTUNITY";
    
    /** 转化类型：商机转订单 */
    public static final String CONVERSION_TYPE_OPPORTUNITY_TO_ORDER = "OPPORTUNITY_TO_ORDER";
    
    /** 转化类型：订单转合同 */
    public static final String CONVERSION_TYPE_ORDER_TO_CONTRACT = "ORDER_TO_CONTRACT";

    /** 实体类型：线索 */
    public static final String ENTITY_TYPE_LEAD = "LEAD";
    
    /** 实体类型：客户 */
    public static final String ENTITY_TYPE_CUSTOMER = "CUSTOMER";
    
    /** 实体类型：商机 */
    public static final String ENTITY_TYPE_OPPORTUNITY = "OPPORTUNITY";
    
    /** 实体类型：订单 */
    public static final String ENTITY_TYPE_ORDER = "ORDER";
    
    /** 实体类型：合同 */
    public static final String ENTITY_TYPE_CONTRACT = "CONTRACT";

    /** 转化成功 */
    public static final Integer SUCCESS_FLAG_SUCCESS = 1;
    
    /** 转化失败 */
    public static final Integer SUCCESS_FLAG_FAILURE = 0;
}
