package com.ruoyi.common.domain.entity;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公海规则配置对象 crm_pool_rules
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmPoolRules extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long id;

    /** 规则名称 */
    private String ruleName;

    /** 规则类型：NO_FOLLOW(未跟进), NO_DEAL(未成交), MANUAL(手动放入) */
    private String ruleType;

    /** 天数限制 */
    private Integer daysLimit;

    /** 是否启用 */
    private Boolean enabled;

    /** 规则描述 */
    private String description;
}