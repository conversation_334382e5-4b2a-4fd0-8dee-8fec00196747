package com.ruoyi.common.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 新客户通知实体类
 * 对应数据库表：crm_new_customer_notifications
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmNewCustomerNotification extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 客户ID */
    private Long customerId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 客户电话 */
    @Excel(name = "客户电话")
    private String customerPhone;

    /** 客户来源 */
    @Excel(name = "客户来源")
    private String customerSource;

    /** 关联订单ID */
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 订单金额 */
    @Excel(name = "订单金额")
    private BigDecimal orderAmount;

    /** 通知类型 */
    @Excel(name = "通知类型", readConverterExp = "NEW_CUSTOMER=新客户,UNASSIGNED_ORDER=未分配订单")
    private String notificationType;

    /** 通知状态 */
    @Excel(name = "通知状态", readConverterExp = "PENDING=待处理,PROCESSING=处理中,COMPLETED=已完成,CANCELLED=已取消")
    private String notificationStatus;

    /** 优先级 */
    @Excel(name = "优先级", readConverterExp = "HIGH=高,NORMAL=普通,LOW=低")
    private String priorityLevel;

    /** 分配给（管理员ID） */
    private Long assignedTo;

    /** 分配人ID */
    private Long assignedBy;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分配时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignedTime;

    /** 处理人ID */
    private Long processedBy;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processedTime;

    /** 处理结果 */
    @Excel(name = "处理结果")
    private String processResult;

    /** 通知渠道（JSON格式） */
    private String notificationChannels;

    /** 企业微信是否已发送 */
    @Excel(name = "微信通知", readConverterExp = "0=未发送,1=已发送")
    private Integer wechatSent;

    /** 邮件是否已发送 */
    @Excel(name = "邮件通知", readConverterExp = "0=未发送,1=已发送")
    private Integer emailSent;

    /** 删除标志 */
    private String delFlag;

    // ========== 关联对象 ==========
    
    /** 关联客户对象 */
    private CrmCustomer customer;

    /** 关联订单对象 */
    private CrmOrder order;

    /** 分配给的管理员对象 */
    private Object assignedToUser;

    /** 分配人对象 */
    private Object assignedByUser;

    /** 处理人对象 */
    private Object processedByUser;

    // ========== 查询条件字段 ==========
    
    /** 开始时间（查询用） */
    private String startTime;

    /** 结束时间（查询用） */
    private String endTime;

    /** 通知状态列表（查询用） */
    private List<String> statusList;

    /** 分配给的用户ID列表（查询用） */
    private List<Long> assignedToList;

    // ========== 常量定义 ==========
    
    /** 通知类型：新客户 */
    public static final String NOTIFICATION_TYPE_NEW_CUSTOMER = "NEW_CUSTOMER";
    
    /** 通知类型：未分配订单 */
    public static final String NOTIFICATION_TYPE_UNASSIGNED_ORDER = "UNASSIGNED_ORDER";

    /** 通知状态：待处理 */
    public static final String STATUS_PENDING = "PENDING";
    
    /** 通知状态：处理中 */
    public static final String STATUS_PROCESSING = "PROCESSING";
    
    /** 通知状态：已完成 */
    public static final String STATUS_COMPLETED = "COMPLETED";
    
    /** 通知状态：已取消 */
    public static final String STATUS_CANCELLED = "CANCELLED";

    /** 优先级：高 */
    public static final String PRIORITY_HIGH = "HIGH";
    
    /** 优先级：普通 */
    public static final String PRIORITY_NORMAL = "NORMAL";
    
    /** 优先级：低 */
    public static final String PRIORITY_LOW = "LOW";

    /** 通知渠道：系统 */
    public static final String CHANNEL_SYSTEM = "SYSTEM";
    
    /** 通知渠道：企业微信 */
    public static final String CHANNEL_WECHAT = "WECHAT";
    
    /** 通知渠道：邮件 */
    public static final String CHANNEL_EMAIL = "EMAIL";

    /** 已发送 */
    public static final Integer SENT_YES = 1;
    
    /** 未发送 */
    public static final Integer SENT_NO = 0;
}
