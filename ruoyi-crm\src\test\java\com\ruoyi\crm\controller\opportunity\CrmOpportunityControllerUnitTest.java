package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmOpportunity;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.controller.CrmOpportunityController.AssignForm;
import com.ruoyi.crm.controller.CrmOpportunityController.StageAdvanceForm;
import com.ruoyi.crm.controller.CrmOpportunityController.ConvertForm;
import com.ruoyi.crm.service.ICrmOpportunityService;
import com.ruoyi.crm.utils.TestWebContextUtils;

/**
 * CrmOpportunityController 纯单元测试
 * 不依赖Spring上下文，只使用Mockito
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("商机控制器纯单元测试")
class CrmOpportunityControllerUnitTest {

    @Mock
    private ICrmOpportunityService crmOpportunityService;

    @InjectMocks
    private CrmOpportunityController crmOpportunityController;

    private CrmOpportunity testOpportunity;
    private AssignForm assignForm;
    private StageAdvanceForm stageAdvanceForm;
    private ConvertForm convertForm;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testOpportunity = new CrmOpportunity();
        testOpportunity.setId(1L);
        testOpportunity.setOpportunityName("测试商机");
        testOpportunity.setCustomerName("测试客户");
        testOpportunity.setManagerId(1L);
        testOpportunity.setOpportunityAmount(BigDecimal.valueOf(100000));
        testOpportunity.setOpportunityStage("initial_contact");
        testOpportunity.setWinRate(BigDecimal.valueOf(10));
        testOpportunity.setExpectedCloseDate(new Date());
        testOpportunity.setOpportunitySource("online");
        testOpportunity.setOpportunityType("new_business");
        testOpportunity.setRemarks("测试备注");
        testOpportunity.setDelFlag("0");

        // 初始化分配表单
        assignForm = new AssignForm();
        assignForm.setOpportunityId(1L);
        assignForm.setNewOwnerId(2L);

        // 初始化阶段推进表单
        stageAdvanceForm = new StageAdvanceForm();
        stageAdvanceForm.setOpportunityId(1L);
        stageAdvanceForm.setNewStage("qualification");
        stageAdvanceForm.setRemarks("推进到需求确认阶段");

        // 初始化转化表单
        convertForm = new ConvertForm();
        convertForm.setOpportunityId(1L);
        convertForm.setContractName("测试合同");
        convertForm.setRemarks("转化为合同");
    }

    @AfterEach
    void tearDown() {
        // Mockito会自动重置Mocks，但为了确保清理，我们显式重置
        reset(crmOpportunityService);
    }

    @Nested
    @DisplayName("商机查询测试")
    class QueryOpportunityTests {

        @Nested
        @DisplayName("分页查询测试")
        class PaginationQueryTests {
            
            @BeforeEach
            void setUpWebContext() {
                // 只在分页查询测试中设置 Web 上下文
                TestWebContextUtils.setupDefaultWebContext();
            }

            @AfterEach
            void tearDownWebContext() {
                TestWebContextUtils.cleanupWebContext();
            }

            @Test
            @DisplayName("获取商机列表")
            void testGetOpportunityList() {
                // ✅ 需要分页，所以需要 Web 上下文
                List<CrmOpportunity> opportunityList = Arrays.asList(testOpportunity);
                when(crmOpportunityService.getOpportunityList(any(CrmOpportunity.class))).thenReturn(opportunityList);

                // Act
                TableDataInfo result = crmOpportunityController.getOpportunityList(new CrmOpportunity());

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                assertEquals(1, result.getTotal());
                assertNotNull(result.getRows());
                verify(crmOpportunityService).getOpportunityList(any(CrmOpportunity.class));
            }

            @Test
            @DisplayName("获取我负责的商机列表")
            void testGetMyOpportunityList() {
                List<CrmOpportunity> opportunityList = Arrays.asList(testOpportunity);
                when(crmOpportunityService.getMyOpportunityList(any(CrmOpportunity.class))).thenReturn(opportunityList);

                // Act
                TableDataInfo result = crmOpportunityController.getMyOpportunityList(new CrmOpportunity());

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                verify(crmOpportunityService).getMyOpportunityList(any(CrmOpportunity.class));
            }

            @Test
            @DisplayName("获取下属的商机列表")
            void testGetSubordinateOpportunityList() {
                List<CrmOpportunity> opportunityList = Arrays.asList(testOpportunity);
                when(crmOpportunityService.getSubordinateOpportunityList(any(CrmOpportunity.class))).thenReturn(opportunityList);

                // Act
                TableDataInfo result = crmOpportunityController.getSubordinateOpportunityList(new CrmOpportunity());

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                verify(crmOpportunityService).getSubordinateOpportunityList(any(CrmOpportunity.class));
            }

            @Test
            @DisplayName("获取我关注的商机列表")
            void testGetFollowedOpportunityList() {
                List<CrmOpportunity> opportunityList = Arrays.asList(testOpportunity);
                when(crmOpportunityService.getFollowedOpportunityList(any(CrmOpportunity.class))).thenReturn(opportunityList);

                // Act
                TableDataInfo result = crmOpportunityController.getFollowedOpportunityList(new CrmOpportunity());

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                verify(crmOpportunityService).getFollowedOpportunityList(any(CrmOpportunity.class));
            }
        }

        @Test
        @DisplayName("获取单个商机详情 - 成功")
        void testGetInfo_Success() {
            // ❌ 不需要分页，不设置 Web 上下文
            // Arrange
            when(crmOpportunityService.selectCrmOpportunityById(1L)).thenReturn(testOpportunity);

            // Act
            AjaxResult result = crmOpportunityController.getInfo(1L);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(testOpportunity, result.get("data"));
            verify(crmOpportunityService).selectCrmOpportunityById(1L);
        }

        @Test
        @DisplayName("获取单个商机详情 - 商机不存在")
        void testGetInfo_NotFound() {
            // Arrange
            when(crmOpportunityService.selectCrmOpportunityById(999L)).thenReturn(null);

            // Act
            AjaxResult result = crmOpportunityController.getInfo(999L);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess()); // 注意：即使返回null，控制器层也返回success
            assertNull(result.get("data"));
            verify(crmOpportunityService).selectCrmOpportunityById(999L);
        }
    }

    @Nested
    @DisplayName("商机CRUD测试")
    class CrudOpportunityTests {

        @Test
        @DisplayName("新增商机 - 成功")
        void testAddWithRecord_Success() {
            // Arrange
            when(crmOpportunityService.insertCrmOpportunity(any(CrmOpportunity.class))).thenReturn(1);

            // Act
            AjaxResult result = crmOpportunityController.addWithRecord(testOpportunity);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmOpportunityService).insertCrmOpportunity(testOpportunity);
        }

        @Test
        @DisplayName("新增商机 - 失败")
        void testAddWithRecord_Failed() {
            // Arrange
            when(crmOpportunityService.insertCrmOpportunity(any(CrmOpportunity.class))).thenReturn(0);

            // Act
            AjaxResult result = crmOpportunityController.addWithRecord(testOpportunity);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            verify(crmOpportunityService).insertCrmOpportunity(testOpportunity);
        }

        @Test
        @DisplayName("新增商机 - 异常")
        void testAddWithRecord_Exception() {
            // Arrange
            when(crmOpportunityService.insertCrmOpportunity(any(CrmOpportunity.class)))
                    .thenThrow(new RuntimeException("数据库连接失败"));

            // Act
            AjaxResult result = crmOpportunityController.addWithRecord(testOpportunity);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("数据库连接失败", result.get("msg"));
            verify(crmOpportunityService).insertCrmOpportunity(testOpportunity);
        }

        @Test
        @DisplayName("修改商机 - 成功")
        void testEditWithRecord_Success() {
            // Arrange
            when(crmOpportunityService.updateCrmOpportunity(any(CrmOpportunity.class))).thenReturn(1);

            // Act
            AjaxResult result = crmOpportunityController.editWithRecord(testOpportunity);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmOpportunityService).updateCrmOpportunity(testOpportunity);
        }

        @Test
        @DisplayName("修改商机 - 异常")
        void testEditWithRecord_Exception() {
            // Arrange
            when(crmOpportunityService.updateCrmOpportunity(any(CrmOpportunity.class)))
                    .thenThrow(new RuntimeException("更新失败"));

            // Act
            AjaxResult result = crmOpportunityController.editWithRecord(testOpportunity);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("更新失败", result.get("msg"));
            verify(crmOpportunityService).updateCrmOpportunity(testOpportunity);
        }

        @Test
        @DisplayName("删除商机 - 成功")
        void testRemoveWithRecord_Success() {
            // Arrange
            Long[] ids = {1L, 2L};
            when(crmOpportunityService.deleteCrmOpportunityByIds(ids)).thenReturn(2);

            // Act
            AjaxResult result = crmOpportunityController.removeWithRecord(ids);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmOpportunityService).deleteCrmOpportunityByIds(ids);
        }

        @Test
        @DisplayName("删除商机 - 异常")
        void testRemoveWithRecord_Exception() {
            // Arrange
            Long[] ids = {1L, 2L};
            when(crmOpportunityService.deleteCrmOpportunityByIds(ids))
                    .thenThrow(new RuntimeException("删除失败"));

            // Act
            AjaxResult result = crmOpportunityController.removeWithRecord(ids);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("删除失败", result.get("msg"));
            verify(crmOpportunityService).deleteCrmOpportunityByIds(ids);
        }
    }

    @Nested
    @DisplayName("商机分配测试")
    class AssignOpportunityTests {

        @Test
        @DisplayName("分配商机 - 成功")
        void testAssignOpportunity_Success() {
            // Arrange
            when(crmOpportunityService.selectCrmOpportunityById(1L)).thenReturn(testOpportunity);
            when(crmOpportunityService.updateCrmOpportunity(any(CrmOpportunity.class))).thenReturn(1);

            // Act
            AjaxResult result = crmOpportunityController.assignOpportunity(assignForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmOpportunityService).selectCrmOpportunityById(1L);
            verify(crmOpportunityService).updateCrmOpportunity(any(CrmOpportunity.class));
        }

        @Test
        @DisplayName("分配商机 - 商机不存在")
        void testAssignOpportunity_OpportunityNotFound() {
            // Arrange
            when(crmOpportunityService.selectCrmOpportunityById(1L)).thenReturn(null);

            // Act
            AjaxResult result = crmOpportunityController.assignOpportunity(assignForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("商机不存在", result.get("msg"));
            verify(crmOpportunityService).selectCrmOpportunityById(1L);
            verify(crmOpportunityService, never()).updateCrmOpportunity(any());
        }

        @Test
        @DisplayName("分配商机 - 更新失败")
        void testAssignOpportunity_UpdateFailed() {
            // Arrange
            when(crmOpportunityService.selectCrmOpportunityById(1L)).thenReturn(testOpportunity);
            when(crmOpportunityService.updateCrmOpportunity(any(CrmOpportunity.class))).thenReturn(0);

            // Act
            AjaxResult result = crmOpportunityController.assignOpportunity(assignForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("分配失败", result.get("msg"));
            verify(crmOpportunityService).selectCrmOpportunityById(1L);
            verify(crmOpportunityService).updateCrmOpportunity(any(CrmOpportunity.class));
        }

        @Test
        @DisplayName("分配商机 - 异常")
        void testAssignOpportunity_Exception() {
            // Arrange
            when(crmOpportunityService.selectCrmOpportunityById(1L))
                    .thenThrow(new RuntimeException("数据库错误"));

            // Act
            AjaxResult result = crmOpportunityController.assignOpportunity(assignForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertTrue(((String) result.get("msg")).contains("数据库错误"));
            verify(crmOpportunityService).selectCrmOpportunityById(1L);
        }
    }

    @Nested
    @DisplayName("商机阶段推进测试")
    class StageAdvanceTests {

        @Test
        @DisplayName("推进商机阶段 - 成功")
        void testAdvanceStage_Success() {
            // Arrange
            when(crmOpportunityService.advanceOpportunityStage(eq(1L), eq("qualification"), isNull()))
                    .thenReturn(1);

            // Act
            AjaxResult result = crmOpportunityController.advanceStage(stageAdvanceForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmOpportunityService).advanceOpportunityStage(1L, "qualification", null);
        }

        @Test
        @DisplayName("推进商机阶段 - 失败")
        void testAdvanceStage_Failed() {
            // Arrange
            when(crmOpportunityService.advanceOpportunityStage(eq(1L), eq("qualification"), isNull()))
                    .thenReturn(0);

            // Act
            AjaxResult result = crmOpportunityController.advanceStage(stageAdvanceForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("阶段推进失败", result.get("msg"));
            verify(crmOpportunityService).advanceOpportunityStage(1L, "qualification", null);
        }

        @Test
        @DisplayName("推进商机阶段 - 业务异常")
        void testAdvanceStage_ServiceException() {
            // Arrange
            when(crmOpportunityService.advanceOpportunityStage(eq(1L), eq("invalid_stage"), isNull()))
                    .thenThrow(new ServiceException("无效的商机阶段"));

            stageAdvanceForm.setNewStage("invalid_stage");

            // Act
            AjaxResult result = crmOpportunityController.advanceStage(stageAdvanceForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertTrue(((String) result.get("msg")).contains("无效的商机阶段"));
            verify(crmOpportunityService).advanceOpportunityStage(1L, "invalid_stage", null);
        }
    }

    @Nested
    @DisplayName("商机转化为合同测试")
    class ConvertToContractTests {

        @Test
        @DisplayName("转化为合同 - 成功")
        void testConvertToContract_Success() {
            // Arrange
            when(crmOpportunityService.convertToContract(1L)).thenReturn(1);

            // Act
            AjaxResult result = crmOpportunityController.convertToContract(convertForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmOpportunityService).convertToContract(1L);
        }

        @Test
        @DisplayName("转化为合同 - 失败")
        void testConvertToContract_Failed() {
            // Arrange
            when(crmOpportunityService.convertToContract(1L)).thenReturn(0);

            // Act
            AjaxResult result = crmOpportunityController.convertToContract(convertForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("转化失败", result.get("msg"));
            verify(crmOpportunityService).convertToContract(1L);
        }

        @Test
        @DisplayName("转化为合同 - 业务异常")
        void testConvertToContract_ServiceException() {
            // Arrange
            when(crmOpportunityService.convertToContract(1L))
                    .thenThrow(new ServiceException("商机已赢单，不能重复转化"));

            // Act
            AjaxResult result = crmOpportunityController.convertToContract(convertForm);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertTrue(((String) result.get("msg")).contains("商机已赢单"));
            verify(crmOpportunityService).convertToContract(1L);
        }
    }

    @Nested
    @DisplayName("导出功能测试")
    class OtherTests {

        @Test
        @DisplayName("导出商机")
        void testExport() {
            // Arrange
            List<CrmOpportunity> opportunityList = Arrays.asList(testOpportunity);
            when(crmOpportunityService.getOpportunityList(any(CrmOpportunity.class))).thenReturn(opportunityList);

            // Act
            AjaxResult result = crmOpportunityController.export(new CrmOpportunity());

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmOpportunityService).getOpportunityList(any(CrmOpportunity.class));
        }
    }

    @Nested
    @DisplayName("内部类测试")
    class InnerClassTests {

        @Test
        @DisplayName("AssignForm测试")
        void testAssignForm() {
            AssignForm form = new AssignForm();
            form.setOpportunityId(1L);
            form.setNewOwnerId(2L);

            assertEquals(1L, form.getOpportunityId());
            assertEquals(2L, form.getNewOwnerId());
        }

        @Test
        @DisplayName("StageAdvanceForm测试")
        void testStageAdvanceForm() {
            StageAdvanceForm form = new StageAdvanceForm();
            form.setOpportunityId(1L);
            form.setNewStage("qualification");
            form.setRemarks("测试推进");

            assertEquals(1L, form.getOpportunityId());
            assertEquals("qualification", form.getNewStage());
            assertEquals("测试推进", form.getRemarks());
        }

        @Test
        @DisplayName("ConvertForm测试")
        void testConvertForm() {
            ConvertForm form = new ConvertForm();
            form.setOpportunityId(1L);
            form.setContractName("测试合同");
            form.setRemarks("测试转化");

            assertEquals(1L, form.getOpportunityId());
            assertEquals("测试合同", form.getContractName());
            assertEquals("测试转化", form.getRemarks());
        }
    }
}
