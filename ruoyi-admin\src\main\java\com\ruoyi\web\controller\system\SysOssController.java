package com.ruoyi.web.controller.system;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.framework.config.OssConfig;

/**
 * OSS对象存储控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/oss")
public class SysOssController extends BaseController {
    
    @Autowired
    private OssConfig ossConfig;
    
    /**
     * 获取OSS上传策略
     */
    @GetMapping("/policy")
    public AjaxResult getPolicy() {
        OSS ossClient = null;
        try {
            String endpoint = ossConfig.getEndpoint().trim();
            String accessKeyId = ossConfig.getAccessKeyId().trim();
            String accessKeySecret = ossConfig.getAccessKeySecret();
            String bucketName = ossConfig.getBucketName().trim();

            String cleanedEndpoint = endpoint.replace("https://", "").replace("http://", "");
            String host = "https://" + bucketName + "." + cleanedEndpoint;

            // 创建OSSClient实例。
            ossClient = new OSSClientBuilder().build(cleanedEndpoint, accessKeyId, accessKeySecret);

            String dir = "uploads/" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")) + "/";

            long expireTime = 30;
            long expireEndTime = System.currentTimeMillis() + expireTime * 1000;
            Date expiration = new Date(expireEndTime);

            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);

            String postPolicy = ossClient.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = ossClient.calculatePostSignature(postPolicy);

            Map<String, String> respMap = new LinkedHashMap<>();
            respMap.put("accessKeyId", accessKeyId);
            respMap.put("policy", encodedPolicy);
            respMap.put("signature", postSignature);
            respMap.put("dir", dir);
            respMap.put("host", host);
            respMap.put("expire", String.valueOf(expireEndTime / 1000));

            return AjaxResult.success(respMap);
        } catch (Exception e) {
            logger.error("获取OSS上传策略失败", e);
            return AjaxResult.error("获取上传策略失败：" + e.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}