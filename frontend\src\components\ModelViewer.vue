<template>
  <el-dialog v-model="dialogVisible" :title="modelInfo?.name || '3D模型预览'" width="90%" class="model-viewer-dialog"
    :destroy-on-close="true">
    <div class="model-viewer-container">
      <div class="viewer-section">
        <div ref="threeContainer" class="three-container"></div>
        <div class="camera-controls">
          <el-tooltip content="重置视角">
            <el-button circle @click="resetCamera">
              <el-icon>
                <Refresh />
              </el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
      <div class="info-section">
        <div class="info-card">
          <h3>模型信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="名称">{{ modelInfo?.name || '-' }}</el-descriptions-item>
            <el-descriptions-item label="体积">{{ formatNumber(modelVolume) }} mm³</el-descriptions-item>
            <el-descriptions-item label="表面积">{{ formatNumber(surfaceArea) }} mm²</el-descriptions-item>
            <el-descriptions-item label="尺寸">
              {{ `${formatNumber(dimensions.x)} × ${formatNumber(dimensions.y)} × ${formatNumber(dimensions.z)} mm` }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="camera-info">
          <p>相机位置: ({{ formatNumber(cameraPosition.x) }}, {{ formatNumber(cameraPosition.y) }}, 
             {{ formatNumber(cameraPosition.z) }})</p>
          <p>缩放比例: {{ formatNumber(zoomLevel) }}</p>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { PLYLoader } from 'three/examples/jsm/loaders/PLYLoader';
import { ColladaLoader } from 'three/examples/jsm/loaders/ColladaLoader';
// import { STEPLoader } from 'three/examples/jsm/loaders/STEPLoader'; // 需要额外的库支持
import { onMounted, onUnmounted, ref, watch } from 'vue';

interface ModelInfo {
  name?: string;
  [key: string]: any;
}

interface Dimensions {
  x: number;
  y: number;
  z: number;
}

interface Position {
  x: number;
  y: number;
  z: number;
}

interface Props {
  modelInfo?: ModelInfo;
}

const props = withDefaults(defineProps<Props>(), {
  modelInfo: () => ({})
});

const dialogVisible = ref(false);
const threeContainer = ref<HTMLElement | null>(null);
const cameraPosition = ref<Position>({ x: 0, y: 0, z: 0 });
const zoomLevel = ref(1);
const modelVolume = ref(0);
const surfaceArea = ref(0);
const dimensions = ref<Dimensions>({ x: 0, y: 0, z: 0 });

// 内部模型数据（用于存储临时URL等）
const internalModel = ref<any>({});

let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let mesh: THREE.Object3D;

// 初始化三维场景
function initThreeJS() {
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  if (!threeContainer.value) {
    console.error('threeContainer DOM元素不存在');
    return;
  }

  // 清理容器中的旧内容
  while (threeContainer.value.firstChild) {
    threeContainer.value.removeChild(threeContainer.value.firstChild);
  }

  camera = new THREE.PerspectiveCamera(75, threeContainer.value.clientWidth / threeContainer.value.clientHeight, 0.1, 1000);
  camera.position.set(130, 189, 153);

  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight);
  threeContainer.value.appendChild(renderer.domElement);
  
  console.log('Three.js初始化完成，容器尺寸:', threeContainer.value.clientWidth, 'x', threeContainer.value.clientHeight);

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(200, 400, 200);
  scene.add(directionalLight);

  // 初始化控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.25;
  controls.target.set(118, 0, 51);

  // 添加网格
  const gridHelper = new THREE.GridHelper(200, 20, 0x888888, 0x888888);
  scene.add(gridHelper);

  // 添加坐标轴
  const axesHelper = new THREE.AxesHelper(100);
  scene.add(axesHelper);

  // 监听窗口变化
  window.addEventListener('resize', onWindowResize);
}

// 计算几何体信息
function calculateGeometryInfo(geometry: THREE.BufferGeometry) {
  // 计算体积
  let volume = 0;
  const position = geometry.attributes.position;
  const faces = position.count / 3;

  for (let i = 0; i < faces; i++) {
    const p1 = new THREE.Vector3().fromBufferAttribute(position, i * 3);
    const p2 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 1);
    const p3 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 2);
    volume += signedVolumeOfTriangle(p1, p2, p3);
  }

  // 计算表面积和尺寸
  geometry.computeBoundingBox();
  const boundingBox = geometry.boundingBox;
  
  if (boundingBox) {
    dimensions.value = {
      x: boundingBox.max.x - boundingBox.min.x,
      y: boundingBox.max.y - boundingBox.min.y,
      z: boundingBox.max.z - boundingBox.min.z
    };
  }

  // 计算表面积
  let area = 0;
  for (let i = 0; i < faces; i++) {
    const p1 = new THREE.Vector3().fromBufferAttribute(position, i * 3);
    const p2 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 1);
    const p3 = new THREE.Vector3().fromBufferAttribute(position, i * 3 + 2);
    area += calculateTriangleArea(p1, p2, p3);
  }

  modelVolume.value = Math.abs(volume);
  surfaceArea.value = area;
}

function signedVolumeOfTriangle(p1: THREE.Vector3, p2: THREE.Vector3, p3: THREE.Vector3): number {
  return p1.dot(p2.cross(p3)) / 6.0;
}

function calculateTriangleArea(p1: THREE.Vector3, p2: THREE.Vector3, p3: THREE.Vector3): number {
  const a = new THREE.Vector3().subVectors(p2, p1);
  const b = new THREE.Vector3().subVectors(p3, p1);
  const cross = new THREE.Vector3().crossVectors(a, b);
  return cross.length() / 2;
}

// 加载3D模型
function loadModel(url: string, fileExtension?: string) {
  if (!url) {
    ElMessage.error('模型路径为空');
    return;
  }

  // 如果提供了文件扩展名，直接使用；否则从URL中解析
  let extension: string | undefined;
  if (fileExtension) {
    extension = fileExtension.toLowerCase();
  } else {
    // 更健壮的扩展名解析，处理URL参数和特殊字符
    const cleanUrl = url.split('?')[0]; // 去除查询参数
    extension = cleanUrl.split('.').pop()?.toLowerCase();
  }
  
  console.log('loadModel called with:', { originalUrl: url, extension, providedExtension: fileExtension });
  
  switch(extension) {
    case 'stl':
      loadSTLModel(url);
      break;
    case 'obj':
      loadOBJModel(url);
      break;
    case 'fbx':
      loadFBXModel(url);
      break;
    case 'gltf':
    case 'glb':
      loadGLTFModel(url);
      break;
    case 'ply':
      loadPLYModel(url);
      break;
    case 'dae':
      loadColladaModel(url);
      break;
    case 'step':
    case 'stp':
      ElMessage.error({
        message: 'STEP格式暂不支持',
        description: 'STEP/STP是CAD专用格式，需要专门的转换库支持。请将文件转换为STL、OBJ或GLTF格式后重新上传。',
        duration: 8000
      });
      console.warn('STEP格式文件需要转换:', { url, extension });
      return;
    default:
      console.warn('Unknown file extension, trying STL loader as fallback:', { url, extension });
      // 如果扩展名未知，尝试作为STL文件加载（兜底方案）
      if (!extension || extension === '') {
        ElMessage.warning('无法识别文件格式，尝试按STL格式加载');
        loadSTLModel(url);
      } else {
        ElMessage.error(`不支持的模型格式: ${extension || '未知'}，支持的格式包括：stl, obj, fbx, gltf, glb, ply, dae`);
      }
      return;
  }
}

// 加载STL模型
function loadSTLModel(url: string) {
  const loader = new STLLoader();
  loader.load(
    url,
    (geometry) => {
      console.log('STL模型加载成功:', geometry);
      createMeshFromGeometry(geometry);
    },
    onLoadProgress,
    (error) => {
      console.error('STL模型加载失败:', error);
      onLoadError(error);
    }
  );
}

// 加载OBJ模型
function loadOBJModel(url: string) {
  const loader = new OBJLoader();
  loader.load(
    url,
    (object) => {
      if (mesh) {
        scene.remove(mesh);
      }
      
      // OBJ加载器返回的是Group对象，需要遍历其子对象
      object.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.material = new THREE.MeshPhongMaterial({
            color: 0x3399ff,
            specular: 0x111111,
            shininess: 200
          });
        }
      });
      
      mesh = object;
      scene.add(mesh);
      
      // 获取几何体信息
      const geometry = extractGeometryFromObject(object);
      if (geometry) {
        calculateGeometryInfo(geometry);
      }
      
      fitCameraToObject(mesh);
      generateThumbnail();
    },
    onLoadProgress,
    onLoadError
  );
}

// 加载FBX模型
function loadFBXModel(url: string) {
  const loader = new FBXLoader();
  loader.load(
    url,
    (object) => {
      if (mesh) {
        scene.remove(mesh);
      }
      
      // 设置材质
      object.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.material = new THREE.MeshPhongMaterial({
            color: 0x3399ff,
            specular: 0x111111,
            shininess: 200
          });
        }
      });
      
      mesh = object;
      scene.add(mesh);
      
      const geometry = extractGeometryFromObject(object);
      if (geometry) {
        calculateGeometryInfo(geometry);
      }
      
      fitCameraToObject(mesh);
      generateThumbnail();
    },
    onLoadProgress,
    onLoadError
  );
}

// 加载GLTF模型
function loadGLTFModel(url: string) {
  const loader = new GLTFLoader();
  loader.load(
    url,
    (gltf) => {
      if (mesh) {
        scene.remove(mesh);
      }
      
      mesh = gltf.scene;
      scene.add(mesh);
      
      const geometry = extractGeometryFromObject(mesh);
      if (geometry) {
        calculateGeometryInfo(geometry);
      }
      
      fitCameraToObject(mesh);
      generateThumbnail();
    },
    onLoadProgress,
    onLoadError
  );
}

// 加载PLY模型
function loadPLYModel(url: string) {
  const loader = new PLYLoader();
  loader.load(
    url,
    (geometry) => {
      createMeshFromGeometry(geometry);
    },
    onLoadProgress,
    onLoadError
  );
}

// 加载Collada模型
function loadColladaModel(url: string) {
  const loader = new ColladaLoader();
  loader.load(
    url,
    (collada) => {
      if (mesh) {
        scene.remove(mesh);
      }
      
      mesh = collada.scene;
      scene.add(mesh);
      
      const geometry = extractGeometryFromObject(mesh);
      if (geometry) {
        calculateGeometryInfo(geometry);
      }
      
      fitCameraToObject(mesh);
      generateThumbnail();
    },
    onLoadProgress,
    onLoadError
  );
}

// 从几何体创建网格
function createMeshFromGeometry(geometry: THREE.BufferGeometry) {
  if (mesh) {
    scene.remove(mesh);
  }

  const material = new THREE.MeshPhongMaterial({
    color: 0x3399ff,
    specular: 0x111111,
    shininess: 200
  });
  
  mesh = new THREE.Mesh(geometry, material);
  scene.add(mesh);

  calculateGeometryInfo(geometry);
  fitCameraToObject(mesh);
  generateThumbnail();
}

// 从对象中提取几何体
function extractGeometryFromObject(object: THREE.Object3D): THREE.BufferGeometry | null {
  let geometry: THREE.BufferGeometry | null = null;
  
  object.traverse((child) => {
    if (child instanceof THREE.Mesh && child.geometry) {
      if (!geometry) {
        geometry = child.geometry;
      }
    }
  });
  
  return geometry;
}

// 加载进度处理
function onLoadProgress(xhr: ProgressEvent) {
  if (xhr.lengthComputable) {
    console.log((xhr.loaded / xhr.total * 100) + '% loaded');
  }
}

// 加载错误处理
function onLoadError(error: any) {
  const errorMsg = error?.message || error?.toString() || '未知错误';
  ElMessage.error(`模型加载失败: ${errorMsg}`);
  console.error('模型加载错误详情:', {
    error,
    errorMessage: errorMsg,
    errorType: typeof error,
    stack: error?.stack
  });
}

function generateThumbnail(): Promise<string> {
  return new Promise((resolve) => {
    // 等待一帧确保渲染完成
    requestAnimationFrame(() => {
      const dataURL = renderer.domElement.toDataURL('image/png');
      resolve(dataURL);
    });
  });
}

// 自动调整相机位置
function fitCameraToObject(object: THREE.Object3D, offset = 1.5) {
  const boundingBox = new THREE.Box3();
  boundingBox.setFromObject(object);
  
  const center = boundingBox.getCenter(new THREE.Vector3());
  const size = boundingBox.getSize(new THREE.Vector3());
  
  const maxDim = Math.max(size.x, size.y, size.z);
  const fov = camera.fov * (Math.PI / 180);
  let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2)) * offset;
  
  camera.position.set(center.x + cameraZ, center.y + cameraZ, center.z + cameraZ);
  controls.target.copy(center);
  
  camera.updateProjectionMatrix();
  controls.update();
}

// 重置相机位置
function resetCamera() {
  if (mesh) {
    fitCameraToObject(mesh);
  }
}

// 窗口大小变化处理
function onWindowResize() {
  if (camera && renderer && threeContainer.value) {
    camera.aspect = threeContainer.value.clientWidth / threeContainer.value.clientHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight);
  }
}

// 动画循环
function animate() {
  requestAnimationFrame(animate);
  
  if (controls) {
    controls.update();
  }
  
  if (camera) {
    const pos = camera.position;
    cameraPosition.value = { x: pos.x, y: pos.y, z: pos.z };
    zoomLevel.value = camera.zoom;
  }
  
  // 确保renderer、scene和camera都已初始化
  if (renderer && scene && camera) {
    renderer.render(scene, camera);
  }
}

// 格式化数字
function formatNumber(num: number): string {
  return num.toFixed(2);
}

// 打开查看器
function open(modelUrl: string, fileExtension?: string) {
  if (!modelUrl) {
    ElMessage.error('模型路径为空');
    return;
  }

  console.log('ModelViewer open called with:', { modelUrl, modelInfo: props.modelInfo, fileExtension });
  
  // 存储当前模型信息（包括临时URL）
  internalModel.value = {
    url: modelUrl,
    name: props.modelInfo?.name || '3D模型',
    extension: fileExtension
  };
  
  dialogVisible.value = true;
  
  // 确保DOM已经更新
  setTimeout(() => {
    // 每次打开都重新初始化Three.js，确保状态干净
    if (!scene || !renderer || !camera) {
      console.log('重新初始化Three.js场景');
      initThreeJS();
      animate();
    } else {
      console.log('Three.js场景已存在，清理旧模型');
      // 如果场景存在，清理旧模型
      if (mesh) {
        scene.remove(mesh);
        mesh = null as any;
      }
    }
    
    loadModel(modelUrl, fileExtension);
  }, 100); // 增加延迟确保DOM准备就绪
}

// 清理资源
function cleanup() {
  if (renderer) {
    renderer.dispose();
    renderer = null as any; // 重置renderer引用
  }
  if (mesh) {
    // 从场景中移除mesh
    if (scene) {
      scene.remove(mesh);
    }
    // 安全地清理mesh资源
    mesh.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) {
          child.geometry.dispose();
        }
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => material.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    });
    mesh = null as any; // 重置mesh引用
  }
  if (controls) {
    controls.dispose();
    controls = null as any; // 重置controls引用
  }
  
  // 重置所有Three.js对象引用，确保下次完全重新初始化
  scene = null as any;
  camera = null as any;
  
  window.removeEventListener('resize', onWindowResize);
}

// 监听对话框关闭
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    cleanup();
    
    // 注意：不在这里清理blob URL，因为主页面可能还在使用
    // blob URL的清理应该由创建者负责
    console.log('ModelViewer对话框已关闭，但保留blob URL给主页面使用');
  }
});

// 暴露方法给父组件
defineExpose({
  open
});

// 组件挂载时初始化
onMounted(() => {
  // 只在对话框打开时才初始化Three.js
  // initThreeJS();
  // animate();
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', onWindowResize);
  if (renderer) {
    renderer.dispose();
  }
  if (controls) {
    controls.dispose();
  }
});
</script>

<style scoped>
.model-viewer-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.model-viewer-container {
  display: flex;
  height: 80vh;
}

.viewer-section {
  flex: 1;
  position: relative;
}

.three-container {
  width: 100%;
  height: 100%;
}

.camera-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
}

.info-section {
  width: 300px;
  padding: 20px;
  background: #f5f7fa;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.info-card {
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.info-card h3 {
  margin: 0 0 15px 0;
  color: #303133;
}

.camera-info {
  margin-top: 20px;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  font-size: 12px;
  color: #606266;
}

.camera-info p {
  margin: 5px 0;
}
</style>
