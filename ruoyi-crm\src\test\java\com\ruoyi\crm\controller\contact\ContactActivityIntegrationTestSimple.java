package com.ruoyi.crm.controller;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.entity.CrmContactFollowupRecords;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmContactFollowupRecordsService;

/**
 * 联系人活动记录集成测试
 * 测试前端对应的所有活动记录接口功能
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@AutoConfigureWebMvc
@Transactional
@Rollback
@DisplayName("联系人活动记录集成测试")
public class ContactActivityIntegrationTestSimple extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmContactsService crmContactsService;

    @Autowired
    private ICrmContactFollowupRecordsService contactFollowupRecordsService;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private Long testContactId;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .build();
        
        objectMapper = new ObjectMapper();
        
        // 创建测试联系人
        setupTestContact();
        
        // 注意：不需要再次设置认证，BaseTestCase已经在setUpBase()中设置了
        // setupAuthentication(); // 删除这行，避免覆盖BaseTestCase的认证设置
    }

    /**
     * 创建测试联系人数据
     */
    private void setupTestContact() {
        CrmContacts testContact = new CrmContacts();
        testContact.setName("测试联系人");
        testContact.setPhone("13800138000");
        testContact.setEmail("<EMAIL>");
        testContact.setPosition("测试职位");
        testContact.setResponsiblePersonId("1");
        testContact.setCreateTime(new Date());
        testContact.setDelFlag("0");
        
        int result = crmContactsService.insertCrmContacts(testContact);
        assertTrue(result > 0, "创建测试联系人失败");
        
        testContactId = testContact.getId();
        assertNotNull(testContactId, "测试联系人ID不能为空");
    }

    /**
     * 设置简单的认证用户
     * 注意：此方法已被注释，因为BaseTestCase已经提供了正确的认证设置
     */
    @Deprecated
    private void setupAuthentication() {
        // 此方法已废弃，BaseTestCase.setUpBase()中的TestAuthenticationUtils.setupDefaultAuthentication()
        // 已经正确设置了包含LoginUser的认证信息，这里不需要再设置
        // 
        // UsernamePasswordAuthenticationToken authToken = 
        //     new UsernamePasswordAuthenticationToken(
        //         "admin", 
        //         "password", 
        //         Arrays.asList(new SimpleGrantedAuthority("ROLE_USER"))
        //     );
        // SecurityContextHolder.getContext().setAuthentication(authToken);
    }

    @Test
//     @Rollback(false)  // 临时禁用回滚，仅用于调试
    @DisplayName("测试获取联系人活动记录列表")
    public void testGetContactActivities() throws Exception {
        // 先创建一些测试数据
        Long activityId1 = createTestActivity("phone", "电话", "测试电话跟进内容", "interested");
        Long activityId2 = createTestActivity("email", "邮件", "测试邮件跟进内容", "considering");
        
        System.out.println("=== 调试信息 ===");
        System.out.println("测试联系人ID: " + testContactId);
        System.out.println("创建的活动记录ID1: " + activityId1);
        System.out.println("创建的活动记录ID2: " + activityId2);
        
        // 检查数据是否真的插入了
        CrmContactFollowupRecords record1 = contactFollowupRecordsService.selectCrmContactFollowupRecordsById(activityId1);
        CrmContactFollowupRecords record2 = contactFollowupRecordsService.selectCrmContactFollowupRecordsById(activityId2);
        System.out.println("从数据库查询到的记录1: " + (record1 != null ? record1.getFollowUpContent() : "null"));
        System.out.println("从数据库查询到的记录2: " + (record2 != null ? record2.getFollowUpContent() : "null"));
        
        // 在这里设置断点，可以查看数据库
        System.out.println("=== 可以在此时查看数据库，数据应该存在 ===");
        
        // 执行测试
        MvcResult result = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andExpect(jsonPath("$.data", hasSize(greaterThanOrEqualTo(2))))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("获取活动记录响应: " + responseContent);
    }

    @Test
    @DisplayName("测试创建联系人活动记录")
    public void testCreateContactActivity() throws Exception {
        CrmContactFollowupRecords newActivity = new CrmContactFollowupRecords();
        newActivity.setContactId(testContactId);
        newActivity.setFollowUpContent("这是一次重要的电话沟通");
        newActivity.setFollowUpMethod("电话");
        newActivity.setNextContactTime(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L)); // 一周后
        newActivity.setCommunicationResult("客户很感兴趣");
        newActivity.setMeetingSummary("电话跟进详细内容");
        newActivity.setContactQuality("high");
        
        String requestJson = objectMapper.writeValueAsString(newActivity);
        System.out.println("创建活动记录请求: " + requestJson);

        MvcResult result = mockMvc.perform(post("/front/crm/contacts/activities")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("创建活动记录响应: " + responseContent);
    }

    @Test
    @DisplayName("测试编辑联系人活动记录")
    public void testUpdateContactActivity() throws Exception {
        // 先创建一个活动记录
        Long activityId = createTestActivity("meeting", "会议", "原始会议内容", "considering");
        
        // 准备更新数据
        CrmContactFollowupRecords updateActivity = new CrmContactFollowupRecords();
        updateActivity.setFollowUpMethod("会议");
        updateActivity.setFollowUpContent("更新后的跟进内容");
        updateActivity.setContactQuality("interested_deep");
        updateActivity.setCommunicationResult("客户表现出强烈兴趣");
        updateActivity.setNextContactTime(new Date(System.currentTimeMillis() + 3 * 24 * 60 * 60 * 1000L)); // 3天后
        
        String requestJson = objectMapper.writeValueAsString(updateActivity);
        System.out.println("更新活动记录请求: " + requestJson);

        MvcResult result = mockMvc.perform(put("/front/crm/contacts/{contactId}/activities/{id}", testContactId, activityId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("更新活动记录响应: " + responseContent);
        
        // 验证数据是否真的更新了
        CrmContactFollowupRecords updatedRecord = contactFollowupRecordsService.selectCrmContactFollowupRecordsById(activityId);
        assertNotNull(updatedRecord);
        assertEquals("更新后的跟进内容", updatedRecord.getFollowUpContent());
        assertEquals("interested_deep", updatedRecord.getContactQuality());
    }

    @Test
    @DisplayName("测试删除联系人活动记录")
    public void testDeleteContactActivity() throws Exception {
        // 先创建一个活动记录
        Long activityId = createTestActivity("visit", "拜访", "客户拜访记录", "no_need");
        
        // 执行删除
        MvcResult result = mockMvc.perform(delete("/front/crm/contacts/{contactId}/activities/{id}", testContactId, activityId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("删除活动记录响应: " + responseContent);
        
        // 验证记录是否被删除
        CrmContactFollowupRecords deletedRecord = contactFollowupRecordsService.selectCrmContactFollowupRecordsById(activityId);
        assertNull(deletedRecord, "记录应该已被删除");
    }

    @Test
    @DisplayName("测试获取联系人活动记录统计")
    public void testGetContactActivityStats() throws Exception {
        // 创建多种类型的活动记录
        createTestActivity("phone", "电话", "电话跟进1", "interested");
        createTestActivity("email", "邮件", "邮件跟进1", "considering");
        createTestActivity("meeting", "会议", "会议跟进1", "interested_deep");
        
        MvcResult result = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities/stats", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andExpect(jsonPath("$.data.totalCount").value(greaterThanOrEqualTo(3)))
                .andExpect(jsonPath("$.data.lastActivityTime").exists())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("获取活动统计响应: " + responseContent);
    }

    @Test
    @DisplayName("测试不存在的联系人ID")
    public void testNonExistentContactId() throws Exception {
        Long nonExistentContactId = 999999L;
        
        mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", nonExistentContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(0))); // 应该返回空列表
    }

    @Test
    @DisplayName("测试编辑不存在的活动记录")
    public void testUpdateNonExistentActivity() throws Exception {
        Long nonExistentActivityId = 999999L;
        
        CrmContactFollowupRecords updateActivity = new CrmContactFollowupRecords();
        updateActivity.setFollowUpContent("尝试更新不存在的记录");
        
        String requestJson = objectMapper.writeValueAsString(updateActivity);

        mockMvc.perform(put("/front/crm/contacts/{contactId}/activities/{id}", testContactId, nonExistentActivityId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg", containsString("不存在")));
    }

    @Test
    @DisplayName("测试活动记录的所有类型")
    public void testAllActivityTypes() throws Exception {
        String[] activityTypes = {"phone", "email", "meeting", "visit", "demo", "other"};
        String[] activityResults = {"interested", "considering", "no_need", "interested_deep", "refused"};
        
        for (int i = 0; i < activityTypes.length; i++) {
            String type = activityTypes[i];
            String result = activityResults[i % activityResults.length];
            
            CrmContactFollowupRecords activity = new CrmContactFollowupRecords();
            activity.setContactId(testContactId);
            activity.setFollowUpContent("跟进内容 - " + type);
            activity.setFollowUpMethod(type);
            activity.setContactQuality(result);
            activity.setCommunicationResult("联系结果 - " + result);
            activity.setCreateTime(new Date());
            
            String requestJson = objectMapper.writeValueAsString(activity);

            mockMvc.perform(post("/front/crm/contacts/activities")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestJson))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
        }
        
        // 验证所有记录都创建成功
        mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(greaterThanOrEqualTo(activityTypes.length))));
    }

    @Test
    @DisplayName("测试完整的活动记录工作流程")
    public void testCompleteActivityWorkflow() throws Exception {
        // 1. 创建活动记录
        CrmContactFollowupRecords newActivity = new CrmContactFollowupRecords();
        newActivity.setContactId(testContactId);
        newActivity.setFollowUpContent("电话跟进内容");
        newActivity.setFollowUpMethod("电话");
        newActivity.setContactQuality("interested");
        newActivity.setCommunicationResult("客户表示感兴趣");
        newActivity.setCreateTime(new Date());
        
        String createJson = objectMapper.writeValueAsString(newActivity);
        
        MvcResult createResult = mockMvc.perform(post("/front/crm/contacts/activities")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();
        
        // 2. 查询活动记录列表，找到刚创建的记录
        MvcResult listResult = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(greaterThan(0))))
                .andReturn();
        
        // 解析响应获取活动ID
        String listResponse = listResult.getResponse().getContentAsString();
        AjaxResult listAjaxResult = objectMapper.readValue(listResponse, AjaxResult.class);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> activities = (List<Map<String, Object>>) listAjaxResult.get("data");
        Long activityId = Long.valueOf(activities.get(0).get("id").toString());
        
        // 3. 编辑活动记录
        CrmContactFollowupRecords updateActivity = new CrmContactFollowupRecords();
        updateActivity.setFollowUpContent("更新后的电话内容");
        updateActivity.setContactQuality("interested_deep");
        updateActivity.setCommunicationResult("客户非常感兴趣，要求详细方案");
        
        String updateJson = objectMapper.writeValueAsString(updateActivity);
        
        mockMvc.perform(put("/front/crm/contacts/{contactId}/activities/{id}", testContactId, activityId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(updateJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
        
        // 4. 获取统计信息
        mockMvc.perform(get("/front/crm/contacts/{contactId}/activities/stats", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.totalCount").value(greaterThan(0)));
        
        // 5. 删除活动记录
        mockMvc.perform(delete("/front/crm/contacts/{contactId}/activities/{id}", testContactId, activityId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
        
        System.out.println("完整工作流程测试完成");
    }

    @Test
    @DisplayName("测试活动记录数据验证")
    public void testActivityDataValidation() throws Exception {
        // 测试各种边界条件和数据验证
        
        // 1. 测试创建活动记录时缺少联系人ID
        CrmContactFollowupRecords invalidActivity1 = new CrmContactFollowupRecords();
        invalidActivity1.setFollowUpContent("缺少联系人ID的活动");
        invalidActivity1.setFollowUpMethod("phone");
        
        String json1 = objectMapper.writeValueAsString(invalidActivity1);
        mockMvc.perform(post("/front/crm/contacts/activities")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json1))
                .andDo(print())
                .andExpect(status().isOk()); // 根据实际业务逻辑调整
        
        // 2. 测试超长内容
        CrmContactFollowupRecords longContentActivity = new CrmContactFollowupRecords();
        longContentActivity.setContactId(testContactId);
        longContentActivity.setFollowUpContent("这是一个非常长的内容".repeat(100)); // 创建超长内容
        longContentActivity.setFollowUpMethod("email");
        
        String json2 = objectMapper.writeValueAsString(longContentActivity);
        mockMvc.perform(post("/front/crm/contacts/activities")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json2))
                .andDo(print())
                .andExpect(status().isOk());
        
        // 3. 测试无效的活动类型
        CrmContactFollowupRecords invalidTypeActivity = new CrmContactFollowupRecords();
        invalidTypeActivity.setContactId(testContactId);
        invalidTypeActivity.setFollowUpContent("无效活动类型测试");
        invalidTypeActivity.setFollowUpMethod("invalid_type");
        
        String json3 = objectMapper.writeValueAsString(invalidTypeActivity);
        mockMvc.perform(post("/front/crm/contacts/activities")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json3))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试并发创建活动记录")
    public void testConcurrentActivityCreation() throws Exception {
        // 模拟并发创建多个活动记录
        int concurrentCount = 5;
        
        for (int i = 0; i < concurrentCount; i++) {
            CrmContactFollowupRecords activity = new CrmContactFollowupRecords();
            activity.setContactId(testContactId);
            activity.setFollowUpContent("并发跟进内容 " + i);
            activity.setFollowUpMethod("电话");
            activity.setContactQuality("interested");
            activity.setCreateTime(new Date());
            
            String json = objectMapper.writeValueAsString(activity);
            
            mockMvc.perform(post("/front/crm/contacts/activities")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(json))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
        }
        
        // 验证所有记录都创建成功
        MvcResult result = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(greaterThanOrEqualTo(concurrentCount))))
                .andReturn();
        
        System.out.println("并发创建测试完成，响应: " + result.getResponse().getContentAsString());
    }

    @Test
//     @Rollback(false)  // 数据不回滚，可以在数据库中查看
    @DisplayName("调试专用：查看数据库中的测试数据")
    public void debugDatabaseData() throws Exception {
        System.out.println("=== 开始调试数据库数据 ===");
        
        // 创建测试数据
        CrmContactFollowupRecords activity1 = new CrmContactFollowupRecords();
        activity1.setContactId(testContactId);
        activity1.setFollowUpMethod("电话");
        activity1.setFollowUpContent("调试测试数据1 - 电话跟进");
        activity1.setCommunicationResult("客户表示感兴趣");
        activity1.setContactQuality("interested");
        activity1.setCreateTime(new Date());
        activity1.setUpdateTime(new Date());
        
        int result1 = contactFollowupRecordsService.insertCrmContactFollowupRecords(activity1);
        System.out.println("插入活动记录1结果: " + result1 + ", ID: " + activity1.getId());
        
        CrmContactFollowupRecords activity2 = new CrmContactFollowupRecords();
        activity2.setContactId(testContactId);
        activity2.setFollowUpMethod("邮件");
        activity2.setFollowUpContent("调试测试数据2 - 邮件跟进");
        activity2.setCommunicationResult("客户要求更多信息");
        activity2.setContactQuality("considering");
        activity2.setCreateTime(new Date());
        activity2.setUpdateTime(new Date());
        
        int result2 = contactFollowupRecordsService.insertCrmContactFollowupRecords(activity2);
        System.out.println("插入活动记录2结果: " + result2 + ", ID: " + activity2.getId());
        
        // 查询联系人信息
        CrmContacts contact = crmContactsService.selectCrmContactsById(testContactId);
        System.out.println("测试联系人信息: " + (contact != null ? contact.getName() : "未找到"));
        
        // 验证数据是否插入成功
        CrmContactFollowupRecords dbRecord1 = contactFollowupRecordsService.selectCrmContactFollowupRecordsById(activity1.getId());
        CrmContactFollowupRecords dbRecord2 = contactFollowupRecordsService.selectCrmContactFollowupRecordsById(activity2.getId());
        
        System.out.println("数据库中的记录1: " + (dbRecord1 != null ? dbRecord1.getFollowUpContent() : "null"));
        System.out.println("数据库中的记录2: " + (dbRecord2 != null ? dbRecord2.getFollowUpContent() : "null"));
        
        System.out.println("=== 数据库表信息 ===");
        System.out.println("联系人表: crm_contacts，联系人ID: " + testContactId);
        System.out.println("活动记录表: crm_contact_followup_records");
        System.out.println("活动记录ID1: " + activity1.getId());
        System.out.println("活动记录ID2: " + activity2.getId());
        
        System.out.println("=== 可以到数据库查看以下数据 ===");
        System.out.println("SELECT * FROM crm_contacts WHERE id = " + testContactId + ";");
        System.out.println("SELECT * FROM crm_contact_followup_records WHERE contact_id = " + testContactId + ";");
        System.out.println("SELECT * FROM crm_contact_followup_records WHERE id IN (" + activity1.getId() + ", " + activity2.getId() + ");");
        
        // 断言确保数据插入成功
        assertNotNull(dbRecord1, "活动记录1应该存在于数据库中");
        assertNotNull(dbRecord2, "活动记录2应该存在于数据库中");
        assertEquals("调试测试数据1 - 电话跟进", dbRecord1.getFollowUpContent());
        assertEquals("调试测试数据2 - 邮件跟进", dbRecord2.getFollowUpContent());
        
        System.out.println("=== 调试完成 ===");
    }

    /**
     * 创建测试活动记录的辅助方法
     */
    private Long createTestActivity(String activityType, String followUpMethod, String content, String result) {
        CrmContactFollowupRecords activity = new CrmContactFollowupRecords();
        activity.setContactId(testContactId);
        activity.setFollowUpMethod(followUpMethod);
        activity.setFollowUpContent(content);
        activity.setCommunicationResult("测试联系结果");
        activity.setContactQuality(result);
        activity.setCreateTime(new Date());
        activity.setUpdateTime(new Date());
        
        int insertResult = contactFollowupRecordsService.insertCrmContactFollowupRecords(activity);
        assertTrue(insertResult > 0, "创建测试活动记录失败");
        
        return activity.getId();
    }

    @Test
    @Rollback(false)  // 禁用回滚，便于调试
    @DisplayName("综合测试：Admin账户查找最新联系人并获取活动记录")
    public void testAdminFindLatestContactAndActivities() throws Exception {
        System.out.println("=== 开始综合测试：以admin身份(ID=1)查找最新联系人并获取活动记录 ===");
        
        // 1. 首先创建多个测试联系人，确保有最新的记录
        Long adminUserId = 1L;
        createMultipleTestContacts(adminUserId);
        
        // 2. 查询联系人列表，按创建时间倒序获取最新的联系人
        MvcResult contactListResult = mockMvc.perform(get("/front/crm/contacts/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .param("orderByColumn", "createTime")
                .param("isAsc", "desc")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows", hasSize(greaterThan(0))))
                .andReturn();
        
        String contactListResponse = contactListResult.getResponse().getContentAsString();
        System.out.println("联系人列表响应: " + contactListResponse);
        
        // 解析响应获取最新联系人ID
        AjaxResult contactListAjaxResult = objectMapper.readValue(contactListResponse, AjaxResult.class);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> contacts = (List<Map<String, Object>>) contactListAjaxResult.get("rows");
        assertNotNull(contacts, "联系人列表不能为空");
        assertTrue(contacts.size() > 0, "至少应该有一个联系人");
        
        // 获取最新联系人（列表第一个，因为按创建时间倒序）
        Map<String, Object> latestContact = contacts.get(0);
        Long latestContactId = Long.valueOf(latestContact.get("id").toString());
        String contactName = (String) latestContact.get("name");
        
        System.out.println("找到最新联系人 - ID: " + latestContactId + ", 姓名: " + contactName);
        
        // 3. 为最新联系人创建多条活动记录
        System.out.println("=== 为最新联系人创建活动记录 ===");
        createActivitiesForContact(latestContactId);
        
        // 4. 获取该联系人的活动记录列表
        MvcResult activitiesResult = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", latestContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andExpect(jsonPath("$.data", hasSize(greaterThanOrEqualTo(3))))
                .andReturn();
        
        String activitiesResponse = activitiesResult.getResponse().getContentAsString();
        System.out.println("联系人活动记录响应: " + activitiesResponse);
        
        // 解析活动记录
        AjaxResult activitiesAjaxResult = objectMapper.readValue(activitiesResponse, AjaxResult.class);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> activities = (List<Map<String, Object>>) activitiesAjaxResult.get("data");
        
        System.out.println("找到活动记录数量: " + activities.size());
        for (int i = 0; i < activities.size(); i++) {
            Map<String, Object> activity = activities.get(i);
            System.out.println("活动记录" + (i + 1) + ": " + 
                "ID=" + activity.get("id") + 
                ", 方式=" + activity.get("followUpMethod") + 
                ", 内容=" + activity.get("followUpContent"));
        }
        
        // 5. 获取该联系人的活动记录统计信息
        MvcResult statsResult = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities/stats", latestContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalCount").value(greaterThanOrEqualTo(3)))
                .andExpect(jsonPath("$.data.lastActivityTime").exists())
                .andReturn();
        
        String statsResponse = statsResult.getResponse().getContentAsString();
        System.out.println("活动统计响应: " + statsResponse);
        
        // 6. 验证活动记录内容和数量
        assertTrue(activities.size() >= 3, "应该有至少3条活动记录");
        
        // 验证活动记录的内容
        boolean hasPhoneActivity = activities.stream().anyMatch(a -> "电话".equals(a.get("followUpMethod")));
        boolean hasEmailActivity = activities.stream().anyMatch(a -> "邮件".equals(a.get("followUpMethod")));
        boolean hasMeetingActivity = activities.stream().anyMatch(a -> "会议".equals(a.get("followUpMethod")));
        
        assertTrue(hasPhoneActivity, "应该包含电话活动记录");
        assertTrue(hasEmailActivity, "应该包含邮件活动记录");
        assertTrue(hasMeetingActivity, "应该包含会议活动记录");
        
        // 7. 测试编辑最新的活动记录
        Map<String, Object> latestActivity = activities.get(0);
        Long latestActivityId = Long.valueOf(latestActivity.get("id").toString());
        
        CrmContactFollowupRecords updateActivity = new CrmContactFollowupRecords();
        updateActivity.setFollowUpContent("admin用户更新的活动记录内容");
        updateActivity.setContactQuality("interested_deep");
        updateActivity.setCommunicationResult("经过深入沟通，客户表现出强烈的合作意向");
        updateActivity.setNextContactTime(new Date(System.currentTimeMillis() + 2 * 24 * 60 * 60 * 1000L)); // 2天后
        
        String updateJson = objectMapper.writeValueAsString(updateActivity);
        
        MvcResult updateResult = mockMvc.perform(put("/front/crm/contacts/{contactId}/activities/{id}", latestContactId, latestActivityId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(updateJson))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andReturn();
        
        System.out.println("活动记录更新结果: " + updateResult.getResponse().getContentAsString());
        
        // 8. 验证更新是否成功
        CrmContactFollowupRecords updatedRecord = contactFollowupRecordsService.selectCrmContactFollowupRecordsById(latestActivityId);
        assertNotNull(updatedRecord, "更新后的活动记录应该存在");
        assertEquals("admin用户更新的活动记录内容", updatedRecord.getFollowUpContent(), "活动内容应该已更新");
        assertEquals("interested_deep", updatedRecord.getContactQuality(), "联系质量应该已更新");
        
        System.out.println("=== 综合测试完成 ===");
        System.out.println("测试总结:");
        System.out.println("- 成功以admin身份(ID=1)查询联系人列表");
        System.out.println("- 找到最新联系人: " + contactName + " (ID: " + latestContactId + ")");
        System.out.println("- 成功为该联系人创建了" + activities.size() + "条活动记录");
        System.out.println("- 成功获取活动记录列表和统计信息");
        System.out.println("- 成功更新了活动记录内容");
        System.out.println("- 所有验证均通过");
    }

    /**
     * 创建多个测试联系人（确保有admin创建的最新记录）
     */
    private void createMultipleTestContacts(Long adminUserId) {
        for (int i = 1; i <= 3; i++) {
            CrmContacts contact = new CrmContacts();
            contact.setName("测试联系人" + i + "_" + System.currentTimeMillis());
            contact.setPhone("1380013800" + i);
            contact.setEmail("test" + i + "@example.com");
            contact.setPosition("测试职位" + i);
            contact.setResponsiblePersonId(adminUserId.toString());
            contact.setCreateBy(adminUserId.toString());
            contact.setUpdateBy(adminUserId.toString());
            contact.setCreateTime(new Date(System.currentTimeMillis() + i * 1000)); // 确保时间递增
            contact.setUpdateTime(new Date());
            contact.setDelFlag("0");
            
            int result = crmContactsService.insertCrmContacts(contact);
            assertTrue(result > 0, "创建测试联系人" + i + "失败");
            System.out.println("创建联系人" + i + ": " + contact.getName() + " (ID: " + contact.getId() + ")");
            
            // 稍微延迟，确保创建时间不同
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 为指定联系人创建多条活动记录
     */
    private void createActivitiesForContact(Long contactId) {
        // 创建电话跟进记录
        CrmContactFollowupRecords phoneActivity = new CrmContactFollowupRecords();
        phoneActivity.setContactId(contactId);
        phoneActivity.setFollowUpMethod("电话");
        phoneActivity.setFollowUpContent("admin进行电话跟进，了解客户需求");
        phoneActivity.setCommunicationResult("客户表示有采购意向");
        phoneActivity.setContactQuality("interested");
        phoneActivity.setCreateTime(new Date());
        phoneActivity.setUpdateTime(new Date());
        
        int result1 = contactFollowupRecordsService.insertCrmContactFollowupRecords(phoneActivity);
        assertTrue(result1 > 0, "创建电话活动记录失败");
        System.out.println("创建电话活动记录: ID=" + phoneActivity.getId());
        
        // 创建邮件跟进记录
        CrmContactFollowupRecords emailActivity = new CrmContactFollowupRecords();
        emailActivity.setContactId(contactId);
        emailActivity.setFollowUpMethod("邮件");
        emailActivity.setFollowUpContent("admin发送产品详细资料和报价单");
        emailActivity.setCommunicationResult("客户回复需要时间考虑");
        emailActivity.setContactQuality("considering");
        emailActivity.setCreateTime(new Date());
        emailActivity.setUpdateTime(new Date());
        
        int result2 = contactFollowupRecordsService.insertCrmContactFollowupRecords(emailActivity);
        assertTrue(result2 > 0, "创建邮件活动记录失败");
        System.out.println("创建邮件活动记录: ID=" + emailActivity.getId());
        
        // 创建会议跟进记录
        CrmContactFollowupRecords meetingActivity = new CrmContactFollowupRecords();
        meetingActivity.setContactId(contactId);
        meetingActivity.setFollowUpMethod("会议");
        meetingActivity.setFollowUpContent("admin与客户进行面对面会议，深入讨论合作方案");
        meetingActivity.setCommunicationResult("客户对方案很满意，准备签约");
        meetingActivity.setContactQuality("interested_deep");
        meetingActivity.setNextContactTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000L)); // 明天
        meetingActivity.setCreateTime(new Date());
        meetingActivity.setUpdateTime(new Date());
        
        int result3 = contactFollowupRecordsService.insertCrmContactFollowupRecords(meetingActivity);
        assertTrue(result3 > 0, "创建会议活动记录失败");
        System.out.println("创建会议活动记录: ID=" + meetingActivity.getId());
    }
}
