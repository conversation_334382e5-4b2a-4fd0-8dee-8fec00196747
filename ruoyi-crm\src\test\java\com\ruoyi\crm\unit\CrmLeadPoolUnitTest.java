package com.ruoyi.crm.unit;

import com.ruoyi.common.domain.entity.CrmLeadPool;
import com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import com.ruoyi.crm.BaseTestCase;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 线索池功能纯单元测试
 * 不依赖Spring上下文，主要测试实体类和业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@DisplayName("线索池功能纯单元测试")
public class CrmLeadPoolUnitTest extends BaseTestCase {

    @Test
    @DisplayName("测试线索池实体类功能")
    void testCrmLeadPoolEntity() {
        System.out.println("🧪 开始测试线索池实体类功能...");
        
        // 1. 测试默认构造函数
        CrmLeadPool pool = new CrmLeadPool();
        assertEquals("available", pool.getPoolStatus());
        assertEquals("C", pool.getQualityLevel());
        assertEquals(5, pool.getPriority());
        assertEquals("new", pool.getSourceType());
        assertEquals(0, pool.getAssignCount());
        assertNotNull(pool.getEnterPoolTime());

        // 2. 测试带参数构造函数
        CrmLeadPool pool2 = new CrmLeadPool(1001L, "imported");
        assertEquals(1001L, pool2.getLeadId());
        assertEquals("imported", pool2.getSourceType());
        assertEquals("available", pool2.getPoolStatus());

        // 3. 测试状态判断方法
        assertTrue(pool.isAvailable());
        assertFalse(pool.isAssigned());
        assertFalse(pool.isLocked());

        // 4. 测试状态设置方法
        pool.setAssigned();
        assertTrue(pool.isAssigned());
        assertFalse(pool.isAvailable());
        assertNotNull(pool.getLastAssignTime());
        assertEquals(1, pool.getAssignCount());

        pool.setAvailable();
        assertTrue(pool.isAvailable());
        assertFalse(pool.isAssigned());

        pool.setLocked();
        assertTrue(pool.isLocked());
        assertFalse(pool.isAvailable());
        assertFalse(pool.isAssigned());

        // 5. 测试字段设置
        pool.setLeadId(2001L);
        pool.setQualityLevel("A");
        pool.setPriority(9);
        pool.setRegion("北京");
        pool.setIndustry("IT");
        pool.setEstimatedValue(new BigDecimal("50000"));
        pool.setRemarks("高质量线索");

        assertEquals(2001L, pool.getLeadId());
        assertEquals("A", pool.getQualityLevel());
        assertEquals(9, pool.getPriority());
        assertEquals("北京", pool.getRegion());
        assertEquals("IT", pool.getIndustry());
        assertEquals(new BigDecimal("50000"), pool.getEstimatedValue());
        assertEquals("高质量线索", pool.getRemarks());

        System.out.println("✅ 线索池实体类测试通过");
    }

    @Test
    @DisplayName("测试分配记录实体类功能")
    void testCrmLeadAssignmentRecordEntity() {
        System.out.println("🧪 开始测试分配记录实体类功能...");
        
        // 1. 测试默认构造函数
        CrmLeadAssignmentRecord record = new CrmLeadAssignmentRecord();
        assertNotNull(record.getAssignmentTime());

        // 2. 测试带参数构造函数
        CrmLeadAssignmentRecord record2 = new CrmLeadAssignmentRecord(
            1001L, 1L, 100L, 200L, "manual", "测试分配", 1L, "admin");
        assertEquals(1001L, record2.getLeadId());
        assertEquals(1L, record2.getPoolId());
        assertEquals(100L, record2.getFromUserId());
        assertEquals(200L, record2.getToUserId());
        assertEquals("manual", record2.getAssignmentType());
        assertEquals("测试分配", record2.getAssignmentReason());
        assertEquals(1L, record2.getOperatorId());
        assertEquals("admin", record2.getOperatorName());

        // 3. 测试工厂方法 - 手动分配
        CrmLeadAssignmentRecord manualRecord = CrmLeadAssignmentRecord.createManualAssignment(
            2001L, 2L, 300L, "手动分配测试", 1L, "admin");
        assertEquals("manual", manualRecord.getAssignmentType());
        assertEquals(2001L, manualRecord.getLeadId());
        assertEquals(300L, manualRecord.getToUserId());
        assertNull(manualRecord.getFromUserId());
        assertTrue(manualRecord.isManualAssignment());
        assertFalse(manualRecord.isGrabAssignment());
        assertFalse(manualRecord.isRecycleRecord());

        // 4. 测试工厂方法 - 抢单
        CrmLeadAssignmentRecord grabRecord = CrmLeadAssignmentRecord.createGrabAssignment(
            2002L, 3L, 400L, "抢单测试", 400L, "user1");
        assertEquals("grab", grabRecord.getAssignmentType());
        assertEquals(2002L, grabRecord.getLeadId());
        assertEquals(400L, grabRecord.getToUserId());
        assertNull(grabRecord.getFromUserId());
        assertFalse(grabRecord.isManualAssignment());
        assertTrue(grabRecord.isGrabAssignment());
        assertFalse(grabRecord.isRecycleRecord());

        // 5. 测试工厂方法 - 回收
        CrmLeadAssignmentRecord recycleRecord = CrmLeadAssignmentRecord.createRecycleRecord(
            2003L, 4L, 500L, "回收测试", 1L, "admin");
        assertEquals("recycle", recycleRecord.getAssignmentType());
        assertEquals(2003L, recycleRecord.getLeadId());
        assertEquals(500L, recycleRecord.getFromUserId());
        assertNull(recycleRecord.getToUserId());
        assertFalse(recycleRecord.isManualAssignment());
        assertFalse(recycleRecord.isGrabAssignment());
        assertTrue(recycleRecord.isRecycleRecord());

        // 6. 测试扩展字段
        record.setLeadName("测试线索");
        record.setCustomerName("测试客户");
        record.setFromUserName("张三");
        record.setToUserName("李四");

        assertEquals("测试线索", record.getLeadName());
        assertEquals("测试客户", record.getCustomerName());
        assertEquals("张三", record.getFromUserName());
        assertEquals("李四", record.getToUserName());

        System.out.println("✅ 分配记录实体类测试通过");
    }

    @Test
    @DisplayName("测试业务逻辑场景")
    void testBusinessLogicScenarios() {
        System.out.println("🧪 开始测试业务逻辑场景...");
        
        // 场景1：线索入池
        CrmLeadPool pool = new CrmLeadPool(3001L, "new");
        pool.setQualityLevel("A");
        pool.setPriority(8);
        pool.setRegion("上海");
        pool.setIndustry("金融");
        pool.setEstimatedValue(new BigDecimal("80000"));

        assertTrue(pool.isAvailable());
        assertEquals("new", pool.getSourceType());
        assertEquals(0, pool.getAssignCount());

        // 场景2：手动分配
        pool.setAssigned();
        assertTrue(pool.isAssigned());
        assertEquals(1, pool.getAssignCount());
        assertNotNull(pool.getLastAssignTime());

        // 创建分配记录
        CrmLeadAssignmentRecord assignRecord = CrmLeadAssignmentRecord.createManualAssignment(
            pool.getLeadId(), 1L, 600L, "管理员手动分配", 1L, "admin");
        assertTrue(assignRecord.isManualAssignment());

        // 场景3：回收线索
        pool.setAvailable();
        pool.setSourceType("recycled");
        assertTrue(pool.isAvailable());
        assertEquals("recycled", pool.getSourceType());

        // 创建回收记录
        CrmLeadAssignmentRecord recycleRecord = CrmLeadAssignmentRecord.createRecycleRecord(
            pool.getLeadId(), 1L, 600L, "线索回收", 1L, "admin");
        assertTrue(recycleRecord.isRecycleRecord());

        // 场景4：抢单
        pool.setAssigned();
        CrmLeadAssignmentRecord grabRecord = CrmLeadAssignmentRecord.createGrabAssignment(
            pool.getLeadId(), 1L, 700L, "销售抢单", 700L, "sales1");
        assertTrue(grabRecord.isGrabAssignment());
        assertEquals(2, pool.getAssignCount()); // 之前分配过一次，现在又分配一次

        System.out.println("✅ 业务逻辑场景测试通过");
    }

    @Test
    @DisplayName("测试数据验证和边界条件")
    void testDataValidationAndBoundaryConditions() {
        System.out.println("🧪 开始测试数据验证和边界条件...");
        
        // 1. 测试优先级边界值
        CrmLeadPool pool = new CrmLeadPool();
        pool.setPriority(1);
        assertEquals(1, pool.getPriority());
        
        pool.setPriority(10);
        assertEquals(10, pool.getPriority());

        // 2. 测试质量等级
        String[] validLevels = {"A", "B", "C", "D"};
        for (String level : validLevels) {
            pool.setQualityLevel(level);
            assertEquals(level, pool.getQualityLevel());
        }

        // 3. 测试状态转换
        pool.setPoolStatus("available");
        assertTrue(pool.isAvailable());
        
        pool.setPoolStatus("assigned");
        assertTrue(pool.isAssigned());
        
        pool.setPoolStatus("locked");
        assertTrue(pool.isLocked());

        // 4. 测试分配次数累加
        pool.setAssignCount(0);
        assertEquals(0, pool.getAssignCount());
        
        pool.setAssigned(); // 会自动增加分配次数
        assertEquals(1, pool.getAssignCount());
        
        pool.setAssigned(); // 再次分配
        assertEquals(2, pool.getAssignCount());

        // 5. 测试时间字段
        Date now = new Date();
        pool.setEnterPoolTime(now);
        assertEquals(now, pool.getEnterPoolTime());

        // 6. 测试金额字段
        BigDecimal amount = new BigDecimal("99999.99");
        pool.setEstimatedValue(amount);
        assertEquals(amount, pool.getEstimatedValue());

        // 7. 测试空值处理
        pool.setLeadId(null);
        assertNull(pool.getLeadId());
        
        pool.setRegion(null);
        assertNull(pool.getRegion());
        
        pool.setIndustry(null);
        assertNull(pool.getIndustry());

        System.out.println("✅ 数据验证和边界条件测试通过");
    }

    @Test
    @DisplayName("测试完整业务流程")
    void testCompleteBusinessWorkflow() {
        System.out.println("🧪 开始测试完整业务流程...");
        
        // 1. 创建线索池
        CrmLeadPool pool = new CrmLeadPool(4001L, "new");
        pool.setQualityLevel("A");
        pool.setPriority(9);
        pool.setRegion("深圳");
        pool.setIndustry("科技");
        pool.setEstimatedValue(new BigDecimal("120000"));
        pool.setRemarks("重要客户线索");

        // 验证初始状态
        assertTrue(pool.isAvailable());
        assertEquals(0, pool.getAssignCount());
        assertNull(pool.getLastAssignTime());

        // 2. 第一次分配
        pool.setAssigned();
        CrmLeadAssignmentRecord firstAssign = CrmLeadAssignmentRecord.createManualAssignment(
            pool.getLeadId(), 1L, 801L, "首次分配给销售A", 1L, "manager1");

        assertTrue(pool.isAssigned());
        assertEquals(1, pool.getAssignCount());
        assertNotNull(pool.getLastAssignTime());
        assertTrue(firstAssign.isManualAssignment());

        // 3. 回收线索
        pool.setAvailable();
        pool.setSourceType("recycled");
        CrmLeadAssignmentRecord recycleRecord = CrmLeadAssignmentRecord.createRecycleRecord(
            pool.getLeadId(), 1L, 801L, "客户暂时无需求，回收线索", 1L, "manager1");

        assertTrue(pool.isAvailable());
        assertEquals("recycled", pool.getSourceType());
        assertTrue(recycleRecord.isRecycleRecord());

        // 4. 抢单分配
        pool.setAssigned();
        CrmLeadAssignmentRecord grabRecord = CrmLeadAssignmentRecord.createGrabAssignment(
            pool.getLeadId(), 1L, 802L, "销售B主动抢单", 802L, "sales_b");

        assertTrue(pool.isAssigned());
        assertEquals(2, pool.getAssignCount()); // 分配次数增加
        assertTrue(grabRecord.isGrabAssignment());

        // 5. 验证整个流程的数据一致性
        assertEquals(4001L, pool.getLeadId());
        assertEquals("A", pool.getQualityLevel());
        assertEquals(9, pool.getPriority());
        assertEquals("深圳", pool.getRegion());
        assertEquals("科技", pool.getIndustry());
        assertEquals(new BigDecimal("120000"), pool.getEstimatedValue());

        // 验证所有分配记录
        assertEquals(4001L, firstAssign.getLeadId());
        assertEquals(4001L, recycleRecord.getLeadId());
        assertEquals(4001L, grabRecord.getLeadId());

        assertEquals("manual", firstAssign.getAssignmentType());
        assertEquals("recycle", recycleRecord.getAssignmentType());
        assertEquals("grab", grabRecord.getAssignmentType());

        System.out.println("✅ 完整业务流程测试通过");
        System.out.println("📊 流程统计：");
        System.out.println("  - 线索ID: " + pool.getLeadId());
        System.out.println("  - 质量等级: " + pool.getQualityLevel());
        System.out.println("  - 当前状态: " + pool.getPoolStatus());
        System.out.println("  - 分配次数: " + pool.getAssignCount());
        System.out.println("  - 预估价值: " + pool.getEstimatedValue());
    }

    @Test
    @DisplayName("测试并发场景模拟")
    void testConcurrentScenarioSimulation() {
        System.out.println("🧪 开始测试并发场景模拟...");
        
        // 模拟多个销售同时抢单的场景
        CrmLeadPool pool = new CrmLeadPool(5001L, "new");
        pool.setQualityLevel("A");
        pool.setPriority(10);

        // 初始状态：可用
        assertTrue(pool.isAvailable());

        // 销售A尝试抢单
        if (pool.isAvailable()) {
            pool.setAssigned();
            CrmLeadAssignmentRecord grabA = CrmLeadAssignmentRecord.createGrabAssignment(
                pool.getLeadId(), 1L, 901L, "销售A抢单", 901L, "sales_a");
            assertTrue(grabA.isGrabAssignment());
        }

        // 销售B尝试抢单（应该失败，因为已经被分配）
        boolean canGrabB = pool.isAvailable();
        assertFalse(canGrabB); // 不能再抢单

        // 验证最终状态
        assertTrue(pool.isAssigned());
        assertEquals(1, pool.getAssignCount());

        System.out.println("✅ 并发场景模拟测试通过");
    }

    @Test
    @DisplayName("测试线索池状态转换")
    void testLeadPoolStatusTransitions() {
        System.out.println("🧪 开始测试线索池状态转换...");
        
        CrmLeadPool pool = new CrmLeadPool();
        
        // 初始状态应该是可用
        assertTrue(pool.isAvailable());
        assertFalse(pool.isAssigned());
        assertFalse(pool.isLocked());
        
        // 测试状态转换：可用 -> 已分配
        pool.setAssigned();
        assertFalse(pool.isAvailable());
        assertTrue(pool.isAssigned());
        assertFalse(pool.isLocked());
        assertEquals(1, pool.getAssignCount());
        assertNotNull(pool.getLastAssignTime());
        
        // 测试状态转换：已分配 -> 锁定
        pool.setLocked();
        assertFalse(pool.isAvailable());
        assertFalse(pool.isAssigned());
        assertTrue(pool.isLocked());
        
        // 测试状态转换：锁定 -> 可用
        pool.setAvailable();
        assertTrue(pool.isAvailable());
        assertFalse(pool.isAssigned());
        assertFalse(pool.isLocked());
        
        // 测试多次分配
        pool.setAssigned();
        assertEquals(2, pool.getAssignCount());
        
        pool.setAssigned();
        assertEquals(3, pool.getAssignCount());
        
        System.out.println("✅ 线索池状态转换测试通过");
    }

    @Test
    @DisplayName("测试分配记录类型判断")
    void testAssignmentRecordTypeChecking() {
        System.out.println("🧪 开始测试分配记录类型判断...");
        
        // 测试手动分配记录
        CrmLeadAssignmentRecord manualRecord = CrmLeadAssignmentRecord.createManualAssignment(
            1001L, 1L, 100L, "手动分配", 1L, "admin");
        assertTrue(manualRecord.isManualAssignment());
        assertFalse(manualRecord.isGrabAssignment());
        assertFalse(manualRecord.isRecycleRecord());
        
        // 测试抢单记录
        CrmLeadAssignmentRecord grabRecord = CrmLeadAssignmentRecord.createGrabAssignment(
            1002L, 2L, 200L, "抢单", 200L, "sales");
        assertFalse(grabRecord.isManualAssignment());
        assertTrue(grabRecord.isGrabAssignment());
        assertFalse(grabRecord.isRecycleRecord());
        
        // 测试回收记录
        CrmLeadAssignmentRecord recycleRecord = CrmLeadAssignmentRecord.createRecycleRecord(
            1003L, 3L, 300L, "回收", 1L, "admin");
        assertFalse(recycleRecord.isManualAssignment());
        assertFalse(recycleRecord.isGrabAssignment());
        assertTrue(recycleRecord.isRecycleRecord());
        
        // 测试自定义类型
        CrmLeadAssignmentRecord customRecord = new CrmLeadAssignmentRecord();
        customRecord.setAssignmentType("custom");
        assertFalse(customRecord.isManualAssignment());
        assertFalse(customRecord.isGrabAssignment());
        assertFalse(customRecord.isRecycleRecord());
        
        System.out.println("✅ 分配记录类型判断测试通过");
    }
}
