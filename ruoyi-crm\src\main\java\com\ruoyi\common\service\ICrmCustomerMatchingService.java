package com.ruoyi.common.service;

import java.util.List;

import com.ruoyi.common.domain.entity.CrmCustomer;

/**
 * CRM客户匹配服务接口
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
public interface ICrmCustomerMatchingService {
    
    /**
     * 根据电话号码匹配客户
     * 
     * @param phone 电话号码
     * @return 匹配结果
     */
    public CustomerMatchResult matchCustomerByPhone(String phone);
    
    /**
     * 根据客户名称匹配客户
     * 
     * @param customerName 客户名称
     * @return 匹配结果列表
     */
    public List<CustomerMatchResult> matchCustomerByName(String customerName);
    
    /**
     * 根据邮箱匹配客户
     * 
     * @param email 邮箱
     * @return 匹配结果
     */
    public CustomerMatchResult matchCustomerByEmail(String email);
    
    /**
     * 综合匹配客户（电话、邮箱、名称）
     * 
     * @param phone 电话号码
     * @param email 邮箱
     * @param customerName 客户名称
     * @return 匹配结果
     */
    public CustomerMatchResult matchCustomerComprehensive(String phone, String email, String customerName);
    
    /**
     * 智能匹配客户（使用多种算法）
     * 
     * @param customerInfo 客户信息
     * @return 匹配结果列表
     */
    public List<CustomerMatchResult> smartMatchCustomer(CustomerMatchInfo customerInfo);
    
    /**
     * 检查是否为新客户
     * 
     * @param phone 电话号码
     * @param email 邮箱
     * @param customerName 客户名称
     * @return 是否为新客户
     */
    public boolean isNewCustomer(String phone, String email, String customerName);
    
    /**
     * 获取客户的负责人
     * 
     * @param customerId 客户ID
     * @return 负责人信息
     */
    public CustomerOwnerInfo getCustomerOwner(Long customerId);
    
    /**
     * 检查负责人状态
     * 
     * @param ownerId 负责人ID
     * @return 负责人状态信息
     */
    public OwnerStatusInfo checkOwnerStatus(Long ownerId);
    
    /**
     * 标准化电话号码
     * 
     * @param phone 原始电话号码
     * @return 标准化后的电话号码
     */
    public String normalizePhone(String phone);
    
    /**
     * 标准化客户名称
     * 
     * @param customerName 原始客户名称
     * @return 标准化后的客户名称
     */
    public String normalizeCustomerName(String customerName);
    
    /**
     * 计算客户匹配相似度
     * 
     * @param customer1 客户1
     * @param customer2 客户2
     * @return 相似度分数（0-100）
     */
    public double calculateSimilarity(CrmCustomer customer1, CrmCustomer customer2);
    
    /**
     * 客户匹配结果
     */
    public static class CustomerMatchResult {
        /** 是否匹配成功 */
        private boolean matched;
        
        /** 匹配的客户 */
        private CrmCustomer customer;
        
        /** 匹配类型 */
        private String matchType;
        
        /** 匹配置信度 */
        private double confidence;
        
        /** 匹配原因 */
        private String reason;
        
        /** 建议操作 */
        private String suggestedAction;
        
        // 构造方法
        public CustomerMatchResult() {}
        
        public CustomerMatchResult(boolean matched, CrmCustomer customer, String matchType) {
            this.matched = matched;
            this.customer = customer;
            this.matchType = matchType;
        }
        
        public static CustomerMatchResult success(CrmCustomer customer, String matchType, double confidence) {
            CustomerMatchResult result = new CustomerMatchResult(true, customer, matchType);
            result.setConfidence(confidence);
            return result;
        }
        
        public static CustomerMatchResult failure(String reason) {
            CustomerMatchResult result = new CustomerMatchResult(false, null, "NO_MATCH");
            result.setReason(reason);
            return result;
        }
        
        // Getters and Setters
        public boolean isMatched() { return matched; }
        public void setMatched(boolean matched) { this.matched = matched; }
        
        public CrmCustomer getCustomer() { return customer; }
        public void setCustomer(CrmCustomer customer) { this.customer = customer; }
        
        public String getMatchType() { return matchType; }
        public void setMatchType(String matchType) { this.matchType = matchType; }
        
        public double getConfidence() { return confidence; }
        public void setConfidence(double confidence) { this.confidence = confidence; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public String getSuggestedAction() { return suggestedAction; }
        public void setSuggestedAction(String suggestedAction) { this.suggestedAction = suggestedAction; }
    }
    
    /**
     * 客户匹配信息
     */
    public static class CustomerMatchInfo {
        private String phone;
        private String email;
        private String customerName;
        private String companyName;
        private String address;
        private String industry;
        
        // Getters and Setters
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getCustomerName() { return customerName; }
        public void setCustomerName(String customerName) { this.customerName = customerName; }
        
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getIndustry() { return industry; }
        public void setIndustry(String industry) { this.industry = industry; }
    }
    
    /**
     * 客户负责人信息
     */
    public static class CustomerOwnerInfo {
        private Long ownerId;
        private String ownerName;
        private String ownerPhone;
        private String ownerEmail;
        private String deptName;
        private boolean isActive;
        
        // Getters and Setters
        public Long getOwnerId() { return ownerId; }
        public void setOwnerId(Long ownerId) { this.ownerId = ownerId; }
        
        public String getOwnerName() { return ownerName; }
        public void setOwnerName(String ownerName) { this.ownerName = ownerName; }
        
        public String getOwnerPhone() { return ownerPhone; }
        public void setOwnerPhone(String ownerPhone) { this.ownerPhone = ownerPhone; }
        
        public String getOwnerEmail() { return ownerEmail; }
        public void setOwnerEmail(String ownerEmail) { this.ownerEmail = ownerEmail; }
        
        public String getDeptName() { return deptName; }
        public void setDeptName(String deptName) { this.deptName = deptName; }
        
        public boolean isActive() { return isActive; }
        public void setActive(boolean active) { isActive = active; }
    }
    
    /**
     * 负责人状态信息
     */
    public static class OwnerStatusInfo {
        private Long ownerId;
        private String ownerName;
        private boolean isOnline;
        private boolean isActive;
        private int currentWorkload;
        private int maxWorkload;
        private String lastActiveTime;
        private String status;
        
        // Getters and Setters
        public Long getOwnerId() { return ownerId; }
        public void setOwnerId(Long ownerId) { this.ownerId = ownerId; }
        
        public String getOwnerName() { return ownerName; }
        public void setOwnerName(String ownerName) { this.ownerName = ownerName; }
        
        public boolean isOnline() { return isOnline; }
        public void setOnline(boolean online) { isOnline = online; }
        
        public boolean isActive() { return isActive; }
        public void setActive(boolean active) { isActive = active; }
        
        public int getCurrentWorkload() { return currentWorkload; }
        public void setCurrentWorkload(int currentWorkload) { this.currentWorkload = currentWorkload; }
        
        public int getMaxWorkload() { return maxWorkload; }
        public void setMaxWorkload(int maxWorkload) { this.maxWorkload = maxWorkload; }
        
        public String getLastActiveTime() { return lastActiveTime; }
        public void setLastActiveTime(String lastActiveTime) { this.lastActiveTime = lastActiveTime; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
    
    // 匹配类型常量
    public static final String MATCH_TYPE_EXACT_PHONE = "EXACT_PHONE";
    public static final String MATCH_TYPE_FUZZY_PHONE = "FUZZY_PHONE";
    public static final String MATCH_TYPE_EXACT_EMAIL = "EXACT_EMAIL";
    public static final String MATCH_TYPE_EXACT_NAME = "EXACT_NAME";
    public static final String MATCH_TYPE_FUZZY_NAME = "FUZZY_NAME";
    public static final String MATCH_TYPE_COMPREHENSIVE = "COMPREHENSIVE";
    public static final String MATCH_TYPE_NO_MATCH = "NO_MATCH";
}
