<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多负责人客户场景分析与解决方案</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- Prism.js for code highlighting -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .problem-box {
            background-color: #fee;
            border: 2px solid #e74c3c;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .solution-box {
            background-color: #e8f5e8;
            border: 2px solid #27ae60;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .warning-box {
            background-color: #fff3cd;
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .mermaid-diagram {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        .code-section {
            margin: 20px 0;
        }
        .code-title {
            background-color: #495057;
            color: white;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin: 0;
            font-weight: bold;
            font-size: 14px;
        }
        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0 0 5px 5px !important;
            font-size: 13px !important;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .pros {
            color: #27ae60;
            font-weight: bold;
        }
        .cons {
            color: #e74c3c;
            font-weight: bold;
        }
        .scenario-example {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 多负责人客户场景分析与解决方案</h1>
        
        <div class="problem-box">
            <h3>🚨 发现的问题</h3>
            <p><strong>当前CRM系统设计无法支持一个客户对应多个负责人的业务场景！</strong></p>
            <ul>
                <li><code>CrmCustomer.responsiblePersonId</code> - 只能存储单个负责人ID</li>
                <li><code>CrmContacts.responsiblePersonId</code> - 联系人也只能有单个负责人</li>
                <li>客户公海逻辑基于单一负责人概念设计</li>
                <li>认领和归还操作会覆盖现有负责人</li>
            </ul>
        </div>

        <h2>💼 典型业务场景</h2>
        
        <div class="scenario-example">
            <h4>场景案例：A公司多部门业务</h4>
            <ul>
                <li><strong>客户：</strong>A公司（玩具制造商）</li>
                <li><strong>部门1：</strong>研发部 - 需要玩具原型服务</li>
                <li><strong>部门2：</strong>市场部 - 需要包装设计服务</li>
                <li><strong>业务员1：</strong>张三（原型业务专家）→ 联系研发部采购经理</li>
                <li><strong>业务员2：</strong>李四（包装业务专家）→ 联系市场部采购经理</li>
            </ul>
            <p><strong>需求：</strong>两个业务员都需要负责A公司，但分别负责不同的业务线。</p>
        </div>

        <h2>🏗️ 当前架构分析</h2>

        <div class="mermaid-diagram">
            <div class="mermaid">
erDiagram
    CrmCustomer {
        Long id PK
        String responsiblePersonId "单一负责人"
        String customerName
        String customerIndustry
    }
    
    CrmContacts {
        Long id PK
        String responsiblePersonId "单一负责人"
        String name
        String department
        String position
    }
    
    CrmCustomerContactRelation {
        Long id PK
        Long customerId FK
        Long contactId FK
        String relationType
        Integer isPrimary
    }
    
    CrmCustomer ||--o{ CrmCustomerContactRelation : "has"
    CrmContacts ||--o{ CrmCustomerContactRelation : "belongs to"
            </div>
        </div>

        <div class="warning-box">
            <h4>⚠️ 架构缺陷</h4>
            <p>当前设计中，<strong>负责人关系是硬编码在实体层面的</strong>，无法灵活支持：</p>
            <ul>
                <li>一个客户有多个负责人</li>
                <li>按业务线分配负责人</li>
                <li>按联系人维度分配负责人</li>
                <li>历史负责人记录追踪</li>
            </ul>
        </div>

        <h2>💡 解决方案对比</h2>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>解决方案</th>
                    <th>设计思路</th>
                    <th>优点</th>
                    <th>缺点</th>
                    <th>实施复杂度</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>方案1：业务线维度</strong></td>
                    <td>在客户表增加业务线字段，同一客户可有多条记录</td>
                    <td class="pros">简单直接<br>兼容现有逻辑</td>
                    <td class="cons">数据冗余<br>查询复杂</td>
                    <td>⭐⭐</td>
                </tr>
                <tr>
                    <td><strong>方案2：客户负责人关系表</strong></td>
                    <td>抽取客户-负责人关系为独立表，支持多对多</td>
                    <td class="pros">灵活性高<br>数据规范<br>支持历史记录</td>
                    <td class="cons">架构改动大<br>查询JOIN多</td>
                    <td>⭐⭐⭐⭐</td>
                </tr>
                <tr>
                    <td><strong>方案3：联系人负责人制</strong></td>
                    <td>客户不设负责人，只在联系人级别设置负责人</td>
                    <td class="pros">精确匹配业务<br>联系人粒度管理</td>
                    <td class="cons">客户层面统计困难<br>公海逻辑复杂</td>
                    <td>⭐⭐⭐</td>
                </tr>
                <tr>
                    <td><strong>方案4：混合模式</strong></td>
                    <td>主负责人+协作负责人模式</td>
                    <td class="pros">兼容性好<br>渐进式改造</td>
                    <td class="cons">逻辑复杂<br>权限混乱风险</td>
                    <td>⭐⭐⭐</td>
                </tr>
            </tbody>
        </table>

        <h2>🎯 推荐解决方案：客户负责人关系表</h2>

        <div class="solution-box">
            <h4>✅ 为什么选择方案2？</h4>
            <ul>
                <li><strong>灵活性：</strong>完全支持多负责人场景</li>
                <li><strong>扩展性：</strong>可以支持未来更复杂的业务需求</li>
                <li><strong>数据完整性：</strong>避免数据冗余，保持数据一致性</li>
                <li><strong>审计友好：</strong>可以记录负责人变更历史</li>
                <li><strong>业务语义清晰：</strong>明确表达客户-负责人的多对多关系</li>
            </ul>
        </div>

        <h3>新架构设计</h3>

        <div class="mermaid-diagram">
            <div class="mermaid">
erDiagram
    CrmCustomer {
        Long id PK
        String customerName
        String customerIndustry
        String primaryContactId "废弃字段"
    }
    
    CrmCustomerResponsible {
        Long id PK
        Long customerId FK
        Long responsiblePersonId FK
        String businessLine "业务线"
        String responsibleType "负责类型"
        Date startDate "开始时间"
        Date endDate "结束时间"
        String status "状态"
        Integer priority "优先级"
    }
    
    CrmContacts {
        Long id PK
        String name
        String department
        String position
    }
    
    CrmCustomerContactRelation {
        Long id PK
        Long customerId FK
        Long contactId FK
        Long responsiblePersonId FK "联系人对应的负责人"
        String relationType
    }
    
    CrmCustomer ||--o{ CrmCustomerResponsible : "has multiple"
    CrmCustomer ||--o{ CrmCustomerContactRelation : "has"
    CrmContacts ||--o{ CrmCustomerContactRelation : "belongs to"
            </div>
        </div>

        <h3>核心表结构设计</h3>

        <div class="code-section">
            <div class="code-title">客户负责人关系表 (crm_customer_responsible)</div>
            <pre><code class="language-sql">CREATE TABLE crm_customer_responsible (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    responsible_person_id BIGINT NOT NULL COMMENT '负责人ID',
    business_line VARCHAR(50) COMMENT '业务线(原型/包装/采购等)',
    responsible_type VARCHAR(20) DEFAULT 'PRIMARY' COMMENT '负责类型:PRIMARY主要/SECONDARY次要/COOPERATE协作',
    start_date DATETIME NOT NULL COMMENT '负责开始时间',
    end_date DATETIME COMMENT '负责结束时间(NULL表示当前负责)',
    status VARCHAR(10) DEFAULT 'ACTIVE' COMMENT '状态:ACTIVE有效/INACTIVE无效',
    priority INT DEFAULT 1 COMMENT '优先级(数字越小优先级越高)',
    remark VARCHAR(500) COMMENT '备注说明',
    create_time DATETIME NOT NULL,
    create_by VARCHAR(64),
    update_time DATETIME,
    update_by VARCHAR(64),
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_responsible_person_id (responsible_person_id),
    INDEX idx_business_line (business_line),
    INDEX idx_status_date (status, start_date, end_date),
    
    UNIQUE KEY uk_customer_responsible_business (customer_id, responsible_person_id, business_line, status)
);</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">更新客户联系人关系表</div>
            <pre><code class="language-sql">-- 在现有表中增加负责人字段
ALTER TABLE crm_customer_contact_relation 
ADD COLUMN responsible_person_id BIGINT COMMENT '该联系人对应的负责人ID',
ADD INDEX idx_responsible_person (responsible_person_id);</code></pre>
        </div>

        <h2>🔄 业务逻辑调整</h2>

        <h3>1. 客户认领逻辑重构</h3>

        <div class="code-section">
            <div class="code-title">新的认领逻辑</div>
            <pre><code class="language-java">@Override
@Transactional
public int claimCustomers(List&lt;Long&gt; customerIds, String businessLine) {
    // 1. 参数验证
    if (customerIds == null || customerIds.isEmpty()) {
        throw new ServiceException("请选择要认领的客户");
    }
    
    Long userId = SecurityUtils.getUserId();
    
    // 2. 检查是否已有该业务线的负责人
    for (Long customerId : customerIds) {
        boolean hasActiveResponsible = customerResponsibleMapper
            .hasActiveResponsible(customerId, businessLine);
        if (hasActiveResponsible) {
            throw new ServiceException("客户在该业务线已有负责人");
        }
    }
    
    // 3. 检查认领限制
    if (!checkClaimLimit(userId, customerIds.size())) {
        throw new ServiceException("超出认领限制");
    }
    
    // 4. 创建负责人关系
    Date now = new Date();
    List&lt;CrmCustomerResponsible&gt; responsibleList = new ArrayList&lt;&gt;();
    
    for (Long customerId : customerIds) {
        CrmCustomerResponsible responsible = new CrmCustomerResponsible();
        responsible.setCustomerId(customerId);
        responsible.setResponsiblePersonId(userId);
        responsible.setBusinessLine(businessLine);
        responsible.setResponsibleType("PRIMARY");
        responsible.setStartDate(now);
        responsible.setStatus("ACTIVE");
        responsible.setPriority(1);
        responsibleList.add(responsible);
    }
    
    // 5. 批量插入负责人关系
    int result = customerResponsibleMapper.batchInsert(responsibleList);
    
    // 6. 更新公海状态
    customerPoolMapper.batchUpdateStatus(customerIds, "CLAIMED");
    
    // 7. 记录操作日志
    recordClaimLog(customerIds, userId, businessLine);
    
    return result;
}</code></pre>
        </div>

        <h3>2. 公海查询逻辑调整</h3>

        <div class="code-section">
            <div class="code-title">支持业务线的公海查询</div>
            <pre><code class="language-java">@Override
public List&lt;CrmCustomerPool&gt; selectPoolCustomerList(CrmCustomerPool query) {
    // 查询在指定业务线无负责人的客户
    return customerPoolMapper.selectPoolCustomersWithBusinessLine(query);
}</code></pre>
        </div>

        <h3>3. 我的客户查询逻辑</h3>

        <div class="code-section">
            <div class="code-title">按业务线分组显示我的客户</div>
            <pre><code class="language-java">@Override
public List&lt;CrmCustomerPool&gt; selectMyCustomerList(Long userId) {
    // 查询用户负责的所有客户，按业务线分组
    return customerResponsibleMapper.selectMyCustomersWithBusinessLine(userId);
}</code></pre>
        </div>

        <h2>⚡ 实施计划调整</h2>

        <div class="warning-box">
            <h4>🔄 原计划需要重大调整</h4>
            <p>由于发现多负责人场景问题，原有的5天实施计划需要调整为<strong>7-10天</strong>：</p>
            
            <h5>新的实施阶段：</h5>
            <ol>
                <li><strong>第1-2天：</strong>架构重构 - 新增客户负责人关系表</li>
                <li><strong>第3-4天：</strong>业务逻辑重写 - 适配多负责人场景</li>
                <li><strong>第5-6天：</strong>数据迁移 - 将现有数据迁移到新架构</li>
                <li><strong>第7-8天：</strong>测试修复 - 修复所有集成测试</li>
                <li><strong>第9-10天：</strong>功能完善 - 补充遗漏功能和优化</li>
            </ol>
        </div>

        <h2>🎯 下一步行动</h2>

        <div class="solution-box">
            <h4>需要您确认的决策点：</h4>
            <ol>
                <li><strong>是否接受架构重构？</strong>这会增加实施复杂度但能彻底解决问题</li>
                <li><strong>业务线如何划分？</strong>需要定义标准的业务线分类</li>
                <li><strong>数据迁移策略？</strong>现有客户数据如何迁移到新架构</li>
                <li><strong>向下兼容性？</strong>是否需要保持对现有API的兼容</li>
                <li><strong>实施优先级？</strong>是否可以分阶段实施，先支持基础多负责人功能</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #fff3cd; border-radius: 5px;">
            <h3>⏳ 等待您的决策</h3>
            <p><strong>选项1：</strong>接受重构，彻底解决多负责人问题 (推荐)</p>
            <p><strong>选项2：</strong>临时方案，在现有架构上打补丁</p>
            <p><strong>选项3：</strong>分阶段实施，先解决测试问题再重构</p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#333333',
                fill: '#ececff',
                fontSize: 12
            }
        });

        // Auto highlight code blocks
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>