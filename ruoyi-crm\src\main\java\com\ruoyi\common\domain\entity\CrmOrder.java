package com.ruoyi.common.domain.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CRM订单实体类
 * 支持3D打印订单和普通订单
 *
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long id;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 订单标题 */
    @Excel(name = "订单标题")
    private String orderTitle;

    /** 询价单号 */
    @Excel(name = "询价单号")
    private String quoteNo;

    /** 客户ID */
    private Long customerId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 联系人ID */
    private Long contactId;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 收货地址 */
    @Excel(name = "收货地址")
    private String deliveryAddress;

    /** 关联商机ID */
    private Long opportunityId;

    /** 关联合同ID */
    private Long contractId;

    /** 订单来源 */
    @Excel(name = "订单来源", readConverterExp = "MANUAL=手动创建,OPPORTUNITY=商机转化,3D_PRINTING=3D打印")
    private String orderSource;

    /** 订单类型 */
    @Excel(name = "订单类型", readConverterExp = "STANDARD=标准,3D_PRINTING=3D打印,CUSTOM=定制")
    private String orderType;

    /** 优先级 */
    @Excel(name = "优先级", readConverterExp = "HIGH=高,NORMAL=普通,LOW=低")
    private String priorityLevel;

    /** 紧急标志 */
    @Excel(name = "紧急标志", readConverterExp = "0=普通,1=紧急")
    private Integer urgentFlag;

    /** 客户类型 */
    @Excel(name = "客户类型", readConverterExp = "NEW=新客户,EXISTING=老客户")
    private String customerType;

    /** 订单总金额 */
    @Excel(name = "订单总金额")
    private BigDecimal totalAmount;

    /** 币种 */
    @Excel(name = "币种")
    private String currency;

    /** 已付金额 */
    @Excel(name = "已付金额")
    private BigDecimal paidAmount;

    /** 订单状态 */
    @Excel(name = "订单状态", readConverterExp = "PENDING=待处理,CONFIRMED=已确认,IN_PROGRESS=进行中,COMPLETED=已完成,CANCELLED=已取消")
    private String status;

    /** 分配状态 */
    @Excel(name = "分配状态", readConverterExp = "UNASSIGNED=未分配,ASSIGNED=已分配")
    private String assignmentStatus;

    /** 负责人ID */
    private Long ownerId;

    /** 负责人姓名 */
    @Excel(name = "负责人")
    private String ownerName;

    /** 分配人ID */
    private Long assignedBy;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分配时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignedTime;

    /** 下单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下单日期", dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    /** 预期交付日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预期交付日期", dateFormat = "yyyy-MM-dd")
    private Date expectedDeliveryDate;

    /** 实际交付日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际交付日期", dateFormat = "yyyy-MM-dd")
    private Date actualDeliveryDate;

    /** 预计发货日期（兼容旧字段） */
    @Excel(name = "预计发货日期", dateFormat = "yyyy-MM-dd")
    private Timestamp estimatedDeliveryDate;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 删除标志 */
    private String delFlag;

    /** 创建人 */
    private Long createdBy;

    /** 更新人 */
    private Long updatedBy;

    /** 订单项列表 */
    private List<CrmOrderItem> orderItems;

    /** 文件URL列表 */
    private List<String> fileUrls;

    // ========== 关联对象 ==========

    /** 关联客户对象 */
    private CrmCustomer customer;

    /** 关联联系人对象 */
    private CrmContacts contact;

    /** 关联商机对象 */
    private CrmOpportunity opportunity;

    /** 关联合同对象 */
    private CrmContract contract;

    // ========== 统计字段 ==========

    /** 订单项数量 */
    private Integer itemCount;

    /** 订单总数量 */
    private Integer totalQuantity;

    /** 订单进度百分比 */
    private Integer progressPercent;

    // ========== 查询条件字段 ==========

    /** 开始日期（查询用） */
    private String startDate;

    /** 结束日期（查询用） */
    private String endDate;

    /** 金额范围最小值（查询用） */
    private BigDecimal minAmount;

    /** 金额范围最大值（查询用） */
    private BigDecimal maxAmount;

    /** 状态列表（查询用） */
    private List<String> statusList;

    /** 负责人ID列表（查询用） */
    private List<Long> ownerIds;
}