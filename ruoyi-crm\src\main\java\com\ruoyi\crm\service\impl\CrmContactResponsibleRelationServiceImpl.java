package com.ruoyi.crm.service.impl;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.mapper.CrmContactResponsibleRelationMapper;
import com.ruoyi.common.mapper.CrmTeamMapper;
import com.ruoyi.common.mapper.CrmTeamMemberMapper;
import com.ruoyi.common.mapper.CrmTeamRelationMapper;
import com.ruoyi.common.mapper.CrmContactsMapper;
import com.ruoyi.common.domain.entity.CrmContactResponsibleRelation;
import com.ruoyi.common.domain.entity.CrmTeam;
import com.ruoyi.common.domain.entity.CrmTeamMember;
import com.ruoyi.common.domain.entity.CrmTeamRelation;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.crm.service.ICrmContactResponsibleRelationService;
import com.ruoyi.crm.domain.vo.TeamAssignRequest;
import com.ruoyi.crm.domain.vo.PoolQueryRequest;
import com.ruoyi.crm.domain.vo.TeamPerformanceVO;

/**
 * 联系人-业务员关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class CrmContactResponsibleRelationServiceImpl implements ICrmContactResponsibleRelationService {
    
    @Autowired
    private CrmContactResponsibleRelationMapper contactResponsibleMapper;
    
    @Autowired
    private CrmTeamMapper teamMapper;
    
    @Autowired
    private CrmTeamMemberMapper teamMemberMapper;
    
    @Autowired
    private CrmTeamRelationMapper teamRelationMapper;
    
    @Autowired
    private CrmContactsMapper contactsMapper;
    
    /**
     * 查询联系人-业务员关系
     * 
     * @param id 联系人-业务员关系主键
     * @return 联系人-业务员关系
     */
    @Override
    public CrmContactResponsibleRelation selectCrmContactResponsibleRelationById(Long id) {
        return contactResponsibleMapper.selectCrmContactResponsibleRelationById(id);
    }
    
    /**
     * 查询联系人-业务员关系列表
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 联系人-业务员关系
     */
    @Override
    public List<CrmContactResponsibleRelation> selectCrmContactResponsibleRelationList(CrmContactResponsibleRelation crmContactResponsibleRelation) {
        return contactResponsibleMapper.selectCrmContactResponsibleRelationList(crmContactResponsibleRelation);
    }
    
    /**
     * 新增联系人-业务员关系
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 结果
     */
    @Override
    public int insertCrmContactResponsibleRelation(CrmContactResponsibleRelation crmContactResponsibleRelation) {
        // 检查是否已存在相同的关系
        int exists = contactResponsibleMapper.checkActiveRelationExists(
            crmContactResponsibleRelation.getContactId(),
            crmContactResponsibleRelation.getResponsiblePersonId(),
            crmContactResponsibleRelation.getBusinessType()
        );
        
        if (exists > 0) {
            throw new ServiceException("该联系人在此业务类型下已有负责人");
        }
        
        // 设置默认值
        if (StringUtils.isEmpty(crmContactResponsibleRelation.getBusinessType())) {
            crmContactResponsibleRelation.setBusinessType(CrmContactResponsibleRelation.BusinessType.GENERAL);
        }
        if (StringUtils.isEmpty(crmContactResponsibleRelation.getRelationStatus())) {
            crmContactResponsibleRelation.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
        }
        if (crmContactResponsibleRelation.getStartDate() == null) {
            crmContactResponsibleRelation.setStartDate(new Date());
        }
        if (StringUtils.isEmpty(crmContactResponsibleRelation.getAssignType())) {
            crmContactResponsibleRelation.setAssignType(CrmContactResponsibleRelation.AssignType.MANUAL);
        }
        
        crmContactResponsibleRelation.setCreateBy(SecurityUtils.getUsername());
        crmContactResponsibleRelation.setCreateTime(new Date());
        
        return contactResponsibleMapper.insertCrmContactResponsibleRelation(crmContactResponsibleRelation);
    }
    
    /**
     * 修改联系人-业务员关系
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 结果
     */
    @Override
    public int updateCrmContactResponsibleRelation(CrmContactResponsibleRelation crmContactResponsibleRelation) {
        crmContactResponsibleRelation.setUpdateBy(SecurityUtils.getUsername());
        crmContactResponsibleRelation.setUpdateTime(new Date());
        return contactResponsibleMapper.updateCrmContactResponsibleRelation(crmContactResponsibleRelation);
    }
    
    /**
     * 批量删除联系人-业务员关系
     * 
     * @param ids 需要删除的联系人-业务员关系主键
     * @return 结果
     */
    @Override
    public int deleteCrmContactResponsibleRelationByIds(Long[] ids) {
        return contactResponsibleMapper.deleteCrmContactResponsibleRelationByIds(ids);
    }
    
    /**
     * 删除联系人-业务员关系信息
     * 
     * @param id 联系人-业务员关系主键
     * @return 结果
     */
    @Override
    public int deleteCrmContactResponsibleRelationById(Long id) {
        return contactResponsibleMapper.deleteCrmContactResponsibleRelationById(id);
    }
    
    /**
     * 团队负责人分配联系人给团队成员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignContactsToTeamMember(TeamAssignRequest request) {
        // 1. 验证当前用户是否为团队负责人
        Long currentUserId = SecurityUtils.getUserId();
        CrmTeam team = teamMapper.selectCrmTeamById(request.getTeamId());
        if (team == null) {
            throw new ServiceException("团队不存在");
        }
        if (!currentUserId.equals(team.getLeaderId())) {
            throw new ServiceException("只有团队负责人可以分配联系人");
        }
        
        // 2. 验证被分配人是否为团队成员
        CrmTeamMember member = teamMemberMapper.selectByTeamAndUser(
            request.getTeamId(), request.getAssignToUserId());
        if (member == null || !CrmTeamMember.Status.NORMAL.equals(member.getStatus())) {
            throw new ServiceException("被分配人不是团队有效成员");
        }
        
        // 3. 批量创建联系人-负责人关系
        List<CrmContactResponsibleRelation> relations = new ArrayList<>();
        Date now = new Date();
        String businessType = StringUtils.isNotEmpty(request.getBusinessType()) ? 
            request.getBusinessType() : CrmContactResponsibleRelation.BusinessType.GENERAL;
        
        for (Long contactId : request.getContactIds()) {
            // 检查是否已存在相同业务类型的关系
            int exists = contactResponsibleMapper.checkActiveRelationExists(
                contactId, request.getAssignToUserId(), businessType);
            if (exists > 0) {
                continue; // 跳过已存在的关系
            }
            
            CrmContactResponsibleRelation relation = new CrmContactResponsibleRelation();
            relation.setContactId(contactId);
            relation.setResponsiblePersonId(request.getAssignToUserId());
            relation.setBusinessType(businessType);
            relation.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
            relation.setStartDate(now);
            relation.setTeamId(request.getTeamId());
            relation.setAssignType(CrmContactResponsibleRelation.AssignType.TEAM_ASSIGN);
            relation.setAssignBy(currentUserId);
            relation.setAssignTime(now);
            relation.setRemark(StringUtils.isNotEmpty(request.getRemark()) ? 
                request.getRemark() : "团队分配：" + team.getTeamName());
            relation.setCreateBy(SecurityUtils.getUsername());
            relation.setCreateTime(now);
            
            relations.add(relation);
        }
        
        if (relations.isEmpty()) {
            return 0;
        }
        
        // 4. 批量插入
        int result = contactResponsibleMapper.batchInsert(relations);
        
        // 5. 建立或更新团队关联关系
        for (Long contactId : request.getContactIds()) {
            CrmTeamRelation existing = teamRelationMapper.selectByTeamAndRelation(
                request.getTeamId(), "CONTACT", contactId);
            if (existing == null) {
                CrmTeamRelation teamRelation = new CrmTeamRelation();
                teamRelation.setTeamId(request.getTeamId());
                teamRelation.setRelationType("CONTACT");
                teamRelation.setRelationId(contactId);
                teamRelation.setCreateBy(SecurityUtils.getUsername());
                teamRelation.setCreateTime(now);
                teamRelationMapper.insertCrmTeamRelation(teamRelation);
            }
        }
        
        return result;
    }
    
    /**
     * 从公海认领联系人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int claimContactsFromPool(Long[] contactIds, String businessType) {
        Long currentUserId = SecurityUtils.getUserId();
        Date now = new Date();
        String bizType = StringUtils.isNotEmpty(businessType) ? 
            businessType : CrmContactResponsibleRelation.BusinessType.GENERAL;
        
        List<CrmContactResponsibleRelation> relations = new ArrayList<>();
        
        for (Long contactId : contactIds) {
            // 检查该联系人在该业务类型下是否已有负责人
            CrmContactResponsibleRelation existing = contactResponsibleMapper
                .selectActiveResponsibleByContactAndBusiness(contactId, bizType);
            
            if (existing != null) {
                throw new ServiceException("联系人ID:" + contactId + " 在业务类型:" + bizType + " 下已有负责人");
            }
            
            // 创建新的关系
            CrmContactResponsibleRelation relation = new CrmContactResponsibleRelation();
            relation.setContactId(contactId);
            relation.setResponsiblePersonId(currentUserId);
            relation.setBusinessType(bizType);
            relation.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
            relation.setStartDate(now);
            relation.setAssignType(CrmContactResponsibleRelation.AssignType.POOL_CLAIM);
            relation.setAssignBy(currentUserId);
            relation.setAssignTime(now);
            relation.setRemark("公海认领");
            relation.setCreateBy(SecurityUtils.getUsername());
            relation.setCreateTime(now);
            
            // 查找用户所属团队
            List<CrmTeamMember> teamMembers = teamMemberMapper.selectByUserId(currentUserId);
            if (!teamMembers.isEmpty()) {
                relation.setTeamId(teamMembers.get(0).getTeamId());
            }
            
            relations.add(relation);
        }
        
        if (relations.isEmpty()) {
            return 0;
        }
        
        return contactResponsibleMapper.batchInsert(relations);
    }
    
    /**
     * 将联系人退回公海
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int returnContactsToPool(Long[] contactIds, Long responsiblePersonId, String reason) {
        // 如果没有指定负责人，默认为当前用户
        if (responsiblePersonId == null) {
            responsiblePersonId = SecurityUtils.getUserId();
        }
        
        Date now = new Date();
        int count = 0;
        
        List<Long> contactIdList = java.util.Arrays.asList(contactIds);
        count = contactResponsibleMapper.batchUpdateRelationStatus(
            contactIdList,
            responsiblePersonId,
            CrmContactResponsibleRelation.RelationStatus.ACTIVE,
            CrmContactResponsibleRelation.RelationStatus.INACTIVE,
            now,
            SecurityUtils.getUsername()
        );
        
        return count;
    }
    
    /**
     * 转移联系人负责人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int transferContacts(Long[] contactIds, Long fromUserId, Long toUserId, String businessType, String remark) {
        Date now = new Date();
        String bizType = StringUtils.isNotEmpty(businessType) ? 
            businessType : CrmContactResponsibleRelation.BusinessType.GENERAL;
        
        // 1. 将原有关系设为已转移
        List<Long> contactIdList = java.util.Arrays.asList(contactIds);
        contactResponsibleMapper.batchUpdateRelationStatus(
            contactIdList,
            fromUserId,
            CrmContactResponsibleRelation.RelationStatus.ACTIVE,
            CrmContactResponsibleRelation.RelationStatus.TRANSFERRED,
            now,
            SecurityUtils.getUsername()
        );
        
        // 2. 创建新的关系
        List<CrmContactResponsibleRelation> newRelations = new ArrayList<>();
        
        for (Long contactId : contactIds) {
            CrmContactResponsibleRelation relation = new CrmContactResponsibleRelation();
            relation.setContactId(contactId);
            relation.setResponsiblePersonId(toUserId);
            relation.setBusinessType(bizType);
            relation.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
            relation.setStartDate(now);
            relation.setAssignType(CrmContactResponsibleRelation.AssignType.MANUAL);
            relation.setAssignBy(SecurityUtils.getUserId());
            relation.setAssignTime(now);
            relation.setRemark(StringUtils.isNotEmpty(remark) ? remark : "转移联系人");
            relation.setCreateBy(SecurityUtils.getUsername());
            relation.setCreateTime(now);
            
            // 设置新负责人的团队信息
            List<CrmTeamMember> teamMembers = teamMemberMapper.selectByUserId(toUserId);
            if (!teamMembers.isEmpty()) {
                relation.setTeamId(teamMembers.get(0).getTeamId());
            }
            
            newRelations.add(relation);
        }
        
        return contactResponsibleMapper.batchInsert(newRelations);
    }
    
    /**
     * 查询公海联系人（多维度）
     */
    @Override
    public List<CrmContacts> selectPoolContacts(PoolQueryRequest request) {
        return contactResponsibleMapper.selectPoolContacts(request);
    }
    
    /**
     * 获取团队业绩统计
     */
    @Override
    public TeamPerformanceVO getTeamPerformance(Long teamId, Date startDate, Date endDate) {
        TeamPerformanceVO performance = new TeamPerformanceVO();
        
        // 1. 团队基本信息
        CrmTeam team = teamMapper.selectCrmTeamById(teamId);
        performance.setTeamInfo(team);
        
        // 2. 团队成员列表
        CrmTeamMember memberQuery = new CrmTeamMember();
        memberQuery.setTeamId(teamId);
        List<CrmTeamMember> members = teamMemberMapper.selectCrmTeamMemberList(memberQuery);
        performance.setMembers(members);
        
        // 3. 团队管理的联系人统计
        int totalContacts = contactResponsibleMapper.countTeamContacts(teamId);
        performance.setTotalContacts(totalContacts);
        
        // 4. 按业务类型统计
        List<Map<String, Object>> businessTypeStats = contactResponsibleMapper
            .countByBusinessTypeAndTeam(teamId, startDate, endDate);
        performance.setBusinessTypeStats(businessTypeStats);
        
        // 5. 按成员统计
        List<Map<String, Object>> memberStatsList = contactResponsibleMapper
            .countByMemberAndDateRange(teamId, startDate, endDate);
        Map<Long, Integer> memberStats = memberStatsList.stream()
            .collect(Collectors.toMap(
                m -> Long.valueOf(m.get("userId").toString()),
                m -> Integer.valueOf(m.get("contactCount").toString())
            ));
        performance.setMemberStats(memberStats);
        
        return performance;
    }
    
    /**
     * 获取个人负责的联系人（按业务类型）
     */
    @Override
    public List<CrmContactResponsibleRelation> getMyContacts(Long userId, String businessType) {
        CrmContactResponsibleRelation query = new CrmContactResponsibleRelation();
        query.setResponsiblePersonId(userId);
        query.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
        if (StringUtils.isNotEmpty(businessType)) {
            query.setBusinessType(businessType);
        }
        
        return contactResponsibleMapper.selectCrmContactResponsibleRelationList(query);
    }
    
    /**
     * 批量更新团队信息
     */
    @Override
    public int updateTeamInfo(Long oldTeamId, Long newTeamId) {
        // TODO: 实现批量更新团队信息的逻辑
        return 0;
    }
    
    /**
     * 检查联系人在指定业务类型下是否有负责人
     */
    @Override
    public boolean hasResponsiblePerson(Long contactId, String businessType) {
        CrmContactResponsibleRelation relation = contactResponsibleMapper
            .selectActiveResponsibleByContactAndBusiness(contactId, businessType);
        return relation != null;
    }
    
    /**
     * 获取联系人在指定业务类型下的负责人
     */
    @Override
    public CrmContactResponsibleRelation getResponsiblePerson(Long contactId, String businessType) {
        return contactResponsibleMapper.selectActiveResponsibleByContactAndBusiness(contactId, businessType);
    }
    
    /**
     * 根据联系人ID查询所有活跃的负责人关系
     */
    @Override
    public List<CrmContactResponsibleRelation> getActiveResponsiblePersons(Long contactId) {
        CrmContactResponsibleRelation query = new CrmContactResponsibleRelation();
        query.setContactId(contactId);
        query.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
        
        return contactResponsibleMapper.selectCrmContactResponsibleRelationList(query);
    }
    
    /**
     * 批量查询联系人的负责人信息
     */
    @Override
    public List<CrmContactResponsibleRelation> getContactsResponsiblePersons(List<Long> contactIds, String businessType) {
        return contactResponsibleMapper.selectResponsibleByContactIds(contactIds, businessType);
    }
}