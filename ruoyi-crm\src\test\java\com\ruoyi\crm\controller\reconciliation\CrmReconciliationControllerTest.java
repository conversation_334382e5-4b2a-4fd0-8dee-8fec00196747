package com.ruoyi.crm.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmReconciliation;
import com.ruoyi.crm.service.ICrmReconciliationService;
import com.ruoyi.crm.service.workflow.IReconciliationWorkflowService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest
@AutoConfigureMockMvc
public class CrmReconciliationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ICrmReconciliationService crmReconciliationService;

    @MockBean
    private IReconciliationWorkflowService workflowService;

    /**
     * 测试查询对账单列表
     */
    @Test
    @WithMockUser
    public void testListReconciliations() throws Exception {
        // 模拟Service层返回的数据
        when(crmReconciliationService.selectCrmReconciliationList(any(CrmReconciliation.class)))
                .thenReturn(Collections.emptyList());

        // 执行GET请求并验证
        mockMvc.perform(get("/front/crm/reconciliation/list"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.total").value(0));
    }

    /**
     * 测试获取单个对账单
     */
    @Test
    @WithMockUser
    public void testGetReconciliationById() throws Exception {
        long reconciliationId = 1L;
        CrmReconciliation reconciliation = new CrmReconciliation();
        reconciliation.setReconciliationId(reconciliationId);

        when(crmReconciliationService.selectCrmReconciliationById(reconciliationId)).thenReturn(reconciliation);

        mockMvc.perform(get("/front/crm/reconciliation/{id}", reconciliationId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.reconciliationId").value(reconciliationId));
    }

    /**
     * 测试新增对账单
     */
    @Test
    @WithMockUser
    public void testAddReconciliation() throws Exception {
        CrmReconciliation reconciliation = new CrmReconciliation();
        reconciliation.setCustomerId(1L); // 设置必要属性

        when(crmReconciliationService.insertCrmReconciliation(any(CrmReconciliation.class))).thenReturn(1);

        mockMvc.perform(post("/front/crm/reconciliation")
                        .contentType("application/json")
                        .content(objectMapper.writeValueAsString(reconciliation)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试修改对账单
     */
    @Test
    @WithMockUser
    public void testUpdateReconciliation() throws Exception {
        CrmReconciliation reconciliation = new CrmReconciliation();
        reconciliation.setReconciliationId(1L);

        when(crmReconciliationService.updateCrmReconciliation(any(CrmReconciliation.class))).thenReturn(1);

        mockMvc.perform(put("/front/crm/reconciliation")
                        .contentType("application/json")
                        .content(objectMapper.writeValueAsString(reconciliation)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试删除对账单
     */
    @Test
    @WithMockUser
    public void testDeleteReconciliation() throws Exception {
        Long[] ids = {1L, 2L};
        // 使用 any() 匹配器来避免数组引用不匹配的问题
        when(crmReconciliationService.deleteCrmReconciliationByIds(any(Long[].class))).thenReturn(ids.length);

        // 手动将ID数组转换为逗号分隔的字符串
        mockMvc.perform(delete("/front/crm/reconciliation/1,2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    /**
     * 测试提交对账单以启动审批流程
     */
    @Test
    @WithMockUser
    public void testSubmitForApproval() throws Exception {
        long reconciliationId = 1L;
        // 模拟工作流服务，使其返回一个模拟的流程实例ID
        when(workflowService.startApprovalProcess(anyLong(), any())).thenReturn("mock-process-instance-id");

        mockMvc.perform(post("/front/crm/reconciliation/submit/{id}", reconciliationId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("提交成功"));
    }

    /**
     * 测试处理审批任务
     */
    @Test
    @WithMockUser
    public void testApproveTask() throws Exception {
        CrmReconciliationController.ApprovalRequest approvalRequest = new CrmReconciliationController.ApprovalRequest();
        approvalRequest.setTaskId("test-task-id");
        approvalRequest.setApproved(true);
        approvalRequest.setComments("Approved");

        // 模拟工作流服务
        doNothing().when(workflowService).processApprovalTask(anyString(), anyBoolean(), anyString());

        mockMvc.perform(post("/front/crm/reconciliation/approve")
                        .contentType("application/json")
                        .content(objectMapper.writeValueAsString(approvalRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));
    }
}
