package com.ruoyi.crm.service;

/**
 * 客户通知服务接口
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
public interface ICustomerNotificationService {
    
    /**
     * 通知管理员有新客户通过3D打印系统创建
     * 
     * @param customerId 客户ID
     * @param customerName 客户名称
     * @param contactPhone 联系电话
     * @param quoteNo 询价单号
     */
    void notifyNewCustomerFromThreeD(Long customerId, String customerName, String contactPhone, String quoteNo);
    
    /**
     * 通知负责人客户有新订单
     * 
     * @param managerId 负责人ID
     * @param customerId 客户ID
     * @param customerName 客户名称
     * @param orderNo 订单号
     * @param orderAmount 订单金额
     */
    void notifyManagerNewOrder(Long managerId, Long customerId, String customerName, String orderNo, java.math.BigDecimal orderAmount);
}