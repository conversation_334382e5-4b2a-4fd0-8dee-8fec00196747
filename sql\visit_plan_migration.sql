-- 拜访计划功能数据库迁移脚本
-- 执行时间：2025-01-29
-- 功能说明：创建拜访计划相关的数据库表结构

-- 1. 创建拜访计划主表
CREATE TABLE `crm_visit_plans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_name` varchar(200) NOT NULL COMMENT '拜访计划名称',
  `visit_time` datetime NOT NULL COMMENT '预计拜访时间',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(200) DEFAULT NULL COMMENT '客户名称（冗余）',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名（冗余）',
  `opportunity_id` bigint(20) DEFAULT NULL COMMENT '商机ID',
  `opportunity_name` varchar(200) DEFAULT NULL COMMENT '商机名称（冗余）',
  `visit_purpose` text COMMENT '拜访目的',
  `remind_time` int(11) DEFAULT '30' COMMENT '提前提醒时间（分钟）',
  `remark` text COMMENT '备注',
  `postpone_reason` varchar(500) DEFAULT NULL COMMENT '延期原因',
  `postpone_remark` text COMMENT '延期备注',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '取消原因',
  `cancel_remark` text COMMENT '取消备注',
  `followup_content` text COMMENT '跟进记录内容',
  `owner_id` bigint(20) NOT NULL COMMENT '负责人ID',
  `owner_name` varchar(100) DEFAULT NULL COMMENT '负责人姓名（冗余）',
  `status` varchar(20) NOT NULL DEFAULT 'planned' COMMENT '状态：planned-计划中,ongoing-进行中,completed-已完成,postponed-已延期,cancelled-已取消',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '所属部门ID',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称（冗余）',
  `actual_visit_time` datetime DEFAULT NULL COMMENT '实际拜访时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_opportunity_id` (`opportunity_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划表';

-- 2. 创建提醒记录表
CREATE TABLE `crm_visit_plan_reminders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_id` bigint(20) NOT NULL COMMENT '拜访计划ID',
  `remind_type` varchar(20) NOT NULL COMMENT '提醒类型：system-系统内,wechat-企业微信,sms-短信,email-邮件',
  `remind_time` datetime NOT NULL COMMENT '提醒时间',
  `remind_status` varchar(20) DEFAULT 'pending' COMMENT '提醒状态：pending-待发送,sent-已发送,failed-发送失败',
  `send_time` datetime DEFAULT NULL COMMENT '实际发送时间',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_plan_id` (`visit_plan_id`),
  KEY `idx_remind_time` (`remind_time`),
  KEY `idx_remind_status` (`remind_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划提醒记录表';

-- 3. 创建状态变更日志表
CREATE TABLE `crm_visit_plan_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `visit_plan_id` bigint(20) NOT NULL COMMENT '拜访计划ID',
  `from_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `to_status` varchar(20) NOT NULL COMMENT '新状态',
  `change_reason` varchar(500) DEFAULT NULL COMMENT '变更原因',
  `change_remark` text COMMENT '变更备注',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_visit_plan_id` (`visit_plan_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拜访计划状态变更日志表';

-- 4. 添加系统菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('拜访计划管理', 2000, 5, 'visitPlan', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'calendar', 'admin', NOW(), '', NULL, '拜访计划管理菜单');

-- 获取刚插入的菜单ID
SET @menuId = LAST_INSERT_ID();

-- 添加子菜单权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('拜访计划查询', @menuId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:query', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划新增', @menuId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:add', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划修改', @menuId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:edit', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划删除', @menuId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:remove', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划导出', @menuId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:export', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划延期', @menuId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:postpone', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划取消', @menuId, 7, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:cancel', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划完成', @menuId, 8, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:complete', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划统计', @menuId, 9, '#', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:statistics', '#', 'admin', NOW(), '', NULL, '');

-- 5. 添加数据字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('拜访计划状态', 'visit_plan_status', '0', 'admin', NOW(), '', NULL, '拜访计划状态列表');

-- 6. 添加数据字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '计划中', 'planned', 'visit_plan_status', '', 'primary', 'Y', '0', 'admin', NOW(), '', NULL, '计划中的拜访'),
(2, '进行中', 'ongoing', 'visit_plan_status', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '正在进行的拜访'),
(3, '已完成', 'completed', 'visit_plan_status', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '已完成的拜访'),
(4, '已延期', 'postponed', 'visit_plan_status', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, '已延期的拜访'),
(5, '已取消', 'cancelled', 'visit_plan_status', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '已取消的拜访');

-- 7. 定时任务配置（用于拜访提醒）
INSERT INTO `sys_job` (`job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('拜访计划提醒任务', 'DEFAULT', 'visitPlanReminder.processReminders', '0 0/5 * * * ?', '3', '1', '0', 'admin', NOW(), '', NULL, '每5分钟执行一次拜访计划提醒检查');
