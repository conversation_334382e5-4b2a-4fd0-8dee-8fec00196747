<template>
    <div class="visit-plan-tab">
        <div class="tab-header">
            <h3>拜访计划</h3>
            <div class="header-actions">
                <el-button 
                    type="primary" 
                    size="small"
                    @click="openVisitPlanDialog"
                >
                    <el-icon><Plus /></el-icon>
                    新建拜访计划
                </el-button>
                <el-button 
                    type="info" 
                    size="small"
                    @click="showStatistics"
                >
                    <el-icon><DataAnalysis /></el-icon>
                    统计信息
                </el-button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="statistics-cards" v-if="statistics">
            <el-row :gutter="16">
                <el-col :span="4">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.total }}</div>
                            <div class="stat-label">总计划数</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="4">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.planned }}</div>
                            <div class="stat-label">计划中</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="4">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.completed }}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="4">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.postponed }}</div>
                            <div class="stat-label">已延期</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="4">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.cancelled }}</div>
                            <div class="stat-label">已取消</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="4">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-value">{{ statistics.completionRate }}%</div>
                            <div class="stat-label">完成率</div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 筛选组件 -->
        <common-filter
            v-model:searchValue="searchInput"
            v-model:filterValue="filterType"
            :config="filterConfig"
            @search="handleSearch"
            @filter="handleFilterChange"
        />

        <!-- 拜访计划列表 -->
        <el-table 
            :data="visitPlans" 
            v-loading="loading"
            border 
            sortable 
            tooltip-effect="dark"
            :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
            style="width: 100%; margin-top: 16px; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
            @selection-change="handleSelectionChange">
            
            <el-table-column type="selection" width="55" />
            
            <el-table-column prop="visitPlanName" label="计划名称" min-width="180" show-overflow-tooltip />
            
            <el-table-column prop="customerName" label="客户名称" min-width="150">
                <template #default="scope">
                    <el-button link type="primary" @click="viewCustomerDetail(scope.row)">
                        {{ scope.row.customerName }}
                    </el-button>
                </template>
            </el-table-column>
            
            <el-table-column prop="contactName" label="联系人" min-width="100" />
            
            <el-table-column prop="visitTime" label="计划拜访时间" min-width="150">
                <template #default="scope">
                    <span :class="{ 'text-danger': isOverdue(scope.row.visitTime, scope.row.status) }">
                        {{ formatDateTime(scope.row.visitTime) }}
                        <el-icon v-if="isOverdue(scope.row.visitTime, scope.row.status)" class="overdue-icon">
                            <Warning />
                        </el-icon>
                    </span>
                </template>
            </el-table-column>
            
            <el-table-column prop="visitPurpose" label="拜访目的" min-width="150" show-overflow-tooltip />
            
            <el-table-column prop="status" label="状态" min-width="100">
                <template #default="scope">
                    <el-tag :type="VISIT_STATUS_MAP[scope.row.status].color" size="small">
                        {{ VISIT_STATUS_MAP[scope.row.status].label }}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="ownerName" label="负责人" min-width="100" />
            
            <el-table-column prop="actualVisitTime" label="实际拜访时间" min-width="150">
                <template #default="scope">
                    {{ scope.row.actualVisitTime ? formatDateTime(scope.row.actualVisitTime) : '-' }}
                </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                    <div class="action-buttons">
                        <el-button 
                            v-if="scope.row.status === 'planned'"
                            type="success" 
                            link 
                            size="small" 
                            @click="completeVisitPlan(scope.row)"
                        >
                            <el-icon><Check /></el-icon>
                            完成
                        </el-button>
                        <el-button 
                            v-if="scope.row.status === 'planned'"
                            type="warning" 
                            link 
                            size="small" 
                            @click="postponeVisitPlan(scope.row)"
                        >
                            <el-icon><Clock /></el-icon>
                            延期
                        </el-button>
                        <el-button 
                            type="primary" 
                            link 
                            size="small" 
                            @click="editVisitPlan(scope.row)"
                        >
                            <el-icon><Edit /></el-icon>
                            编辑
                        </el-button>
                        <el-button 
                            v-if="scope.row.status !== 'completed'"
                            type="danger" 
                            link 
                            size="small" 
                            @click="cancelVisitPlan(scope.row)"
                        >
                            <el-icon><Close /></el-icon>
                            取消
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        
        <!-- 分页组件 -->
        <pagination 
            v-show="totalVisitPlans > 0" 
            :total="totalVisitPlans" 
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handlePagination" 
        />

        <!-- 新增/编辑拜访计划对话框 -->
        <el-dialog 
            v-model="dialogVisible" 
            :title="dialogTitle"
            width="800px"
            :before-close="handleDialogClose"
        >
            <el-form :model="visitPlanForm" :rules="visitPlanRules" ref="visitPlanFormRef" label-width="120px">
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="计划名称" prop="visitPlanName">
                            <el-input v-model="visitPlanForm.visitPlanName" placeholder="请输入拜访计划名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="关联客户" prop="customerId">
                            <el-select 
                                v-model="visitPlanForm.customerId" 
                                placeholder="请选择客户" 
                                style="width: 100%"
                                filterable
                                remote
                                :remote-method="searchCustomers"
                                :loading="customerSearchLoading"
                                @change="handleCustomerChange"
                            >
                                <el-option 
                                    v-for="customer in customerOptions" 
                                    :key="customer.id" 
                                    :label="customer.customerName" 
                                    :value="customer.id" 
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系人" prop="contactId">
                            <el-select 
                                v-model="visitPlanForm.contactId" 
                                placeholder="请选择联系人" 
                                style="width: 100%"
                                :disabled="!visitPlanForm.customerId"
                            >
                                <el-option 
                                    v-for="contact in contactOptions" 
                                    :key="contact.id" 
                                    :label="contact.contactName" 
                                    :value="contact.id" 
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="计划拜访时间" prop="visitTime">
                            <el-date-picker 
                                v-model="visitPlanForm.visitTime" 
                                type="datetime" 
                                placeholder="选择拜访时间"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="提醒时间" prop="remindTime">
                            <el-select v-model="visitPlanForm.remindTime" placeholder="请选择提醒时间" style="width: 100%">
                                <el-option 
                                    v-for="option in REMIND_TIME_OPTIONS" 
                                    :key="option.value" 
                                    :label="option.label" 
                                    :value="option.value" 
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-form-item label="拜访目的" prop="visitPurpose">
                    <el-input 
                        v-model="visitPlanForm.visitPurpose" 
                        type="textarea" 
                        :rows="3" 
                        placeholder="请描述拜访目的"
                    />
                </el-form-item>
                
                <el-form-item label="备注" prop="remark">
                    <el-input 
                        v-model="visitPlanForm.remark" 
                        type="textarea" 
                        :rows="2" 
                        placeholder="请输入备注信息"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="handleDialogClose">取消</el-button>
                    <el-button type="primary" @click="handleSaveVisitPlan" :loading="saving">
                        {{ isEdit ? '更新' : '创建' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 延期对话框 -->
        <el-dialog v-model="postponeDialogVisible" title="延期拜访计划" width="500px">
            <el-form :model="postponeForm" :rules="postponeRules" ref="postponeFormRef" label-width="100px">
                <el-form-item label="延期原因" prop="reason">
                    <el-select v-model="postponeForm.reason" placeholder="请选择延期原因" style="width: 100%">
                        <el-option label="客户时间冲突" value="customer_conflict" />
                        <el-option label="我方时间冲突" value="our_conflict" />
                        <el-option label="客户临时取消" value="customer_cancel" />
                        <el-option label="其他紧急事务" value="emergency" />
                        <el-option label="其他" value="other" />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="新拜访时间" prop="newVisitTime">
                    <el-date-picker 
                        v-model="postponeForm.newVisitTime" 
                        type="datetime" 
                        placeholder="选择新的拜访时间"
                        style="width: 100%"
                    />
                </el-form-item>
                
                <el-form-item label="详细说明" prop="remark">
                    <el-input 
                        v-model="postponeForm.remark" 
                        type="textarea" 
                        :rows="3" 
                        placeholder="请输入延期详细说明"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="postponeDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirmPostpone" :loading="postponeLoading">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 取消对话框 -->
        <el-dialog v-model="cancelDialogVisible" title="取消拜访计划" width="500px">
            <el-form :model="cancelForm" :rules="cancelRules" ref="cancelFormRef" label-width="100px">
                <el-form-item label="取消原因" prop="reason">
                    <el-select v-model="cancelForm.reason" placeholder="请选择取消原因" style="width: 100%">
                        <el-option label="客户不配合" value="customer_uncooperative" />
                        <el-option label="客户需求变更" value="customer_change" />
                        <el-option label="项目暂停" value="project_pause" />
                        <el-option label="客户已成交" value="customer_closed" />
                        <el-option label="其他" value="other" />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="详细说明" prop="remark">
                    <el-input 
                        v-model="cancelForm.remark" 
                        type="textarea" 
                        :rows="3" 
                        placeholder="请输入取消详细说明"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancelDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirmCancel" :loading="cancelLoading">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 完成对话框 -->
        <el-dialog v-model="completeDialogVisible" title="完成拜访计划" width="600px">
            <el-form :model="completeForm" :rules="completeRules" ref="completeFormRef" label-width="100px">
                <el-form-item label="跟进内容" prop="followupContent">
                    <el-input 
                        v-model="completeForm.followupContent" 
                        type="textarea" 
                        :rows="6" 
                        placeholder="请输入拜访跟进内容，包括拜访结果、客户反馈、下一步计划等"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="completeDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirmComplete" :loading="completeLoading">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
    Plus,
    DataAnalysis,
    Check,
    Clock,
    Edit,
    Close,
    Warning
} from '@element-plus/icons-vue';

import CommonFilter from '@/components/CommonFilter/index.vue';
import Pagination from '@/components/Pagination/index.vue';

import {
    getVisitPlanList,
    addVisitPlan,
    updateVisitPlan,
    deleteVisitPlan,
    postponeVisitPlan as postponeVisitPlanApi,
    cancelVisitPlan as cancelVisitPlanApi,
    completeVisitPlan as completeVisitPlanApi,
    getVisitPlanStatistics
} from '@/api/visit-plan';

import type { VisitPlan, VisitPlanStatistics, PostponeRequest, CancelRequest, CompleteRequest } from '@/types/visit-plan';
import { VISIT_STATUS_MAP, REMIND_TIME_OPTIONS, DEFAULT_VISIT_PLAN } from '@/types/visit-plan';
import request from '@/utils/request';

// 状态管理
const loading = ref(false);
const saving = ref(false);
const postponeLoading = ref(false);
const cancelLoading = ref(false);
const completeLoading = ref(false);
const customerSearchLoading = ref(false);

// 数据
const visitPlans = ref<VisitPlan[]>([]);
const selectedVisitPlans = ref<VisitPlan[]>([]);
const statistics = ref<VisitPlanStatistics | null>(null);
const customerOptions = ref<any[]>([]);
const contactOptions = ref<any[]>([]);

// 统计
const totalVisitPlans = ref(0);

// 弹窗状态
const dialogVisible = ref(false);
const postponeDialogVisible = ref(false);
const cancelDialogVisible = ref(false);
const completeDialogVisible = ref(false);

// 当前操作的拜访计划
const currentVisitPlan = ref<VisitPlan | null>(null);

// 搜索和筛选
const searchInput = ref('');
const filterType = ref('all');

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    visitPlanName: '',
    customerName: '',
    status: '',
    ownerName: ''
});

// 拜访计划表单
const visitPlanForm = ref<Partial<VisitPlan>>({ ...DEFAULT_VISIT_PLAN });

// 延期表单
const postponeForm = ref<PostponeRequest>({
    reason: '',
    remark: '',
    newVisitTime: ''
});

// 取消表单
const cancelForm = ref<CancelRequest>({
    reason: '',
    remark: ''
});

// 完成表单
const completeForm = ref<CompleteRequest>({
    followupContent: ''
});

// 计算属性
const isEdit = computed(() => !!visitPlanForm.value.id);
const dialogTitle = computed(() => isEdit.value ? '编辑拜访计划' : '新建拜访计划');

// 表单验证规则
const visitPlanRules = {
    visitPlanName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
    customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
    visitTime: [{ required: true, message: '请选择拜访时间', trigger: 'change' }],
    visitPurpose: [{ required: true, message: '请输入拜访目的', trigger: 'blur' }],
    remindTime: [{ required: true, message: '请选择提醒时间', trigger: 'change' }]
};

const postponeRules = {
    reason: [{ required: true, message: '请选择延期原因', trigger: 'change' }],
    newVisitTime: [{ required: true, message: '请选择新的拜访时间', trigger: 'change' }]
};

const cancelRules = {
    reason: [{ required: true, message: '请选择取消原因', trigger: 'change' }]
};

const completeRules = {
    followupContent: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }]
};

// 筛选配置
const filterConfig = {
    search: {
        placeholder: '搜索计划名称、客户名称',
        width: '240px',
        icon: 'Search',
        debounceTime: 300
    },
    filter: {
        label: '状态筛选：',
        options: [
            { label: '全部', value: 'all' },
            { label: '计划中', value: 'planned' },
            { label: '进行中', value: 'ongoing' },
            { label: '已完成', value: 'completed' },
            { label: '已延期', value: 'postponed' },
            { label: '已取消', value: 'cancelled' }
        ],
        buttonStyle: true,
        size: 'default'
    }
};

// 生命周期
onMounted(() => {
    loadVisitPlans();
    loadStatistics();
});

// 方法
const loadVisitPlans = async () => {
    loading.value = true;
    try {
        const response = await getVisitPlanList(queryParams);
        if (response.code === 200) {
            visitPlans.value = response.rows || [];
            totalVisitPlans.value = response.total || 0;
        }
    } catch (error) {
        ElMessage.error('加载拜访计划失败');
    } finally {
        loading.value = false;
    }
};

const loadStatistics = async () => {
    try {
        const response = await getVisitPlanStatistics();
        if (response.code === 200) {
            statistics.value = response.data;
        }
    } catch (error) {
        console.error('加载统计信息失败:', error);
    }
};

const handleSelectionChange = (selection: VisitPlan[]) => {
    selectedVisitPlans.value = selection;
};

const openVisitPlanDialog = () => {
    visitPlanForm.value = { ...DEFAULT_VISIT_PLAN };
    dialogVisible.value = true;
};

const editVisitPlan = (visitPlan: VisitPlan) => {
    visitPlanForm.value = { ...visitPlan };
    dialogVisible.value = true;
};

const handleDialogClose = () => {
    dialogVisible.value = false;
};

const handleSaveVisitPlan = async () => {
    saving.value = true;
    try {
        let response;
        if (isEdit.value) {
            response = await updateVisitPlan(visitPlanForm.value as VisitPlan);
        } else {
            response = await addVisitPlan(visitPlanForm.value);
        }
        
        if (response.code === 200) {
            ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
            dialogVisible.value = false;
            loadVisitPlans();
            loadStatistics();
        }
    } catch (error) {
        ElMessage.error('保存失败');
    } finally {
        saving.value = false;
    }
};

const postponeVisitPlan = (visitPlan: VisitPlan) => {
    currentVisitPlan.value = visitPlan;
    postponeForm.value = {
        reason: '',
        remark: '',
        newVisitTime: ''
    };
    postponeDialogVisible.value = true;
};

const handleConfirmPostpone = async () => {
    if (!currentVisitPlan.value) return;
    
    postponeLoading.value = true;
    try {
        const response = await postponeVisitPlanApi(currentVisitPlan.value.id!, postponeForm.value);
        if (response.code === 200) {
            ElMessage.success('延期成功');
            postponeDialogVisible.value = false;
            loadVisitPlans();
            loadStatistics();
        }
    } catch (error) {
        ElMessage.error('延期失败');
    } finally {
        postponeLoading.value = false;
    }
};

const cancelVisitPlan = (visitPlan: VisitPlan) => {
    currentVisitPlan.value = visitPlan;
    cancelForm.value = {
        reason: '',
        remark: ''
    };
    cancelDialogVisible.value = true;
};

const handleConfirmCancel = async () => {
    if (!currentVisitPlan.value) return;
    
    cancelLoading.value = true;
    try {
        const response = await cancelVisitPlanApi(currentVisitPlan.value.id!, cancelForm.value);
        if (response.code === 200) {
            ElMessage.success('取消成功');
            cancelDialogVisible.value = false;
            loadVisitPlans();
            loadStatistics();
        }
    } catch (error) {
        ElMessage.error('取消失败');
    } finally {
        cancelLoading.value = false;
    }
};

const completeVisitPlan = (visitPlan: VisitPlan) => {
    currentVisitPlan.value = visitPlan;
    completeForm.value = {
        followupContent: ''
    };
    completeDialogVisible.value = true;
};

const handleConfirmComplete = async () => {
    if (!currentVisitPlan.value) return;
    
    completeLoading.value = true;
    try {
        const response = await completeVisitPlanApi(currentVisitPlan.value.id!, completeForm.value);
        if (response.code === 200) {
            ElMessage.success('完成成功');
            completeDialogVisible.value = false;
            loadVisitPlans();
            loadStatistics();
        }
    } catch (error) {
        ElMessage.error('完成失败');
    } finally {
        completeLoading.value = false;
    }
};

const searchCustomers = async (query: string) => {
    if (!query) return;
    customerSearchLoading.value = true;
    try {
        const response = await request({
            url: '/crm/customer/search',
            method: 'get',
            params: { customerName: query }
        });
        if (response.code === 200) {
            customerOptions.value = response.data || [];
        }
    } catch (error) {
        ElMessage.error('搜索客户失败');
    } finally {
        customerSearchLoading.value = false;
    }
};

const handleCustomerChange = async (customerId: number) => {
    try {
        // TODO: 根据客户ID加载联系人
        contactOptions.value = [
            { id: 1, contactName: '联系人1' },
            { id: 2, contactName: '联系人2' }
        ];
    } catch (error) {
        ElMessage.error('加载联系人失败');
    }
};

const viewCustomerDetail = (visitPlan: VisitPlan) => {
    ElMessage.info('查看客户详情功能开发中');
};

const showStatistics = () => {
    ElMessage.info('统计信息功能开发中');
};

const handleSearch = (value: string) => {
    queryParams.visitPlanName = value;
    queryParams.pageNum = 1;
    loadVisitPlans();
};

const handleFilterChange = (value: string) => {
    queryParams.status = value === 'all' ? '' : value;
    queryParams.pageNum = 1;
    loadVisitPlans();
};

const handlePagination = (val: { page: number; limit: number }) => {
    queryParams.pageNum = val.page;
    queryParams.pageSize = val.limit;
    loadVisitPlans();
};

// 工具方法
const formatDateTime = (dateTime: string | Date) => {
    if (!dateTime) return '';
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const isOverdue = (visitTime: string | Date, status: string) => {
    if (status === 'completed' || status === 'cancelled') return false;
    return new Date(visitTime) < new Date();
};
</script>

<style scoped>
.visit-plan-tab {
    padding: 20px;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tab-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #303133;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.statistics-cards {
    margin-bottom: 20px;
}

.stat-card {
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-content {
    padding: 10px 0;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #909399;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.text-danger {
    color: #f56c6c;
}

.overdue-icon {
    margin-left: 4px;
    color: #f56c6c;
}
</style>