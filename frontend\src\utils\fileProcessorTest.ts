// 3D文件格式支持测试工具
import { FileProcessor, SUPPORTED_3D_FORMATS, FORMAT_INFO } from './fileProcessor'

export class FileProcessorTest {
  // 测试支持的格式列表
  static testSupportedFormats() {
    console.log('📋 支持的3D文件格式:')
    SUPPORTED_3D_FORMATS.forEach(format => {
      const info = FORMAT_INFO[format as keyof typeof FORMAT_INFO]
      console.log(`  ✅ ${format.toUpperCase()} - ${info.name}: ${info.description}`)
    })
  }

  // 创建测试文件（模拟不同格式）
  static createTestFile(format: string, name: string = 'test'): File {
    const content = FileProcessorTest.getTestContent(format)
    const blob = new Blob([content], { type: 'application/octet-stream' })
    return new File([blob], `${name}.${format}`, { type: 'application/octet-stream' })
  }

  // 获取不同格式的测试内容
  private static getTestContent(format: string): string {
    switch (format.toLowerCase()) {
      case 'stl':
        return FileProcessorTest.getSTLTestContent()
      case 'obj':
        return FileProcessorTest.getOBJTestContent()
      case 'ply':
        return FileProcessorTest.getPLYTestContent()
      case 'gltf':
        return FileProcessorTest.getGLTFTestContent()
      case 'dae':
        return FileProcessorTest.getColladaTestContent()
      default:
        return 'Test 3D Model Content'
    }
  }

  // STL测试内容（简单三角形）
  private static getSTLTestContent(): string {
    return `solid test
  facet normal 0 0 1
    outer loop
      vertex 0 0 0
      vertex 1 0 0
      vertex 0 1 0
    endloop
  endfacet
endsolid test`
  }

  // OBJ测试内容（简单三角形）
  private static getOBJTestContent(): string {
    return `# Test OBJ file
v 0.0 0.0 0.0
v 1.0 0.0 0.0
v 0.0 1.0 0.0
f 1 2 3`
  }

  // PLY测试内容
  private static getPLYTestContent(): string {
    return `ply
format ascii 1.0
element vertex 3
property float x
property float y
property float z
element face 1
property list uchar int vertex_indices
end_header
0.0 0.0 0.0
1.0 0.0 0.0
0.0 1.0 0.0
3 0 1 2`
  }

  // glTF测试内容
  private static getGLTFTestContent(): string {
    return JSON.stringify({
      "asset": { "version": "2.0" },
      "scene": 0,
      "scenes": [{ "nodes": [0] }],
      "nodes": [{ "mesh": 0 }],
      "meshes": [{
        "primitives": [{
          "attributes": { "POSITION": 0 },
          "indices": 1
        }]
      }],
      "accessors": [
        {
          "bufferView": 0,
          "componentType": 5126,
          "count": 3,
          "type": "VEC3",
          "max": [1, 1, 0],
          "min": [0, 0, 0]
        },
        {
          "bufferView": 1,
          "componentType": 5123,
          "count": 3,
          "type": "SCALAR"
        }
      ],
      "bufferViews": [
        { "buffer": 0, "byteOffset": 0, "byteLength": 36 },
        { "buffer": 0, "byteOffset": 36, "byteLength": 6 }
      ],
      "buffers": [{ "byteLength": 42 }]
    })
  }

  // Collada测试内容
  private static getColladaTestContent(): string {
    return `<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <created>2025-01-01T00:00:00Z</created>
    <modified>2025-01-01T00:00:00Z</modified>
  </asset>
  <library_geometries>
    <geometry id="test-geometry">
      <mesh>
        <source id="test-positions">
          <float_array id="test-positions-array" count="9">0 0 0 1 0 0 0 1 0</float_array>
          <technique_common>
            <accessor source="#test-positions-array" count="3" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="test-vertices">
          <input semantic="POSITION" source="#test-positions"/>
        </vertices>
        <triangles count="1">
          <input semantic="VERTEX" source="#test-vertices" offset="0"/>
          <p>0 1 2</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene">
      <node id="test-node">
        <instance_geometry url="#test-geometry"/>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>`
  }

  // 测试文件处理
  static async testFileProcessing(format: string): Promise<void> {
    console.log(`🧪 测试 ${format.toUpperCase()} 格式处理...`)
    
    try {
      const testFile = FileProcessorTest.createTestFile(format)
      console.log(`📁 创建测试文件: ${testFile.name} (${testFile.size} bytes)`)
      
      // 测试文件处理
      const processedFile = await FileProcessor.process(testFile)
      console.log(`✅ 文件处理成功:`, {
        isSupported: processedFile.isSupported,
        message: processedFile.message
      })
      
      // 测试模型信息提取
      if (processedFile.isSupported) {
        const modelInfo = await FileProcessor.extractModelInfo(testFile, format)
        console.log(`📊 模型信息:`, modelInfo)
      }
      
    } catch (error) {
      console.error(`❌ ${format.toUpperCase()} 格式测试失败:`, error)
    }
  }

  // 运行所有格式测试
  static async runAllTests(): Promise<void> {
    console.log('🚀 开始3D文件格式支持测试...\n')
    
    FileProcessorTest.testSupportedFormats()
    console.log('\n')
    
    for (const format of SUPPORTED_3D_FORMATS) {
      await FileProcessorTest.testFileProcessing(format)
      console.log('---')
    }
    
    console.log('✨ 所有测试完成!')
  }

  // 格式兼容性检查
  static checkFormatCompatibility(): void {
    console.log('🔍 格式兼容性检查:')
    
    const recommendations = {
      '最佳选择': ['stl', 'gltf', 'glb'],
      '良好支持': ['obj', 'ply'],
      '实验性支持': ['fbx', 'dae'],
      '暂不支持': ['step', 'stp', '3ds', 'max']
    }
    
    Object.entries(recommendations).forEach(([level, formats]) => {
      console.log(`  ${level}: ${formats.join(', ')}`)
    })
  }
}

// 导出测试函数到全局作用域（开发环境使用）
if (typeof window !== 'undefined') {
  (window as any).FileProcessorTest = FileProcessorTest
}