package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;

import org.springframework.test.web.servlet.MvcResult;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * 测试断言辅助类
 * 提供更详细的测试验证和调试信息
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
public class TestAssertionHelper {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 验证MvcResult并返回解析后的AjaxResult
     */
    public static AjaxResult assertAndParseResult(MvcResult result, String testDescription) throws Exception {
        assertNotNull(result, testDescription + " - MvcResult不能为空");
        
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("🔍 " + testDescription + " - 响应内容: " + responseContent);
        
        assertNotNull(responseContent, testDescription + " - 响应内容不能为空");
        assertFalse(responseContent.trim().isEmpty(), testDescription + " - 响应内容不能为空字符串");
        
        AjaxResult ajaxResult = objectMapper.readValue(responseContent, AjaxResult.class);
        assertNotNull(ajaxResult, testDescription + " - 解析后的结果不能为空");
        
        return ajaxResult;
    }
    
    /**
     * 验证成功的响应
     */
    public static void assertSuccessResponse(AjaxResult result, String testDescription) {
        assertNotNull(result, testDescription + " - 结果不能为空");
        
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");
        Object data = result.get("data");
        
        System.out.println("🔍 " + testDescription + " - 响应码: " + code);
        System.out.println("🔍 " + testDescription + " - 响应消息: " + msg);
        System.out.println("🔍 " + testDescription + " - 响应数据: " + data);
        
        assertTrue(result.isSuccess(), 
            testDescription + " - 应该成功，但失败了: 错误码=" + code + ", 错误信息=" + msg);
        
        assertEquals(200, code.intValue(), 
            testDescription + " - 成功响应的错误码应该是200，实际: " + code);
    }
    
    /**
     * 验证失败的响应
     */
    public static void assertFailureResponse(AjaxResult result, String testDescription, String expectedErrorKeyword) {
        assertNotNull(result, testDescription + " - 结果不能为空");
        
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");
        
        System.out.println("🔍 " + testDescription + " - 响应码: " + code);
        System.out.println("🔍 " + testDescription + " - 响应消息: " + msg);
        
        assertFalse(result.isSuccess(), 
            testDescription + " - 应该失败，但成功了: 响应码=" + code + ", 响应消息=" + msg);
        
        assertTrue(code != 200, 
            testDescription + " - 失败响应的错误码不应该是200，实际: " + code);
        
        if (expectedErrorKeyword != null && !expectedErrorKeyword.trim().isEmpty()) {
            assertNotNull(msg, testDescription + " - 错误消息不能为空");
            assertTrue(msg.contains(expectedErrorKeyword), 
                testDescription + " - 错误消息应该包含关键词 '" + expectedErrorKeyword + "'，实际消息: " + msg);
        }
    }
    
    /**
     * 验证响应数据的结构
     */
    public static void assertResponseDataStructure(Object data, String testDescription, String... expectedFields) {
        if (data == null) {
            System.out.println("⚠️  " + testDescription + " - 响应数据为空");
            return;
        }
        
        System.out.println("🔍 " + testDescription + " - 数据类型: " + data.getClass().getName());
        
        if (data instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = (Map<String, Object>) data;
            
            System.out.println("🔍 " + testDescription + " - 数据字段: " + dataMap.keySet());
            
            for (String field : expectedFields) {
                assertTrue(dataMap.containsKey(field), 
                    testDescription + " - 响应数据应该包含字段 '" + field + "'，实际字段: " + dataMap.keySet());
            }
        }
    }
    
    /**
     * 打印详细的调试信息
     */
    public static void printDebugInfo(String title, Object... keyValuePairs) {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("🔍 " + title);
        System.out.println("=".repeat(50));
        
        for (int i = 0; i < keyValuePairs.length; i += 2) {
            if (i + 1 < keyValuePairs.length) {
                String key = keyValuePairs[i].toString();
                Object value = keyValuePairs[i + 1];
                
                if (value != null) {
                    try {
                        String valueStr = objectMapper.writeValueAsString(value);
                        System.out.println("🔍 " + key + ": " + valueStr);
                    } catch (Exception e) {
                        System.out.println("🔍 " + key + ": " + value.toString());
                    }
                } else {
                    System.out.println("🔍 " + key + ": null");
                }
            }
        }
        System.out.println("=".repeat(50) + "\n");
    }
    
    /**
     * 验证两个对象的特定字段是否相等
     */
    public static void assertFieldEquals(Object obj1, Object obj2, String fieldName, String testDescription) {
        try {
            String field1 = objectMapper.writeValueAsString(obj1);
            String field2 = objectMapper.writeValueAsString(obj2);
            
            System.out.println("🔍 " + testDescription + " - 比较字段 " + fieldName);
            System.out.println("🔍 对象1." + fieldName + ": " + field1);
            System.out.println("🔍 对象2." + fieldName + ": " + field2);
            
            assertEquals(obj1, obj2, 
                testDescription + " - 字段 " + fieldName + " 应该相等，对象1: " + field1 + ", 对象2: " + field2);
                
        } catch (Exception e) {
            assertEquals(obj1, obj2, 
                testDescription + " - 字段 " + fieldName + " 应该相等");
        }
    }
    
    /**
     * 验证业务逻辑的前后状态变化
     */
    public static void assertStateChange(Object beforeState, Object afterState, 
                                       String testDescription, boolean shouldChange) {
        printDebugInfo(testDescription + " - 状态变化检查",
            "操作前状态", beforeState,
            "操作后状态", afterState,
            "预期是否变化", shouldChange
        );
        
        boolean actualChanged = !beforeState.equals(afterState);
        
        if (shouldChange) {
            assertTrue(actualChanged, 
                testDescription + " - 状态应该发生变化，但没有变化。操作前: " + beforeState + ", 操作后: " + afterState);
        } else {
            assertFalse(actualChanged, 
                testDescription + " - 状态不应该发生变化，但发生了变化。操作前: " + beforeState + ", 操作后: " + afterState);
        }
    }

    /**
     * 安全输出中文内容，避免乱码
     */
    public static void safePrintln(String message) {
        try {
            // 尝试使用 UTF-8 编码输出
            System.out.write((message + "\n").getBytes("UTF-8"));
            System.out.flush();
        } catch (Exception e) {
            // 如果失败，使用默认编码
            System.out.println(message);
        }
    }

    /**
     * 安全输出调试信息
     */
    public static void safeDebugPrint(String prefix, Object... keyValuePairs) {
        StringBuilder sb = new StringBuilder();
        sb.append(prefix);
        
        for (int i = 0; i < keyValuePairs.length; i += 2) {
            if (i + 1 < keyValuePairs.length) {
                sb.append(", ").append(keyValuePairs[i]).append(": ").append(keyValuePairs[i + 1]);
            }
        }
        
        safePrintln(sb.toString());
    }
} 