# 开发阶段配置变更说明

## 概述

为了简化开发阶段的复杂性，我们暂时禁用了以下功能：

## 1. MyBatis-Plus 完全移除 ✅

### 变更内容
- 移除了所有 `BaseMapper` 继承
- 清理了实体类中的 MyBatis-Plus 注解 (`@TableName`, `@TableId`, `@TableField`)
- 修改配置从 `mybatis-plus` 改为标准 `mybatis`
- 移除了 `mybatis-plus-boot-starter` 依赖

### 影响
- ✅ 解决了参数绑定错误：`Parameter 'customerName' not found`
- ✅ 统一了技术栈，全部使用传统 MyBatis
- ✅ 简化了开发和维护复杂度

## 2. 分表功能暂时禁用 ✅

### 变更内容
- 注释了以下类的 Spring 注解：
  - `TableShardingConfig` - 分表配置类
  - `TableShardingManager` - 分表管理器
  - `TableShardingController` - 分表控制器
  - `TableShardingServiceImpl` - 分表服务实现
  - `TableShardingMonitor` - 分表监控
  - `ShardingConfiguration` - 分表配置属性

### 配置文件变更
- `application.yml`: 注释了 `include: sharding`
- `application-sharding.yml`: 整个文件被注释

### 数据库表使用
当前直接使用以下固定表名（无分表）：
- `crm_business_customers` - 客户表
- `crm_business_leads` - 线索表  
- `crm_lead_operation_log` - 线索操作日志
- `crm_customer_operation_log` - 客户操作日志

### 影响
- ✅ 简化了开发阶段的数据库设计
- ✅ 避免了动态表名带来的调试复杂性
- ✅ 减少了启动和运行时的依赖

## 恢复指南

### 重新启用 MyBatis-Plus（不推荐）
如果将来需要重新启用 MyBatis-Plus：
1. 恢复 `pom.xml` 中的 `mybatis-plus-boot-starter` 依赖
2. 修改 `application.yml` 中的 `mybatis` 配置为 `mybatis-plus`
3. 恢复实体类中的注解
4. 修改 Mapper 接口继承 `BaseMapper`
5. 调整 XML 中的参数引用

### 重新启用分表功能
当数据量增长需要分表时：
1. 取消注释所有分表相关类的 Spring 注解
2. 恢复 `application.yml` 中的 `include: sharding`
3. 取消注释 `application-sharding.yml` 中的配置
4. 根据实际需求调整分表策略

## 当前状态

- ✅ **编译成功** - 所有代码可以正常编译
- ✅ **功能简化** - 移除了过早优化的复杂性
- ✅ **开发友好** - 数据库结构简单清晰
- ✅ **可扩展** - 所有分表代码都保留，仅是暂时禁用

## 建议

在开发阶段专注于：
1. **业务逻辑实现** - 完善 CRM 核心功能
2. **用户体验** - 优化前端交互
3. **性能基准** - 在单表情况下建立性能基准
4. **测试覆盖** - 确保功能的正确性

当系统稳定且数据量增长时，再考虑重新启用分表功能。

---
*更新时间: 2025-07-29*
*状态: 开发阶段 - 分表功能已禁用*