<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmOrderAssignmentLogMapper">
    
    <resultMap type="CrmOrderAssignmentLog" id="CrmOrderAssignmentLogResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="actionType"    column="action_type"    />
        <result property="fromUserId"    column="from_user_id"    />
        <result property="fromUserName"    column="from_user_name"    />
        <result property="toUserId"    column="to_user_id"    />
        <result property="toUserName"    column="to_user_name"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="reason"    column="reason"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="userAgent"    column="user_agent"    />
    </resultMap>

    <sql id="selectCrmOrderAssignmentLogVo">
        select id, order_id, action_type, from_user_id, from_user_name, to_user_id, to_user_name, 
               operator_id, operator_name, reason, operation_time, ip_address, user_agent 
        from crm_order_assignment_log
    </sql>

    <select id="selectCrmOrderAssignmentLogList" parameterType="CrmOrderAssignmentLog" resultMap="CrmOrderAssignmentLogResult">
        <include refid="selectCrmOrderAssignmentLogVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="actionType != null  and actionType != ''"> and action_type = #{actionType}</if>
            <if test="fromUserId != null "> and from_user_id = #{fromUserId}</if>
            <if test="toUserId != null "> and to_user_id = #{toUserId}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="startTime != null and startTime != ''"> and operation_time &gt;= #{startTime}</if>
            <if test="endTime != null and endTime != ''"> and operation_time &lt;= #{endTime}</if>
            <if test="orderNo != null and orderNo != ''"> 
                and order_id in (select id from crm_order where order_no like concat('%', #{orderNo}, '%'))
            </if>
            <if test="customerName != null and customerName != ''"> 
                and order_id in (select id from crm_order where customer_name like concat('%', #{customerName}, '%'))
            </if>
        </where>
        order by operation_time desc
    </select>
    
    <select id="selectCrmOrderAssignmentLogById" parameterType="Long" resultMap="CrmOrderAssignmentLogResult">
        <include refid="selectCrmOrderAssignmentLogVo"/>
        where id = #{id}
    </select>

    <select id="selectCrmOrderAssignmentLogByOrderId" parameterType="Long" resultMap="CrmOrderAssignmentLogResult">
        <include refid="selectCrmOrderAssignmentLogVo"/>
        where order_id = #{orderId}
        order by operation_time desc
    </select>

    <select id="selectCrmOrderAssignmentLogByUserId" parameterType="Long" resultMap="CrmOrderAssignmentLogResult">
        <include refid="selectCrmOrderAssignmentLogVo"/>
        where to_user_id = #{userId} or from_user_id = #{userId} or operator_id = #{userId}
        order by operation_time desc
    </select>

    <select id="selectCrmOrderAssignmentLogByActionType" parameterType="String" resultMap="CrmOrderAssignmentLogResult">
        <include refid="selectCrmOrderAssignmentLogVo"/>
        where action_type = #{actionType}
        order by operation_time desc
    </select>

    <select id="selectAssignmentStatsByUser" resultMap="CrmOrderAssignmentLogResult">
        select action_type, count(*) as assignment_count, to_user_id, to_user_name
        from crm_order_assignment_log
        where 1=1
        <if test="userId != null">and to_user_id = #{userId}</if>
        <if test="startTime != null and startTime != ''">and operation_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and operation_time &lt;= #{endTime}</if>
        group by action_type, to_user_id, to_user_name
        order by assignment_count desc
    </select>

    <select id="selectLatestAssignmentByOrderId" parameterType="Long" resultMap="CrmOrderAssignmentLogResult">
        <include refid="selectCrmOrderAssignmentLogVo"/>
        where order_id = #{orderId}
        order by operation_time desc
        limit 1
    </select>

    <select id="countTodayGrabsByUserId" parameterType="Long" resultType="int">
        select count(*) from crm_order_assignment_log
        where to_user_id = #{userId} 
        and action_type = 'GRAB'
        and date(operation_time) = curdate()
    </select>

    <select id="countAssignmentsByOrderId" parameterType="Long" resultType="int">
        select count(*) from crm_order_assignment_log
        where order_id = #{orderId}
    </select>

    <select id="selectAssignmentStatistics" resultMap="CrmOrderAssignmentLogResult">
        select action_type, count(*) as assignment_count, 
               date(operation_time) as operation_date,
               to_user_id, to_user_name
        from crm_order_assignment_log
        where 1=1
        <if test="startTime != null and startTime != ''">and operation_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and operation_time &lt;= #{endTime}</if>
        <if test="actionType != null and actionType != ''">and action_type = #{actionType}</if>
        group by action_type, date(operation_time), to_user_id, to_user_name
        order by operation_date desc, assignment_count desc
    </select>

    <select id="selectWorkloadStatistics" resultMap="CrmOrderAssignmentLogResult">
        select to_user_id, to_user_name, count(*) as assignment_count,
               sum(case when action_type = 'ASSIGN' then 1 else 0 end) as assign_count,
               sum(case when action_type = 'GRAB' then 1 else 0 end) as grab_count,
               sum(case when action_type = 'TRANSFER' then 1 else 0 end) as transfer_count
        from crm_order_assignment_log
        where 1=1
        <if test="userIds != null and userIds.size() > 0">
            and to_user_id in
            <foreach item="userId" collection="userIds" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">and operation_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and operation_time &lt;= #{endTime}</if>
        group by to_user_id, to_user_name
        order by assignment_count desc
    </select>
        
    <insert id="insertCrmOrderAssignmentLog" parameterType="CrmOrderAssignmentLog" useGeneratedKeys="true" keyProperty="id">
        insert into crm_order_assignment_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="actionType != null">action_type,</if>
            <if test="fromUserId != null">from_user_id,</if>
            <if test="fromUserName != null">from_user_name,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="toUserName != null">to_user_name,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="reason != null">reason,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="userAgent != null">user_agent,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="actionType != null">#{actionType},</if>
            <if test="fromUserId != null">#{fromUserId},</if>
            <if test="fromUserName != null">#{fromUserName},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="toUserName != null">#{toUserName},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="reason != null">#{reason},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="userAgent != null">#{userAgent},</if>
         </trim>
    </insert>

    <update id="updateCrmOrderAssignmentLog" parameterType="CrmOrderAssignmentLog">
        update crm_order_assignment_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="actionType != null">action_type = #{actionType},</if>
            <if test="fromUserId != null">from_user_id = #{fromUserId},</if>
            <if test="fromUserName != null">from_user_name = #{fromUserName},</if>
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="toUserName != null">to_user_name = #{toUserName},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmOrderAssignmentLogById" parameterType="Long">
        delete from crm_order_assignment_log where id = #{id}
    </delete>

    <delete id="deleteCrmOrderAssignmentLogByIds" parameterType="String">
        delete from crm_order_assignment_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCrmOrderAssignmentLogByOrderId" parameterType="Long">
        delete from crm_order_assignment_log where order_id = #{orderId}
    </delete>

    <insert id="batchInsertCrmOrderAssignmentLog" parameterType="java.util.List">
        insert into crm_order_assignment_log(order_id, action_type, from_user_id, from_user_name, to_user_id, to_user_name, 
                                              operator_id, operator_name, reason, operation_time, ip_address, user_agent)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderId}, #{item.actionType}, #{item.fromUserId}, #{item.fromUserName}, #{item.toUserId}, #{item.toUserName},
             #{item.operatorId}, #{item.operatorName}, #{item.reason}, #{item.operationTime}, #{item.ipAddress}, #{item.userAgent})
        </foreach>
    </insert>

</mapper>
