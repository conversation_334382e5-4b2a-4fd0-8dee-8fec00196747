package com.ruoyi.crm.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 客户联系人关联关系控制器测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class CrmCustomerContactRelationControllerTest {

    @Test
    public void testIntegerToLongConversion() {
        // 模拟前端传来的数据，其中ID是Integer类型
        Map<String, Object> params = new HashMap<>();
        params.put("customerId", 1); // Integer
        params.put("contactIds", Arrays.asList(1, 2, 3)); // List<Integer>
        params.put("relationType", "次要联系人");
        
        // 测试Integer到Long的转换
        @SuppressWarnings("unchecked")
        List<Object> contactIdObjects = (List<Object>) params.get("contactIds");
        
        assertNotNull(contactIdObjects);
        assertFalse(contactIdObjects.isEmpty());
        
        // 模拟控制器中的转换逻辑
        List<Long> contactIds = contactIdObjects.stream()
                .map(obj -> Long.valueOf(obj.toString()))
                .collect(Collectors.toList());
        
        // 验证转换结果
        assertEquals(3, contactIds.size());
        assertTrue(contactIds.get(0) instanceof Long);
        assertEquals(Long.valueOf(1), contactIds.get(0));
        assertEquals(Long.valueOf(2), contactIds.get(1));
        assertEquals(Long.valueOf(3), contactIds.get(2));
        
        // 测试customerId转换
        Long customerId = Long.valueOf(params.get("customerId").toString());
        assertEquals(Long.valueOf(1), customerId);
        
        System.out.println("Integer/Long 转换测试通过");
    }
    
    @Test
    public void testMixedTypeConversion() {
        // 测试混合类型的转换（Integer和Long）
        Map<String, Object> params = new HashMap<>();
        params.put("customerId", 100L); // Long
        params.put("contactIds", Arrays.asList(1, 2L, 3)); // 混合Integer和Long
        
        @SuppressWarnings("unchecked")
        List<Object> contactIdObjects = (List<Object>) params.get("contactIds");
        
        List<Long> contactIds = contactIdObjects.stream()
                .map(obj -> Long.valueOf(obj.toString()))
                .collect(Collectors.toList());
        
        assertEquals(3, contactIds.size());
        assertEquals(Long.valueOf(1), contactIds.get(0));
        assertEquals(Long.valueOf(2), contactIds.get(1));
        assertEquals(Long.valueOf(3), contactIds.get(2));
        
        Long customerId = Long.valueOf(params.get("customerId").toString());
        assertEquals(Long.valueOf(100), customerId);
        
        System.out.println("混合类型转换测试通过");
    }
}