package com.ruoyi.common.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.mapper.CrmCustomerMapper;
import com.ruoyi.common.service.ICrmCustomerMatchingService;

import lombok.extern.slf4j.Slf4j;

/**
 * CRM客户匹配服务实现类
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Slf4j
@Service
public class CrmCustomerMatchingServiceImpl implements ICrmCustomerMatchingService {

    @Autowired
    private CrmCustomerMapper crmCustomerMapper;

    /**
     * 根据电话号码匹配客户
     * 
     * @param phone 电话号码
     * @return 匹配结果
     */
    @Override
    public CustomerMatchResult matchCustomerByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return CustomerMatchResult.failure("电话号码为空");
        }
        
        try {
            // 1. 精确匹配
            CrmCustomer customer = crmCustomerMapper.findByPhone(phone);
            if (customer != null) {
                log.info("精确匹配到客户: {}, 电话: {}", customer.getCustomerName(), phone);
                return CustomerMatchResult.success(customer, MATCH_TYPE_EXACT_PHONE, 100.0);
            }
            
            // 2. 标准化电话号码后模糊匹配
            String normalizedPhone = normalizePhone(phone);
            customer = crmCustomerMapper.findByNormalizedPhone(normalizedPhone);
            if (customer != null) {
                log.info("模糊匹配到客户: {}, 标准化电话: {}", customer.getCustomerName(), normalizedPhone);
                return CustomerMatchResult.success(customer, MATCH_TYPE_FUZZY_PHONE, 85.0);
            }
            
            // 3. 部分匹配（去除国家代码、区号等）
            List<String> phoneVariants = generatePhoneVariants(phone);
            for (String variant : phoneVariants) {
                customer = crmCustomerMapper.findByPhoneLike(variant);
                if (customer != null) {
                    log.info("部分匹配到客户: {}, 电话变体: {}", customer.getCustomerName(), variant);
                    return CustomerMatchResult.success(customer, MATCH_TYPE_FUZZY_PHONE, 70.0);
                }
            }
            
            log.info("未匹配到客户，电话: {}", phone);
            return CustomerMatchResult.failure("未找到匹配的客户");
            
        } catch (Exception e) {
            log.error("电话号码匹配异常: {}", phone, e);
            return CustomerMatchResult.failure("匹配过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 根据客户名称匹配客户
     * 
     * @param customerName 客户名称
     * @return 匹配结果列表
     */
    @Override
    public List<CustomerMatchResult> matchCustomerByName(String customerName) {
        List<CustomerMatchResult> results = new ArrayList<>();
        
        if (!StringUtils.hasText(customerName)) {
            return results;
        }
        
        try {
            // 1. 精确匹配
            List<CrmCustomer> exactMatches = crmCustomerMapper.findByCustomerName(customerName);
            for (CrmCustomer customer : exactMatches) {
                results.add(CustomerMatchResult.success(customer, MATCH_TYPE_EXACT_NAME, 100.0));
            }
            
            // 2. 模糊匹配
            String normalizedName = normalizeCustomerName(customerName);
            List<CrmCustomer> fuzzyMatches = crmCustomerMapper.findByCustomerNameLike(normalizedName);
            for (CrmCustomer customer : fuzzyMatches) {
                // 避免重复添加精确匹配的结果
                if (!exactMatches.contains(customer)) {
                    double similarity = calculateNameSimilarity(normalizedName, customer.getCustomerName());
                    if (similarity > 60.0) { // 相似度阈值
                        results.add(CustomerMatchResult.success(customer, MATCH_TYPE_FUZZY_NAME, similarity));
                    }
                }
            }
            
            log.info("客户名称匹配结果数量: {}, 查询名称: {}", results.size(), customerName);
            
        } catch (Exception e) {
            log.error("客户名称匹配异常: {}", customerName, e);
        }
        
        return results;
    }

    /**
     * 根据邮箱匹配客户
     * 
     * @param email 邮箱
     * @return 匹配结果
     */
    @Override
    public CustomerMatchResult matchCustomerByEmail(String email) {
        if (!StringUtils.hasText(email) || !isValidEmail(email)) {
            return CustomerMatchResult.failure("邮箱格式无效");
        }
        
        try {
            CrmCustomer customer = crmCustomerMapper.findByEmail(email.toLowerCase());
            if (customer != null) {
                log.info("邮箱匹配到客户: {}, 邮箱: {}", customer.getCustomerName(), email);
                return CustomerMatchResult.success(customer, MATCH_TYPE_EXACT_EMAIL, 100.0);
            }
            
            return CustomerMatchResult.failure("未找到匹配的客户");
            
        } catch (Exception e) {
            log.error("邮箱匹配异常: {}", email, e);
            return CustomerMatchResult.failure("匹配过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 综合匹配客户（电话、邮箱、名称）
     * 
     * @param phone 电话号码
     * @param email 邮箱
     * @param customerName 客户名称
     * @return 匹配结果
     */
    @Override
    public CustomerMatchResult matchCustomerComprehensive(String phone, String email, String customerName) {
        log.info("开始综合匹配客户 - 电话: {}, 邮箱: {}, 名称: {}", phone, email, customerName);
        
        // 1. 优先级最高：电话号码匹配
        if (StringUtils.hasText(phone)) {
            CustomerMatchResult phoneResult = matchCustomerByPhone(phone);
            if (phoneResult.isMatched()) {
                phoneResult.setMatchType(MATCH_TYPE_COMPREHENSIVE);
                phoneResult.setReason("通过电话号码匹配");
                return phoneResult;
            }
        }
        
        // 2. 优先级第二：邮箱匹配
        if (StringUtils.hasText(email)) {
            CustomerMatchResult emailResult = matchCustomerByEmail(email);
            if (emailResult.isMatched()) {
                emailResult.setMatchType(MATCH_TYPE_COMPREHENSIVE);
                emailResult.setReason("通过邮箱匹配");
                return emailResult;
            }
        }
        
        // 3. 优先级第三：客户名称匹配（取最高相似度的结果）
        if (StringUtils.hasText(customerName)) {
            List<CustomerMatchResult> nameResults = matchCustomerByName(customerName);
            if (!nameResults.isEmpty()) {
                CustomerMatchResult bestMatch = nameResults.get(0);
                for (CustomerMatchResult result : nameResults) {
                    if (result.getConfidence() > bestMatch.getConfidence()) {
                        bestMatch = result;
                    }
                }
                
                if (bestMatch.getConfidence() > 80.0) { // 高置信度阈值
                    bestMatch.setMatchType(MATCH_TYPE_COMPREHENSIVE);
                    bestMatch.setReason("通过客户名称匹配");
                    return bestMatch;
                }
            }
        }
        
        log.info("综合匹配未找到客户");
        return CustomerMatchResult.failure("综合匹配未找到客户");
    }

    /**
     * 智能匹配客户（使用多种算法）
     * 
     * @param customerInfo 客户信息
     * @return 匹配结果列表
     */
    @Override
    public List<CustomerMatchResult> smartMatchCustomer(CustomerMatchInfo customerInfo) {
        List<CustomerMatchResult> results = new ArrayList<>();
        
        // 综合匹配
        CustomerMatchResult comprehensiveResult = matchCustomerComprehensive(
            customerInfo.getPhone(), 
            customerInfo.getEmail(), 
            customerInfo.getCustomerName()
        );
        
        if (comprehensiveResult.isMatched()) {
            results.add(comprehensiveResult);
        }
        
        // TODO: 实现更多智能匹配算法
        // - 基于公司名称匹配
        // - 基于地址匹配
        // - 基于行业匹配
        // - 机器学习相似度计算
        
        return results;
    }

    /**
     * 检查是否为新客户
     * 
     * @param phone 电话号码
     * @param email 邮箱
     * @param customerName 客户名称
     * @return 是否为新客户
     */
    @Override
    public boolean isNewCustomer(String phone, String email, String customerName) {
        CustomerMatchResult result = matchCustomerComprehensive(phone, email, customerName);
        return !result.isMatched();
    }

    /**
     * 获取客户的负责人
     * 
     * @param customerId 客户ID
     * @return 负责人信息
     */
    @Override
    public CustomerOwnerInfo getCustomerOwner(Long customerId) {
        try {
            CrmCustomer customer = crmCustomerMapper.selectCrmCustomerById(customerId);
            if (customer == null) {
                return null;
            }
            
            CustomerOwnerInfo ownerInfo = new CustomerOwnerInfo();
            if (customer.getResponsiblePersonId() != null) {
                ownerInfo.setOwnerId(Long.valueOf(customer.getResponsiblePersonId()));
                ownerInfo.setOwnerName(customer.getResponsiblePersonId()); // 暂时使用ID作为名称
            }
            // TODO: 查询更详细的负责人信息
            
            return ownerInfo;
            
        } catch (Exception e) {
            log.error("获取客户负责人异常, 客户ID: {}", customerId, e);
            return null;
        }
    }

    /**
     * 检查负责人状态
     * 
     * @param ownerId 负责人ID
     * @return 负责人状态信息
     */
    @Override
    public OwnerStatusInfo checkOwnerStatus(Long ownerId) {
        // TODO: 实现负责人状态检查
        OwnerStatusInfo statusInfo = new OwnerStatusInfo();
        statusInfo.setOwnerId(ownerId);
        statusInfo.setActive(true); // 默认激活
        statusInfo.setOnline(false); // 默认离线
        statusInfo.setCurrentWorkload(0);
        statusInfo.setMaxWorkload(100);
        statusInfo.setStatus("ACTIVE");
        
        return statusInfo;
    }

    /**
     * 标准化电话号码
     * 
     * @param phone 原始电话号码
     * @return 标准化后的电话号码
     */
    @Override
    public String normalizePhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return "";
        }
        
        // 移除所有非数字字符
        String normalized = phone.replaceAll("[^0-9]", "");
        
        // 移除国家代码
        if (normalized.startsWith("86") && normalized.length() > 11) {
            normalized = normalized.substring(2);
        }
        
        // 移除区号（简单处理）
        if (normalized.startsWith("0") && normalized.length() > 11) {
            normalized = normalized.substring(1);
        }
        
        return normalized;
    }

    /**
     * 标准化客户名称
     * 
     * @param customerName 原始客户名称
     * @return 标准化后的客户名称
     */
    @Override
    public String normalizeCustomerName(String customerName) {
        if (!StringUtils.hasText(customerName)) {
            return "";
        }
        
        // 去除首尾空格，转换为小写
        String normalized = customerName.trim().toLowerCase();
        
        // 移除常见的公司后缀
        String[] companySuffixes = {"有限公司", "股份有限公司", "集团", "公司", "企业", "工厂", 
                                   "ltd", "co", "inc", "corp", "group", "company"};
        
        for (String suffix : companySuffixes) {
            if (normalized.endsWith(suffix)) {
                normalized = normalized.substring(0, normalized.length() - suffix.length()).trim();
            }
        }
        
        return normalized;
    }

    /**
     * 计算客户匹配相似度
     * 
     * @param customer1 客户1
     * @param customer2 客户2
     * @return 相似度分数（0-100）
     */
    @Override
    public double calculateSimilarity(CrmCustomer customer1, CrmCustomer customer2) {
        if (customer1 == null || customer2 == null) {
            return 0.0;
        }
        
        double totalScore = 0.0;
        int factors = 0;
        
        // 客户名称相似度（权重40%）
        if (StringUtils.hasText(customer1.getCustomerName()) && StringUtils.hasText(customer2.getCustomerName())) {
            double nameScore = calculateNameSimilarity(customer1.getCustomerName(), customer2.getCustomerName());
            totalScore += nameScore * 0.4;
            factors++;
        }
        
        // 电话号码相似度（权重30%）
        if (StringUtils.hasText(customer1.getPhone()) && StringUtils.hasText(customer2.getPhone())) {
            double phoneScore = customer1.getPhone().equals(customer2.getPhone()) ? 100.0 : 0.0;
            totalScore += phoneScore * 0.3;
            factors++;
        }
        
        // 邮箱相似度（权重20%）
        if (StringUtils.hasText(customer1.getEmail()) && StringUtils.hasText(customer2.getEmail())) {
            double emailScore = customer1.getEmail().equalsIgnoreCase(customer2.getEmail()) ? 100.0 : 0.0;
            totalScore += emailScore * 0.2;
            factors++;
        }
        
        // 地址相似度（权重10%）
        if (StringUtils.hasText(customer1.getCustomerAddress()) && StringUtils.hasText(customer2.getCustomerAddress())) {
            double addressScore = calculateStringSimilarity(customer1.getCustomerAddress(), customer2.getCustomerAddress());
            totalScore += addressScore * 0.1;
            factors++;
        }
        
        return factors > 0 ? totalScore : 0.0;
    }

    /**
     * 生成电话号码变体
     */
    private List<String> generatePhoneVariants(String phone) {
        List<String> variants = new ArrayList<>();
        String normalized = normalizePhone(phone);
        
        if (normalized.length() >= 7) {
            // 后7位
            variants.add(normalized.substring(normalized.length() - 7));
        }
        
        if (normalized.length() >= 8) {
            // 后8位
            variants.add(normalized.substring(normalized.length() - 8));
        }
        
        return variants;
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        Pattern pattern = Pattern.compile(emailRegex);
        return pattern.matcher(email).matches();
    }

    /**
     * 计算名称相似度
     */
    private double calculateNameSimilarity(String name1, String name2) {
        if (name1 == null || name2 == null) {
            return 0.0;
        }
        
        String normalized1 = normalizeCustomerName(name1);
        String normalized2 = normalizeCustomerName(name2);
        
        if (normalized1.equals(normalized2)) {
            return 100.0;
        }
        
        return calculateStringSimilarity(normalized1, normalized2);
    }

    /**
     * 计算字符串相似度（使用编辑距离算法）
     */
    private double calculateStringSimilarity(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return 0.0;
        }
        
        if (str1.equals(str2)) {
            return 100.0;
        }
        
        int maxLength = Math.max(str1.length(), str2.length());
        if (maxLength == 0) {
            return 100.0;
        }
        
        int editDistance = calculateEditDistance(str1, str2);
        return (1.0 - (double) editDistance / maxLength) * 100.0;
    }

    /**
     * 计算编辑距离
     */
    private int calculateEditDistance(String str1, String str2) {
        int m = str1.length();
        int n = str2.length();
        
        int[][] dp = new int[m + 1][n + 1];
        
        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
}
