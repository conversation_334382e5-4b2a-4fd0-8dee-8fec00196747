package com.ruoyi.common.domain.entity;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户认领限制对象 crm_claim_limits
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmClaimLimits extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 限制ID */
    private Long id;

    /** 用户ID（为空表示全局限制） */
    private Long userId;

    /** 角色ID（为空表示全局限制） */
    private Long roleId;

    /** 每日最大认领数 */
    private Integer maxClaimDaily;

    /** 最大持有客户数 */
    private Integer maxClaimTotal;

    /** 是否启用 */
    private Boolean enabled;
}