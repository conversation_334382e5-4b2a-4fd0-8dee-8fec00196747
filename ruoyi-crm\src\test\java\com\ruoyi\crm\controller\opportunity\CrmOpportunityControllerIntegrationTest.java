package com.ruoyi.crm.controller;

import static com.ruoyi.crm.controller.TestAssertionHelper.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.math.BigDecimal;
import java.util.Date;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmOpportunity;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.controller.CrmOpportunityController.AssignForm;
import com.ruoyi.crm.controller.CrmOpportunityController.StageAdvanceForm;
import com.ruoyi.crm.controller.CrmOpportunityController.ConvertForm;
import com.ruoyi.crm.service.ICrmOpportunityService;

/**
 * CrmOpportunityController 集成测试类
 * 使用真实的数据库和完整的Spring上下文进行测试
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("商机控制器集成测试")
class CrmOpportunityControllerIntegrationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(CrmOpportunityControllerIntegrationTest.class);
    
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmOpportunityService crmOpportunityService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        // 只设置MockMvc，不创建测试数据
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .alwaysDo(print())
                .build();
    }

    // 保留工具方法，供需要的测试使用
    private CrmOpportunity createTestOpportunity(String opportunityName, String customerName) {
        CrmOpportunity testOpportunity = new CrmOpportunity();
        testOpportunity.setOpportunityName(opportunityName);
        testOpportunity.setCustomerName(customerName);
        testOpportunity.setManagerId(1L);
        testOpportunity.setOpportunityAmount(BigDecimal.valueOf(100000));
        testOpportunity.setOpportunityStage("initial_contact");
        testOpportunity.setWinRate(BigDecimal.valueOf(10));
        testOpportunity.setExpectedCloseDate(new Date());
        testOpportunity.setOpportunitySource("online");
        testOpportunity.setOpportunityType("new_business");
        testOpportunity.setRemarks("集成测试用例");

        // 插入测试数据
        crmOpportunityService.insertCrmOpportunity(testOpportunity);
        assertNotNull(testOpportunity.getId(), "测试数据创建失败");
        return testOpportunity;
    }

    private void cleanupTestOpportunity(Long opportunityId) {
        if (opportunityId != null) {
            try {
                crmOpportunityService.deleteCrmOpportunityById(opportunityId);
            } catch (Exception e) {
                // 忽略清理错误
            }
        }
    }

    @Nested
    @DisplayName("商机CRUD集成测试")
    class CrudIntegrationTests {

        @Test
        @DisplayName("完整的CRUD流程测试")
        void testFullCrudFlow() throws Exception {
            // 这个测试自己创建数据，不需要预置数据
            // 1. 创建商机
            CrmOpportunity newOpportunity = new CrmOpportunity();
            newOpportunity.setOpportunityName("CRUD测试商机");
            newOpportunity.setCustomerName("CRUD测试客户");
            newOpportunity.setManagerId(1L);
            newOpportunity.setOpportunityAmount(BigDecimal.valueOf(50000));
            newOpportunity.setOpportunityStage("initial_contact");
            newOpportunity.setWinRate(BigDecimal.valueOf(15));
            newOpportunity.setExpectedCloseDate(new Date());
            newOpportunity.setOpportunitySource("referral");
            newOpportunity.setOpportunityType("existing_customer");
            newOpportunity.setRemarks("CRUD测试备注");

            MvcResult createResult = mockMvc.perform(post("/front/crm/opportunities")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newOpportunity)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            
            // 方案1：直接从 JSON path 获取 ID（最简单）
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdOpportunityId = rootNode.path("data").path("id").asLong();
            
            // 方案2：如果需要完整的 CrmOpportunity 对象
            JsonNode dataNode = rootNode.path("data");
            CrmOpportunity createdOpportunity = objectMapper.treeToValue(dataNode, CrmOpportunity.class);
            // 验证两种方式得到的 ID 一致
            assertEquals(createdOpportunityId, createdOpportunity.getId());
            
            assertNotNull(createdOpportunityId, "创建商机后应返回ID");
            logger.warn("···createdOpportunityId: {}", createdOpportunityId);
            logger.warn("···createdOpportunity ！！！: {}", createdOpportunity);
            
            try {
                // 2. 查询单个商机
                mockMvc.perform(get("/front/crm/opportunities/{id}", createdOpportunityId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.opportunityName").value("CRUD测试商机"))
                        .andExpect(jsonPath("$.data.customerName").value("CRUD测试客户"));

                // 3. 修改商机
                newOpportunity.setId(createdOpportunityId);
                newOpportunity.setOpportunityName("修改后的商机名称");
                newOpportunity.setRemarks("已修改");

                mockMvc.perform(put("/front/crm/opportunities")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newOpportunity)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证修改结果
                mockMvc.perform(get("/front/crm/opportunities/{id}", createdOpportunityId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.data.opportunityName").value("修改后的商机名称"))
                        .andExpect(jsonPath("$.data.remarks").value("已修改"));

                // 5. 删除商机
                mockMvc.perform(delete("/front/crm/opportunities/{ids}", createdOpportunityId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 6. 验证删除结果（查询应该返回null或404）
                CrmOpportunity deletedOpportunity = crmOpportunityService.selectCrmOpportunityById(createdOpportunityId);
                assertNull(deletedOpportunity, "删除后查询应该返回null");

            } finally {
                // 确保清理测试数据
                cleanupTestOpportunity(createdOpportunityId);
            }
        }

        @Test
        @DisplayName("查询商机列表 - 带分页和筛选")
        void testGetOpportunityListWithFilters() throws Exception {
            // 这个测试需要预置数据来验证筛选功能
            CrmOpportunity testOpportunity = createTestOpportunity("列表测试商机", "列表测试客户");
            Long testOpportunityId = testOpportunity.getId();
            
            try {
                MvcResult result = mockMvc.perform(get("/front/crm/opportunities/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .param("opportunityName", "列表测试")
                        .param("opportunityStage", "initial_contact"))
                        .andExpect(status().isOk())
                        .andReturn();

                String responseContent = result.getResponse().getContentAsString();
                TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
                // 打印响应内容 json格式
                String jsonResponse = objectMapper.writeValueAsString(response);
                // 使用 UTF-8 编码输出，避免乱码
                try {
                    System.out.write(("🔍 响应内容: " + jsonResponse + "\n").getBytes("UTF-8"));
                    System.out.flush();
                } catch (Exception e) {
                    System.out.println("Response content: " + jsonResponse);
                }
                assertNotNull(response);
                assertEquals(200, response.getCode());
                assertNotNull(response.getRows());
                assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
            } finally {
                cleanupTestOpportunity(testOpportunityId);
            }
        }
    }

    @Nested
    @DisplayName("商机分配集成测试")
    class AssignIntegrationTests {
        
        private CrmOpportunity testOpportunity;
        private Long testOpportunityId;
        
        @BeforeEach
        void setUpAssignTests() {
            // 只有分配测试需要预置数据
            testOpportunity = createTestOpportunity("分配测试商机", "分配测试客户");
            testOpportunityId = testOpportunity.getId();
        }
        
        @AfterEach
        void tearDownAssignTests() {
            cleanupTestOpportunity(testOpportunityId);
        }

        @Test
        @DisplayName("商机分配流程测试")
        void testAssignFlow() throws Exception {
            // 1. 确保测试商机存在
            CrmOpportunity originalOpportunity = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
            assertNotNull(originalOpportunity);
            Long originalManagerId = originalOpportunity.getManagerId();

            // 2. 执行分配
            AssignForm assignForm = new AssignForm();
            assignForm.setOpportunityId(testOpportunityId);
            assignForm.setNewOwnerId(2L);

            mockMvc.perform(post("/front/crm/opportunities/assign")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(assignForm)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            // 3. 验证分配结果
            CrmOpportunity updatedOpportunity = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
            assertNotNull(updatedOpportunity);
            assertEquals(2L, updatedOpportunity.getManagerId());
            assertNotEquals(originalManagerId, updatedOpportunity.getManagerId());
        }

        @Test
        @DisplayName("分配不存在的商机")
        void testAssignNonExistentOpportunity() throws Exception {
            AssignForm assignForm = new AssignForm();
            assignForm.setOpportunityId(99999L);
            assignForm.setNewOwnerId(2L);

            mockMvc.perform(post("/front/crm/opportunities/assign")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(assignForm)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500));
        }
    }

    @Nested
    @DisplayName("商机阶段推进集成测试")
    class StageAdvanceIntegrationTests {
        
        private CrmOpportunity testOpportunity;
        private Long testOpportunityId;
        
        @BeforeEach
        void setUpStageTests() {
            // 阶段推进测试需要预置数据
            testOpportunity = createTestOpportunity("阶段推进测试商机", "阶段推进测试客户");
            testOpportunityId = testOpportunity.getId();
        }
        
        @AfterEach
        void tearDownStageTests() {
            cleanupTestOpportunity(testOpportunityId);
        }

        @Test
        @DisplayName("商机阶段推进流程 - 从初期接触到需求确认")
        void testStageAdvanceFlow() throws Exception {
            // 1. 获取当前阶段
            CrmOpportunity originalOpportunity = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
            assertNotNull(originalOpportunity);
            assertEquals("initial_contact", originalOpportunity.getOpportunityStage());

            // 2. 推进到需求确认阶段
            StageAdvanceForm stageForm = new StageAdvanceForm();
            stageForm.setOpportunityId(testOpportunityId);
            stageForm.setNewStage("qualification");
            stageForm.setRemarks("推进到需求确认阶段");

            mockMvc.perform(post("/front/crm/opportunities/advance-stage")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(stageForm)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            // 3. 验证阶段推进结果
            CrmOpportunity updatedOpportunity = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
            assertNotNull(updatedOpportunity);
            assertEquals("qualification", updatedOpportunity.getOpportunityStage());
        }

        @Test
        @DisplayName("推进到无效阶段")
        void testAdvanceToInvalidStage() throws Exception {
            StageAdvanceForm stageForm = new StageAdvanceForm();
            stageForm.setOpportunityId(testOpportunityId);
            stageForm.setNewStage("invalid_stage");

            mockMvc.perform(post("/front/crm/opportunities/advance-stage")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(stageForm)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500));
        }
    }

    @Nested
    @DisplayName("商机转化集成测试")
    class ConvertIntegrationTests {
        
        private CrmOpportunity testOpportunity;
        private Long testOpportunityId;
        
        @BeforeEach
        void setUpConvertTests() {
            // 转化测试需要预置数据
            testOpportunity = createTestOpportunity("转化测试商机", "转化测试客户");
            testOpportunityId = testOpportunity.getId();
        }
        
        @AfterEach
        void tearDownConvertTests() {
            cleanupTestOpportunity(testOpportunityId);
        }

        @Test
        @DisplayName("商机转化为合同流程 - 完整验证")
        void testConvertToContract_Success() throws Exception {
            // 1. 获取转化前的商机状态
            CrmOpportunity originalOpportunity = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
            assertNotNull(originalOpportunity, "测试商机不存在");
            String originalStage = originalOpportunity.getOpportunityStage();
            
            System.out.println("🔍 转化前商机阶段: " + originalStage);
            System.out.println("🔍 转化前商机信息: " + objectMapper.writeValueAsString(originalOpportunity));

            // 2. 准备转化数据
            ConvertForm convertForm = new ConvertForm();
            convertForm.setOpportunityId(testOpportunityId);
            convertForm.setContractName("转化生成的新合同");
            convertForm.setRemarks("商机转化为合同测试");

            System.out.println("🔍 转化请求数据: " + objectMapper.writeValueAsString(convertForm));

            // 3. 执行转化并获取详细响应
            MvcResult result = mockMvc.perform(post("/front/crm/opportunities/convert-to-contract")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(convertForm)))
                    .andExpect(status().isOk())
                    .andReturn();

            // 4. 解析并验证响应
            String responseContent = result.getResponse().getContentAsString();
            System.out.println("🔍 转化响应内容: " + responseContent);
            
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            // 验证响应结构
            assertNotNull(response, "响应不能为空");
            System.out.println("🔍 响应码: " + response.get("code"));
            System.out.println("🔍 响应消息: " + response.get("msg"));
            System.out.println("🔍 响应数据: " + response.get("data"));
            
            // 如果失败，打印详细错误信息
            if (!response.isSuccess()) {
                fail("商机转化失败: 错误码=" + response.get("code") + 
                     ", 错误信息=" + response.get("msg") + 
                     ", 响应数据=" + response.get("data"));
            }
            
            // 验证响应码和消息
            assertEquals(200, ((Integer) response.get("code")).intValue(), 
                "转化应该成功，但返回错误码: " + response.get("code") + ", 错误信息: " + response.get("msg"));

            // 5. 验证数据库中商机状态已改变
            CrmOpportunity convertedOpportunity = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
            assertNotNull(convertedOpportunity, "转化后商机不应该被删除");
            
            System.out.println("🔍 转化后商机阶段: " + convertedOpportunity.getOpportunityStage());
            System.out.println("🔍 转化后商机信息: " + objectMapper.writeValueAsString(convertedOpportunity));
            
            // 验证阶段变为赢单
            assertEquals("won", convertedOpportunity.getOpportunityStage(), "商机应该转化为赢单状态");
            assertEquals(BigDecimal.valueOf(100), convertedOpportunity.getWinRate(), "赢单率应该为100%");
        }

        @Test
        @DisplayName("商机转化 - 参数验证测试")
        void testConvertToContract_ParameterValidation() throws Exception {
            // 这些测试不需要预置数据，测试参数验证
            // 测试缺少必要参数的情况
            System.out.println("\n🔍 测试1: 空的转化请求");
            ConvertForm emptyForm = new ConvertForm();
            
            MvcResult emptyResult = mockMvc.perform(post("/front/crm/opportunities/convert-to-contract")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(emptyForm)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String emptyResponse = emptyResult.getResponse().getContentAsString();
            System.out.println("🔍 空请求响应: " + emptyResponse);
            
            AjaxResult emptyResult_parsed = objectMapper.readValue(emptyResponse, AjaxResult.class);
            assertFalse(emptyResult_parsed.isSuccess(), 
                "空请求应该失败，但返回: " + emptyResponse);

            // 测试缺少商机ID的情况
            System.out.println("\n🔍 测试2: 缺少商机ID");
            ConvertForm noOpportunityIdForm = new ConvertForm();
            noOpportunityIdForm.setContractName("测试合同");
            
            MvcResult noOpportunityIdResult = mockMvc.perform(post("/front/crm/opportunities/convert-to-contract")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(noOpportunityIdForm)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String noOpportunityIdResponse = noOpportunityIdResult.getResponse().getContentAsString();
            System.out.println("🔍 缺少商机ID响应: " + noOpportunityIdResponse);
            
            AjaxResult noOpportunityIdResult_parsed = objectMapper.readValue(noOpportunityIdResponse, AjaxResult.class);
            assertFalse(noOpportunityIdResult_parsed.isSuccess(), 
                "缺少商机ID应该失败，但返回: " + noOpportunityIdResponse);
        }

        @Test
        @DisplayName("转化不存在的商机")
        void testConvertNonExistentOpportunity() throws Exception {
            // 这个测试不需要预置数据，测试不存在的商机
            Long nonExistentOpportunityId = 99999L;
            
            System.out.println("🔍 测试转化不存在的商机ID: " + nonExistentOpportunityId);
            
            ConvertForm convertForm = new ConvertForm();
            convertForm.setOpportunityId(nonExistentOpportunityId);
            convertForm.setContractName("测试合同");

            MvcResult result = mockMvc.perform(post("/front/crm/opportunities/convert-to-contract")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(convertForm)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            System.out.println("🔍 不存在商机转化响应: " + responseContent);
            
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            // 验证应该返回错误
            assertFalse(response.isSuccess(), 
                "转化不存在的商机应该失败，但返回成功: " + responseContent);
            
            // 验证错误信息
            Integer code = (Integer) response.get("code");
            String msg = (String) response.get("msg");
            
            assertTrue(code == 500 || code == 404 || !response.isSuccess(), 
                "错误码应该表示失败，实际错误码: " + code);
            
            assertTrue(msg != null && (
                msg.contains("商机不存在") || 
                msg.contains("not found") || 
                msg.contains("不存在")
            ), "错误信息应该明确说明商机不存在，实际错误信息: " + msg);
        }

        @Test
        @DisplayName("商机转化 - 重复转化测试")
        void testConvertAlreadyConvertedOpportunity() throws Exception {
            // 这个测试需要自己的测试数据来进行重复转化测试
            CrmOpportunity repeatTestOpportunity = createTestOpportunity("重复转化测试商机", "重复转化测试客户");
            Long repeatTestOpportunityId = repeatTestOpportunity.getId();
            
            try {
                // 先成功转化一次
                ConvertForm convertForm = new ConvertForm();
                convertForm.setOpportunityId(repeatTestOpportunityId);
                convertForm.setContractName("首次转化合同");
                convertForm.setRemarks("首次转化测试");

                System.out.println("🔍 首次转化请求: " + objectMapper.writeValueAsString(convertForm));

                // 第一次转化
                MvcResult firstResult = mockMvc.perform(post("/front/crm/opportunities/convert-to-contract")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(convertForm)))
                        .andExpect(status().isOk())
                        .andReturn();

                String firstResponse = firstResult.getResponse().getContentAsString();
                System.out.println("🔍 首次转化响应: " + firstResponse);

                // 再次尝试转化同一商机
                convertForm.setContractName("重复转化合同");
                convertForm.setRemarks("重复转化测试");
                
                System.out.println("🔍 重复转化请求: " + objectMapper.writeValueAsString(convertForm));

                MvcResult secondResult = mockMvc.perform(post("/front/crm/opportunities/convert-to-contract")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(convertForm)))
                        .andExpect(status().isOk())
                        .andReturn();

                String secondResponse = secondResult.getResponse().getContentAsString();
                System.out.println("🔍 重复转化响应: " + secondResponse);
                
                AjaxResult secondResult_parsed = objectMapper.readValue(secondResponse, AjaxResult.class);
                
                // 重复转化应该失败或给出明确提示
                if (secondResult_parsed.isSuccess()) {
                    System.out.println("⚠️  注意: 系统允许重复转化，可能需要检查业务逻辑");
                } else {
                    String msg = (String) secondResult_parsed.get("msg");
                    assertTrue(msg != null && (
                        msg.contains("已赢单") || 
                        msg.contains("重复") || 
                        msg.contains("已经")
                    ), "重复转化的错误信息应该明确，实际错误信息: " + msg);
                }
            } finally {
                cleanupTestOpportunity(repeatTestOpportunityId);
            }
        }
    }

    @Nested
    @DisplayName("数据权限测试")
    class DataPermissionTests {

        @Test
        @DisplayName("我负责的商机列表")
        void testMyOpportunityList() throws Exception {
            mockMvc.perform(get("/front/crm/opportunities/my")
                    .param("pageNum", "1")
                    .param("pageSize", "10"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
        }

        @Test
        @DisplayName("下属的商机列表")
        void testSubordinateOpportunityList() throws Exception {
            mockMvc.perform(get("/front/crm/opportunities/subordinate")
                    .param("pageNum", "1")
                    .param("pageSize", "10"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
        }

        @Test
        @DisplayName("我关注的商机列表")
        void testFollowedOpportunityList() throws Exception {
            mockMvc.perform(get("/front/crm/opportunities/followed")
                    .param("pageNum", "1")
                    .param("pageSize", "10"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
        }
    }

    @Nested
    @DisplayName("导出功能集成测试")
    class ExportIntegrationTests {

        @Test
        @DisplayName("导出商机数据")
        void testExportOpportunities() throws Exception {
            // 导出测试不需要特定的预置数据，使用查询条件即可
            CrmOpportunity queryOpportunity = new CrmOpportunity();
            queryOpportunity.setOpportunityStage("initial_contact");

            MvcResult result = mockMvc.perform(post("/front/crm/opportunities/export")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(queryOpportunity)))
                    .andExpect(status().isOk())
                    .andReturn();

            // 验证返回结果包含导出文件信息
            String responseContent = result.getResponse().getContentAsString();
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            assertNotNull(response);
            assertEquals(200, (Integer) response.get("code"));
        }
    }

    @Nested
    @DisplayName("异常处理集成测试")
    class ExceptionIntegrationTests {
        
        private CrmOpportunity testOpportunity;
        private Long testOpportunityId;
        
        @BeforeEach
        void setUpExceptionTests() {
            // 异常测试需要预置数据
            testOpportunity = createTestOpportunity("异常测试商机", "异常测试客户");
            testOpportunityId = testOpportunity.getId();
        }
        
        @AfterEach
        void tearDownExceptionTests() {
            cleanupTestOpportunity(testOpportunityId);
        }

        @Test
        @DisplayName("无效的JSON请求体")
        void testInvalidJsonRequest() throws Exception {
            mockMvc.perform(post("/front/crm/opportunities")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content("{ invalid json"))
                    .andExpect(status().is4xxClientError());
        }

        @Test
        @DisplayName("缺少必要字段的请求")
        void testMissingRequiredFields() throws Exception {
            CrmOpportunity invalidOpportunity = new CrmOpportunity();
            // 不设置必要字段

            mockMvc.perform(post("/front/crm/opportunities")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidOpportunity)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500)); // 应该返回业务错误
        }

        @Test
        @DisplayName("并发修改测试")
        void testConcurrentModification() throws Exception {
            // 模拟两个用户同时修改同一商机
            CrmOpportunity opportunity1 = new CrmOpportunity();
            opportunity1.setId(testOpportunityId);
            opportunity1.setOpportunityName("用户1修改");
            opportunity1.setRemarks("用户1的修改");

            CrmOpportunity opportunity2 = new CrmOpportunity();
            opportunity2.setId(testOpportunityId);
            opportunity2.setOpportunityName("用户2修改");
            opportunity2.setRemarks("用户2的修改");

            // 第一个修改
            mockMvc.perform(put("/front/crm/opportunities")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(opportunity1)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            // 第二个修改
            mockMvc.perform(put("/front/crm/opportunities")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(opportunity2)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            // 验证最终结果
            CrmOpportunity finalOpportunity = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
            assertNotNull(finalOpportunity);
            // 最后一次修改应该生效
            assertEquals("用户2修改", finalOpportunity.getOpportunityName());
        }
    }

    @Nested
    @DisplayName("使用TestAssertionHelper的改进测试示例")
    class ImprovedTestExamples {
        
        private CrmOpportunity testOpportunity;
        private Long testOpportunityId;
        
        @BeforeEach
        void setUpImprovedTests() {
            // 改进测试示例需要预置数据
            testOpportunity = createTestOpportunity("改进测试商机", "改进测试客户");
            testOpportunityId = testOpportunity.getId();
        }
        
        @AfterEach
        void tearDownImprovedTests() {
            cleanupTestOpportunity(testOpportunityId);
        }

        @Test
        @DisplayName("商机转化 - 使用辅助类进行详细验证")
        void testConvertOpportunityWithHelper() throws Exception {
            // 使用辅助类打印调试信息
            printDebugInfo("商机转化测试开始",
                "测试商机ID", testOpportunityId,
                "测试描述", "验证商机转化功能的完整流程"
            );

            // 准备转化数据
            ConvertForm convertForm = new ConvertForm();
            convertForm.setOpportunityId(testOpportunityId);
            convertForm.setContractName("辅助类测试合同");
            convertForm.setRemarks("使用辅助类测试");

            // 获取转化前状态
            CrmOpportunity beforeConvert = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
            printDebugInfo("转化前状态", "商机阶段", beforeConvert.getOpportunityStage());

            // 执行转化
            MvcResult result = mockMvc.perform(post("/front/crm/opportunities/convert-to-contract")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(convertForm)))
                    .andExpect(status().isOk())
                    .andReturn();

            // 使用辅助类验证响应
            AjaxResult response = assertAndParseResult(result, "商机转化");
            
            if (response.isSuccess()) {
                assertSuccessResponse(response, "商机转化");
                
                // 验证响应数据结构
                assertResponseDataStructure(
                    response.get("data"), 
                    "转化结果", 
                    "contractId", "opportunityId"  // 根据实际API调整
                );
                
                // 验证状态变化
                CrmOpportunity afterConvert = crmOpportunityService.selectCrmOpportunityById(testOpportunityId);
                assertStateChange(
                    beforeConvert.getOpportunityStage(), 
                    afterConvert.getOpportunityStage(), 
                    "商机阶段转化", 
                    true  // 应该发生变化
                );
                
            } else {
                assertFailureResponse(response, "商机转化", "商机已赢单");
            }
        }
    }
}
