import request from '@/utils/request'

// 查询客户联系人关联关系列表
export function listCustomerContactRelations(query) {
  return request({
    url: '/crm/customer-contact-relation/list',
    method: 'get',
    params: query
  })
}

// 查询客户联系人关联关系详细
export function getCustomerContactRelation(id) {
  return request({
    url: '/crm/customer-contact-relation/' + id,
    method: 'get'
  })
}

// 新增客户联系人关联关系
export function addCustomerContactRelation(data) {
  return request({
    url: '/crm/customer-contact-relation',
    method: 'post',
    data: data
  })
}

// 修改客户联系人关联关系
export function updateCustomerContactRelation(data) {
  return request({
    url: '/crm/customer-contact-relation',
    method: 'put',
    data: data
  })
}

// 删除客户联系人关联关系
export function delCustomerContactRelation(id) {
  return request({
    url: '/crm/customer-contact-relation/' + id,
    method: 'delete'
  })
}

// 根据客户ID查询关联的联系人列表
export function getContactsByCustomerId(customerId) {
  return request({
    url: '/crm/customer-contact-relation/customer/' + customerId,
    method: 'get'
  })
}

// 根据客户ID查询联系人（使用现有接口）
export function getContactsByCustomerIdFromContacts(customerId) {
  return request({
    url: '/front/crm/contacts/customer/' + customerId,
    method: 'get'
  })
}

// 根据联系人ID查询关联的客户列表
export function getCustomersByContactId(contactId) {
  return request({
    url: '/crm/customer-contact-relation/contact/' + contactId,
    method: 'get'
  })
}

// 根据客户ID和联系人ID查询关联关系
export function getRelationByCustomerAndContact(customerId, contactId) {
  return request({
    url: '/crm/customer-contact-relation/relation',
    method: 'get',
    params: {
      customerId: customerId,
      contactId: contactId
    }
  })
}

// 建立客户与联系人的关联关系
export function linkCustomerContact(data) {
  return request({
    url: '/crm/customer-contact-relation/link',
    method: 'post',
    data: data
  })
}

// 取消客户与联系人的关联关系
export function unlinkCustomerContact(data) {
  return request({
    url: '/crm/customer-contact-relation/unlink',
    method: 'post',
    data: data
  })
}

// 批量建立客户联系人关联关系
export function batchLinkCustomerContacts(data) {
  return request({
    url: '/crm/customer-contact-relation/batch-link',
    method: 'post',
    data: data
  })
}

// 设置主要联系人
export function setPrimaryContact(data) {
  return request({
    url: '/crm/customer-contact-relation/set-primary',
    method: 'put',
    data: data
  })
}

// 搜索可关联的联系人（排除已关联的）
export function getAvailableContacts(customerId, keyword) {
  return request({
    url: '/crm/customer-contact-relation/available-contacts/' + customerId,
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

// 获取关联关系统计信息
export function getCustomerContactStatistics() {
  return request({
    url: '/crm/customer-contact-relation/statistics',
    method: 'get'
  })
}

// 导出客户联系人关联关系列表
export function exportCustomerContactRelation(query) {
  return request({
    url: '/crm/customer-contact-relation/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  })
}