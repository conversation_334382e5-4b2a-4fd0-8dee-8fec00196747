package com.ruoyi.crm.service;

import com.ruoyi.crm.domain.dto.ThreeDPrintingOrderCreateDTO;
import com.ruoyi.crm.domain.vo.ThreeDPrintingOrderResultVO;

/**
 * 3D打印CRM集成服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface IThreeDPrintingCRMIntegrationService {
    
    /**
     * 创建3D打印订单并自动集成CRM
     * 包含：检查/创建客户、创建联系人、创建商机、创建订单
     * 
     * @param orderCreateDTO 订单创建请求数据
     * @return 创建结果，包含订单ID、客户ID、联系人ID、商机ID
     */
    ThreeDPrintingOrderResultVO createOrderWithCRMIntegration(ThreeDPrintingOrderCreateDTO orderCreateDTO);
    
    /**
     * 检查客户是否存在（通过手机号）
     * 
     * @param mobile 手机号
     * @return 客户ID，如果不存在返回null
     */
    Long checkCustomerExistsByMobile(String mobile);
    
    /**
     * 检查联系人是否存在（通过客户ID和手机号）
     * 
     * @param customerId 客户ID
     * @param mobile 手机号
     * @return 联系人ID，如果不存在返回null
     */
    Long checkContactExists(Long customerId, String mobile);
}