package com.ruoyi.crm.service.impl;

import com.ruoyi.common.domain.entity.*;
import com.ruoyi.common.mapper.*;
import com.ruoyi.crm.service.ICrmCustomerPoolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户公海Service业务层处理 - 简化版本
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class CrmCustomerPoolServiceImpl implements ICrmCustomerPoolService {
    
    @Autowired
    private CrmCustomerPoolMapper customerPoolMapper;
    
    @Autowired
    private CrmCustomerMapper customerMapper;

    /**
     * 查询公海客户列表
     */
    @Override
    public List<CrmCustomerPool> selectPoolCustomerList(CrmCustomerPool crmCustomerPool) {
        if (crmCustomerPool == null) {
            crmCustomerPool = new CrmCustomerPool();
        }
        crmCustomerPool.setStatus("IN_POOL");
        return customerPoolMapper.selectPoolCustomerList(crmCustomerPool);
    }

    /**
     * 查询我的客户列表
     */
    @Override
    public List<CrmCustomerPool> selectMyCustomerList(Long userId) {
        // 查询用户负责的客户
        CrmCustomer query = new CrmCustomer();
        query.setResponsiblePersonId(String.valueOf(userId));
        query.setDelFlag("0");
        
        List<CrmCustomer> customers = customerMapper.selectList(query);
        
        // 转换为公海格式（方便前端统一处理）
        return customers.stream().map(customer -> {
            CrmCustomerPool pool = new CrmCustomerPool();
            pool.setCustomerId(customer.getId());
            pool.setCustomerName(customer.getCustomerName());
            pool.setMobile(customer.getMobile());
            pool.setEmail(customer.getEmail());
            pool.setCustomerIndustry(customer.getCustomerIndustry());
            pool.setCustomerLevel(customer.getCustomerLevel());
            pool.setStatus("OWNED"); // 标记为用户拥有
            return pool;
        }).collect(Collectors.toList());
    }
    
    // 暂时简化实现其他方法
    @Override
    public int returnToPool(List<Long> customerIds, String reason, String remark) {
        // TODO: 实现放入公海逻辑
        return 0;
    }
    
    @Override
    public int claimCustomers(List<Long> customerIds) {
        // TODO: 实现认领客户逻辑
        return 0;
    }
    
    @Override
    public int autoReturnToPool() {
        // TODO: 实现自动放入公海逻辑
        return 0;
    }
    
    @Override
    public void updateDaysInPool() {
        // TODO: 实现更新在公海天数逻辑
        customerPoolMapper.updateDaysInPool();
    }
    
    @Override
    public void recordOperationLog(CrmPoolOperationLog log) {
        // TODO: 实现记录操作日志逻辑
        // operationLogMapper.insert(log);
    }
    
    @Override
    public boolean checkClaimLimit(Long userId, int requestCount) {
        // TODO: 实现认领限制检查逻辑
        return true; // 暂时允许所有认领
    }
}