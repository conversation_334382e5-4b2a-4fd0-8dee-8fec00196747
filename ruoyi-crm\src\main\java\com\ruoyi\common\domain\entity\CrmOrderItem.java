package com.ruoyi.common.domain.entity;

import java.math.BigDecimal;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CRM订单项实体类
 * 支持3D打印产品和普通产品
 *
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmOrderItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 订单项ID */
    private Long id;

    /** 订单ID */
    private Long orderId;

    /** 产品ID */
    private Long productId;

    /** 产品编码 */
    @Excel(name = "产品编码")
    private String productCode;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 模型名称（3D打印专用） */
    @Excel(name = "模型名称")
    private String modelName;

    /** 模型文件URL（3D打印专用） */
    @Excel(name = "模型文件URL")
    private String modelFileUrl;

    /** 规格参数 */
    @Excel(name = "规格参数")
    private String specification;

    /** 模型尺寸（3D打印专用） */
    @Excel(name = "模型尺寸")
    private String dimensions;

    /** 模型体积（3D打印专用） */
    @Excel(name = "模型体积")
    private String volume;

    /** 模型表面积（3D打印专用） */
    @Excel(name = "模型表面积")
    private String surfaceArea;

    /** 材料ID */
    private Long materialId;

    /** 材料名称 */
    @Excel(name = "材料名称")
    private String materialName;

    /** 数量 */
    @Excel(name = "数量")
    private Integer quantity;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /** 折扣率 */
    @Excel(name = "折扣率")
    private BigDecimal discountRate;

    /** 小计金额 */
    @Excel(name = "小计金额")
    private BigDecimal subtotal;

    /** 总价（兼容旧字段） */
    @Excel(name = "总价")
    private BigDecimal totalPrice;

    /** 后处理选项（3D打印专用） */
    @Excel(name = "后处理选项")
    private String processOptions;

    /** 3D打印选项（JSON格式） */
    private String printOptions;

    /** 排序 */
    private Integer sortOrder;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 创建人 */
    private Long createdBy;

    /** 更新人 */
    private Long updatedBy;

    // ========== 关联对象 ==========

    /** 关联订单对象 */
    private CrmOrder order;

    /** 关联产品对象 */
    private Object product;

    /** 关联材料对象 */
    private Object material;

    // ========== 计算字段 ==========

    /** 实际单价（考虑折扣后） */
    private BigDecimal actualUnitPrice;

    /** 折扣金额 */
    private BigDecimal discountAmount;

    // ========== 查询条件字段 ==========

    /** 产品名称模糊查询 */
    private String productNameLike;

    /** 材料名称模糊查询 */
    private String materialNameLike;

    // ========== 常量定义 ==========

    /** 默认单位 */
    public static final String DEFAULT_UNIT = "个";

    /** 默认折扣率 */
    public static final BigDecimal DEFAULT_DISCOUNT_RATE = BigDecimal.ZERO;

    /** 默认排序 */
    public static final Integer DEFAULT_SORT_ORDER = 0;
}