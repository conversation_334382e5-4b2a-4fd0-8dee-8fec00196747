<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人客户模块改造详细实施方案</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- Prism.js for code highlighting -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
            line-height: 1.6;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            text-align: center;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .overview-box {
            background-color: #e8f5e8;
            border: 2px solid #27ae60;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .warning-box {
            background-color: #fee;
            border: 2px solid #e74c3c;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .implementation-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        .code-section {
            margin: 20px 0;
        }
        .code-title {
            background-color: #495057;
            color: white;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin: 0;
            font-weight: bold;
            font-size: 14px;
        }
        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0 0 5px 5px !important;
            font-size: 13px !important;
            max-height: 500px;
            overflow-y: auto;
        }
        .step-card {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        .step-card h4 {
            color: #3498db;
            margin-top: 0;
        }
        .checklist {
            margin: 15px 0;
            padding: 15px;
            background-color: #f0f8ff;
            border-radius: 5px;
        }
        .checklist ul {
            margin: 10px 0;
            padding-left: 25px;
        }
        .checklist li {
            margin: 5px 0;
        }
        .mermaid-diagram {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        .api-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .api-table th {
            background-color: #3498db;
            color: white;
            padding: 12px;
            text-align: left;
        }
        .api-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .api-table tr:hover {
            background-color: #f5f5f5;
        }
        .test-case {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ 联系人客户模块改造详细实施方案</h1>
        
        <div class="overview-box">
            <h3>📋 方案概述</h3>
            <p>基于《联系人客户模块完整改造计划》，本文档提供详细的实施步骤和代码实现。重点解决：</p>
            <ul>
                <li><strong>多负责人管理：</strong>实现联系人-业务员多对多关系</li>
                <li><strong>团队协作：</strong>集成现有团队管理体系</li>
                <li><strong>公海升级：</strong>支持多维度公海管理</li>
                <li><strong>统计分析：</strong>个人/团队/部门多层级统计</li>
            </ul>
        </div>

        <h2>第一阶段：数据库架构升级（Day 1-2）</h2>

        <div class="step-card">
            <h4>Step 1.1: 创建联系人-业务员关系表</h4>
            <div class="code-section">
                <div class="code-title">SQL - crm_contact_responsible_relation.sql</div>
                <pre><code class="language-sql">-- 创建联系人-业务员多对多关系表
CREATE TABLE `crm_contact_responsible_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contact_id` bigint(20) NOT NULL COMMENT '联系人ID',
  `responsible_person_id` bigint(20) NOT NULL COMMENT '负责人ID(业务员)',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型(PACKAGING/PROTOTYPE/3D_PRINTING等)',
  `relation_status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '关系状态(ACTIVE/INACTIVE/TRANSFERRED)',
  `start_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始负责时间',
  `end_date` datetime DEFAULT NULL COMMENT '结束负责时间',
  `team_id` bigint(20) DEFAULT NULL COMMENT '所属团队ID',
  `assign_type` varchar(20) DEFAULT 'MANUAL' COMMENT '分配方式(MANUAL/TEAM_ASSIGN/POOL_CLAIM)',
  `assign_by` bigint(20) DEFAULT NULL COMMENT '分配人ID',
  `assign_time` datetime DEFAULT NULL COMMENT '分配时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contact_responsible_business` (`contact_id`,`responsible_person_id`,`business_type`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_responsible_person_id` (`responsible_person_id`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_relation_status` (`relation_status`),
  KEY `idx_start_date` (`start_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人-业务员关系表';

-- 添加索引优化查询性能
CREATE INDEX idx_contact_status_business ON crm_contact_responsible_relation(contact_id, relation_status, business_type);
CREATE INDEX idx_responsible_status_date ON crm_contact_responsible_relation(responsible_person_id, relation_status, start_date);</code></pre>
            </div>
        </div>

        <div class="step-card">
            <h4>Step 1.2: 数据迁移脚本</h4>
            <div class="code-section">
                <div class="code-title">SQL - migrate_existing_data.sql</div>
                <pre><code class="language-sql">-- 1. 迁移现有联系人负责人数据
INSERT INTO crm_contact_responsible_relation 
    (contact_id, responsible_person_id, business_type, relation_status, start_date, assign_type, create_by, create_time)
SELECT 
    id AS contact_id,
    responsible_person_id,
    'GENERAL' AS business_type,  -- 默认通用业务类型
    'ACTIVE' AS relation_status,
    IFNULL(create_time, NOW()) AS start_date,
    'MIGRATED' AS assign_type,
    'system' AS create_by,
    NOW() AS create_time
FROM crm_contacts 
WHERE responsible_person_id IS NOT NULL 
  AND responsible_person_id != ''
  AND del_flag = '0';

-- 2. 更新团队关联信息
UPDATE crm_contact_responsible_relation r
INNER JOIN crm_team_member tm ON r.responsible_person_id = tm.user_id
SET r.team_id = tm.team_id
WHERE tm.status = '0' 
  AND tm.role_type IN ('owner', 'admin', 'member');

-- 3. 处理团队关联表中的联系人关系
UPDATE crm_contact_responsible_relation r
INNER JOIN crm_team_relation tr ON r.contact_id = tr.relation_id
SET r.team_id = tr.team_id
WHERE tr.relation_type = 'CONTACT'
  AND r.team_id IS NULL;

-- 4. 验证迁移结果
SELECT 
    'Total Contacts' AS metric, COUNT(DISTINCT id) AS count 
FROM crm_contacts 
WHERE responsible_person_id IS NOT NULL AND del_flag = '0'
UNION ALL
SELECT 
    'Migrated Relations' AS metric, COUNT(*) AS count 
FROM crm_contact_responsible_relation 
WHERE assign_type = 'MIGRATED';</code></pre>
            </div>
        </div>

        <h2>第二阶段：后端业务逻辑实现（Day 3-5）</h2>

        <div class="step-card">
            <h4>Step 2.1: 创建实体类</h4>
            <div class="code-section">
                <div class="code-title">Java - CrmContactResponsibleRelation.java</div>
                <pre><code class="language-java">package com.ruoyi.common.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import javax.validation.constraints.NotNull;

/**
 * 联系人-业务员关系对象 crm_contact_responsible_relation
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class CrmContactResponsibleRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 联系人ID */
    @NotNull(message = "联系人ID不能为空")
    @Excel(name = "联系人ID")
    private Long contactId;

    /** 负责人ID(业务员) */
    @NotNull(message = "负责人ID不能为空")
    @Excel(name = "负责人ID")
    private Long responsiblePersonId;

    /** 业务类型 */
    @Excel(name = "业务类型")
    private String businessType;

    /** 关系状态 */
    @Excel(name = "关系状态")
    private String relationStatus;

    /** 开始负责时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始负责时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 结束负责时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束负责时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /** 所属团队ID */
    @Excel(name = "所属团队ID")
    private Long teamId;

    /** 分配方式 */
    @Excel(name = "分配方式")
    private String assignType;

    /** 分配人ID */
    @Excel(name = "分配人ID")
    private Long assignBy;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分配时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    // 关联查询字段
    /** 联系人姓名 */
    private String contactName;
    
    /** 负责人姓名 */
    private String responsiblePersonName;
    
    /** 团队名称 */
    private String teamName;
    
    /** 分配人姓名 */
    private String assignByName;

    // 业务类型常量
    public static class BusinessType {
        public static final String GENERAL = "GENERAL";           // 通用
        public static final String PACKAGING = "PACKAGING";       // 包装
        public static final String PROTOTYPE = "PROTOTYPE";       // 原型
        public static final String PRINTING_3D = "3D_PRINTING";   // 3D打印
        public static final String MOLD = "MOLD";                 // 模具
        public static final String DESIGN = "DESIGN";             // 设计
    }

    // 关系状态常量
    public static class RelationStatus {
        public static final String ACTIVE = "ACTIVE";             // 活跃
        public static final String INACTIVE = "INACTIVE";         // 非活跃
        public static final String TRANSFERRED = "TRANSFERRED";   // 已转移
    }

    // 分配方式常量
    public static class AssignType {
        public static final String MANUAL = "MANUAL";             // 手动分配
        public static final String TEAM_ASSIGN = "TEAM_ASSIGN";   // 团队分配
        public static final String POOL_CLAIM = "POOL_CLAIM";     // 公海认领
        public static final String MIGRATED = "MIGRATED";         // 数据迁移
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getResponsiblePersonId() {
        return responsiblePersonId;
    }

    public void setResponsiblePersonId(Long responsiblePersonId) {
        this.responsiblePersonId = responsiblePersonId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getRelationStatus() {
        return relationStatus;
    }

    public void setRelationStatus(String relationStatus) {
        this.relationStatus = relationStatus;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getAssignType() {
        return assignType;
    }

    public void setAssignType(String assignType) {
        this.assignType = assignType;
    }

    public Long getAssignBy() {
        return assignBy;
    }

    public void setAssignBy(Long assignBy) {
        this.assignBy = assignBy;
    }

    public Date getAssignTime() {
        return assignTime;
    }

    public void setAssignTime(Date assignTime) {
        this.assignTime = assignTime;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getResponsiblePersonName() {
        return responsiblePersonName;
    }

    public void setResponsiblePersonName(String responsiblePersonName) {
        this.responsiblePersonName = responsiblePersonName;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getAssignByName() {
        return assignByName;
    }

    public void setAssignByName(String assignByName) {
        this.assignByName = assignByName;
    }
}</code></pre>
            </div>
        </div>

        <div class="step-card">
            <h4>Step 2.2: 创建Mapper接口</h4>
            <div class="code-section">
                <div class="code-title">Java - CrmContactResponsibleRelationMapper.java</div>
                <pre><code class="language-java">package com.ruoyi.common.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.common.domain.entity.CrmContactResponsibleRelation;

/**
 * 联系人-业务员关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface CrmContactResponsibleRelationMapper {
    
    /**
     * 查询联系人-业务员关系
     */
    CrmContactResponsibleRelation selectCrmContactResponsibleRelationById(Long id);
    
    /**
     * 查询联系人-业务员关系列表
     */
    List<CrmContactResponsibleRelation> selectCrmContactResponsibleRelationList(CrmContactResponsibleRelation relation);
    
    /**
     * 新增联系人-业务员关系
     */
    int insertCrmContactResponsibleRelation(CrmContactResponsibleRelation relation);
    
    /**
     * 批量新增联系人-业务员关系
     */
    int batchInsert(@Param("list") List<CrmContactResponsibleRelation> list);
    
    /**
     * 修改联系人-业务员关系
     */
    int updateCrmContactResponsibleRelation(CrmContactResponsibleRelation relation);
    
    /**
     * 删除联系人-业务员关系
     */
    int deleteCrmContactResponsibleRelationById(Long id);
    
    /**
     * 批量删除联系人-业务员关系
     */
    int deleteCrmContactResponsibleRelationByIds(Long[] ids);
    
    /**
     * 根据联系人ID查询所有负责人
     */
    List<CrmContactResponsibleRelation> selectByContactId(@Param("contactId") Long contactId);
    
    /**
     * 根据负责人ID查询所有联系人
     */
    List<CrmContactResponsibleRelation> selectByResponsiblePersonId(@Param("responsiblePersonId") Long responsiblePersonId);
    
    /**
     * 根据团队ID查询团队管理的联系人
     */
    List<CrmContactResponsibleRelation> selectByTeamId(@Param("teamId") Long teamId);
    
    /**
     * 检查是否存在关系
     */
    int checkRelationExists(@Param("contactId") Long contactId, 
                           @Param("responsiblePersonId") Long responsiblePersonId,
                           @Param("businessType") String businessType);
    
    /**
     * 更新关系状态
     */
    int updateRelationStatus(@Param("contactId") Long contactId,
                            @Param("responsiblePersonId") Long responsiblePersonId,
                            @Param("oldStatus") String oldStatus,
                            @Param("newStatus") String newStatus);
    
    /**
     * 统计团队联系人数量
     */
    int countTeamContacts(@Param("teamId") Long teamId);
    
    /**
     * 按业务类型和团队统计
     */
    List<Map<String, Object>> countByBusinessTypeAndTeam(@Param("teamId") Long teamId,
                                                        @Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate);
}</code></pre>
            </div>
        </div>

        <div class="step-card">
            <h4>Step 2.3: 创建Service接口和实现</h4>
            <div class="code-section">
                <div class="code-title">Java - ICrmContactResponsibleRelationService.java</div>
                <pre><code class="language-java">package com.ruoyi.crm.service;

import java.util.List;
import com.ruoyi.common.domain.entity.CrmContactResponsibleRelation;
import com.ruoyi.crm.domain.vo.TeamAssignRequest;
import com.ruoyi.crm.domain.vo.PoolQueryRequest;
import com.ruoyi.crm.domain.vo.TeamPerformanceVO;

/**
 * 联系人-业务员关系Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface ICrmContactResponsibleRelationService {
    
    /**
     * 查询联系人-业务员关系
     */
    CrmContactResponsibleRelation selectCrmContactResponsibleRelationById(Long id);
    
    /**
     * 查询联系人-业务员关系列表
     */
    List<CrmContactResponsibleRelation> selectCrmContactResponsibleRelationList(CrmContactResponsibleRelation relation);
    
    /**
     * 新增联系人-业务员关系
     */
    int insertCrmContactResponsibleRelation(CrmContactResponsibleRelation relation);
    
    /**
     * 修改联系人-业务员关系
     */
    int updateCrmContactResponsibleRelation(CrmContactResponsibleRelation relation);
    
    /**
     * 删除联系人-业务员关系
     */
    int deleteCrmContactResponsibleRelationByIds(Long[] ids);
    
    /**
     * 团队负责人分配联系人给团队成员
     */
    int assignContactsToTeamMember(TeamAssignRequest request);
    
    /**
     * 从公海认领联系人
     */
    int claimContactsFromPool(Long[] contactIds, String businessType);
    
    /**
     * 将联系人退回公海
     */
    int returnContactsToPool(Long[] contactIds, Long responsiblePersonId, String reason);
    
    /**
     * 转移联系人负责人
     */
    int transferContacts(Long[] contactIds, Long fromUserId, Long toUserId, String businessType, String remark);
    
    /**
     * 查询公海联系人（多维度）
     */
    List<CrmContacts> selectPoolContacts(PoolQueryRequest request);
    
    /**
     * 获取团队业绩统计
     */
    TeamPerformanceVO getTeamPerformance(Long teamId, Date startDate, Date endDate);
    
    /**
     * 获取个人负责的联系人（按业务类型）
     */
    List<CrmContactResponsibleRelation> getMyContacts(Long userId, String businessType);
    
    /**
     * 批量更新团队信息
     */
    int updateTeamInfo(Long oldTeamId, Long newTeamId);
}</code></pre>
            </div>
        </div>

        <div class="step-card">
            <h4>Step 2.4: 创建Service实现类（核心业务逻辑）</h4>
            <div class="code-section">
                <div class="code-title">Java - CrmContactResponsibleRelationServiceImpl.java</div>
                <pre><code class="language-java">package com.ruoyi.crm.service.impl;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.mapper.CrmContactResponsibleRelationMapper;
import com.ruoyi.common.mapper.CrmTeamMapper;
import com.ruoyi.common.mapper.CrmTeamMemberMapper;
import com.ruoyi.common.mapper.CrmTeamRelationMapper;
import com.ruoyi.common.mapper.CrmContactsMapper;
import com.ruoyi.common.domain.entity.CrmContactResponsibleRelation;
import com.ruoyi.common.domain.entity.CrmTeam;
import com.ruoyi.common.domain.entity.CrmTeamMember;
import com.ruoyi.common.domain.entity.CrmTeamRelation;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.crm.service.ICrmContactResponsibleRelationService;
import com.ruoyi.crm.domain.vo.TeamAssignRequest;
import com.ruoyi.crm.domain.vo.PoolQueryRequest;
import com.ruoyi.crm.domain.vo.TeamPerformanceVO;

/**
 * 联系人-业务员关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class CrmContactResponsibleRelationServiceImpl implements ICrmContactResponsibleRelationService {
    
    @Autowired
    private CrmContactResponsibleRelationMapper contactResponsibleMapper;
    
    @Autowired
    private CrmTeamMapper teamMapper;
    
    @Autowired
    private CrmTeamMemberMapper teamMemberMapper;
    
    @Autowired
    private CrmTeamRelationMapper teamRelationMapper;
    
    @Autowired
    private CrmContactsMapper contactsMapper;
    
    /**
     * 团队负责人分配联系人给团队成员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int assignContactsToTeamMember(TeamAssignRequest request) {
        // 1. 验证当前用户是否为团队负责人
        Long currentUserId = SecurityUtils.getUserId();
        CrmTeam team = teamMapper.selectCrmTeamById(request.getTeamId());
        if (team == null) {
            throw new ServiceException("团队不存在");
        }
        if (!currentUserId.equals(team.getLeaderId())) {
            throw new ServiceException("只有团队负责人可以分配联系人");
        }
        
        // 2. 验证被分配人是否为团队成员
        CrmTeamMember member = teamMemberMapper.selectByTeamAndUser(
            request.getTeamId(), request.getAssignToUserId());
        if (member == null || !CrmTeamMember.Status.NORMAL.equals(member.getStatus())) {
            throw new ServiceException("被分配人不是团队有效成员");
        }
        
        // 3. 批量创建联系人-负责人关系
        List<CrmContactResponsibleRelation> relations = new ArrayList<>();
        Date now = new Date();
        
        for (Long contactId : request.getContactIds()) {
            // 检查是否已存在相同业务类型的关系
            int exists = contactResponsibleMapper.checkRelationExists(
                contactId, request.getAssignToUserId(), request.getBusinessType());
            if (exists > 0) {
                continue; // 跳过已存在的关系
            }
            
            CrmContactResponsibleRelation relation = new CrmContactResponsibleRelation();
            relation.setContactId(contactId);
            relation.setResponsiblePersonId(request.getAssignToUserId());
            relation.setBusinessType(request.getBusinessType());
            relation.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
            relation.setStartDate(now);
            relation.setTeamId(request.getTeamId());
            relation.setAssignType(CrmContactResponsibleRelation.AssignType.TEAM_ASSIGN);
            relation.setAssignBy(currentUserId);
            relation.setAssignTime(now);
            relation.setRemark("团队分配：" + team.getTeamName());
            relation.setCreateBy(SecurityUtils.getUsername());
            relation.setCreateTime(now);
            
            relations.add(relation);
        }
        
        if (relations.isEmpty()) {
            return 0;
        }
        
        // 4. 批量插入
        int result = contactResponsibleMapper.batchInsert(relations);
        
        // 5. 建立或更新团队关联关系
        for (Long contactId : request.getContactIds()) {
            CrmTeamRelation teamRelation = new CrmTeamRelation();
            teamRelation.setTeamId(request.getTeamId());
            teamRelation.setRelationType("CONTACT");
            teamRelation.setRelationId(contactId);
            teamRelation.setCreateBy(SecurityUtils.getUsername());
            teamRelation.setCreateTime(now);
            
            // 检查是否已存在团队关联
            CrmTeamRelation existing = teamRelationMapper.selectByTeamAndRelation(
                request.getTeamId(), "CONTACT", contactId);
            if (existing == null) {
                teamRelationMapper.insertCrmTeamRelation(teamRelation);
            }
        }
        
        // 6. 记录操作日志（这里可以调用业务日志服务）
        // recordTeamAssignLog(request, currentUserId);
        
        return result;
    }
    
    /**
     * 从公海认领联系人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int claimContactsFromPool(Long[] contactIds, String businessType) {
        Long currentUserId = SecurityUtils.getUserId();
        Date now = new Date();
        
        List<CrmContactResponsibleRelation> relations = new ArrayList<>();
        
        for (Long contactId : contactIds) {
            // 检查该联系人在该业务类型下是否已有负责人
            CrmContactResponsibleRelation query = new CrmContactResponsibleRelation();
            query.setContactId(contactId);
            query.setBusinessType(businessType);
            query.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
            
            List<CrmContactResponsibleRelation> existingRelations = 
                contactResponsibleMapper.selectCrmContactResponsibleRelationList(query);
            
            if (!existingRelations.isEmpty()) {
                throw new ServiceException("联系人ID:" + contactId + " 在业务类型:" + businessType + " 下已有负责人");
            }
            
            // 创建新的关系
            CrmContactResponsibleRelation relation = new CrmContactResponsibleRelation();
            relation.setContactId(contactId);
            relation.setResponsiblePersonId(currentUserId);
            relation.setBusinessType(businessType);
            relation.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
            relation.setStartDate(now);
            relation.setAssignType(CrmContactResponsibleRelation.AssignType.POOL_CLAIM);
            relation.setAssignBy(currentUserId);
            relation.setAssignTime(now);
            relation.setRemark("公海认领");
            relation.setCreateBy(SecurityUtils.getUsername());
            relation.setCreateTime(now);
            
            // 查找用户所属团队
            List<CrmTeamMember> teamMembers = teamMemberMapper.selectByUserId(currentUserId);
            if (!teamMembers.isEmpty()) {
                relation.setTeamId(teamMembers.get(0).getTeamId());
            }
            
            relations.add(relation);
        }
        
        if (relations.isEmpty()) {
            return 0;
        }
        
        return contactResponsibleMapper.batchInsert(relations);
    }
    
    /**
     * 将联系人退回公海
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int returnContactsToPool(Long[] contactIds, Long responsiblePersonId, String reason) {
        // 如果没有指定负责人，默认为当前用户
        if (responsiblePersonId == null) {
            responsiblePersonId = SecurityUtils.getUserId();
        }
        
        Date now = new Date();
        int count = 0;
        
        for (Long contactId : contactIds) {
            // 更新所有该负责人的活跃关系为非活跃
            CrmContactResponsibleRelation updateRelation = new CrmContactResponsibleRelation();
            updateRelation.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.INACTIVE);
            updateRelation.setEndDate(now);
            updateRelation.setUpdateBy(SecurityUtils.getUsername());
            updateRelation.setUpdateTime(now);
            updateRelation.setRemark("退回公海：" + reason);
            
            // 构建查询条件
            CrmContactResponsibleRelation query = new CrmContactResponsibleRelation();
            query.setContactId(contactId);
            query.setResponsiblePersonId(responsiblePersonId);
            query.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
            
            List<CrmContactResponsibleRelation> activeRelations = 
                contactResponsibleMapper.selectCrmContactResponsibleRelationList(query);
            
            for (CrmContactResponsibleRelation relation : activeRelations) {
                updateRelation.setId(relation.getId());
                count += contactResponsibleMapper.updateCrmContactResponsibleRelation(updateRelation);
            }
        }
        
        return count;
    }
    
    /**
     * 查询公海联系人（多维度）
     */
    @Override
    public List<CrmContacts> selectPoolContacts(PoolQueryRequest request) {
        switch (request.getQueryType()) {
            case "PERSONAL":
                // 查询个人可认领的联系人（在指定业务类型下无负责人）
                return contactsMapper.selectPersonalPoolContacts(
                    request.getUserId(), request.getBusinessType());
                
            case "TEAM":
                // 查询团队可认领的联系人（团队成员都未负责的联系人）
                return contactsMapper.selectTeamPoolContacts(
                    request.getTeamId(), request.getBusinessType());
                
            case "BUSINESS_TYPE":
                // 查询业务类型公海（该业务类型无任何负责人的联系人）
                return contactsMapper.selectBusinessTypePoolContacts(
                    request.getBusinessType());
                
            default:
                return new ArrayList<>();
        }
    }
    
    /**
     * 获取团队业绩统计
     */
    @Override
    public TeamPerformanceVO getTeamPerformance(Long teamId, Date startDate, Date endDate) {
        TeamPerformanceVO performance = new TeamPerformanceVO();
        
        // 1. 团队基本信息
        CrmTeam team = teamMapper.selectCrmTeamById(teamId);
        performance.setTeamInfo(team);
        
        // 2. 团队成员列表
        CrmTeamMember memberQuery = new CrmTeamMember();
        memberQuery.setTeamId(teamId);
        List<CrmTeamMember> members = teamMemberMapper.selectCrmTeamMemberList(memberQuery);
        performance.setMembers(members);
        
        // 3. 团队管理的联系人统计
        int totalContacts = contactResponsibleMapper.countTeamContacts(teamId);
        performance.setTotalContacts(totalContacts);
        
        // 4. 按业务类型统计
        List<Map<String, Object>> businessTypeStats = contactResponsibleMapper
            .countByBusinessTypeAndTeam(teamId, startDate, endDate);
        performance.setBusinessTypeStats(businessTypeStats);
        
        // 5. 按成员统计
        Map<Long, Integer> memberStats = members.stream()
            .collect(Collectors.toMap(
                CrmTeamMember::getUserId,
                member -> {
                    CrmContactResponsibleRelation query = new CrmContactResponsibleRelation();
                    query.setResponsiblePersonId(member.getUserId());
                    query.setTeamId(teamId);
                    query.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
                    return contactResponsibleMapper.selectCrmContactResponsibleRelationList(query).size();
                }
            ));
        performance.setMemberStats(memberStats);
        
        return performance;
    }
    
    // 其他方法实现...
}</code></pre>
            </div>
        </div>

        <h2>第三阶段：API接口开发（Day 6）</h2>

        <div class="step-card">
            <h4>Step 3.1: 扩展联系人Controller</h4>
            <div class="code-section">
                <div class="code-title">Java - CrmContactsController扩展</div>
                <pre><code class="language-java">// 在CrmContactsController中添加以下方法

/**
 * 获取联系人的所有负责人
 */
@GetMapping("/{contactId}/responsibles")
public AjaxResult getContactResponsibles(@PathVariable("contactId") Long contactId,
                                        @RequestParam(required = false) String businessType) {
    try {
        CrmContactResponsibleRelation query = new CrmContactResponsibleRelation();
        query.setContactId(contactId);
        if (businessType != null && !businessType.isEmpty()) {
            query.setBusinessType(businessType);
        }
        query.setRelationStatus(CrmContactResponsibleRelation.RelationStatus.ACTIVE);
        
        List<CrmContactResponsibleRelation> relations = 
            contactResponsibleRelationService.selectCrmContactResponsibleRelationList(query);
        
        return AjaxResult.success("查询成功", relations);
    } catch (Exception e) {
        logger.error("获取联系人负责人失败", e);
        return AjaxResult.error("获取联系人负责人失败：" + e.getMessage());
    }
}

/**
 * 为联系人添加负责人
 */
@PostMapping("/{contactId}/responsibles")
public AjaxResult addContactResponsible(@PathVariable("contactId") Long contactId,
                                       @RequestBody CrmContactResponsibleRelation relation) {
    try {
        relation.setContactId(contactId);
        int result = contactResponsibleRelationService.insertCrmContactResponsibleRelation(relation);
        if (result > 0) {
            return AjaxResult.success("添加负责人成功");
        } else {
            return AjaxResult.error("添加负责人失败");
        }
    } catch (Exception e) {
        logger.error("添加联系人负责人失败", e);
        return AjaxResult.error("添加联系人负责人失败：" + e.getMessage());
    }
}

/**
 * 移除联系人负责人
 */
@DeleteMapping("/{contactId}/responsibles/{relationId}")
public AjaxResult removeContactResponsible(@PathVariable("contactId") Long contactId,
                                          @PathVariable("relationId") Long relationId) {
    try {
        // 验证关系是否属于该联系人
        CrmContactResponsibleRelation relation = 
            contactResponsibleRelationService.selectCrmContactResponsibleRelationById(relationId);
        if (relation == null || !contactId.equals(relation.getContactId())) {
            return AjaxResult.error("关系不存在或不属于该联系人");
        }
        
        int result = contactResponsibleRelationService.deleteCrmContactResponsibleRelationByIds(new Long[]{relationId});
        if (result > 0) {
            return AjaxResult.success("移除负责人成功");
        } else {
            return AjaxResult.error("移除负责人失败");
        }
    } catch (Exception e) {
        logger.error("移除联系人负责人失败", e);
        return AjaxResult.error("移除联系人负责人失败：" + e.getMessage());
    }
}</code></pre>
            </div>
        </div>

        <div class="step-card">
            <h4>Step 3.2: 团队分配接口</h4>
            <div class="code-section">
                <div class="code-title">Java - 在CrmTeamController中添加</div>
                <pre><code class="language-java">/**
 * 团队负责人分配联系人给成员
 */
@PostMapping("/{teamId}/assign-contacts")
@PreAuthorize("@ss.hasPermi('crm:team:assign')")
public AjaxResult assignContactsToMember(@PathVariable("teamId") Long teamId,
                                        @RequestBody TeamAssignRequest request) {
    try {
        request.setTeamId(teamId);
        int result = contactResponsibleRelationService.assignContactsToTeamMember(request);
        return AjaxResult.success("成功分配 " + result + " 个联系人");
    } catch (ServiceException e) {
        return AjaxResult.error(e.getMessage());
    } catch (Exception e) {
        logger.error("团队分配联系人失败", e);
        return AjaxResult.error("团队分配失败：" + e.getMessage());
    }
}

/**
 * 获取团队业绩统计
 */
@GetMapping("/{teamId}/performance")
@PreAuthorize("@ss.hasPermi('crm:team:query')")
public AjaxResult getTeamPerformance(@PathVariable("teamId") Long teamId,
                                    @RequestParam(required = false) String startDate,
                                    @RequestParam(required = false) String endDate) {
    try {
        Date start = startDate != null ? DateUtils.parseDate(startDate) : DateUtils.addMonths(new Date(), -1);
        Date end = endDate != null ? DateUtils.parseDate(endDate) : new Date();
        
        TeamPerformanceVO performance = contactResponsibleRelationService.getTeamPerformance(teamId, start, end);
        return AjaxResult.success("查询成功", performance);
    } catch (Exception e) {
        logger.error("获取团队业绩统计失败", e);
        return AjaxResult.error("获取团队业绩失败：" + e.getMessage());
    }
}</code></pre>
            </div>
        </div>

        <h2>第四阶段：公海管理升级（Day 7）</h2>

        <div class="step-card">
            <h4>Step 4.1: 公海Controller</h4>
            <div class="code-section">
                <div class="code-title">Java - CrmContactPoolController.java</div>
                <pre><code class="language-java">package com.ruoyi.crm.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.crm.service.ICrmContactResponsibleRelationService;
import com.ruoyi.crm.domain.vo.PoolQueryRequest;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 联系人公海管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/crm/contact-pool")
public class CrmContactPoolController extends BaseController {
    
    @Autowired
    private ICrmContactResponsibleRelationService contactResponsibleRelationService;
    
    /**
     * 查询公海联系人列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PoolQueryRequest request) {
        // 设置默认值
        if (request.getUserId() == null) {
            request.setUserId(SecurityUtils.getUserId());
        }
        if (request.getQueryType() == null) {
            request.setQueryType("PERSONAL");
        }
        
        startPage();
        List<CrmContacts> list = contactResponsibleRelationService.selectPoolContacts(request);
        return getDataTable(list);
    }
    
    /**
     * 认领公海联系人
     */
    @PostMapping("/claim")
    public AjaxResult claimContacts(@RequestBody Map<String, Object> params) {
        try {
            Long[] contactIds = ((List<Integer>) params.get("contactIds"))
                .stream().map(Long::valueOf).toArray(Long[]::new);
            String businessType = (String) params.get("businessType");
            
            if (businessType == null || businessType.isEmpty()) {
                businessType = CrmContactResponsibleRelation.BusinessType.GENERAL;
            }
            
            int result = contactResponsibleRelationService.claimContactsFromPool(contactIds, businessType);
            return AjaxResult.success("成功认领 " + result + " 个联系人");
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            logger.error("认领公海联系人失败", e);
            return AjaxResult.error("认领失败：" + e.getMessage());
        }
    }
    
    /**
     * 退回联系人到公海
     */
    @PostMapping("/return")
    public AjaxResult returnContacts(@RequestBody Map<String, Object> params) {
        try {
            Long[] contactIds = ((List<Integer>) params.get("contactIds"))
                .stream().map(Long::valueOf).toArray(Long[]::new);
            String reason = (String) params.get("reason");
            Long responsiblePersonId = params.get("responsiblePersonId") != null ? 
                Long.valueOf(params.get("responsiblePersonId").toString()) : null;
            
            int result = contactResponsibleRelationService.returnContactsToPool(
                contactIds, responsiblePersonId, reason);
            return AjaxResult.success("成功退回 " + result + " 个联系人关系");
        } catch (Exception e) {
            logger.error("退回联系人到公海失败", e);
            return AjaxResult.error("退回失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量转移联系人
     */
    @PostMapping("/transfer")
    public AjaxResult transferContacts(@RequestBody Map<String, Object> params) {
        try {
            Long[] contactIds = ((List<Integer>) params.get("contactIds"))
                .stream().map(Long::valueOf).toArray(Long[]::new);
            Long toUserId = Long.valueOf(params.get("toUserId").toString());
            String businessType = (String) params.get("businessType");
            String remark = (String) params.get("remark");
            
            int result = contactResponsibleRelationService.transferContacts(
                contactIds, SecurityUtils.getUserId(), toUserId, businessType, remark);
            return AjaxResult.success("成功转移 " + result + " 个联系人");
        } catch (Exception e) {
            logger.error("转移联系人失败", e);
            return AjaxResult.error("转移失败：" + e.getMessage());
        }
    }
}</code></pre>
            </div>
        </div>

        <h2>测试用例示例（Day 8）</h2>

        <div class="test-case">
            <h4>测试场景1：团队负责人分配联系人</h4>
            <pre><code class="language-java">@Test
public void testTeamLeaderAssignContacts() {
    // 准备测试数据
    TeamAssignRequest request = new TeamAssignRequest();
    request.setTeamId(1L);
    request.setAssignToUserId(102L);
    request.setContactIds(Arrays.asList(1001L, 1002L, 1003L));
    request.setBusinessType("PACKAGING");
    
    // 模拟团队负责人登录
    SecurityUtils.setUserId(101L);
    
    // 执行分配
    int result = service.assignContactsToTeamMember(request);
    
    // 验证结果
    assertEquals(3, result);
    
    // 验证关系创建
    List<CrmContactResponsibleRelation> relations = 
        mapper.selectByResponsiblePersonId(102L);
    assertEquals(3, relations.size());
    assertTrue(relations.stream().allMatch(r -> 
        "TEAM_ASSIGN".equals(r.getAssignType())));
}</code></pre>
        </div>

        <h2>API接口清单</h2>

        <table class="api-table">
            <thead>
                <tr>
                    <th>接口路径</th>
                    <th>方法</th>
                    <th>说明</th>
                    <th>权限标识</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>/crm/contacts/{id}/responsibles</td>
                    <td>GET</td>
                    <td>获取联系人的所有负责人</td>
                    <td>crm:contacts:query</td>
                </tr>
                <tr>
                    <td>/crm/contacts/{id}/responsibles</td>
                    <td>POST</td>
                    <td>为联系人添加负责人</td>
                    <td>crm:contacts:edit</td>
                </tr>
                <tr>
                    <td>/crm/team/{teamId}/assign-contacts</td>
                    <td>POST</td>
                    <td>团队负责人分配联系人</td>
                    <td>crm:team:assign</td>
                </tr>
                <tr>
                    <td>/crm/team/{teamId}/performance</td>
                    <td>GET</td>
                    <td>获取团队业绩统计</td>
                    <td>crm:team:query</td>
                </tr>
                <tr>
                    <td>/crm/contact-pool/list</td>
                    <td>GET</td>
                    <td>查询公海联系人</td>
                    <td>crm:pool:list</td>
                </tr>
                <tr>
                    <td>/crm/contact-pool/claim</td>
                    <td>POST</td>
                    <td>认领公海联系人</td>
                    <td>crm:pool:claim</td>
                </tr>
                <tr>
                    <td>/crm/contact-pool/return</td>
                    <td>POST</td>
                    <td>退回联系人到公海</td>
                    <td>crm:pool:return</td>
                </tr>
            </tbody>
        </table>

        <h2>实施清单</h2>

        <div class="checklist">
            <h4>✅ 数据库层面</h4>
            <ul>
                <li>创建 crm_contact_responsible_relation 表</li>
                <li>添加必要的索引优化查询性能</li>
                <li>执行数据迁移脚本</li>
                <li>验证数据完整性</li>
            </ul>
            
            <h4>✅ 后端开发</h4>
            <ul>
                <li>创建 CrmContactResponsibleRelation 实体类</li>
                <li>创建 Mapper 接口和 XML 映射文件</li>
                <li>实现 Service 层核心业务逻辑</li>
                <li>扩展 Controller 层 API 接口</li>
                <li>添加权限控制注解</li>
            </ul>
            
            <h4>✅ 测试验证</h4>
            <ul>
                <li>修复原有的集成测试</li>
                <li>添加多对多关系测试用例</li>
                <li>团队分配功能测试</li>
                <li>公海管理功能测试</li>
                <li>性能测试和优化</li>
            </ul>
            
            <h4>✅ 前端适配（基于实际权限模型）</h4>
            <div class="warning-box" style="margin: 15px 0; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                <h5>🔍 现有权限模型分析</h5>
                <p><strong>联系人列表筛选：</strong>mine(我的) | subordinate(下属的) | following(关注的) | all(全部-管理员)</p>
                <p><strong>权限分级：</strong>普通业务员 → 团队负责人 → 管理员</p>
            </div>
            
            <h5>Phase 1: 最小化必要改动</h5>
            <ul>
                <li><strong>联系人列表页：</strong>增加 "unassigned"(无负责人) 筛选选项</li>
                <li><strong>团队管理页：</strong>团队负责人可见"分配联系人"功能入口</li>
                <li><strong>联系人详情页：</strong>显示当前用户在此联系人中的业务类型</li>
            </ul>
            
            <h5>Phase 2: 渐进式优化（后期实施）</h5>
            <ul>
                <li><strong>独立公海管理页：</strong>根据使用反馈决定是否需要</li>
                <li><strong>多维度统计报表：</strong>个人/团队/部门层级统计</li>
                <li><strong>业务类型筛选：</strong>按包装/原型/3D打印等维度筛选</li>
            </ul>
            
            <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <h5>💡 关键设计原则</h5>
                <ul>
                    <li><strong>权限隔离：</strong>普通业务员只能看到自己负责的联系人</li>
                    <li><strong>功能渐进：</strong>先实现核心功能，再根据反馈优化界面</li>
                    <li><strong>向后兼容：</strong>保持现有操作习惯，不影响现有工作流程</li>
                </ul>
            </div>
        </div>

        <div class="warning-box">
            <h4>⚠️ 注意事项</h4>
            <ul>
                <li><strong>向后兼容：</strong>保留原有的 responsible_person_id 字段，确保旧功能正常</li>
                <li><strong>数据一致性：</strong>使用事务确保关系表和团队关联表的一致性</li>
                <li><strong>权限控制：</strong>团队负责人才能分配，普通成员只能认领</li>
                <li><strong>性能优化：</strong>大数据量查询需要合理使用索引和分页</li>
                <li><strong>业务规则：</strong>同一联系人在同一业务类型下只能有一个负责人</li>
            </ul>
        </div>

        <h2>📱 前端具体改动清单</h2>
        
        <div class="implementation-section">
            <h4>1. 联系人列表页改动 (/front/crm/contacts)</h4>
            <div class="code-section">
                <div class="code-title">Vue - 筛选组件扩展</div>
                <pre><code class="language-javascript">// 在现有筛选选项中增加
const filterOptions = [
  { label: '我负责的', value: 'mine' },          // 现有
  { label: '下属负责的', value: 'subordinate' },   // 现有  
  { label: '我关注的', value: 'following' },      // 现有
  { label: '全部联系人', value: 'all' },          // 现有
  { label: '无负责人', value: 'unassigned' }     // 新增 ⭐
]

// 新增业务类型筛选（可选）
const businessTypeOptions = [
  { label: '全部业务', value: '' },
  { label: '包装业务', value: 'PACKAGING' },
  { label: '原型业务', value: 'PROTOTYPE' },
  { label: '3D打印', value: '3D_PRINTING' }
]</code></pre>
            </div>
        </div>

        <div class="implementation-section">
            <h4>2. 团队管理页改动 (/crm/team)</h4>
            <div class="code-section">
                <div class="code-title">Vue - 团队负责人专用功能</div>
                <pre><code class="language-javascript">// 仅团队负责人可见
<el-button 
  v-if="isTeamLeader" 
  type="primary" 
  @click="openAssignDialog">
  分配联系人
</el-button>

// 分配对话框
<el-dialog title="分配联系人给团队成员" v-model="assignDialogVisible">
  <el-form>
    <el-form-item label="选择成员">
      <el-select v-model="assignForm.assignToUserId">
        <el-option 
          v-for="member in teamMembers" 
          :key="member.userId"
          :label="member.nickName"
          :value="member.userId">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="业务类型">
      <el-select v-model="assignForm.businessType">
        <el-option label="包装业务" value="PACKAGING"></el-option>
        <el-option label="原型业务" value="PROTOTYPE"></el-option>
        <!-- 其他业务类型 -->
      </el-select>
    </el-form-item>
  </el-form>
</el-dialog></code></pre>
            </div>
        </div>

        <div class="implementation-section">
            <h4>3. 联系人详情页改动</h4>
            <div class="code-section">
                <div class="code-title">Vue - 负责人信息显示</div>
                <pre><code class="language-javascript">// 不显示所有负责人，只显示与当前用户相关的信息
<el-descriptions title="我的负责信息" v-if="myResponsibleInfo.length > 0">
  <el-descriptions-item 
    v-for="info in myResponsibleInfo" 
    :key="info.businessType"
    :label="info.businessTypeName">
    {{ info.startDate }} 开始负责
  </el-descriptions-item>
</el-descriptions>

// 团队负责人额外可见团队分布（可选）
<el-descriptions title="团队负责分布" v-if="isTeamLeader && teamResponsibleInfo.length > 0">
  <el-descriptions-item 
    v-for="info in teamResponsibleInfo" 
    :key="info.id"
    :label="info.businessTypeName">
    {{ info.responsiblePersonName }} ({{ info.teamName }})
  </el-descriptions-item>
</el-descriptions></code></pre>
            </div>
        </div>

        <div class="step-card">
            <h4>权限控制代码示例</h4>
            <div class="code-section">
                <div class="code-title">Vue - 权限判断逻辑</div>
                <pre><code class="language-javascript">computed: {
  // 判断是否为团队负责人
  isTeamLeader() {
    return this.userTeams.some(team => 
      team.leaderId === this.$store.state.user.userId
    );
  },
  
  // 获取当前用户在此联系人中的负责信息
  myResponsibleInfo() {
    return this.contactResponsibleList.filter(item => 
      item.responsiblePersonId === this.$store.state.user.userId &&
      item.relationStatus === 'ACTIVE'
    );
  },
  
  // 团队负责人可见的团队分布信息
  teamResponsibleInfo() {
    if (!this.isTeamLeader) return [];
    return this.contactResponsibleList.filter(item => 
      this.myTeamMemberIds.includes(item.responsiblePersonId) &&
      item.relationStatus === 'ACTIVE'
    );
  }
}</code></pre>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #e8f5e8; border-radius: 5px;">
            <h3>🎯 实施建议</h3>
            <p><strong>优先级：</strong>数据库 → 后端核心 → API接口 → 测试验证 → 前端适配Phase1</p>
            <p><strong>关键点：</strong>确保数据迁移安全，保持向后兼容，逐步推进避免影响现有业务</p>
            <p><strong>前端策略：</strong>最小化改动，先实现核心功能，根据用户反馈迭代优化</p>
            <p style="color: #27ae60; font-weight: bold;">按照此方案实施，可以平滑升级到多负责人架构！</p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });

        // Auto highlight code blocks
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>