<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户公海功能补全计划</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- Prism.js for code highlighting -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.todo { background-color: #e74c3c; color: white; }
        .status.partial { background-color: #f39c12; color: white; }
        .status.done { background-color: #27ae60; color: white; }
        .method-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .method-item {
            margin: 8px 0;
            padding: 8px;
            border-left: 3px solid #ddd;
            background-color: white;
        }
        .method-item.critical {
            border-left-color: #e74c3c;
        }
        .method-item.important {
            border-left-color: #f39c12;
        }
        .method-item.normal {
            border-left-color: #3498db;
        }
        .mermaid-diagram {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        .code-section {
            margin: 20px 0;
        }
        .code-title {
            background-color: #495057;
            color: white;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin: 0;
            font-weight: bold;
            font-size: 14px;
        }
        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0 0 5px 5px !important;
            font-size: 13px !important;
        }
        .mermaid {
            font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif !important;
        }
        .task-priority {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin-left: 10px;
        }
        .priority-high { background-color: #e74c3c; color: white; }
        .priority-medium { background-color: #f39c12; color: white; }
        .priority-low { background-color: #95a5a6; color: white; }
        .timeline {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .timeline-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #3498db;
            background-color: white;
        }
        .risk-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .tech-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏊‍♂️ 客户公海功能补全计划</h1>
        
        <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;">
            <strong>📋 项目概述：</strong>完善CRM系统中的客户公海管理功能，实现客户在公海和私有客户之间的流转管理。
            <br><strong>⏰ 预计工期：</strong>3-5个工作日
            <br><strong>🎯 目标：</strong>让所有集成测试通过，确保功能完整可用
        </div>

        <h2>📊 当前状态分析</h2>
        
        <h3>已实现功能</h3>
        <div class="method-list">
            <div class="method-item normal">
                <strong>selectPoolCustomerList()</strong> <span class="status done">已实现</span>
                <br>查询公海客户列表，基础查询功能正常
            </div>
            <div class="method-item normal">
                <strong>selectMyCustomerList()</strong> <span class="status done">已实现</span>
                <br>查询用户负责的客户列表，数据转换逻辑完整
            </div>
        </div>

        <h3>未实现功能（关键问题）</h3>
        <div class="method-list">
            <div class="method-item critical">
                <strong>returnToPool()</strong> <span class="status todo">TODO</span>
                <br>将客户放入公海 - 核心业务逻辑缺失
            </div>
            <div class="method-item critical">
                <strong>claimCustomers()</strong> <span class="status todo">TODO</span>
                <br>认领客户 - 核心业务逻辑缺失
            </div>
            <div class="method-item important">
                <strong>checkClaimLimit()</strong> <span class="status todo">TODO</span>
                <br>认领限制检查 - 业务规则验证缺失
            </div>
            <div class="method-item important">
                <strong>recordOperationLog()</strong> <span class="status todo">TODO</span>
                <br>操作日志记录 - 审计功能缺失
            </div>
            <div class="method-item normal">
                <strong>autoReturnToPool()</strong> <span class="status todo">TODO</span>
                <br>自动回收客户 - 定时任务功能缺失
            </div>
        </div>

        <h2>🏗️ 实现方案设计</h2>

        <h3>1. 核心业务流程 - 客户认领 (claimCustomers)</h3>
        
        <div class="mermaid-diagram">
            <div class="mermaid">
sequenceDiagram
    participant C as Controller
    participant S as Service
    participant CLM as ClaimLimitsMapper
    participant CPM as CustomerPoolMapper
    participant CM as CustomerMapper

    C->>S: claimCustomers(customerIds)
    S->>S: 1. 参数验证(空列表检查)
    S->>S: 2. 获取当前用户信息
    S->>CLM: 3. checkClaimLimit(userId, count)
    CLM->>S: 返回限制检查结果
    S->>CPM: 4. 查询客户状态
    CPM->>S: 返回客户列表
    S->>S: 5. 验证客户是否在公海
    S->>CM: 6. 批量更新客户负责人
    CM->>S: 返回更新结果
    S->>CPM: 7. 更新公海状态
    CPM->>S: 返回更新结果
    S->>S: 8. 记录操作日志
    S->>C: 返回认领结果
            </div>
        </div>

        <div class="code-section">
            <div class="code-title">核心实现逻辑：</div>
            <pre><code class="language-java">@Override
@Transactional
public int claimCustomers(List&lt;Long&gt; customerIds) {
    // 1. 参数验证
    if (customerIds == null || customerIds.isEmpty()) {
        throw new ServiceException("请选择要认领的客户");
    }
    
    // 2. 获取当前用户
    Long userId = SecurityUtils.getUserId();
    
    // 3. 检查认领限制
    if (!checkClaimLimit(userId, customerIds.size())) {
        throw new ServiceException("超出认领限制");
    }
    
    // 4. 验证客户状态
    List&lt;CrmCustomerPool&gt; poolCustomers = customerPoolMapper
        .selectByCustomerIds(customerIds);
    validateCustomersInPool(poolCustomers, customerIds);
    
    // 5. 执行认领操作
    int result = customerMapper.batchUpdateResponsiblePerson(
        customerIds, String.valueOf(userId));
    
    // 6. 更新公海状态
    customerPoolMapper.batchUpdateStatus(customerIds, "CLAIMED");
    
    // 7. 记录操作日志
    recordClaimLog(customerIds, userId);
    
    return result;
}</code></pre>
        </div>

        <h3>2. 核心业务流程 - 放入公海 (returnToPool)</h3>
        
        <div class="mermaid-diagram">
            <div class="mermaid">
sequenceDiagram
    participant C as Controller
    participant S as Service
    participant CM as CustomerMapper
    participant CPM as CustomerPoolMapper

    C->>S: returnToPool(customerIds, reason, remark)
    S->>S: 1. 参数验证
    S->>CM: 2. 查询客户信息
    CM->>S: 返回客户列表
    S->>S: 3. 验证客户归属权限
    S->>CM: 4. 清空客户负责人
    CM->>S: 返回更新结果
    S->>CPM: 5. 插入/更新公海记录
    CPM->>S: 返回操作结果
    S->>S: 6. 记录操作日志
    S->>C: 返回操作结果
            </div>
        </div>

        <div class="code-section">
            <div class="code-title">核心实现逻辑：</div>
            <pre><code class="language-java">@Override
@Transactional
public int returnToPool(List&lt;Long&gt; customerIds, String reason, String remark) {
    // 1. 参数验证
    if (customerIds == null || customerIds.isEmpty()) {
        throw new ServiceException("请选择要放入公海的客户");
    }
    
    // 2. 查询客户信息
    List&lt;CrmCustomer&gt; customers = customerMapper
        .selectByIds(customerIds);
    
    // 3. 验证权限（只能放入自己负责的客户）
    Long currentUserId = SecurityUtils.getUserId();
    validateOwnership(customers, currentUserId);
    
    // 4. 清空负责人
    customerMapper.batchClearResponsiblePerson(customerIds);
    
    // 5. 更新公海状态
    Date now = new Date();
    List&lt;CrmCustomerPool&gt; poolRecords = customers.stream()
        .map(customer -&gt; createPoolRecord(customer, reason, remark, now))
        .collect(Collectors.toList());
    
    customerPoolMapper.batchInsertOrUpdate(poolRecords);
    
    // 6. 记录操作日志
    recordReturnLog(customerIds, reason, remark, currentUserId);
    
    return customers.size();
}</code></pre>
        </div>

        <h3>3. 业务规则验证 - 认领限制检查</h3>

        <div class="code-section">
            <div class="code-title">认领限制实现逻辑：</div>
            <pre><code class="language-java">@Override
public boolean checkClaimLimit(Long userId, int requestCount) {
    // 1. 查询用户认领限制配置
    CrmClaimLimits limits = claimLimitsMapper.selectByUserId(userId);
    if (limits == null || !limits.getEnabled()) {
        return true; // 无限制
    }
    
    // 2. 检查单次认领数量限制
    if (limits.getMaxClaimBatch() != null && 
        requestCount &gt; limits.getMaxClaimBatch()) {
        throw new ServiceException("单次认领数量不能超过" + 
            limits.getMaxClaimBatch() + "个");
    }
    
    // 3. 检查日认领限制
    if (limits.getMaxClaimDaily() != null) {
        int todayCount = getTodayClaimCount(userId);
        if (todayCount + requestCount &gt; limits.getMaxClaimDaily()) {
            throw new ServiceException("今日认领数量已达上限");
        }
    }
    
    // 4. 检查总认领限制
    if (limits.getMaxClaimTotal() != null) {
        int totalCount = getTotalClaimCount(userId);
        if (totalCount + requestCount &gt; limits.getMaxClaimTotal()) {
            throw new ServiceException("总认领数量已达上限");
        }
    }
    
    return true;
}</code></pre>
        </div>

        <h2>🗄️ 数据库设计补充</h2>

        <h3>需要新增的表结构</h3>
        <div class="code-section">
            <div class="code-title">1. 公海操作日志表 (crm_pool_operation_log)</div>
            <pre><code class="language-sql">CREATE TABLE crm_pool_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型:CLAIM/RETURN',
    operator_id BIGINT NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    reason VARCHAR(100) COMMENT '操作原因',
    remark VARCHAR(500) COMMENT '备注',
    operation_time DATETIME NOT NULL COMMENT '操作时间',
    ip_address VARCHAR(50) COMMENT 'IP地址'
);</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">2. 认领限制配置表 (crm_claim_limits)</div>
            <pre><code class="language-sql">CREATE TABLE crm_claim_limits (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID(NULL表示全局配置)',
    dept_id BIGINT COMMENT '部门ID',
    max_claim_daily INT COMMENT '日认领上限',
    max_claim_total INT COMMENT '总认领上限',
    max_claim_batch INT COMMENT '单次认领上限',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    create_time DATETIME,
    update_time DATETIME
);</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">3. 公海规则表补充字段</div>
            <pre><code class="language-sql">ALTER TABLE crm_pool_rules ADD COLUMN auto_return_enabled TINYINT(1) DEFAULT 0;
ALTER TABLE crm_pool_rules ADD COLUMN last_execution_time DATETIME;</code></pre>
        </div>

        <h2>🔧 技术实现要点</h2>

        <div class="tech-stack">
            <div class="tech-item">
                <h4>🔐 权限控制</h4>
                <ul>
                    <li>Spring Security集成</li>
                    <li>@PreAuthorize注解</li>
                    <li>用户权限验证</li>
                    <li>数据权限过滤</li>
                </ul>
            </div>
            <div class="tech-item">
                <h4>💾 事务管理</h4>
                <ul>
                    <li>@Transactional注解</li>
                    <li>批量操作优化</li>
                    <li>异常回滚处理</li>
                    <li>分布式事务考虑</li>
                </ul>
            </div>
            <div class="tech-item">
                <h4>🎯 并发控制</h4>
                <ul>
                    <li>数据库乐观锁</li>
                    <li>Redis分布式锁</li>
                    <li>客户状态一致性</li>
                    <li>重复认领防护</li>
                </ul>
            </div>
            <div class="tech-item">
                <h4>📝 日志审计</h4>
                <ul>
                    <li>操作日志记录</li>
                    <li>业务链路追踪</li>
                    <li>异常信息记录</li>
                    <li>性能监控</li>
                </ul>
            </div>
        </div>

        <h2>📅 实施计划</h2>

        <div class="timeline">
            <div class="timeline-item">
                <strong>第1天：基础框架搭建</strong> <span class="task-priority priority-high">高优先级</span>
                <ul>
                    <li>完善数据库表结构</li>
                    <li>创建缺失的Mapper接口和XML</li>
                    <li>实现基础的CRUD操作</li>
                </ul>
            </div>
            <div class="timeline-item">
                <strong>第2天：核心业务实现</strong> <span class="task-priority priority-high">高优先级</span>
                <ul>
                    <li>实现claimCustomers方法</li>
                    <li>实现returnToPool方法</li>
                    <li>添加参数验证和异常处理</li>
                </ul>
            </div>
            <div class="timeline-item">
                <strong>第3天：业务规则完善</strong> <span class="task-priority priority-medium">中优先级</span>
                <ul>
                    <li>实现checkClaimLimit认领限制</li>
                    <li>实现recordOperationLog日志记录</li>
                    <li>添加并发控制机制</li>
                </ul>
            </div>
            <div class="timeline-item">
                <strong>第4天：测试修复</strong> <span class="task-priority priority-high">高优先级</span>
                <ul>
                    <li>修复集成测试失败问题</li>
                    <li>完善权限控制测试</li>
                    <li>优化异常处理和错误消息</li>
                </ul>
            </div>
            <div class="timeline-item">
                <strong>第5天：优化完善</strong> <span class="task-priority priority-low">低优先级</span>
                <ul>
                    <li>实现autoReturnToPool自动回收</li>
                    <li>性能优化和代码重构</li>
                    <li>文档补充和代码注释</li>
                </ul>
            </div>
        </div>

        <h2>⚠️ 风险评估与预案</h2>

        <div class="risk-warning">
            <h4>🔴 高风险项</h4>
            <ul>
                <li><strong>并发认领冲突：</strong>多用户同时认领同一客户可能导致数据不一致
                    <br>→ <em>预案：实现分布式锁机制，确保原子操作</em></li>
                <li><strong>大批量操作性能：</strong>批量认领大量客户可能导致性能问题
                    <br>→ <em>预案：分批处理，添加进度反馈机制</em></li>
            </ul>
        </div>

        <div class="risk-warning">
            <h4>🟡 中风险项</h4>
            <ul>
                <li><strong>测试环境数据：</strong>测试数据准备和清理复杂
                    <br>→ <em>预案：使用事务回滚和测试数据工厂</em></li>
                <li><strong>权限配置：</strong>Spring Security配置可能影响测试
                    <br>→ <em>预案：创建专用的测试安全配置</em></li>
            </ul>
        </div>

        <h2>✅ 验收标准</h2>

        <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bee5eb;">
            <h4>功能验收标准：</h4>
            <ul>
                <li>✅ 所有集成测试通过（22个测试用例）</li>
                <li>✅ 客户认领功能正常，支持单个和批量操作</li>                
                <li>✅ 客户放入公海功能正常，支持原因和备注</li>
                <li>✅ 认领限制规则生效，超限时正确拒绝</li>
                <li>✅ 权限控制正常，未授权用户无法操作</li>
                <li>✅ 并发操作安全，无数据不一致</li>
                <li>✅ 操作日志完整记录，支持审计追踪</li>
            </ul>
        </div>

        <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;">
            <h4>性能验收标准：</h4>
            <ul>
                <li>📊 单次认领响应时间 < 2秒</li>
                <li>📊 批量认领100个客户 < 10秒</li>
                <li>📊 公海列表查询1000条记录 < 3秒</li>
                <li>📊 并发10用户同时操作无异常</li>
            </ul>
        </div>

        <h2>🚀 后续优化建议</h2>

        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ddd;">
            <ul>
                <li><strong>缓存优化：</strong>添加Redis缓存提升查询性能</li>
                <li><strong>消息队列：</strong>异步处理大批量操作</li>
                <li><strong>监控告警：</strong>添加业务指标监控</li>
                <li><strong>API文档：</strong>补充Swagger文档</li>
                <li><strong>移动端适配：</strong>支持移动端客户公海管理</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #e8f5e8; border-radius: 5px;">
            <h3>📋 计划状态</h3>
            <p><strong>文档版本：</strong>v1.0 | <strong>创建时间：</strong>2025-07-29 | <strong>状态：</strong>待审核</p>
            <p style="color: #666;">请仔细审阅此计划，确认无误后我们即可开始实施 🚀</p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });

        // Auto highlight code blocks
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>