package com.ruoyi.crm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.service.ICrmCustomerMatchingService;
import com.ruoyi.common.service.ICrmCustomerMatchingService.CustomerMatchInfo;
import com.ruoyi.common.service.ICrmCustomerMatchingService.CustomerMatchResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * CRM客户匹配控制器
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Api(tags = "CRM客户匹配")
@RestController
@RequestMapping("/crm/customer/matching")
public class CrmCustomerMatchingController extends BaseController {

    @Autowired
    private ICrmCustomerMatchingService customerMatchingService;

    /**
     * 根据电话号码匹配客户
     */
    @ApiOperation("根据电话号码匹配客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/phone")
    public AjaxResult matchByPhone(@ApiParam("电话号码") @RequestParam String phone) {
        CustomerMatchResult result = customerMatchingService.matchCustomerByPhone(phone);
        return success(result);
    }

    /**
     * 根据客户名称匹配客户
     */
    @ApiOperation("根据客户名称匹配客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/name")
    public AjaxResult matchByName(@ApiParam("客户名称") @RequestParam String customerName) {
        List<CustomerMatchResult> results = customerMatchingService.matchCustomerByName(customerName);
        return success(results);
    }

    /**
     * 根据邮箱匹配客户
     */
    @ApiOperation("根据邮箱匹配客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/email")
    public AjaxResult matchByEmail(@ApiParam("邮箱") @RequestParam String email) {
        CustomerMatchResult result = customerMatchingService.matchCustomerByEmail(email);
        return success(result);
    }

    /**
     * 综合匹配客户
     */
    @ApiOperation("综合匹配客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/comprehensive")
    public AjaxResult matchComprehensive(
            @ApiParam("电话号码") @RequestParam(required = false) String phone,
            @ApiParam("邮箱") @RequestParam(required = false) String email,
            @ApiParam("客户名称") @RequestParam(required = false) String customerName) {
        CustomerMatchResult result = customerMatchingService.matchCustomerComprehensive(phone, email, customerName);
        return success(result);
    }

    /**
     * 智能匹配客户
     */
    @ApiOperation("智能匹配客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @PostMapping("/smart")
    public AjaxResult smartMatch(@RequestBody CustomerMatchInfo customerInfo) {
        List<CustomerMatchResult> results = customerMatchingService.smartMatchCustomer(customerInfo);
        return success(results);
    }

    /**
     * 检查是否为新客户
     */
    @ApiOperation("检查是否为新客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/check/new")
    public AjaxResult checkNewCustomer(
            @ApiParam("电话号码") @RequestParam(required = false) String phone,
            @ApiParam("邮箱") @RequestParam(required = false) String email,
            @ApiParam("客户名称") @RequestParam(required = false) String customerName) {
        boolean isNewCustomer = customerMatchingService.isNewCustomer(phone, email, customerName);
        return success(isNewCustomer);
    }

    /**
     * 获取客户负责人信息
     */
    @ApiOperation("获取客户负责人信息")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/owner")
    public AjaxResult getCustomerOwner(@ApiParam("客户ID") @RequestParam Long customerId) {
        ICrmCustomerMatchingService.CustomerOwnerInfo ownerInfo = customerMatchingService.getCustomerOwner(customerId);
        return success(ownerInfo);
    }

    /**
     * 检查负责人状态
     */
    @ApiOperation("检查负责人状态")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/owner/status")
    public AjaxResult checkOwnerStatus(@ApiParam("负责人ID") @RequestParam Long ownerId) {
        ICrmCustomerMatchingService.OwnerStatusInfo statusInfo = customerMatchingService.checkOwnerStatus(ownerId);
        return success(statusInfo);
    }

    /**
     * 标准化电话号码
     */
    @ApiOperation("标准化电话号码")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/normalize/phone")
    public AjaxResult normalizePhone(@ApiParam("原始电话号码") @RequestParam String phone) {
        String normalizedPhone = customerMatchingService.normalizePhone(phone);
        return success(normalizedPhone);
    }

    /**
     * 标准化客户名称
     */
    @ApiOperation("标准化客户名称")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/normalize/name")
    public AjaxResult normalizeCustomerName(@ApiParam("原始客户名称") @RequestParam String customerName) {
        String normalizedName = customerMatchingService.normalizeCustomerName(customerName);
        return success(normalizedName);
    }

    /**
     * 批量匹配客户
     */
    @ApiOperation("批量匹配客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @Log(title = "批量客户匹配", businessType = BusinessType.OTHER)
    @PostMapping("/batch")
    public AjaxResult batchMatch(@RequestBody List<CustomerMatchInfo> customerInfoList) {
        try {
            List<List<CustomerMatchResult>> batchResults = customerInfoList.stream()
                    .map(customerMatchingService::smartMatchCustomer)
                    .collect(java.util.stream.Collectors.toList());
            
            return success(batchResults);
        } catch (Exception e) {
            logger.error("批量客户匹配失败", e);
            return error("批量匹配失败: " + e.getMessage());
        }
    }

    /**
     * 客户匹配统计
     */
    @ApiOperation("客户匹配统计")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/statistics")
    public AjaxResult getMatchingStatistics(
            @ApiParam("开始日期") @RequestParam(required = false) String startDate,
            @ApiParam("结束日期") @RequestParam(required = false) String endDate) {
        // TODO: 实现匹配统计逻辑
        return success("匹配统计功能待实现");
    }

    /**
     * 客户重复检测
     */
    @ApiOperation("客户重复检测")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @PostMapping("/duplicate/check")
    public AjaxResult checkDuplicate(@RequestBody CustomerMatchInfo customerInfo) {
        try {
            // 使用智能匹配检测重复
            List<CustomerMatchResult> results = customerMatchingService.smartMatchCustomer(customerInfo);
            
            // 过滤高置信度的匹配结果
            List<CustomerMatchResult> duplicates = results.stream()
                    .filter(result -> result.getConfidence() > 80.0)
                    .collect(java.util.stream.Collectors.toList());
            
            boolean hasDuplicate = !duplicates.isEmpty();
            
            AjaxResult result = success();
            result.put("hasDuplicate", hasDuplicate);
            result.put("duplicates", duplicates);
            result.put("duplicateCount", duplicates.size());
            
            return result;
        } catch (Exception e) {
            logger.error("客户重复检测失败", e);
            return error("重复检测失败: " + e.getMessage());
        }
    }

    /**
     * 客户匹配建议
     */
    @ApiOperation("客户匹配建议")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @PostMapping("/suggestion")
    public AjaxResult getMatchingSuggestion(@RequestBody CustomerMatchInfo customerInfo) {
        try {
            List<CustomerMatchResult> results = customerMatchingService.smartMatchCustomer(customerInfo);
            
            // 生成匹配建议
            String suggestion;
            if (results.isEmpty()) {
                suggestion = "未找到匹配的客户，建议创建新客户";
            } else {
                CustomerMatchResult bestMatch = results.get(0);
                if (bestMatch.getConfidence() > 90.0) {
                    suggestion = "发现高度匹配的客户，建议使用现有客户";
                } else if (bestMatch.getConfidence() > 70.0) {
                    suggestion = "发现可能匹配的客户，建议人工确认";
                } else {
                    suggestion = "匹配度较低，建议创建新客户";
                }
            }
            
            AjaxResult result = success();
            result.put("suggestion", suggestion);
            result.put("matches", results);
            result.put("matchCount", results.size());
            
            return result;
        } catch (Exception e) {
            logger.error("获取匹配建议失败", e);
            return error("获取建议失败: " + e.getMessage());
        }
    }

    /**
     * 客户匹配配置
     */
    @ApiOperation("获取客户匹配配置")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/config")
    public AjaxResult getMatchingConfig() {
        AjaxResult result = success();
        result.put("phoneMatchEnabled", true);
        result.put("emailMatchEnabled", true);
        result.put("nameMatchEnabled", true);
        result.put("confidenceThreshold", 70.0);
        result.put("duplicateThreshold", 80.0);
        result.put("maxMatchResults", 10);
        
        return result;
    }
}
