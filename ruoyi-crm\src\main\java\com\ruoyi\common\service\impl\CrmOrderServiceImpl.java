package com.ruoyi.common.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.dto.CrmOrderAssignmentDTO;
import com.ruoyi.common.domain.dto.CrmOrderDTO;
import com.ruoyi.common.domain.dto.CrmOrderQueryDTO;
import com.ruoyi.common.domain.entity.CrmOrder;
import com.ruoyi.common.domain.entity.CrmOrderAssignmentLog;
import com.ruoyi.common.domain.entity.CrmOrderItem;
import com.ruoyi.common.mapper.CrmBusinessConversionLogMapper;
import com.ruoyi.common.mapper.CrmOrderAssignmentLogMapper;
import com.ruoyi.common.mapper.CrmOrderItemMapper;
import com.ruoyi.common.mapper.CrmOrderMapper;
import com.ruoyi.common.service.ICrmOrderService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * CRM订单服务实现类
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Slf4j
@Service
public class CrmOrderServiceImpl implements ICrmOrderService {

    @Autowired
    private CrmOrderMapper crmOrderMapper;

    @Autowired
    private CrmOrderItemMapper crmOrderItemMapper;

    @Autowired
    private CrmOrderAssignmentLogMapper crmOrderAssignmentLogMapper;

    @Autowired
    private CrmBusinessConversionLogMapper crmBusinessConversionLogMapper;

    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public CrmOrder selectCrmOrderById(Long id) {
        CrmOrder order = crmOrderMapper.selectCrmOrderById(id);
        if (order != null) {
            // 查询订单项
            List<CrmOrderItem> orderItems = crmOrderItemMapper.selectCrmOrderItemByOrderId(id);
            order.setOrderItems(orderItems);
        }
        return order;
    }

    /**
     * 查询订单列表
     * 
     * @param crmOrder 订单
     * @return 订单集合
     */
    @Override
    public List<CrmOrder> selectCrmOrderList(CrmOrder crmOrder) {
        return crmOrderMapper.selectCrmOrderList(crmOrder);
    }

    /**
     * 分页查询订单列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Override
    public TableDataInfo selectCrmOrderPage(CrmOrderQueryDTO queryDTO) {
        // 设置分页参数
        if (queryDTO.getPageNum() != null && queryDTO.getPageSize() != null) {
            PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        }
        
        // 构建查询条件
        CrmOrder queryOrder = new CrmOrder();
        BeanUtils.copyProperties(queryDTO, queryOrder);
        
        // 执行查询
        List<CrmOrder> list = crmOrderMapper.selectCrmOrderList(queryOrder);
        
        // 构建分页结果
        PageInfo<CrmOrder> pageInfo = new PageInfo<>(list);
        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setCode(200);
        dataTable.setMsg("查询成功");
        dataTable.setRows(list);
        dataTable.setTotal(pageInfo.getTotal());
        
        return dataTable;
    }

    /**
     * 新增订单
     * 
     * @param crmOrder 订单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertCrmOrder(CrmOrder crmOrder) {
        // 设置创建信息
        crmOrder.setCreateTime(DateUtils.getNowDate());
        crmOrder.setCreateBy(SecurityUtils.getUsername());
        
        // 设置默认值
        if (StringUtils.isEmpty(crmOrder.getOrderSource())) {
            crmOrder.setOrderSource("MANUAL");
        }
        if (StringUtils.isEmpty(crmOrder.getOrderType())) {
            crmOrder.setOrderType("STANDARD");
        }
        if (StringUtils.isEmpty(crmOrder.getPriorityLevel())) {
            crmOrder.setPriorityLevel("NORMAL");
        }
        if (StringUtils.isEmpty(crmOrder.getAssignmentStatus())) {
            crmOrder.setAssignmentStatus("UNASSIGNED");
        }
        if (StringUtils.isEmpty(crmOrder.getStatus())) {
            crmOrder.setStatus("PENDING");
        }
        if (StringUtils.isEmpty(crmOrder.getCurrency())) {
            crmOrder.setCurrency("CNY");
        }
        if (crmOrder.getOrderDate() == null) {
            crmOrder.setOrderDate(new Date());
        }
        
        // 插入订单
        int result = crmOrderMapper.insertCrmOrder(crmOrder);
        
        // 插入订单项
        if (crmOrder.getOrderItems() != null && !crmOrder.getOrderItems().isEmpty()) {
            for (CrmOrderItem item : crmOrder.getOrderItems()) {
                item.setOrderId(crmOrder.getId());
                item.setCreateTime(DateUtils.getNowDate());
                item.setCreateBy(SecurityUtils.getUsername());
                crmOrderItemMapper.insertCrmOrderItem(item);
            }
        }
        
        return result;
    }

    /**
     * 修改订单
     * 
     * @param crmOrder 订单
     * @return 结果
     */
    @Override
    @Transactional
    public int updateCrmOrder(CrmOrder crmOrder) {
        crmOrder.setUpdateTime(DateUtils.getNowDate());
        crmOrder.setUpdateBy(SecurityUtils.getUsername());
        
        int result = crmOrderMapper.updateCrmOrder(crmOrder);
        
        // 更新订单项
        if (crmOrder.getOrderItems() != null) {
            // 删除原有订单项
            crmOrderItemMapper.deleteCrmOrderItemByOrderId(crmOrder.getId());
            
            // 插入新的订单项
            for (CrmOrderItem item : crmOrder.getOrderItems()) {
                item.setOrderId(crmOrder.getId());
                item.setCreateTime(DateUtils.getNowDate());
                item.setCreateBy(SecurityUtils.getUsername());
                crmOrderItemMapper.insertCrmOrderItem(item);
            }
        }
        
        return result;
    }

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键集合
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCrmOrderByIds(Long[] ids) {
        // 删除订单项
        for (Long id : ids) {
            crmOrderItemMapper.deleteCrmOrderItemByOrderId(id);
        }
        
        // 软删除订单
        return crmOrderMapper.deleteCrmOrderByIds(ids);
    }

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCrmOrderById(Long id) {
        // 删除订单项
        crmOrderItemMapper.deleteCrmOrderItemByOrderId(id);
        
        // 软删除订单
        return crmOrderMapper.deleteCrmOrderById(id);
    }

    /**
     * 获取订单详情（包含关联信息）
     * 
     * @param id 订单ID
     * @return 订单详情DTO
     */
    @Override
    public CrmOrderDTO getCrmOrderDetail(Long id) {
        CrmOrder order = selectCrmOrderById(id);
        if (order == null) {
            return null;
        }
        
        CrmOrderDTO orderDTO = new CrmOrderDTO();
        BeanUtils.copyProperties(order, orderDTO);
        orderDTO.setOrderItems(order.getOrderItems());
        
        // TODO: 查询关联的客户、联系人、商机等信息
        
        return orderDTO;
    }

    /**
     * 创建订单（包含订单项）
     * 
     * @param orderDTO 订单DTO
     * @return 结果
     */
    @Override
    @Transactional
    public int createCrmOrder(CrmOrderDTO orderDTO) {
        CrmOrder order = new CrmOrder();
        BeanUtils.copyProperties(orderDTO, order);
        
        return insertCrmOrder(order);
    }

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 新状态
     * @param operatorId 操作人ID
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderStatus(Long id, String status, Long operatorId) {
        CrmOrder order = new CrmOrder();
        order.setId(id);
        order.setStatus(status);
        order.setUpdateTime(DateUtils.getNowDate());
        order.setUpdateBy(SecurityUtils.getUsername());
        
        int result = crmOrderMapper.updateCrmOrder(order);
        
        // 记录状态变更日志
        // TODO: 实现状态变更日志记录
        
        return result;
    }

    /**
     * 批量更新订单状态
     * 
     * @param ids 订单ID列表
     * @param status 新状态
     * @param operatorId 操作人ID
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUpdateOrderStatus(List<Long> ids, String status, Long operatorId) {
        int result = 0;
        for (Long id : ids) {
            result += updateOrderStatus(id, status, operatorId);
        }
        return result;
    }

    /**
     * 分配订单
     * 
     * @param assignmentDTO 分配信息
     * @return 结果
     */
    @Override
    @Transactional
    public int assignOrder(CrmOrderAssignmentDTO assignmentDTO) {
        Long orderId = assignmentDTO.getOrderId();
        Long toUserId = assignmentDTO.getToUserId();
        String toUserName = assignmentDTO.getToUserName();
        
        // 检查订单是否可以分配
        if (!canAssignOrder(orderId, toUserId)) {
            throw new RuntimeException("订单不能分配给该用户");
        }
        
        // 获取原订单信息
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        // 更新订单分配信息
        CrmOrder updateOrder = new CrmOrder();
        updateOrder.setId(orderId);
        updateOrder.setOwnerId(toUserId);
        updateOrder.setOwnerName(toUserName);
        updateOrder.setAssignmentStatus("ASSIGNED");
        updateOrder.setAssignedBy(assignmentDTO.getOperatorId());
        updateOrder.setAssignedTime(new Date());
        updateOrder.setUpdateTime(DateUtils.getNowDate());
        updateOrder.setUpdateBy(SecurityUtils.getUsername());
        
        int result = crmOrderMapper.updateCrmOrder(updateOrder);
        
        // 记录分配历史
        CrmOrderAssignmentLog assignmentLog = new CrmOrderAssignmentLog();
        assignmentLog.setOrderId(orderId);
        assignmentLog.setActionType(CrmOrderAssignmentLog.ACTION_TYPE_ASSIGN);
        assignmentLog.setFromUserId(order.getOwnerId());
        assignmentLog.setFromUserName(order.getOwnerName());
        assignmentLog.setToUserId(toUserId);
        assignmentLog.setToUserName(toUserName);
        assignmentLog.setOperatorId(assignmentDTO.getOperatorId());
        assignmentLog.setOperatorName(assignmentDTO.getOperatorName());
        assignmentLog.setReason(assignmentDTO.getReason());
        assignmentLog.setOperationTime(new Date());
        assignmentLog.setIpAddress(assignmentDTO.getIpAddress());
        assignmentLog.setUserAgent(assignmentDTO.getUserAgent());
        
        crmOrderAssignmentLogMapper.insertCrmOrderAssignmentLog(assignmentLog);
        
        // TODO: 发送分配通知
        
        return result;
    }

    /**
     * 批量分配订单
     * 
     * @param assignmentDTO 分配信息
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAssignOrders(CrmOrderAssignmentDTO assignmentDTO) {
        int result = 0;
        for (Long orderId : assignmentDTO.getOrderIds()) {
            CrmOrderAssignmentDTO singleAssignment = new CrmOrderAssignmentDTO();
            BeanUtils.copyProperties(assignmentDTO, singleAssignment);
            singleAssignment.setOrderId(orderId);
            result += assignOrder(singleAssignment);
        }
        return result;
    }

    /**
     * 检查订单是否可以分配
     * 
     * @param orderId 订单ID
     * @param userId 目标用户ID
     * @return 检查结果
     */
    @Override
    public boolean canAssignOrder(Long orderId, Long userId) {
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null) {
            return false;
        }
        
        // 检查订单状态
        if ("CANCELLED".equals(order.getStatus()) || "COMPLETED".equals(order.getStatus())) {
            return false;
        }
        
        // TODO: 检查用户权限、工作量等
        
        return true;
    }

    /**
     * 检查用户是否可以抢单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 检查结果
     */
    @Override
    public boolean canGrabOrder(Long orderId, Long userId) {
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null) {
            return false;
        }
        
        // 检查订单是否未分配
        if (!"UNASSIGNED".equals(order.getAssignmentStatus())) {
            return false;
        }
        
        // 检查今日抢单次数
        int todayGrabCount = crmOrderAssignmentLogMapper.countTodayGrabsByUserId(userId);
        if (todayGrabCount >= 10) { // 假设每日最多抢单10次
            return false;
        }
        
        return true;
    }

    // 其他方法的实现将在后续添加...
    
    /**
     * 转移订单
     *
     * @param assignmentDTO 转移信息
     * @return 结果
     */
    @Override
    @Transactional
    public int transferOrder(CrmOrderAssignmentDTO assignmentDTO) {
        Long orderId = assignmentDTO.getOrderId();
        Long fromUserId = assignmentDTO.getFromUserId();
        Long toUserId = assignmentDTO.getToUserId();

        // 获取原订单信息
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        // 检查原负责人
        if (!order.getOwnerId().equals(fromUserId)) {
            throw new RuntimeException("只能转移自己负责的订单");
        }

        // 更新订单负责人
        CrmOrder updateOrder = new CrmOrder();
        updateOrder.setId(orderId);
        updateOrder.setOwnerId(toUserId);
        updateOrder.setOwnerName(assignmentDTO.getToUserName());
        updateOrder.setUpdateTime(DateUtils.getNowDate());
        updateOrder.setUpdateBy(SecurityUtils.getUsername());

        int result = crmOrderMapper.updateCrmOrder(updateOrder);

        // 记录转移历史
        CrmOrderAssignmentLog assignmentLog = new CrmOrderAssignmentLog();
        assignmentLog.setOrderId(orderId);
        assignmentLog.setActionType(CrmOrderAssignmentLog.ACTION_TYPE_TRANSFER);
        assignmentLog.setFromUserId(fromUserId);
        assignmentLog.setFromUserName(assignmentDTO.getFromUserName());
        assignmentLog.setToUserId(toUserId);
        assignmentLog.setToUserName(assignmentDTO.getToUserName());
        assignmentLog.setOperatorId(assignmentDTO.getOperatorId());
        assignmentLog.setOperatorName(assignmentDTO.getOperatorName());
        assignmentLog.setReason(assignmentDTO.getReason());
        assignmentLog.setOperationTime(new Date());

        crmOrderAssignmentLogMapper.insertCrmOrderAssignmentLog(assignmentLog);

        return result;
    }

    /**
     * 抢单
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int grabOrder(Long orderId, Long userId) {
        // 检查是否可以抢单
        if (!canGrabOrder(orderId, userId)) {
            throw new RuntimeException("不能抢单");
        }

        // 获取订单信息
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        // 更新订单分配信息
        CrmOrder updateOrder = new CrmOrder();
        updateOrder.setId(orderId);
        updateOrder.setOwnerId(userId);
        updateOrder.setAssignmentStatus("ASSIGNED");
        updateOrder.setAssignedBy(userId);
        updateOrder.setAssignedTime(new Date());
        updateOrder.setUpdateTime(DateUtils.getNowDate());
        updateOrder.setUpdateBy(SecurityUtils.getUsername());

        int result = crmOrderMapper.updateCrmOrder(updateOrder);

        // 记录抢单历史
        CrmOrderAssignmentLog assignmentLog = new CrmOrderAssignmentLog();
        assignmentLog.setOrderId(orderId);
        assignmentLog.setActionType(CrmOrderAssignmentLog.ACTION_TYPE_GRAB);
        assignmentLog.setToUserId(userId);
        assignmentLog.setOperatorId(userId);
        assignmentLog.setReason("用户抢单");
        assignmentLog.setOperationTime(new Date());

        crmOrderAssignmentLogMapper.insertCrmOrderAssignmentLog(assignmentLog);

        return result;
    }

    /**
     * 回收订单到公海池
     *
     * @param orderId 订单ID
     * @param operatorId 操作人ID
     * @param reason 回收原因
     * @return 结果
     */
    @Override
    @Transactional
    public int reclaimOrder(Long orderId, Long operatorId, String reason) {
        // 获取订单信息
        CrmOrder order = crmOrderMapper.selectCrmOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        // 更新订单为未分配状态
        CrmOrder updateOrder = new CrmOrder();
        updateOrder.setId(orderId);
        updateOrder.setOwnerId(null);
        updateOrder.setOwnerName(null);
        updateOrder.setAssignmentStatus("UNASSIGNED");
        updateOrder.setUpdateTime(DateUtils.getNowDate());
        updateOrder.setUpdateBy(SecurityUtils.getUsername());

        int result = crmOrderMapper.updateCrmOrder(updateOrder);

        // 记录回收历史
        CrmOrderAssignmentLog assignmentLog = new CrmOrderAssignmentLog();
        assignmentLog.setOrderId(orderId);
        assignmentLog.setActionType(CrmOrderAssignmentLog.ACTION_TYPE_RECLAIM);
        assignmentLog.setFromUserId(order.getOwnerId());
        assignmentLog.setFromUserName(order.getOwnerName());
        assignmentLog.setOperatorId(operatorId);
        assignmentLog.setReason(reason);
        assignmentLog.setOperationTime(new Date());

        crmOrderAssignmentLogMapper.insertCrmOrderAssignmentLog(assignmentLog);

        return result;
    }

    @Override
    public TableDataInfo getMyOrders(Long userId, CrmOrderQueryDTO queryDTO) {
        // TODO: 实现我的订单查询
        return null;
    }

    @Override
    public TableDataInfo getUnassignedOrders(CrmOrderQueryDTO queryDTO) {
        // TODO: 实现未分配订单查询
        return null;
    }

    @Override
    public TableDataInfo getDeptOrders(Long deptId, CrmOrderQueryDTO queryDTO) {
        // TODO: 实现部门订单查询
        return null;
    }

    @Override
    public int convertOpportunityToOrder(Long opportunityId, CrmOrderDTO orderDTO) {
        // TODO: 实现商机转订单
        return 0;
    }

    @Override
    public int convertOrderToContract(Long orderId, Object contractData) {
        // TODO: 实现订单转合同
        return 0;
    }

    @Override
    public int batchConvertOrdersToContract(List<Long> orderIds, Object contractData) {
        // TODO: 实现批量订单转合同
        return 0;
    }

    @Override
    public Object getOrderStatistics(Long userId) {
        // TODO: 实现订单统计
        return null;
    }

    @Override
    public List<Object> getOrderTrendData(String startDate, String endDate, String groupBy) {
        // TODO: 实现订单趋势数据
        return null;
    }

    @Override
    public List<Object> getOrderAssignmentHistory(Long orderId) {
        // TODO: 实现分配历史查询
        return null;
    }

    @Override
    public List<CrmOrder> exportOrderData(CrmOrderQueryDTO queryDTO) {
        // TODO: 实现订单导出
        return null;
    }

    @Override
    public Object importOrderData(List<CrmOrder> orderList, Long operatorId) {
        // TODO: 实现订单导入
        return null;
    }
}
