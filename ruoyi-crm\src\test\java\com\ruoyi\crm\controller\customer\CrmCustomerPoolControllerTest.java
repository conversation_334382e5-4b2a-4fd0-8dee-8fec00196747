package com.ruoyi.crm.controller.customer;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.ruoyi.crm.service.ICrmCustomerPoolService;

/**
 * 客户公海控制器测试
 */
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
@Transactional
public class CrmCustomerPoolControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmCustomerPoolService customerPoolService;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void testGetPoolCustomers() throws Exception {
        mockMvc.perform(get("/crm/customer/pool")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testGetOwnCustomers() throws Exception {
        mockMvc.perform(get("/crm/customer/own")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testClaimCustomer() throws Exception {
        // 需要先有公海客户才能测试认领
        // 这里使用假设的客户ID进行测试
        Long customerId = 1L;
        
        mockMvc.perform(post("/crm/customer/{id}/claim", customerId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void testReturnToPool() throws Exception {
        Long customerId = 1L;
        
        mockMvc.perform(post("/crm/customer/{id}/return", customerId)
                .param("reason", "MANUAL")
                .param("remark", "测试放入公海")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void testBatchClaimCustomers() throws Exception {
        String requestBody = "[1, 2, 3]";
        
        mockMvc.perform(post("/crm/customer/batch/claim")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void testBatchReturnToPool() throws Exception {
        String requestBody = "{\"customerIds\":[1,2,3],\"reason\":\"MANUAL\",\"remark\":\"批量测试\"}";
        
        mockMvc.perform(post("/crm/customer/batch/return")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}