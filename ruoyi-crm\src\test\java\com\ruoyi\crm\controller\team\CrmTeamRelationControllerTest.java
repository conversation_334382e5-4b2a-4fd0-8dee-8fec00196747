package com.ruoyi.crm.controller;

import com.ruoyi.common.domain.entity.CrmTeamRelation;
import com.ruoyi.crm.service.ICrmTeamRelationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 团队关联控制器测试
 * 用于验证团队分配功能的数据问题修复
 */
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Transactional
public class CrmTeamRelationControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmTeamRelationService crmTeamRelationService;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @DisplayName("测试根据业务对象查询团队 - API响应数据结构")
    void testGetTeamByBiz_ApiResponseStructure() throws Exception {
        // 准备测试数据
        Long testTeamId = 1L;
        Long testContactId = 1L;
        String bizType = "CONTACT";

        // 先分配一个团队给联系人
        crmTeamRelationService.assignTeamToBiz(testTeamId, testContactId, bizType);

        // 测试API响应
        mockMvc.perform(get("/crm/relation/team")
                .param("bizId", testContactId.toString())
                .param("bizType", bizType))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.teamId").value(testTeamId))
                .andExpect(jsonPath("$.data.teamName").exists()) // 验证团队名称字段存在
                .andExpect(jsonPath("$.data.leaderName").exists()) // 验证负责人字段存在
                .andExpect(jsonPath("$.data.relationType").value(bizType))
                .andExpect(jsonPath("$.data.relationId").value(testContactId));
    }

    @Test
    @DisplayName("测试根据业务对象查询团队 - 无分配情况")
    void testGetTeamByBiz_NoAssignment() throws Exception {
        Long testContactId = 999L; // 使用一个不存在分配的ID
        String bizType = "CONTACT";

        mockMvc.perform(get("/crm/relation/team")
                .param("bizId", testContactId.toString())
                .param("bizType", bizType))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").doesNotExist()); // 无分配时应返回null
    }

    @Test
    @DisplayName("测试分配团队到业务对象")
    void testAssignTeamToBiz() throws Exception {
        Long testTeamId = 1L;
        Long testContactId = 2L;
        String bizType = "CONTACT";

        // 执行分配
        mockMvc.perform(post("/crm/relation/assign")
                .param("teamId", testTeamId.toString())
                .param("bizId", testContactId.toString())
                .param("bizType", bizType))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证分配结果
        CrmTeamRelation relation = crmTeamRelationService.getTeamRelationByBiz(testContactId, bizType);
        assert relation != null;
        assert relation.getTeamId().equals(testTeamId);
        assert relation.getTeamName() != null; // 验证团队名称已填充
        assert relation.getLeaderName() != null; // 验证负责人名称已填充
    }

    @Test
    @DisplayName("测试取消团队分配")
    void testUnassignTeamFromBiz() throws Exception {
        Long testTeamId = 1L;
        Long testContactId = 3L;
        String bizType = "CONTACT";

        // 先分配
        crmTeamRelationService.assignTeamToBiz(testTeamId, testContactId, bizType);

        // 执行取消分配
        mockMvc.perform(post("/crm/relation/unassign")
                .param("bizId", testContactId.toString())
                .param("bizType", bizType))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证取消分配结果
        CrmTeamRelation relation = crmTeamRelationService.getTeamRelationByBiz(testContactId, bizType);
        assert relation == null; // 应该没有分配记录
    }

    @Test
    @DisplayName("测试数据完整性 - 团队信息关联查询")
    void testDataIntegrity_TeamInfoJoin() throws Exception {
        Long testTeamId = 1L;
        Long testContactId = 4L;
        String bizType = "CONTACT";

        // 分配团队
        crmTeamRelationService.assignTeamToBiz(testTeamId, testContactId, bizType);

        // 获取关联信息
        CrmTeamRelation relation = crmTeamRelationService.getTeamRelationByBiz(testContactId, bizType);

        // 验证数据完整性
        assert relation != null : "关联记录不应为空";
        assert relation.getTeamId() != null : "团队ID不应为空";
        assert relation.getTeamName() != null : "团队名称不应为空";
        assert relation.getLeaderName() != null : "负责人名称不应为空（即使是'--'）";
        assert relation.getRelationType().equals(bizType) : "业务类型应匹配";
        assert relation.getRelationId().equals(testContactId) : "业务ID应匹配";

        System.out.println("团队分配信息验证通过:");
        System.out.println("- 团队ID: " + relation.getTeamId());
        System.out.println("- 团队名称: " + relation.getTeamName());
        System.out.println("- 负责人: " + relation.getLeaderName());
        System.out.println("- 业务类型: " + relation.getRelationType());
        System.out.println("- 业务ID: " + relation.getRelationId());
    }

    @Test
    @DisplayName("测试重复分配处理")
    void testDuplicateAssignment() throws Exception {
        Long testTeamId = 1L;
        Long testContactId = 5L;
        String bizType = "CONTACT";

        // 第一次分配
        crmTeamRelationService.assignTeamToBiz(testTeamId, testContactId, bizType);

        // 尝试重复分配（应该失败或更新）
        try {
            crmTeamRelationService.assignTeamToBiz(testTeamId, testContactId, bizType);
            // 如果没有抛出异常，验证是否只有一条记录
            CrmTeamRelation relation = crmTeamRelationService.getTeamRelationByBiz(testContactId, bizType);
            assert relation != null : "应该有分配记录";
        } catch (Exception e) {
            // 如果抛出异常，说明有唯一约束保护，这是正确的
            System.out.println("重复分配被正确阻止: " + e.getMessage());
        }
    }
}
