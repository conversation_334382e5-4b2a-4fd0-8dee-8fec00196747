<template>
    <el-container class="customer-management">
        <!-- 使用新的导航组件 -->
        <side-nav
            v-model="activeTab"
            :title="navConfig.title"
            :menu-items="navConfig.menuItems"
        />

        <!-- 主内容区域 -->
        <el-container class="main-container">
            <el-header class="header">
                <h1>客户管理</h1>
                <div class="header-actions">
                    <el-button 
                        type="success" 
                        plain
                        size="small"
                        :disabled="selectedCustomers.length === 0"
                        @click="handleBatchFollow"
                        class="action-btn"
                    >
                        <el-icon><Star /></el-icon>
                        批量关注
                    </el-button>
                    <el-button 
                        type="warning" 
                        plain
                        size="small"
                        :disabled="selectedCustomers.length === 0"
                        @click="handleBatchUnfollow"
                        class="action-btn"
                    >
                        <el-icon><StarFilled /></el-icon>
                        取消关注
                    </el-button>
                    <el-button 
                        type="info" 
                        plain
                        size="small"
                        :disabled="selectedCustomers.length === 0"
                        @click="handleBatchAssign"
                        class="action-btn"
                    >
                        <el-icon><Share /></el-icon>
                        批量分配
                    </el-button>
                    <el-button 
                        type="primary" 
                        size="small"
                        @click="openCustomerDialog"
                        class="action-btn primary-btn"
                    >
                        <el-icon><Plus /></el-icon>
                        新建客户
                    </el-button>
                </div>
            </el-header>

            <el-main>
                <!-- 使用统一的筛选组件 -->
                <common-filter
                    v-model:searchValue="searchInput"
                    v-model:filterValue="filterType"
                    :config="filterConfig"
                    @search="handleSearch"
                    @filter="handleFilterChange"
                />

                <!-- 数据表格 -->
                <el-table 
                    ref="customerTable" 
                    :data="customers" 
                    v-loading="loading"
                    border 
                    sortable 
                    tooltip-effect="dark"
                    :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                    style="width: 100%; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
                    @selection-change="handleSelectionChange">
                    
                    <el-table-column type="selection" width="55" />
                    
                    <el-table-column prop="customerName" label="客户名称" min-width="150">
                        <template #default="scope">
                            <el-button link type="primary" class="link-button" @click="openDrawer(scope.row)">
                                {{ scope.row.customerName }}
                            </el-button>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="mobile" label="手机号" min-width="130" />
                    
                    <el-table-column prop="phone" label="电话" min-width="130" />
                    
                    <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
                    
                    <el-table-column prop="customerIndustry" label="所属行业" min-width="120" />
                    
                    <el-table-column prop="customerLevel" label="客户级别" min-width="120" />
                    
                    <el-table-column prop="customerSource" label="客户来源" min-width="120" />
                    
                    <el-table-column prop="status" label="状态" min-width="80">
                        <template #default="scope">
                            <el-tag v-if="scope.row.status" :type="scope.row.status === '1' ? 'success' : 'info'">
                                {{ scope.row.status === '1' ? '正常' : '禁用' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="isFollowing" label="关注状态" width="100">
                        <template #default="scope">
                            <el-tag :type="scope.row.isFollowing ? 'success' : 'info'" size="small">
                                <el-icon style="margin-right: 4px;">
                                    <StarFilled v-if="scope.row.isFollowing" />
                                    <Star v-else />
                                </el-icon>
                                {{ scope.row.isFollowing ? '已关注' : '未关注' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="createTime" label="创建时间" min-width="150" />
                    
                    <table-operations :buttons="tableButtons" />
                </el-table>
                
                <!-- 分页组件 -->
                <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize" @pagination="handlePagination" />
            </el-main>
        </el-container>

        <!-- 抽屉组件 -->
        <common-drawer 
            v-model="drawerVisible" 
            entity-type="customer" 
            :model-name="'客户'" 
            :entity-data="selectedCustomer" 
            :drawer-config="localDrawerConfig"
            :header-component="CustomerHeaderTab" 
            :header-props="{ userOptions }"
            :actions="drawerActions"
            @update:entity-data="handleCustomerUpdate"
            @toggle-follow="handleDrawerToggleFollow"
        />
                    <el-descriptions-item label="客户名称">
                        {{ selectedCustomer.customerName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="手机号">
                        {{ selectedCustomer.mobile || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="电话">
                        {{ selectedCustomer.phone || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="邮箱">
                        {{ selectedCustomer.email || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="所属行业">
                        {{ selectedCustomer.customerIndustry || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="客户级别">
                        {{ selectedCustomer.customerLevel || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="客户来源">
                        {{ selectedCustomer.customerSource || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                        <el-tag :type="selectedCustomer.status === '1' ? 'success' : 'info'">
                            {{ selectedCustomer.status === '1' ? '正常' : '禁用' }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="关注状态">
                        <el-tag :type="selectedCustomer.isFollowing ? 'success' : 'info'" size="small">
                            <el-icon style="margin-right: 4px;">
                                <StarFilled v-if="selectedCustomer.isFollowing" />
                                <Star v-else />
                            </el-icon>
                            {{ selectedCustomer.isFollowing ? '已关注' : '未关注' }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="网址">
                        {{ selectedCustomer.website || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="地址">
                        {{ selectedCustomer.customerAddress || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="首要联系人">
                        {{ selectedCustomer.primaryContact || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="成交状态">
                        {{ selectedCustomer.dealStatus || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="下次联系时间">
                        {{ selectedCustomer.nextContactTime || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="负责人 ID">
                        {{ selectedCustomer.responsiblePersonId || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间">
                        {{ selectedCustomer.createTime || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="更新时间">
                        {{ selectedCustomer.updateTime || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="备注" :span="2">
                        {{ selectedCustomer.remarks || '-' }}
                    </el-descriptions-item>
                </el-descriptions>
                
                <!-- 关注操作按钮 -->
                <div style="margin-top: 20px; text-align: center;">
                    <el-button
                        :type="selectedCustomer.isFollowing ? 'warning' : 'success'"
                        @click="handleToggleFollow(selectedCustomer)"
                        :loading="detailLoading"
                    >
                        <el-icon>
                            <StarFilled v-if="selectedCustomer.isFollowing" />
                            <Star v-else />
                        </el-icon>
                        {{ selectedCustomer.isFollowing ? '取消关注' : '关注客户' }}
                    </el-button>
                </div>
                    </el-tab-pane>

                    <!-- 跟进记录Tab -->
                    <el-tab-pane label="跟进记录" name="followup">
                        <div style="margin-bottom: 16px;">
                            <el-button type="primary" size="small" @click="openFollowupDialog">
                                <el-icon><Plus /></el-icon>
                                新增跟进记录
                            </el-button>
                        </div>
                        
                        <div v-loading="followupLoading">
                            <div v-if="followupRecords.length === 0" style="text-align: center; padding: 50px; color: #999;">
                                暂无跟进记录
                            </div>
                            <div v-else>
                                <el-timeline>
                                    <el-timeline-item
                                        v-for="record in followupRecords"
                                        :key="record.id"
                                        :timestamp="record.createTime"
                                        :type="record.isImportant ? 'danger' : 'primary'"
                                    >
                                        <el-card shadow="hover" style="margin-bottom: 8px;">
                                            <template #header>
                                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                                    <span>
                                                        <el-tag size="small" :type="record.followupType === 'call' ? 'success' : 'info'">
                                                            {{ getFollowupTypeName(record.followupType) }}
                                                        </el-tag>
                                                        <el-tag v-if="record.followupResult" size="small" type="warning" style="margin-left: 8px;">
                                                            {{ getFollowupResultName(record.followupResult) }}
                                                        </el-tag>
                                                        <el-tag v-if="record.isImportant" size="small" type="danger" style="margin-left: 8px;">
                                                            重要
                                                        </el-tag>
                                                    </span>
                                                    <div>
                                                        <el-button 
                                                            type="danger" 
                                                            link 
                                                            size="small" 
                                                            @click="handleDeleteFollowupRecord(record)"
                                                        >
                                                            删除
                                                        </el-button>
                                                    </div>
                                                </div>
                                            </template>
                                            <div>
                                                <p>{{ record.followupContent }}</p>
                                                <div v-if="record.nextFollowupTime" style="margin-top: 8px; color: #666; font-size: 12px;">
                                                    <el-icon><Clock /></el-icon>
                                                    下次跟进时间：{{ record.nextFollowupTime }}
                                                </div>
                                                <div style="margin-top: 8px; color: #999; font-size: 12px;">
                                                    跟进人：{{ record.userNickName || record.userName || '未知' }}
                                                </div>
                                            </div>
                                        </el-card>
                                    </el-timeline-item>
                                </el-timeline>
                            </div>
                        </div>
                    </el-tab-pane>

                    <!-- 操作日志Tab -->
                    <el-tab-pane label="操作日志" name="logs">
                        <div v-loading="operationLogsLoading">
                            <!-- 统计卡片 -->
                            <div v-if="Object.keys(operationTypeStats).length > 0" style="margin-bottom: 20px;">
                                <el-row :gutter="16">
                                    <el-col :span="6" v-for="(count, type) in operationTypeStats" :key="type">
                                        <el-card shadow="hover" style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #409EFF;">{{ count }}</div>
                                            <div style="color: #666; margin-top: 8px;">{{ getOperationTypeName(type) }}</div>
                                        </el-card>
                                    </el-col>
                                </el-row>
                            </div>

                            <div v-if="operationLogs.length === 0" style="text-align: center; padding: 50px; color: #999;">
                                暂无操作日志
                            </div>
                            <div v-else>
                                <el-timeline>
                                    <el-timeline-item
                                        v-for="log in operationLogs"
                                        :key="log.id"
                                        :timestamp="log.operationTime"
                                        :type="getOperationTypeTagType(log.operationType) === 'danger' ? 'danger' : 'primary'"
                                    >
                                        <el-card shadow="hover" style="margin-bottom: 8px;">
                                            <template #header>
                                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                                    <span>
                                                        <el-tag 
                                                            size="small" 
                                                            :type="getOperationTypeTagType(log.operationType)"
                                                        >
                                                            {{ getOperationTypeName(log.operationType) }}
                                                        </el-tag>
                                                        <el-tag 
                                                            size="small" 
                                                            type="info" 
                                                            style="margin-left: 8px;"
                                                        >
                                                            {{ log.businessType }}
                                                        </el-tag>
                                                    </span>
                                                    <span style="color: #999; font-size: 12px;">
                                                        操作人：{{ log.operatorName || '未知' }}
                                                    </span>
                                                </div>
                                            </template>
                                            <div>
                                                <p style="margin: 0 0 8px 0; font-weight: 500;">
                                                    {{ log.operationContent }}
                                                </p>
                                                <div 
                                                    v-if="log.operationDetails" 
                                                    style="background: #f5f7fa; padding: 8px; border-radius: 4px; font-size: 12px; color: #666; white-space: pre-wrap;"
                                                >
                                                    {{ log.operationDetails }}
                                                </div>
                                                <div 
                                                    v-if="log.extraData" 
                                                    style="margin-top: 8px; font-size: 12px; color: #999;"
                                                >
                                                    附加信息：{{ log.extraData }}
                                                </div>
                                            </div>
                                        </el-card>
                                    </el-timeline-item>
                                </el-timeline>
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </el-drawer>

        <!-- 新建/编辑客户对话框 -->
        <el-dialog 
            v-model="dialogVisible" 
            :title="isEditing ? '编辑客户' : '新建客户'" 
            width="800px"
            :close-on-click-modal="false"
        >
            <el-form 
                :model="customerForm" 
                :rules="customerFormRules"
                label-width="120px" 
                ref="customerFormRef"
                label-position="left"
            >
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item label="客户名称" prop="customerName">
                            <el-input 
                                v-model="customerForm.customerName" 
                                placeholder="请输入客户名称" 
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机号" prop="mobile">
                            <el-input 
                                v-model="customerForm.mobile" 
                                placeholder="请输入手机号" 
                                clearable
                                maxlength="11"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item label="电话号" prop="phone">
                            <el-input 
                                v-model="customerForm.phone" 
                                placeholder="请输入电话号" 
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="电子邮箱" prop="email">
                            <el-input 
                                v-model="customerForm.email" 
                                placeholder="请输入电子邮箱" 
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item label="所属行业">
                            <el-select 
                                v-model="customerForm.customerIndustry" 
                                placeholder="请选择所属行业"
                                clearable
                                filterable
                                style="width: 100%"
                            >
                                <el-option label="信息技术" value="IT" />
                                <el-option label="制造业" value="制造业" />
                                <el-option label="金融业" value="金融业" />
                                <el-option label="教育" value="教育" />
                                <el-option label="医疗" value="医疗" />
                                <el-option label="房地产" value="房地产" />
                                <el-option label="餐饮" value="餐饮" />
                                <el-option label="其他" value="其他" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="客户级别">
                            <el-select 
                                v-model="customerForm.customerLevel" 
                                placeholder="请选择客户级别"
                                clearable
                                style="width: 100%"
                            >
                                <el-option label="A级客户（重要）" value="A" />
                                <el-option label="B级客户（普通）" value="B" />
                                <el-option label="C级客户（一般）" value="C" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item label="客户来源">
                            <el-select 
                                v-model="customerForm.customerSource" 
                                placeholder="请选择客户来源"
                                clearable
                                style="width: 100%"
                            >
                                <el-option label="网络推广" value="网络推广" />
                                <el-option label="朋友介绍" value="朋友介绍" />
                                <el-option label="电话销售" value="电话销售" />
                                <el-option label="展会" value="展会" />
                                <el-option label="官网" value="官网" />
                                <el-option label="其他" value="其他" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="网址">
                            <el-input 
                                v-model="customerForm.website" 
                                placeholder="请输入客户网址" 
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="24">
                    <el-col :span="24">
                        <el-form-item label="地址">
                            <el-input 
                                v-model="customerForm.customerAddress" 
                                placeholder="请输入客户地址" 
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="24">
                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input 
                                v-model="customerForm.remarks" 
                                type="textarea" 
                                placeholder="请输入备注信息" 
                                :rows="3"
                                maxlength="500"
                                show-word-limit
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelCustomer" size="default">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="saveCustomer" 
                        :loading="loading"
                        size="default"
                    >
                        {{ isEditing ? '更新' : '保存' }}
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 分配客户对话框 -->
        <el-dialog 
            v-model="assignDialogVisible" 
            :title="isBatchAssign ? '批量分配客户' : '分配客户'" 
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form 
                :model="isBatchAssign ? batchAssignForm : assignForm" 
                ref="assignFormRef"
                label-width="120px" 
                label-position="left"
            >
                <el-form-item label="分配给" prop="assignToUserId" :rules="[{ required: true, message: '请选择分配给的用户', trigger: 'change' }]">
                    <el-select 
                        v-model="currentAssignForm.assignToUserId"
                        placeholder="请选择分配给的用户"
                        style="width: 100%"
                        filterable
                    >
                        <el-option
                            v-for="user in userOptions"
                            :key="user.userId"
                            :label="`${user.nickName} (${user.userName})`"
                            :value="user.userId"
                        >
                            <span style="float: left">{{ user.nickName }} ({{ user.userName }})</span>
                            <span style="float: right; color: #8492a6; font-size: 13px">{{ user.deptName }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="分配原因">
                    <el-input 
                        v-model="currentAssignForm.reason"
                        type="textarea" 
                        placeholder="请输入分配原因（可选）" 
                        :rows="3"
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
                
                <div v-if="isBatchAssign" style="margin-bottom: 16px;">
                    <el-text type="info">
                        将分配 {{ batchAssignForm.customerIds.length }} 个客户给选中的用户
                    </el-text>
                </div>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelAssign" size="default">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="confirmAssign" 
                        :loading="loading"
                        size="default"
                    >
                        确认分配
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 跟进记录对话框 -->
        <el-dialog 
            v-model="followupDialogVisible" 
            title="新增跟进记录" 
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form 
                :model="followupForm" 
                ref="followupFormRef"
                label-width="120px" 
                label-position="left"
                :rules="followupFormRules"
            >
                <el-form-item label="跟进方式" prop="followupType">
                    <el-select 
                        v-model="followupForm.followupType"
                        placeholder="请选择跟进方式"
                        style="width: 100%"
                    >
                        <el-option label="电话" value="call" />
                        <el-option label="拜访" value="visit" />
                        <el-option label="邮件" value="email" />
                        <el-option label="微信" value="wechat" />
                        <el-option label="其他" value="other" />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="跟进内容" prop="followupContent">
                    <el-input 
                        v-model="followupForm.followupContent"
                        type="textarea" 
                        placeholder="请输入跟进内容" 
                        :rows="4"
                        maxlength="500"
                        show-word-limit
                    />
                </el-form-item>
                
                <el-form-item label="跟进结果">
                    <el-select 
                        v-model="followupForm.followupResult"
                        placeholder="请选择跟进结果"
                        style="width: 100%"
                        clearable
                    >
                        <el-option label="有兴趣" value="interested" />
                        <el-option label="无兴趣" value="no_interest" />
                        <el-option label="待定" value="pending" />
                        <el-option label="成交" value="deal" />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="下次跟进时间">
                    <el-date-picker
                        v-model="followupForm.nextFollowupTime"
                        type="datetime"
                        placeholder="选择下次跟进时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                    />
                </el-form-item>
                
                <el-form-item label="重要跟进">
                    <el-switch 
                        v-model="followupForm.isImportant"
                        active-text="是"
                        inactive-text="否"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelFollowupDialog" size="default">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="saveFollowupRecord" 
                        :loading="followupLoading"
                        size="default"
                    >
                        保存
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </el-container>
</template>

<script setup lang="ts">
import CommonFilter from '@/components/CommonFilter/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import SideNav from '@/components/SideNav/index.vue';
import type { TableButton } from '@/components/TableOperations/index.vue';
import TableOperations from '@/components/TableOperations/index.vue';
import { FilterType } from '@/types';
import { Plus, Star, StarFilled, Share, Clock } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { navConfig, tableColumns } from './config';
import { listCustomers, getCustomer, createCustomer, updateCustomer, deleteCustomer, followCustomer, unfollowCustomer, getFollowStatus, batchFollowCustomers, batchUnfollowCustomers, assignCustomer, batchAssignCustomers, getFollowupRecordsByCustomerId, createFollowupRecord, deleteFollowupRecords, getOperationLogsByCustomerId, getOperationTypeStatistics } from '@/api/crm/customer';
import type { CustomerData, AssignCustomerData, BatchAssignCustomerData, UserOption, FollowupRecord, CreateFollowupRecordDTO, OperationLog, OperationTypeStatistics } from '@/api/crm/customer/types';

// API调用方法已通过import导入

// 加载状态
const loading = ref(false);

// 筛选配置
const filterConfig = {
    search: {
        placeholder: '客户名称/手机/电话',
        width: '240px',
        icon: 'Search'
    },
    filter: {
        label: '显示：',
        options: [
            { label: '全部客户', value: 'all' },
            { label: '我负责的', value: 'mine' },
            { label: '下属负责的', value: 'subordinate' },
            { label: '我关注的客户', value: 'following' }
        ],
        buttonStyle: true,
        size: 'default' as const
    }
};

// 查询参数状态定义
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    customerName: '',
    mobile: '',
    phone: '',
    email: '',
    filterType: undefined as string | undefined
});

// 组件状态定义
const activeTab = ref('customers');
const filterType = ref<FilterType>('all');
const searchInput = ref('');
const total = ref(0);
const customers = ref<CustomerData[]>([]);

const dialogVisible = ref(false);
const isEditing = ref(false);
const editingCustomerId = ref<number | null>(null);
const customerForm = ref<CreateCustomerDTO>({
    customerName: '',
    mobile: '',
    phone: '',
    email: '',
    website: '',
    customerIndustry: '',
    customerLevel: '',
    customerSource: '',
    customerAddress: '',
    remarks: ''
});

const drawerVisible = ref(false);
const selectedCustomer = ref<CustomerData | null>(null);
const detailLoading = ref(false);
const activeDrawerTab = ref('info');
const customerFormRef = ref();
const selectedCustomers = ref<CustomerData[]>([]);

// 分配相关状态
const assignDialogVisible = ref(false);
const isBatchAssign = ref(false);
const assignForm = ref<AssignCustomerData>({
    customerId: 0,
    assignToUserId: undefined,
    reason: ''
});
const batchAssignForm = ref<BatchAssignCustomerData>({
    customerIds: [],
    assignToUserId: undefined,
    reason: ''
});
const userOptions = ref<UserOption[]>([]);
const assignFormRef = ref();

// 计算属性：当前分配表单
const currentAssignForm = computed(() => {
    return isBatchAssign.value ? batchAssignForm.value : assignForm.value;
});

// 跟进记录相关状态
const followupRecords = ref<FollowupRecord[]>([]);
const followupDialogVisible = ref(false);
const followupForm = ref<CreateFollowupRecordDTO>({
    customerId: 0,
    followupType: 'call',
    followupContent: '',
    followupResult: '',
    nextFollowupTime: '',
    isImportant: false
});
const followupFormRef = ref();
const followupLoading = ref(false);

// 操作日志相关状态
const operationLogs = ref<OperationLog[]>([]);
const operationLogsLoading = ref(false);
const operationTypeStats = ref<OperationTypeStatistics>({});

// 表单验证规则
const followupFormRules = {
    followupType: [
        { required: true, message: '请选择跟进方式', trigger: 'change' }
    ],
    followupContent: [
        { required: true, message: '请输入跟进内容', trigger: 'blur' },
        { min: 5, max: 500, message: '跟进内容长度在5-500个字符', trigger: 'blur' }
    ]
};

const customerFormRules = {
    customerName: [
        { required: true, message: '请输入客户名称', trigger: 'blur' },
        { min: 2, max: 50, message: '客户名称长度在2-50个字符', trigger: 'blur' }
    ],
    mobile: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    phone: [
        { pattern: /^\d{3,4}-?\d{7,8}$/, message: '请输入正确的电话号码', trigger: 'blur' }
    ],
    email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
};

// 表格操作按钮配置
const tableButtons: TableButton[] = [
    {
        label: (row: CustomerData) => row.isFollowing ? '取关' : '关注',
        type: (row: CustomerData) => row.isFollowing ? 'warning' : 'success',
        link: true,
        icon: (row: CustomerData) => row.isFollowing ? 'StarFilled' : 'Star',
        handler: (row: CustomerData) => handleToggleFollow(row)
    },
    {
        label: '编辑',
        type: 'primary',
        link: true,
        icon: 'Edit',
        handler: (row: CustomerData) => openEditDialog(row)
    },
    {
        label: '分配',
        type: 'info',
        link: true,
        icon: 'Share',
        handler: (row: CustomerData) => handleAssignTeam(row)
    },
    {
        label: '删除',
        type: 'danger',
        link: true,
        icon: 'Delete',
        handler: (row: CustomerData) => handleDelete(row)
    }
];

// 方法定义

// 加载客户列表数据
const loadCustomers = async (): Promise<void> => {
    try {
        loading.value = true;
        const response = await listCustomers(queryParams);
        
        // 处理后端返回的数据结构（TableDataInfo）
        if (response && response.rows) {
            // 并行查询每个客户的关注状态
            customers.value = await Promise.all((response.rows || []).map(async (item: any) => {
                let isFollowing = false;
                try {
                    const followResponse = await getFollowStatus(item.id);
                    isFollowing = followResponse.data || false;
                } catch (error) {
                    console.warn('获取关注状态失败:', error);
                }
                
                return {
                    ...item,
                    isFollowing
                };
            }));
            total.value = response.total || 0;
        } else {
            // 如果返回的是直接数组
            customers.value = response || [];
            total.value = customers.value.length;
        }
    } catch (error) {
        console.error('加载客户列表失败:', error);
        ElMessage.error('加载客户列表失败');
        customers.value = [];
        total.value = 0;
    } finally {
        loading.value = false;
    }
};

const handleSelectionChange = (val: CustomerData[]): void => {
    selectedCustomers.value = val;
};

const saveCustomer = async (): Promise<void> => {
    try {
        // 表单验证
        if (!customerFormRef.value) return;
        const valid = await customerFormRef.value.validate().catch(() => false);
        if (!valid) {
            return;
        }
        
        loading.value = true;
        
        if (isEditing.value && editingCustomerId.value) {
            // 编辑模式
            const updateData = { ...customerForm.value, id: editingCustomerId.value };
            await updateCustomer(updateData);
            ElMessage.success('更新客户成功');
        } else {
            // 新建模式
            await createCustomer(customerForm.value);
            ElMessage.success('新建客户成功');
        }
        
        cancelCustomer();
        
        // 重新加载列表
        await loadCustomers();
    } catch (error) {
        console.error('保存客户失败:', error);
        ElMessage.error('保存客户失败');
    } finally {
        loading.value = false;
    }
};

// 重置表单数据
const resetForm = (): void => {
    customerForm.value = {
        customerName: '',
        mobile: '',
        phone: '',
        email: '',
        website: '',
        customerIndustry: '',
        customerLevel: '',
        customerSource: '',
        customerAddress: '',
        remarks: ''
    };
    // 清除表单验证状态
    if (customerFormRef.value) {
        customerFormRef.value.clearValidate();
    }
};

const openCustomerDialog = (): void => {
    isEditing.value = false;
    editingCustomerId.value = null;
    resetForm();
    dialogVisible.value = true;
};

const openEditDialog = async (row: CustomerData): Promise<void> => {
    try {
        isEditing.value = true;
        editingCustomerId.value = row.id;
        
        // 获取客户详细信息用于编辑
        const customerDetail = await getCustomer(row.id);
        
        // 填充表单数据
        customerForm.value = {
            customerName: customerDetail.customerName || '',
            mobile: customerDetail.mobile || '',
            phone: customerDetail.phone || '',
            email: customerDetail.email || '',
            website: customerDetail.website || '',
            customerIndustry: customerDetail.customerIndustry || '',
            customerLevel: customerDetail.customerLevel || '',
            customerSource: customerDetail.customerSource || '',
            customerAddress: customerDetail.customerAddress || '',
            remarks: customerDetail.remarks || ''
        };
        
        dialogVisible.value = true;
    } catch (error) {
        console.error('获取客户信息失败:', error);
        ElMessage.error('获取客户信息失败');
    }
};

const cancelCustomer = (): void => {
    dialogVisible.value = false;
    isEditing.value = false;
    editingCustomerId.value = null;
    resetForm();
    // 清除表单验证状态
    if (customerFormRef.value) {
        customerFormRef.value.clearValidate();
    }
};

const openDrawer = async (row: CustomerData): Promise<void> => {
    try {
        drawerVisible.value = true;
        detailLoading.value = true;
        
        // 获取客户详细信息
        const customerDetail = await getCustomer(row.id);
        
        // 获取关注状态
        let isFollowing = false;
        try {
            const followResponse = await getFollowStatus(row.id);
            isFollowing = followResponse.data || false;
        } catch (error) {
            console.warn('获取关注状态失败:', error);
        }
        
        selectedCustomer.value = {
            ...customerDetail,
            isFollowing
        };
        
        // 加载跟进记录和操作日志
        await Promise.all([
            loadFollowupRecords(row.id),
            loadOperationLogs(row.id)
        ]);
    } catch (error) {
        console.error('获取客户详情失败:', error);
        ElMessage.error('获取客户详情失败');
    } finally {
        detailLoading.value = false;
    }
};

const handleDelete = async (row: CustomerData): Promise<void> => {
    try {
        await ElMessageBox.confirm('确认删除该客户吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });
        
        loading.value = true;
        await deleteCustomer(row.id.toString());
        
        ElMessage.success('删除成功');
        // 重新加载列表
        await loadCustomers();
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除客户失败:', error);
            ElMessage.error('删除客户失败');
        }
    } finally {
        loading.value = false;
    }
};

const handleSearch = async (value: string): Promise<void> => {
    queryParams.customerName = value;
    queryParams.pageNum = 1; // 重置到第一页
    await loadCustomers();
};

const handleFilterChange = async (value: FilterType): Promise<void> => {
    // 根据筛选类型设置不同的查询参数
    queryParams.pageNum = 1;
    
    // 设置筛选条件
    if (value === 'all') {
        queryParams.filterType = undefined;
    } else {
        queryParams.filterType = value;
    }
    
    await loadCustomers();
};

const handlePagination = async (val: { page: number; limit: number }) => {
    queryParams.pageNum = val.page;
    queryParams.pageSize = val.limit;
    await loadCustomers();
};

/**
 * 切换关注状态
 */
const handleToggleFollow = async (row: CustomerData): Promise<void> => {
    try {
        if (row.isFollowing) {
            // 取消关注
            const response = await unfollowCustomer(row.id);
            if (response.code === 200) {
                ElMessage.success('取消关注成功');
                row.isFollowing = false;
                // 同步更新抽屉中的状态
                if (selectedCustomer.value && selectedCustomer.value.id === row.id) {
                    selectedCustomer.value.isFollowing = false;
                }
            } else {
                ElMessage.error(response.msg || '取消关注失败');
            }
        } else {
            // 关注
            const response = await followCustomer(row.id);
            if (response.code === 200) {
                ElMessage.success('关注成功');
                row.isFollowing = true;
                // 同步更新抽屉中的状态
                if (selectedCustomer.value && selectedCustomer.value.id === row.id) {
                    selectedCustomer.value.isFollowing = true;
                }
            } else {
                ElMessage.error(response.msg || '关注失败');
            }
        }
    } catch (error) {
        console.error('切换关注状态失败:', error);
        ElMessage.error('操作失败');
    }
};

/**
 * 批量关注客户
 */
const handleBatchFollow = async (): Promise<void> => {
    if (selectedCustomers.value.length === 0) {
        ElMessage.warning('请选择要关注的客户');
        return;
    }

    try {
        const customerIds = selectedCustomers.value.map(customer => customer.id);
        const response = await batchFollowCustomers(customerIds);
        
        if (response.code === 200) {
            ElMessage.success('批量关注成功');
            await loadCustomers(); // 重新加载列表
        } else {
            ElMessage.error(response.msg || '批量关注失败');
        }
    } catch (error) {
        console.error('批量关注失败:', error);
        ElMessage.error('批量关注失败');
    }
};

/**
 * 批量取消关注客户
 */
const handleBatchUnfollow = async (): Promise<void> => {
    if (selectedCustomers.value.length === 0) {
        ElMessage.warning('请选择要取消关注的客户');
        return;
    }

    try {
        const customerIds = selectedCustomers.value.map(customer => customer.id);
        const response = await batchUnfollowCustomers(customerIds);
        
        if (response.code === 200) {
            ElMessage.success('批量取消关注成功');
            await loadCustomers(); // 重新加载列表
        } else {
            ElMessage.error(response.msg || '批量取消关注失败');
        }
    } catch (error) {
        console.error('批量取消关注失败:', error);
        ElMessage.error('批量取消关注失败');
    }
};

/**
 * 加载用户选项
 */
const loadUserOptions = async (): Promise<void> => {
    try {
        // 模拟用户数据，实际应调用用户管理接口
        userOptions.value = [
            { userId: 1, userName: 'admin', nickName: '管理员', deptName: '研发部门' },
            { userId: 2, userName: 'manager', nickName: '经理', deptName: '销售部门' },
            { userId: 3, userName: 'sales1', nickName: '销售员1', deptName: '销售部门' },
            { userId: 4, userName: 'sales2', nickName: '销售员2', deptName: '销售部门' }
        ];
    } catch (error) {
        console.error('加载用户列表失败:', error);
        ElMessage.error('加载用户列表失败');
    }
};

/**
 * 处理团队分配
 */
const handleAssignTeam = (row: CustomerData): void => {
    assignDialogVisible.value = true;
    assignForm.value = {
        customerId: row.id,
        assignToUserId: undefined,
        reason: ''
    };
    isBatchAssign.value = false;
    // 加载用户列表
    loadUserOptions();
};

/**
 * 批量分配客户
 */
const handleBatchAssign = (): void => {
    if (selectedCustomers.value.length === 0) {
        ElMessage.warning('请选择要分配的客户');
        return;
    }
    
    assignDialogVisible.value = true;
    batchAssignForm.value = {
        customerIds: selectedCustomers.value.map(customer => customer.id),
        assignToUserId: undefined,
        reason: ''
    };
    isBatchAssign.value = true;
    // 加载用户列表
    loadUserOptions();
};

/**
 * 确认分配
 */
const confirmAssign = async (): Promise<void> => {
    try {
        // 表单验证
        if (!assignFormRef.value) return;
        const valid = await assignFormRef.value.validate().catch(() => false);
        if (!valid) return;
        
        loading.value = true;
        
        if (isBatchAssign.value) {
            // 批量分配
            const response = await batchAssignCustomers(batchAssignForm.value);
            if (response.code === 200) {
                ElMessage.success('批量分配成功');
            } else {
                ElMessage.error(response.msg || '批量分配失败');
            }
        } else {
            // 单个分配
            const response = await assignCustomer(assignForm.value);
            if (response.code === 200) {
                ElMessage.success('分配成功');
            } else {
                ElMessage.error(response.msg || '分配失败');
            }
        }
        
        cancelAssign();
        await loadCustomers(); // 重新加载列表
    } catch (error) {
        console.error('分配客户失败:', error);
        ElMessage.error('分配客户失败');
    } finally {
        loading.value = false;
    }
};

/**
 * 取消分配
 */
const cancelAssign = (): void => {
    assignDialogVisible.value = false;
    isBatchAssign.value = false;
    assignForm.value = {
        customerId: 0,
        assignToUserId: undefined,
        reason: ''
    };
    batchAssignForm.value = {
        customerIds: [],
        assignToUserId: undefined,
        reason: ''
    };
    if (assignFormRef.value) {
        assignFormRef.value.clearValidate();
    }
};

/**
 * 加载跟进记录
 */
const loadFollowupRecords = async (customerId: number): Promise<void> => {
    try {
        followupLoading.value = true;
        const response = await getFollowupRecordsByCustomerId(customerId);
        followupRecords.value = response.data || [];
    } catch (error) {
        console.error('加载跟进记录失败:', error);
        ElMessage.error('加载跟进记录失败');
    } finally {
        followupLoading.value = false;
    }
};

/**
 * 打开跟进记录对话框
 */
const openFollowupDialog = (): void => {
    if (!selectedCustomer.value) {
        ElMessage.warning('请先选择客户');
        return;
    }
    
    followupDialogVisible.value = true;
    followupForm.value = {
        customerId: selectedCustomer.value.id,
        followupType: 'call',
        followupContent: '',
        followupResult: '',
        nextFollowupTime: '',
        isImportant: false
    };
};

/**
 * 保存跟进记录
 */
const saveFollowupRecord = async (): Promise<void> => {
    try {
        // 表单验证
        if (!followupFormRef.value) return;
        const valid = await followupFormRef.value.validate().catch(() => false);
        if (!valid) return;
        
        followupLoading.value = true;
        
        const response = await createFollowupRecord(followupForm.value);
        if (response.code === 200) {
            ElMessage.success('新增跟进记录成功');
            cancelFollowupDialog();
            // 重新加载跟进记录
            if (selectedCustomer.value) {
                await loadFollowupRecords(selectedCustomer.value.id);
            }
        } else {
            ElMessage.error(response.msg || '新增跟进记录失败');
        }
    } catch (error) {
        console.error('保存跟进记录失败:', error);
        ElMessage.error('保存跟进记录失败');
    } finally {
        followupLoading.value = false;
    }
};

/**
 * 取消跟进记录对话框
 */
const cancelFollowupDialog = (): void => {
    followupDialogVisible.value = false;
    followupForm.value = {
        customerId: 0,
        followupType: 'call',
        followupContent: '',
        followupResult: '',
        nextFollowupTime: '',
        isImportant: false
    };
    if (followupFormRef.value) {
        followupFormRef.value.clearValidate();
    }
};

/**
 * 删除跟进记录
 */
const handleDeleteFollowupRecord = async (record: FollowupRecord): Promise<void> => {
    try {
        await ElMessageBox.confirm('确认删除该跟进记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });
        
        if (record.id) {
            const response = await deleteFollowupRecords([record.id]);
            if (response.code === 200) {
                ElMessage.success('删除成功');
                // 重新加载跟进记录
                if (selectedCustomer.value) {
                    await loadFollowupRecords(selectedCustomer.value.id);
                }
            } else {
                ElMessage.error(response.msg || '删除失败');
            }
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除跟进记录失败:', error);
            ElMessage.error('删除跟进记录失败');
        }
    }
};

/**
 * 获取跟进方式名称
 */
const getFollowupTypeName = (type: string): string => {
    const typeMap: Record<string, string> = {
        'call': '电话',
        'visit': '拜访',
        'email': '邮件',
        'wechat': '微信',
        'other': '其他'
    };
    return typeMap[type] || type;
};

/**
 * 获取跟进结果名称
 */
const getFollowupResultName = (result: string): string => {
    const resultMap: Record<string, string> = {
        'interested': '有兴趣',
        'no_interest': '无兴趣',
        'pending': '待定',
        'deal': '成交'
    };
    return resultMap[result] || result;
};

/**
 * 加载操作日志
 */
const loadOperationLogs = async (customerId: number): Promise<void> => {
    try {
        operationLogsLoading.value = true;
        const [logsResponse, statsResponse] = await Promise.all([
            getOperationLogsByCustomerId(customerId),
            getOperationTypeStatistics({ customerId })
        ]);
        
        operationLogs.value = logsResponse.data || [];
        operationTypeStats.value = statsResponse.data || {};
    } catch (error) {
        console.error('加载操作日志失败:', error);
        ElMessage.error('加载操作日志失败');
    } finally {
        operationLogsLoading.value = false;
    }
};

/**
 * 获取操作类型名称
 */
const getOperationTypeName = (type: string): string => {
    const typeMap: Record<string, string> = {
        'CREATE': '创建',
        'UPDATE': '更新',
        'DELETE': '删除',
        'FOLLOW': '关注',
        'UNFOLLOW': '取消关注',
        'ASSIGN': '分配',
        'CONTACT': '联系',
        'VISIT': '拜访',
        'OTHER': '其他'
    };
    return typeMap[type] || type;
};

/**
 * 获取操作类型标签类型
 */
const getOperationTypeTagType = (type: string): string => {
    const typeMap: Record<string, string> = {
        'CREATE': 'success',
        'UPDATE': 'info',
        'DELETE': 'danger',
        'FOLLOW': 'warning',
        'UNFOLLOW': 'info',
        'ASSIGN': 'primary',
        'CONTACT': 'success',
        'VISIT': 'warning'
    };
    return typeMap[type] || 'default';
};

// 监听筛选类型变化
watch(filterType, (newType: FilterType) => {
    handleFilterChange(newType);
});

// 监听搜索输入值变化
watch(searchInput, (newValue: string) => {
    handleSearch(newValue);
});

// 组件挂载时加载数据
onMounted(() => {
    loadCustomers();
});
</script>

<style scoped>
/* 表格样式 */
.el-table {
    margin-bottom: 20px;
}

/* 表格行高度 */
.el-table .el-table__body-wrapper tbody tr {
    height: 20px;
}

.el-table .el-table__body-wrapper tbody td {
    padding: 5px 0;
}

/* 客户管理容器样式 */
.customer-management {
    background-color: #fff;
    height: 100vh;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

/* 主容器样式 */
.main-container {
    flex: 1;
    padding: 0 20px;
    background-color: #fff;
}

/* 头部操作区域样式 */
.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

/* 链接按钮样式 */
.link-button {
    padding: 0;
    height: auto;
    font-weight: normal;

    &:hover {
        text-decoration: underline;
    }
}

/* 对话框样式 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.el-dialog__body {
    padding: 20px 24px;
}

/* 表单样式优化 */
.el-form-item {
    margin-bottom: 18px;
}

.el-form-item__label {
    font-weight: 500;
    color: #606266;
}

.el-input, .el-select {
    width: 100%;
}

.el-textarea__inner {
    resize: vertical;
}
</style>