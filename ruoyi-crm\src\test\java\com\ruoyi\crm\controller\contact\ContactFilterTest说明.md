# 联系人管理后端过滤查询功能测试用例说明

## 一、测试目标
验证后端接口 `/front/crm/contacts/list` 的过滤查询功能，确保不同 filterType、searchKeyword 参数组合下，返回数据正确。

## 二、自动化测试用例覆盖
后端已存在自动化集成测试，主要文件：
- `ContactManagementPhase4Test.java`
- `ContactManagementPhase4IntegrationTest.java`

### 主要测试点
1. **全部联系人筛选**
   - filterType=all
2. **我负责的联系人**
   - filterType=mine
3. **下属负责的联系人**
   - filterType=subordinate
4. **我关注的联系人**
   - filterType=following
5. **关键词搜索**
   - searchKeyword=姓名/手机号/邮箱等
6. **分页、排序、空条件、异常边界**

## 三、测试流程规划

### 1. 测试数据准备
- 自动化测试用例会在@BeforeEach自动插入3个测试联系人（不同负责人/手机号/姓名）。
- 测试结束后自动清理。

### 2. 测试步骤
#### 2.1 全部联系人
- 请求：GET `/front/crm/contacts/list?pageNum=1&pageSize=10&filterType=all`
- 断言：返回code=200，total>=3

#### 2.2 我负责的
- 请求：GET `/front/crm/contacts/list?pageNum=1&pageSize=10&filterType=mine`
- 断言：返回code=200，total>=1

#### 2.3 下属负责的
- 请求：GET `/front/crm/contacts/list?pageNum=1&pageSize=10&filterType=subordinate`
- 断言：返回code=200，total>=0

#### 2.4 我关注的
- 先关注一个联系人
- 请求：GET `/front/crm/contacts/list?pageNum=1&pageSize=10&filterType=following`
- 断言：返回code=200，total>=1

#### 2.5 关键词搜索
- 请求：GET `/front/crm/contacts/list?pageNum=1&pageSize=10&searchKeyword=测试联系人1`
- 断言：返回code=200，rows包含目标联系人

#### 2.6 组合条件
- 请求：GET `/front/crm/contacts/list?pageNum=1&pageSize=10&filterType=mine&searchKeyword=测试联系人1`
- 断言：返回code=200，rows只包含本人负责且匹配关键字的联系人

#### 2.7 边界与异常
- 空filterType、空searchKeyword、无匹配数据、分页、排序
- 断言：返回code=200，rows为空或正常

## 四、如何运行
- 进入`ruoyi-crm`模块，运行上述Test类（支持JUnit/IDEA/命令行）。
- 查看日志与断言结果。

## 五、补充说明
- 所有测试用例均自动准备和清理数据，无需手工干预。
- 如需扩展，请在上述Test类中新增对应场景。

---
如需详细代码示例或扩展测试场景，请补充说明。
