-- CRM通用附件表
DROP TABLE IF EXISTS `crm_attachments`;
CREATE TABLE `crm_attachments` (
  -- 基本信息
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `entity_type` varchar(50) NOT NULL COMMENT '实体类型：contact/customer/opportunity/contract/lead/product',
  `entity_id` bigint(20) NOT NULL COMMENT '关联实体的主键ID',
  
  -- 文件信息
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名(唯一)',
  `original_name` varchar(255) NOT NULL COMMENT '用户上传的原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件在服务器上的完整路径', 
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件MIME类型(image/jpeg)',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名(.jpg)',
  
  -- 业务信息
  `category` varchar(50) DEFAULT NULL COMMENT '附件分类：contract/image/document/spreadsheet/presentation/video/audio/other',
  `description` varchar(500) DEFAULT NULL COMMENT '附件描述信息',
  `upload_by` varchar(64) DEFAULT NULL COMMENT '上传用户',
  `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数统计',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开访问(0私有 1公开)',
  `sort_order` int(11) DEFAULT 0 COMMENT '显示排序(数字越小越靠前)',
  
  -- 系统信息
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者', 
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 主键和索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_file_name` (`file_name`),
  KEY `idx_entity` (`entity_type`, `entity_id`, `del_flag`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_category` (`category`),
  KEY `idx_upload_by` (`upload_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CRM通用附件表';

-- 初始化测试数据
INSERT INTO `crm_attachments` (`entity_type`, `entity_id`, `file_name`, `original_name`, `file_path`, `file_size`, `file_type`, `file_extension`, `category`, `description`, `upload_by`, `upload_time`, `download_count`, `is_public`, `sort_order`, `create_by`) VALUES
('contact', 1, '20250701_001_contract.pdf', '合同文件.pdf', '/uploads/contact/1/2025/07/01/20250701_001_contract.pdf', 2048000, 'application/pdf', '.pdf', 'contract', '重要合同文件', 'admin', '2025-07-01 10:00:00', 0, 0, 1, 'admin'),
('contact', 1, '20250701_002_screenshot.png', '截图.png', '/uploads/contact/1/2025/07/01/20250701_002_screenshot.png', 1200000, 'image/png', '.png', 'image', '系统截图', 'admin', '2025-07-01 10:30:00', 0, 1, 2, 'admin'),
('contact', 2, '20250701_003_report.docx', '工作报告.docx', '/uploads/contact/2/2025/07/01/20250701_003_report.docx', 3100000, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', '.docx', 'document', '月度工作报告', 'admin', '2025-07-01 11:00:00', 0, 0, 1, 'admin'),
('customer', 1, '20250701_004_data.xlsx', '数据表格.xlsx', '/uploads/customer/1/2025/07/01/20250701_004_data.xlsx', 856000, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '.xlsx', 'spreadsheet', '客户数据统计', 'admin', '2025-07-01 12:00:00', 0, 0, 1, 'admin');
