<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理模块详细设计方案</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
        }
        h1 {
            text-align: center;
            border-bottom: 4px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            border: none;
        }
        h2 {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            margin-top: 40px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        h3 {
            color: #3498db;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            background-color: #f8fbff;
            padding: 12px 15px;
            border-radius: 0 8px 8px 0;
            margin-top: 30px;
        }
        h4 {
            color: #2980b9;
            margin-top: 25px;
            padding-bottom: 8px;
            border-bottom: 2px solid #ecf0f1;
        }
        .section {
            background-color: white;
            padding: 30px;
            margin-bottom: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: 1px solid #e8eef5;
        }
        .module-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 1px solid #dee2e6;
            border-left: 5px solid #3498db;
            padding: 25px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.05);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .feature-item {
            background-color: #ffffff;
            border: 1px solid #e8eef5;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }
        .feature-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .feature-item h4 {
            margin-top: 0;
            color: #2980b9;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 6px rgba(0,0,0,0.05);
        }
        th, td {
            border: 1px solid #e8eef5;
            padding: 15px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            font-weight: 600;
            font-size: 14px;
        }
        tr:nth-child(even) {
            background-color: #f8fbff;
        }
        tr:hover {
            background-color: #e8f4fd;
        }
        .workflow-diagram {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            padding: 30px;
            border-radius: 12px;
            margin: 25px 0;
            border: 2px solid #e8eef5;
            position: relative;
        }
        .workflow-step {
            display: inline-block;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 25px;
            font-weight: 600;
            position: relative;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }
        .workflow-arrow {
            display: inline-block;
            margin: 0 15px;
            font-size: 28px;
            color: #3498db;
            vertical-align: middle;
        }
        .ui-mockup {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
        }
        .ui-mockup::before {
            content: '🖼️ UI设计';
            position: absolute;
            top: -12px;
            left: 20px;
            background-color: #3498db;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
        .api-endpoint {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .method-get { border-left-color: #28a745; }
        .method-post { border-left-color: #007bff; }
        .method-put { border-left-color: #ffc107; }
        .method-delete { border-left-color: #dc3545; }
        .timeline {
            position: relative;
            padding-left: 50px;
            margin: 30px 0;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #3498db, #2ecc71);
            border-radius: 2px;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 35px;
            background-color: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.1);
            border: 1px solid #e8eef5;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -42px;
            top: 30px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #3498db;
            border: 4px solid white;
            box-shadow: 0 0 0 4px #3498db;
        }
        .component-tree {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 25px;
            border-radius: 10px;
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 25px 0;
        }
        .component-tree .folder {
            color: #e67e22;
            font-weight: bold;
        }
        .component-tree .file {
            color: #3498db;
        }
        .priority-high {
            border-left: 5px solid #e74c3c;
            background-color: #fdf2f2;
        }
        .priority-medium {
            border-left: 5px solid #f39c12;
            background-color: #fef9e7;
        }
        .priority-low {
            border-left: 5px solid #27ae60;
            background-color: #f0f9f0;
        }
        .code-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px 0;
            overflow-x: auto;
        }
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            text-align: center;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin: 2px;
        }
        .status-todo { background-color: #6c757d; color: white; }
        .status-progress { background-color: #ffc107; color: #212529; }
        .status-review { background-color: #17a2b8; color: white; }
        .status-done { background-color: #28a745; color: white; }
        .drawer-mockup {
            background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }
        .drawer-mockup::before {
            content: '📱 抽屉设计';
            position: absolute;
            top: -12px;
            left: 20px;
            background-color: #17a2b8;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1>📋 订单管理模块详细设计方案</h1>

    <div class="highlight">
        <h3 style="margin-top: 0; color: white;">🎯 设计理念</h3>
        <p style="margin-bottom: 0; font-size: 18px;">
            订单模块作为CRM核心业务模块，设计思路借鉴客户模块的成熟架构，
            <br>包含完整的生命周期管理、公海池机制、抽屉式详情展示等功能
        </p>
    </div>

    <div class="section">
        <h2>一、模块总体架构</h2>
        
        <h3>1.1 模块结构对比</h3>
        <table>
            <thead>
                <tr>
                    <th>功能对比</th>
                    <th>客户模块</th>
                    <th>联系人模块</th>
                    <th>商机模块</th>
                    <th>订单模块（新设计）</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>主列表页</strong></td>
                    <td>✅ 客户列表</td>
                    <td>✅ 联系人列表</td>
                    <td>✅ 商机列表</td>
                    <td>🆕 订单列表</td>
                </tr>
                <tr>
                    <td><strong>公海池</strong></td>
                    <td>✅ 客户公海</td>
                    <td>❌</td>
                    <td>✅ 商机公海</td>
                    <td>🆕 未分配订单池</td>
                </tr>
                <tr>
                    <td><strong>我的数据</strong></td>
                    <td>✅ 我的客户</td>
                    <td>✅ 我的联系人</td>
                    <td>✅ 我的商机</td>
                    <td>🆕 我的订单</td>
                </tr>
                <tr>
                    <td><strong>抽屉详情</strong></td>
                    <td>✅ 客户详情抽屉</td>
                    <td>✅ 联系人详情抽屉</td>
                    <td>✅ 商机详情抽屉</td>
                    <td>🆕 订单详情抽屉</td>
                </tr>
                <tr>
                    <td><strong>关联关系</strong></td>
                    <td>关联联系人、商机</td>
                    <td>关联客户</td>
                    <td>关联客户、联系人</td>
                    <td>🆕 关联客户、商机、合同</td>
                </tr>
                <tr>
                    <td><strong>跟进记录</strong></td>
                    <td>✅</td>
                    <td>✅</td>
                    <td>✅</td>
                    <td>🆕 订单跟进</td>
                </tr>
                <tr>
                    <td><strong>转化功能</strong></td>
                    <td>转为商机</td>
                    <td>转为客户</td>
                    <td>转为订单</td>
                    <td>🆕 转为合同</td>
                </tr>
            </tbody>
        </table>

        <h3>1.2 订单模块组件架构</h3>
        <div class="component-tree">
<pre>
<span class="folder">📁 views/OrderManagement/</span>
├── <span class="file">📄 index.vue</span>                     # 订单管理主页面
├── <span class="folder">📁 components/</span>
│   ├── <span class="file">📄 OrderList.vue</span>             # 订单列表组件
│   ├── <span class="file">📄 OrderTable.vue</span>            # 订单表格组件
│   ├── <span class="file">📄 OrderDrawer.vue</span>           # 订单详情抽屉
│   ├── <span class="file">📄 OrderForm.vue</span>             # 订单表单组件
│   ├── <span class="file">📄 OrderFilter.vue</span>           # 高级筛选组件
│   ├── <span class="file">📄 OrderStatusTag.vue</span>        # 订单状态标签
│   ├── <span class="file">📄 OrderProgress.vue</span>         # 订单进度条
│   └── <span class="file">📄 OrderActions.vue</span>          # 订单操作按钮组
├── <span class="folder">📁 subpages/</span>
│   ├── <span class="file">📄 MyOrders.vue</span>              # 我的订单页面
│   ├── <span class="file">📄 UnassignedOrders.vue</span>      # 未分配订单池
│   ├── <span class="file">📄 OrderAssignCenter.vue</span>     # 订单分配中心
│   └── <span class="file">📄 OrderAnalytics.vue</span>        # 订单统计分析
├── <span class="folder">📁 dialogs/</span>
│   ├── <span class="file">📄 CreateOrderDialog.vue</span>     # 创建订单对话框
│   ├── <span class="file">📄 AssignOrderDialog.vue</span>     # 分配订单对话框
│   ├── <span class="file">📄 ConvertToContractDialog.vue</span> # 转合同对话框
│   └── <span class="file">📄 OrderFollowUpDialog.vue</span>   # 订单跟进对话框
└── <span class="folder">📁 types/</span>
    ├── <span class="file">📄 order.ts</span>                  # 订单类型定义
    ├── <span class="file">📄 orderItem.ts</span>              # 订单项类型定义
    └── <span class="file">📄 orderAssignment.ts</span>        # 订单分配类型定义
</pre>
        </div>
    </div>

    <div class="section">
        <h2>二、子模块详细设计</h2>

        <h3>2.1 订单列表模块（主页面）</h3>
        <div class="module-card">
            <h4>📋 功能概述</h4>
            <p>订单管理的核心入口，提供全面的订单查看、筛选、操作功能，类似客户列表的设计理念。</p>
            
            <div class="feature-grid">
                <div class="feature-item priority-high">
                    <h4>🔍 高级搜索筛选</h4>
                    <ul>
                        <li>订单号、客户名称快速搜索</li>
                        <li>订单状态多选筛选</li>
                        <li>日期范围筛选（创建时间、交付时间）</li>
                        <li>金额区间筛选</li>
                        <li>订单来源筛选（3D打印、手动创建、商机转化）</li>
                        <li>负责人筛选</li>
                        <li>客户类型筛选（新客户、老客户）</li>
                    </ul>
                </div>
                <div class="feature-item priority-high">
                    <h4>📊 列表展示</h4>
                    <ul>
                        <li>表格形式展示，支持列排序</li>
                        <li>订单基本信息：订单号、客户名、金额、状态</li>
                        <li>关联信息：负责人、商机、创建时间</li>
                        <li>状态标签：不同颜色区分订单状态</li>
                        <li>快速操作按钮：查看、编辑、分配、转化</li>
                        <li>支持列表/卡片视图切换</li>
                    </ul>
                </div>
                <div class="feature-item priority-medium">
                    <h4>🛠️ 批量操作</h4>
                    <ul>
                        <li>批量选择订单</li>
                        <li>批量分配负责人</li>
                        <li>批量更新状态</li>
                        <li>批量导出</li>
                        <li>批量删除</li>
                        <li>批量转为合同</li>
                    </ul>
                </div>
                <div class="feature-item priority-medium">
                    <h4>📈 数据统计</h4>
                    <ul>
                        <li>顶部数据卡片：总订单数、总金额</li>
                        <li>状态分布统计</li>
                        <li>本月新增订单</li>
                        <li>待处理订单提醒</li>
                        <li>个人业绩统计</li>
                    </ul>
                </div>
            </div>

            <div class="ui-mockup">
                <h4>界面布局设计</h4>
                <pre>
┌────────────────────────────────────────────────────────────────┐
│  📋 订单管理                                    🔍 搜索框  ➕ 新建订单  │
├────────────────────────────────────────────────────────────────┤
│  📊 数据概览卡片                                                    │
│  [总订单: 1,248] [总金额: ¥2,456,789] [待处理: 23] [本月新增: 89]    │
├────────────────────────────────────────────────────────────────┤
│  🔧 筛选工具栏                                                      │
│  [状态▼] [日期▼] [金额▼] [负责人▼] [客户类型▼] [重置] [导出Excel]        │
├────────────────────────────────────────────────────────────────┤
│  📋 订单列表表格                                                    │
│  ☑ │订单号    │客户名称   │金额      │状态    │负责人  │创建时间   │操作 │
│  ☑ │ORD001   │ABC公司   │¥12,500  │进行中  │张三   │2025-02-01│[👁][✏][📱] │
│  ☑ │ORD002   │XYZ科技   │¥8,900   │待分配  │-     │2025-02-02│[👁][📋][📱] │
├────────────────────────────────────────────────────────────────┤
│  分页控件 [◀] 1 2 3 4 5 [▶]                          每页20条      │
└────────────────────────────────────────────────────────────────┘
                </pre>
            </div>
        </div>

        <h3>2.2 未分配订单池模块</h3>
        <div class="module-card">
            <h4>🏊 功能概述</h4>
            <p>类似客户公海池的设计，管理所有未分配负责人的订单，支持抢单和管理员分配。</p>
            
            <div class="feature-grid">
                <div class="feature-item priority-high">
                    <h4>🎯 订单池管理</h4>
                    <ul>
                        <li>显示所有未分配订单</li>
                        <li>订单质量评级（A/B/C/D级）</li>
                        <li>进入公海时间显示</li>
                        <li>预估订单价值</li>
                        <li>客户背景信息展示</li>
                        <li>紧急程度标识</li>
                    </ul>
                </div>
                <div class="feature-item priority-high">
                    <h4>🏃 抢单机制</h4>
                    <ul>
                        <li>一键抢单功能</li>
                        <li>抢单数量限制</li>
                        <li>抢单冷却时间</li>
                        <li>抢单历史记录</li>
                        <li>抢单成功通知</li>
                    </ul>
                </div>
                <div class="feature-item priority-medium">
                    <h4>👨‍💼 管理员分配</h4>
                    <ul>
                        <li>批量分配功能</li>
                        <li>按规则自动分配</li>
                        <li>负载均衡分配</li>
                        <li>强制回收订单</li>
                        <li>分配历史追踪</li>
                    </ul>
                </div>
                <div class="feature-item priority-low">
                    <h4>📊 池统计分析</h4>
                    <ul>
                        <li>公海订单趋势</li>
                        <li>抢单活跃度</li>
                        <li>平均处理时间</li>
                        <li>转化率统计</li>
                    </ul>
                </div>
            </div>

            <div class="ui-mockup">
                <h4>未分配订单池界面</h4>
                <pre>
┌────────────────────────────────────────────────────────────────┐
│  🏊 未分配订单池                                  📊 池统计   ⚙️ 分配规则  │
├────────────────────────────────────────────────────────────────┤
│  📈 公海概览                                                        │
│  [待分配: 15个] [总价值: ¥186,500] [平均等待: 2.3天] [今日新增: 5个]   │
├────────────────────────────────────────────────────────────────┤
│  🔍 智能筛选                                                        │
│  [价值等级▼] [紧急程度▼] [进入时间▼] [客户类型▼] [重置筛选]            │
├────────────────────────────────────────────────────────────────┤
│  📋 公海订单列表                                                    │
│  │等级│订单号   │客户      │价值     │进入时间   │紧急度  │操作        │
│  │🅰️  │ORD003  │新客户A   │¥25,000 │2天前     │🔴高   │[🏃抢单][👁详情]│
│  │🅱️  │ORD004  │老客户B   │¥18,600 │1天前     │🟡中   │[🏃抢单][👁详情]│
│  │🅲  │ORD005  │新客户C   │¥12,300 │4小时前   │🟢低   │[🏃抢单][👁详情]│
├────────────────────────────────────────────────────────────────┤
│  [⚡ 批量分配] [📤 导出Excel] [🔄 刷新]               [◀] 1/3 [▶]      │
└────────────────────────────────────────────────────────────────┘
                </pre>
            </div>
        </div>

        <h3>2.3 我的订单模块</h3>
        <div class="module-card">
            <h4>👤 功能概述</h4>
            <p>个人订单工作台，专注于当前用户负责的订单管理，提供个性化的工作视图。</p>
            
            <div class="feature-grid">
                <div class="feature-item priority-high">
                    <h4>📋 个人订单看板</h4>
                    <ul>
                        <li>按状态分类显示（待处理、进行中、已完成）</li>
                        <li>紧急订单置顶显示</li>
                        <li>逾期订单红色标识</li>
                        <li>今日待办订单</li>
                        <li>本周计划完成订单</li>
                    </ul>
                </div>
                <div class="feature-item priority-high">
                    <h4>⏰ 时间管理</h4>
                    <ul>
                        <li>订单截止日期提醒</li>
                        <li>跟进计划日历视图</li>
                        <li>任务优先级排序</li>
                        <li>工作量评估</li>
                    </ul>
                </div>
                <div class="feature-item priority-medium">
                    <h4>📊 个人业绩</h4>
                    <ul>
                        <li>本月订单完成情况</li>
                        <li>个人业绩排名</li>
                        <li>订单平均处理时间</li>
                        <li>客户满意度评分</li>
                    </ul>
                </div>
                <div class="feature-item priority-low">
                    <h4>🎯 工作提醒</h4>
                    <ul>
                        <li>待跟进订单提醒</li>
                        <li>即将到期订单警告</li>
                        <li>客户回访提醒</li>
                        <li>合同签署提醒</li>
                    </ul>
                </div>
            </div>

            <div class="ui-mockup">
                <h4>我的订单工作台</h4>
                <pre>
┌────────────────────────────────────────────────────────────────┐
│  👤 我的订单 - 张三                               📅 2025-02-02   │
├────────────────────────────────────────────────────────────────┤
│  📊 今日概览                                                        │
│  [我的订单: 23个] [待处理: 5个] [本月完成: 12个] [业绩: ¥145,600]     │
├────────────────────────────────────────────────────────────────┤
│  🚨 紧急订单 (3个)                                                  │
│  │🔴 ORD001 - ABC公司 - ¥25,000 - 明天到期                        │
│  │🟡 ORD002 - XYZ科技 - ¥18,000 - 3天后到期                       │
│  │🟡 ORD003 - DEF集团 - ¥32,000 - 1周后到期                       │
├────────────────────────────────────────────────────────────────┤
│  📋 订单状态看板                                                    │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐              │
│  │ 待处理(5) │ │ 进行中(8) │ │ 待交付(6) │ │ 已完成(4) │              │
│  │ ORD004   │ │ ORD007   │ │ ORD010   │ │ ORD013   │              │
│  │ ORD005   │ │ ORD008   │ │ ORD011   │ │ ORD014   │              │
│  │ ORD006   │ │ ORD009   │ │ ORD012   │ │ ORD015   │              │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘              │
├────────────────────────────────────────────────────────────────┤
│  📅 今日待办 (7项)                            📈 本月业绩趋势图      │
└────────────────────────────────────────────────────────────────┘
                </pre>
            </div>
        </div>

        <h3>2.4 订单详情抽屉模块</h3>
        <div class="module-card">
            <h4>📱 功能概述</h4>
            <p>右侧滑出的详情抽屉，提供订单的完整信息展示和操作，支持快速编辑和状态更新。</p>
            
            <div class="feature-grid">
                <div class="feature-item priority-high">
                    <h4>📄 基础信息区域</h4>
                    <ul>
                        <li>订单基本信息（订单号、状态、金额、日期）</li>
                        <li>客户信息（客户名称、联系人、电话）</li>
                        <li>负责人信息（负责人、团队、联系方式）</li>
                        <li>关联信息（商机、合同、报价单）</li>
                        <li>订单来源标识</li>
                    </ul>
                </div>
                <div class="feature-item priority-high">
                    <h4>📦 订单明细区域</h4>
                    <ul>
                        <li>产品/服务明细列表</li>
                        <li>数量、单价、小计</li>
                        <li>规格参数展示</li>
                        <li>附件文件预览</li>
                        <li>特殊要求说明</li>
                    </ul>
                </div>
                <div class="feature-item priority-high">
                    <h4>📈 进度跟踪区域</h4>
                    <ul>
                        <li>订单状态流转时间线</li>
                        <li>关键节点记录</li>
                        <li>跟进记录列表</li>
                        <li>操作日志</li>
                        <li>下次跟进计划</li>
                    </ul>
                </div>
                <div class="feature-item priority-medium">
                    <h4>🛠️ 快速操作区域</h4>
                    <ul>
                        <li>状态快速更新</li>
                        <li>添加跟进记录</li>
                        <li>转为合同</li>
                        <li>分配/转移</li>
                        <li>打印/导出</li>
                    </ul>
                </div>
            </div>

            <div class="drawer-mockup">
                <h4>订单详情抽屉布局</h4>
                <pre>
                                    ┌─────────────────────────────────┐
                                    │ 📋 订单详情 - ORD001        ❌  │
                                    ├─────────────────────────────────┤
                                    │ 📊 订单状态: [进行中] 🟡        │
                                    │ 💰 订单金额: ¥25,000           │
                                    │ 📅 创建时间: 2025-02-01         │
                                    │ 🏢 客户: ABC公司                │
                                    │ 👤 负责人: 张三                 │
                                    ├─────────────────────────────────┤
                                    │ 📦 订单明细                     │
                                    │ ┌─────────────────────────────┐ │
                                    │ │产品A × 2    ¥8,000         │ │
                                    │ │产品B × 1    ¥17,000        │ │
                                    │ │合计:        ¥25,000        │ │
                                    │ └─────────────────────────────┘ │
                                    ├─────────────────────────────────┤
                                    │ ⏱️ 进度时间线                   │
                                    │ ● 2025-02-01 订单创建          │
                                    │ ● 2025-02-02 确认接单          │
                                    │ ○ 预计交付: 2025-02-15         │
                                    ├─────────────────────────────────┤
                                    │ 💬 跟进记录 (3条)               │
                                    │ 📝 添加跟进                     │
                                    ├─────────────────────────────────┤
                                    │ [更新状态] [转为合同] [编辑]    │
                                    │ [分配] [打印] [导出]            │
                                    └─────────────────────────────────┘
                </pre>
            </div>
        </div>

        <h3>2.5 订单分配中心模块</h3>
        <div class="module-card">
            <h4>🎯 功能概述</h4>
            <p>管理员专用的订单分配管理中心，提供智能分配建议和批量操作功能。</p>
            
            <div class="feature-grid">
                <div class="feature-item priority-high">
                    <h4>🤖 智能分配建议</h4>
                    <ul>
                        <li>基于销售负载的分配建议</li>
                        <li>基于客户关系的分配建议</li>
                        <li>基于专业领域的分配建议</li>
                        <li>基于地域的分配建议</li>
                        <li>基于历史成功率的分配建议</li>
                    </ul>
                </div>
                <div class="feature-item priority-high">
                    <h4>👥 团队负载管理</h4>
                    <ul>
                        <li>销售人员工作负载可视化</li>
                        <li>团队产能分析</li>
                        <li>订单分配历史</li>
                        <li>绩效对比分析</li>
                    </ul>
                </div>
                <div class="feature-item priority-medium">
                    <h4>⚙️ 分配规则配置</h4>
                    <ul>
                        <li>自动分配规则设置</li>
                        <li>分配权重配置</li>
                        <li>特殊客户分配规则</li>
                        <li>紧急订单分配策略</li>
                    </ul>
                </div>
                <div class="feature-item priority-low">
                    <h4>📊 分配效果分析</h4>
                    <ul>
                        <li>分配成功率统计</li>
                        <li>平均处理时间分析</li>
                        <li>客户满意度跟踪</li>
                        <li>ROI分析报告</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>三、数据结构设计</h2>

        <h3>3.1 核心数据表结构</h3>
        <div class="code-block">
-- 1. 订单主表 (crm_order)
CREATE TABLE `crm_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `order_title` varchar(200) DEFAULT NULL COMMENT '订单标题',
  
  -- 客户关联信息
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(100) NOT NULL COMMENT '客户名称（冗余）',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '联系人ID',
  `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名（冗余）',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话（冗余）',
  
  -- 业务关联信息
  `opportunity_id` bigint(20) DEFAULT NULL COMMENT '关联商机ID',
  `contract_id` bigint(20) DEFAULT NULL COMMENT '关联合同ID',
  `quote_no` varchar(50) DEFAULT NULL COMMENT '报价单号',
  
  -- 订单基本信息
  `order_type` varchar(50) DEFAULT 'STANDARD' COMMENT '订单类型：STANDARD,3D_PRINTING,CUSTOM',
  `order_source` varchar(50) DEFAULT 'MANUAL' COMMENT '订单来源：MANUAL,OPPORTUNITY,3D_PRINTING',
  `priority_level` varchar(20) DEFAULT 'NORMAL' COMMENT '优先级：HIGH,NORMAL,LOW',
  
  -- 金额信息
  `total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `paid_amount` decimal(15,2) DEFAULT '0.00' COMMENT '已付金额',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
  
  -- 时间信息
  `order_date` date NOT NULL COMMENT '下单日期',
  `expected_delivery_date` date DEFAULT NULL COMMENT '预期交付日期',
  `actual_delivery_date` date DEFAULT NULL COMMENT '实际交付日期',
  
  -- 状态信息
  `status` varchar(50) DEFAULT 'PENDING' COMMENT '订单状态：PENDING,CONFIRMED,IN_PROGRESS,DELIVERED,COMPLETED,CANCELLED',
  `assignment_status` varchar(20) DEFAULT 'UNASSIGNED' COMMENT '分配状态：UNASSIGNED,ASSIGNED',
  
  -- 负责人信息
  `owner_id` bigint(20) DEFAULT NULL COMMENT '负责人ID',
  `owner_name` varchar(100) DEFAULT NULL COMMENT '负责人姓名（冗余）',
  `assigned_by` bigint(20) DEFAULT NULL COMMENT '分配人ID',
  `assigned_time` datetime DEFAULT NULL COMMENT '分配时间',
  
  -- 其他信息
  `remarks` text COMMENT '订单备注',
  `urgent_flag` tinyint(1) DEFAULT '0' COMMENT '紧急标志：0-普通,1-紧急',
  `customer_type` varchar(20) DEFAULT NULL COMMENT '客户类型：NEW,EXISTING',
  
  -- 审计字段
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_opportunity_id` (`opportunity_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`),
  KEY `idx_assignment_status` (`assignment_status`),
  KEY `idx_order_date` (`order_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';
        </div>

        <div class="code-block">
-- 2. 订单明细表 (crm_order_item)
CREATE TABLE `crm_order_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单明细ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  
  -- 产品信息
  `product_id` bigint(20) DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(200) NOT NULL COMMENT '产品名称',
  `product_code` varchar(100) DEFAULT NULL COMMENT '产品编码',
  `specification` text COMMENT '规格参数',
  
  -- 数量和价格
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `unit_price` decimal(15,2) NOT NULL COMMENT '单价',
  `discount_rate` decimal(5,2) DEFAULT '0.00' COMMENT '折扣率',
  `subtotal` decimal(15,2) NOT NULL COMMENT '小计金额',
  
  -- 3D打印特有字段
  `model_name` varchar(100) DEFAULT NULL COMMENT '模型名称',
  `material_name` varchar(100) DEFAULT NULL COMMENT '材料名称',
  `print_options` json DEFAULT NULL COMMENT '打印选项',
  
  -- 其他信息
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  
  -- 审计字段
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `crm_order` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';
        </div>

        <div class="code-block">
-- 3. 订单分配历史表 (crm_order_assignment_log)
CREATE TABLE `crm_order_assignment_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  
  -- 分配信息
  `action_type` varchar(50) NOT NULL COMMENT '操作类型：ASSIGN,TRANSFER,RECLAIM,GRAB',
  `from_user_id` bigint(20) DEFAULT NULL COMMENT '原负责人ID',
  `from_user_name` varchar(100) DEFAULT NULL COMMENT '原负责人姓名',
  `to_user_id` bigint(20) NOT NULL COMMENT '新负责人ID',
  `to_user_name` varchar(100) NOT NULL COMMENT '新负责人姓名',
  
  -- 操作信息
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `reason` varchar(500) DEFAULT NULL COMMENT '操作原因',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  
  -- 其他信息
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_to_user_id` (`to_user_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单分配历史表';
        </div>

        <h3>3.2 订单状态流转图</h3>
        <div class="workflow-diagram">
            <h4>📊 订单生命周期管理</h4>
            <div style="text-align: center; padding: 20px;">
                <div class="workflow-step">待分配</div>
                <span class="workflow-arrow">→</span>
                <div class="workflow-step">已分配</div>
                <span class="workflow-arrow">→</span>
                <div class="workflow-step">确认接单</div>
                <span class="workflow-arrow">→</span>
                <div class="workflow-step">生产中</div>
                <br><br>
                <div class="workflow-step">质检</div>
                <span class="workflow-arrow">←</span>
                <div class="workflow-step">包装发货</div>
                <span class="workflow-arrow">←</span>
                <div class="workflow-step">客户验收</div>
                <span class="workflow-arrow">←</span>
                <div class="workflow-step">订单完成</div>
            </div>
            
            <table style="margin-top: 20px;">
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>英文标识</th>
                        <th>颜色标识</th>
                        <th>可执行操作</th>
                        <th>流转条件</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>待分配</td>
                        <td>UNASSIGNED</td>
                        <td style="color: #6c757d;">⚫ 灰色</td>
                        <td>分配负责人、抢单</td>
                        <td>新订单创建</td>
                    </tr>
                    <tr>
                        <td>已分配</td>
                        <td>ASSIGNED</td>
                        <td style="color: #17a2b8;">🔵 蓝色</td>
                        <td>确认接单、拒单、转移</td>
                        <td>管理员分配或抢单成功</td>
                    </tr>
                    <tr>
                        <td>确认接单</td>
                        <td>CONFIRMED</td>
                        <td style="color: #28a745;">🟢 绿色</td>
                        <td>开始生产、修改需求</td>
                        <td>负责人确认</td>
                    </tr>
                    <tr>
                        <td>生产中</td>
                        <td>IN_PROGRESS</td>
                        <td style="color: #ffc107;">🟡 黄色</td>
                        <td>更新进度、暂停、完成</td>
                        <td>开始生产</td>
                    </tr>
                    <tr>
                        <td>待发货</td>
                        <td>READY_TO_SHIP</td>
                        <td style="color: #fd7e14;">🟠 橙色</td>
                        <td>安排发货、质检</td>
                        <td>生产完成</td>
                    </tr>
                    <tr>
                        <td>已发货</td>
                        <td>SHIPPED</td>
                        <td style="color: #6610f2;">🟣 紫色</td>
                        <td>跟踪物流、确认收货</td>
                        <td>发货完成</td>
                    </tr>
                    <tr>
                        <td>已完成</td>
                        <td>COMPLETED</td>
                        <td style="color: #198754;">✅ 深绿</td>
                        <td>评价、转合同、归档</td>
                        <td>客户确认收货</td>
                    </tr>
                    <tr>
                        <td>已取消</td>
                        <td>CANCELLED</td>
                        <td style="color: #dc3545;">❌ 红色</td>
                        <td>查看取消原因</td>
                        <td>任意状态可取消</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="section">
        <h2>四、API接口设计</h2>

        <h3>4.1 订单基础接口</h3>
        <div class="api-endpoint method-get">
            <strong>GET</strong> /api/orders<br>
            <small>获取订单列表，支持分页和筛选</small>
        </div>
        <div class="api-endpoint method-get">
            <strong>GET</strong> /api/orders/{id}<br>
            <small>获取订单详情信息</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders<br>
            <small>创建新订单</small>
        </div>
        <div class="api-endpoint method-put">
            <strong>PUT</strong> /api/orders/{id}<br>
            <small>更新订单信息</small>
        </div>
        <div class="api-endpoint method-delete">
            <strong>DELETE</strong> /api/orders/{id}<br>
            <small>删除订单（软删除）</small>
        </div>

        <h3>4.2 订单分配接口</h3>
        <div class="api-endpoint method-get">
            <strong>GET</strong> /api/orders/unassigned<br>
            <small>获取未分配订单列表</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/{id}/assign<br>
            <small>分配订单给指定用户</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/{id}/grab<br>
            <small>用户抢单操作</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/{id}/transfer<br>
            <small>转移订单给其他用户</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/batch-assign<br>
            <small>批量分配订单</small>
        </div>

        <h3>4.3 订单状态管理接口</h3>
        <div class="api-endpoint method-put">
            <strong>PUT</strong> /api/orders/{id}/status<br>
            <small>更新订单状态</small>
        </div>
        <div class="api-endpoint method-get">
            <strong>GET</strong> /api/orders/{id}/status-history<br>
            <small>获取订单状态变更历史</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/{id}/follow-up<br>
            <small>添加订单跟进记录</small>
        </div>

        <h3>4.4 订单转化接口</h3>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/{id}/convert-to-contract<br>
            <small>订单转为合同</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/batch-convert-to-contract<br>
            <small>批量订单转合同</small>
        </div>

        <h3>4.5 统计分析接口</h3>
        <div class="api-endpoint method-get">
            <strong>GET</strong> /api/orders/statistics<br>
            <small>获取订单统计数据</small>
        </div>
        <div class="api-endpoint method-get">
            <strong>GET</strong> /api/orders/my-dashboard<br>
            <small>获取个人订单仪表盘数据</small>
        </div>
    </div>

    <div class="section">
        <h2>五、开发时序安排</h2>

        <div class="timeline">
            <div class="timeline-item">
                <h3>第1周：数据库设计与基础架构</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-progress">进行中</span>
                    <span class="effort-estimate" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">40小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>数据库表设计</strong> - 订单主表、明细表、分配日志表结构设计</li>
                    <li><strong>索引优化</strong> - 根据查询场景设计合适的索引</li>
                    <li><strong>数据迁移脚本</strong> - 现有3D订单数据结构调整</li>
                    <li><strong>基础Entity和DTO</strong> - Java实体类和数据传输对象</li>
                    <li><strong>基础Mapper接口</strong> - MyBatis数据访问层</li>
                </ul>

                <h4>🚀 交付成果</h4>
                <ul>
                    <li>完整的数据库DDL脚本</li>
                    <li>数据迁移和回滚脚本</li>
                    <li>基础的Java实体类</li>
                    <li>MyBatis XML映射文件</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第2周：后端核心服务开发</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate" style="background: linear-gradient(135deg, #3498db, #2980b9);">48小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>订单基础服务</strong> - CRUD操作、状态管理、查询筛选</li>
                    <li><strong>订单分配服务</strong> - 分配逻辑、抢单机制、转移功能</li>
                    <li><strong>客户匹配服务</strong> - 电话号码匹配、新老客户识别</li>
                    <li><strong>通知服务扩展</strong> - 订单相关的通知模板和发送</li>
                    <li><strong>权限控制</strong> - 订单数据权限验证</li>
                </ul>

                <h4>🚀 交付成果</h4>
                <ul>
                    <li>完整的订单管理Service层</li>
                    <li>订单分配核心算法</li>
                    <li>客户智能匹配服务</li>
                    <li>单元测试用例（覆盖率≥80%）</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第3周：API接口层开发</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">36小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>订单Controller开发</strong> - REST API接口实现</li>
                    <li><strong>参数验证</strong> - 请求参数校验和异常处理</li>
                    <li><strong>分页排序</strong> - 列表查询的分页和排序功能</li>
                    <li><strong>高级筛选</strong> - 多条件组合查询接口</li>
                    <li><strong>批量操作接口</strong> - 批量分配、状态更新等</li>
                </ul>

                <h4>🚀 交付成果</h4>
                <ul>
                    <li>完整的REST API接口</li>
                    <li>Swagger API文档</li>
                    <li>集成测试用例</li>
                    <li>API性能测试报告</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第4-5周：前端界面开发</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate" style="background: linear-gradient(135deg, #f39c12, #e67e22);">80小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>订单列表页面</strong> - 主列表、筛选器、批量操作</li>
                    <li><strong>未分配订单池</strong> - 公海池列表、抢单功能</li>
                    <li><strong>我的订单工作台</strong> - 个人订单看板、统计图表</li>
                    <li><strong>订单详情抽屉</strong> - 详情展示、快速编辑</li>
                    <li><strong>分配中心界面</strong> - 管理员分配工具</li>
                    <li><strong>各种对话框</strong> - 创建、编辑、分配、转化对话框</li>
                </ul>

                <h4>🚀 交付成果</h4>
                <ul>
                    <li>完整的订单管理前端模块</li>
                    <li>响应式界面适配</li>
                    <li>用户交互优化</li>
                    <li>前端单元测试</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第6周：业务流程整合</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate" style="background: linear-gradient(135deg, #27ae60, #229954);">40小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>3D订单创建逻辑改造</strong> - 集成新的分配机制</li>
                    <li><strong>商机转订单功能</strong> - 商机界面增加转订单按钮</li>
                    <li><strong>订单转合同功能</strong> - 订单界面增加转合同功能</li>
                    <li><strong>权限体系集成</strong> - 与现有权限系统集成</li>
                    <li><strong>通知机制完善</strong> - 企业微信、邮件通知</li>
                </ul>

                <h4>🚀 交付成果</h4>
                <ul>
                    <li>完整的业务流程链路</li>
                    <li>多渠道通知系统</li>
                    <li>权限验证机制</li>
                    <li>业务流程测试报告</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第7周：测试与上线</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate" style="background: linear-gradient(135deg, #16a085, #138d75);">32小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>系统集成测试</strong> - 端到端业务流程测试</li>
                    <li><strong>性能压力测试</strong> - 高并发场景测试</li>
                    <li><strong>用户验收测试</strong> - 业务人员测试验证</li>
                    <li><strong>数据迁移执行</strong> - 生产环境数据迁移</li>
                    <li><strong>灰度发布</strong> - 小范围用户试用</li>
                    <li><strong>培训文档</strong> - 用户手册和培训材料</li>
                </ul>

                <h4>🚀 交付成果</h4>
                <ul>
                    <li>测试报告和问题修复</li>
                    <li>生产环境部署</li>
                    <li>用户培训材料</li>
                    <li>运维监控配置</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>六、技术实现细节</h2>

        <h3>6.1 前端组件设计规范</h3>
        <div class="code-block">
// 订单状态标签组件示例
&lt;template&gt;
  &lt;el-tag 
    :type="getStatusType(status)" 
    :effect="effect"
    size="small"
    class="order-status-tag"
  &gt;
    {{ getStatusText(status) }}
  &lt;/el-tag&gt;
&lt;/template&gt;

&lt;script setup lang="ts"&gt;
interface Props {
  status: string
  effect?: 'dark' | 'light' | 'plain'
}

const props = withDefaults(defineProps&lt;Props&gt;(), {
  effect: 'light'
})

const statusConfig = {
  'UNASSIGNED': { type: 'info', text: '待分配' },
  'ASSIGNED': { type: 'primary', text: '已分配' },
  'CONFIRMED': { type: 'success', text: '确认接单' },
  'IN_PROGRESS': { type: 'warning', text: '生产中' },
  'COMPLETED': { type: 'success', text: '已完成' },
  'CANCELLED': { type: 'danger', text: '已取消' }
}

const getStatusType = (status: string) =&gt; {
  return statusConfig[status]?.type || 'info'
}

const getStatusText = (status: string) =&gt; {
  return statusConfig[status]?.text || status
}
&lt;/script&gt;
        </div>

        <h3>6.2 后端服务层设计模式</h3>
        <div class="code-block">
@Service
@Transactional(rollbackFor = Exception.class)
public class OrderManagementService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private CustomerMatchingService customerMatchingService;
    
    @Autowired
    private NotificationService notificationService;
    
    /**
     * 智能订单分配
     */
    public AssignmentResult smartAssignOrder(Long orderId, AssignmentStrategy strategy) {
        // 1. 获取订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 2. 根据策略选择负责人
        Long assigneeId = selectAssigneeByStrategy(order, strategy);
        
        // 3. 执行分配
        boolean result = assignOrderToUser(orderId, assigneeId);
        
        // 4. 发送通知
        if (result) {
            notificationService.sendOrderAssignmentNotification(orderId, assigneeId);
        }
        
        return AssignmentResult.builder()
            .success(result)
            .orderId(orderId)
            .assigneeId(assigneeId)
            .assignmentTime(LocalDateTime.now())
            .build();
    }
    
    /**
     * 根据策略选择负责人
     */
    private Long selectAssigneeByStrategy(Order order, AssignmentStrategy strategy) {
        switch (strategy) {
            case CUSTOMER_RELATIONSHIP:
                return customerMatchingService.findOwnerByCustomer(order.getCustomerId());
            case LOAD_BALANCE:
                return loadBalanceService.findLeastLoadedUser();
            case REGION_BASED:
                return regionService.findUserByRegion(order.getCustomerRegion());
            default:
                throw new BusinessException("不支持的分配策略");
        }
    }
}
        </div>

        <h3>6.3 数据库查询优化</h3>
        <div class="code-block">
-- 订单列表查询优化（支持多条件筛选）
SELECT 
    o.id,
    o.order_no,
    o.customer_name,
    o.total_amount,
    o.status,
    o.owner_name,
    o.create_time,
    o.expected_delivery_date,
    c.customer_type,
    opp.opportunity_name
FROM crm_order o
LEFT JOIN crm_customer c ON o.customer_id = c.id
LEFT JOIN crm_opportunity opp ON o.opportunity_id = opp.id
WHERE o.del_flag = '0'
  AND (@status IS NULL OR o.status = @status)
  AND (@ownerId IS NULL OR o.owner_id = @ownerId)
  AND (@customerType IS NULL OR c.customer_type = @customerType)
  AND (@startDate IS NULL OR o.create_time >= @startDate)
  AND (@endDate IS NULL OR o.create_time <= @endDate)
  AND (@minAmount IS NULL OR o.total_amount >= @minAmount)
  AND (@maxAmount IS NULL OR o.total_amount <= @maxAmount)
  AND (@keyword IS NULL OR (
    o.order_no LIKE CONCAT('%', @keyword, '%') OR
    o.customer_name LIKE CONCAT('%', @keyword, '%')
  ))
ORDER BY 
  CASE WHEN o.urgent_flag = 1 THEN 0 ELSE 1 END,
  o.create_time DESC
LIMIT @offset, @pageSize;

-- 对应的索引设计
CREATE INDEX idx_order_search ON crm_order (
  status, owner_id, create_time, total_amount, urgent_flag
);
CREATE INDEX idx_order_customer_name ON crm_order (customer_name);
CREATE INDEX idx_order_no ON crm_order (order_no);
        </div>
    </div>

    <div class="section">
        <h2>七、质量保证与测试策略</h2>

        <h3>7.1 测试覆盖计划</h3>
        <table>
            <thead>
                <tr>
                    <th>测试类型</th>
                    <th>测试范围</th>
                    <th>覆盖率目标</th>
                    <th>测试工具</th>
                    <th>负责人</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>单元测试</td>
                    <td>Service层业务逻辑</td>
                    <td>≥85%</td>
                    <td>JUnit 5 + Mockito</td>
                    <td>后端开发</td>
                </tr>
                <tr>
                    <td>集成测试</td>
                    <td>API接口调用</td>
                    <td>100%接口</td>
                    <td>Spring Boot Test</td>
                    <td>后端开发</td>
                </tr>
                <tr>
                    <td>前端单测</td>
                    <td>Vue组件逻辑</td>
                    <td>≥70%</td>
                    <td>Vitest + Vue Test Utils</td>
                    <td>前端开发</td>
                </tr>
                <tr>
                    <td>E2E测试</td>
                    <td>关键业务流程</td>
                    <td>核心场景100%</td>
                    <td>Cypress</td>
                    <td>测试工程师</td>
                </tr>
                <tr>
                    <td>性能测试</td>
                    <td>高并发场景</td>
                    <td>响应时间&lt;2s</td>
                    <td>JMeter</td>
                    <td>测试工程师</td>
                </tr>
            </tbody>
        </table>

        <h3>7.2 关键测试用例</h3>
        <div class="feature-grid">
            <div class="feature-item priority-high">
                <h4>🔄 订单分配测试</h4>
                <ul>
                    <li>新客户订单自动进入待分配池</li>
                    <li>老客户订单自动分配给原负责人</li>
                    <li>抢单功能正常工作</li>
                    <li>分配历史记录准确</li>
                    <li>通知发送成功</li>
                </ul>
            </div>
            <div class="feature-item priority-high">
                <h4>📊 数据一致性测试</h4>
                <ul>
                    <li>订单状态变更同步</li>
                    <li>关联数据更新正确</li>
                    <li>并发操作数据一致</li>
                    <li>事务回滚机制</li>
                </ul>
            </div>
            <div class="feature-item priority-medium">
                <h4>🔍 查询性能测试</h4>
                <ul>
                    <li>大数据量列表查询</li>
                    <li>复杂条件筛选</li>
                    <li>分页查询效率</li>
                    <li>统计查询性能</li>
                </ul>
            </div>
            <div class="feature-item priority-medium">
                <h4>🛡️ 权限安全测试</h4>
                <ul>
                    <li>数据访问权限验证</li>
                    <li>操作权限控制</li>
                    <li>跨用户数据隔离</li>
                    <li>敏感操作审计</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="highlight" style="margin-top: 40px;">
        <h3 style="margin-top: 0; color: white;">🎯 总结</h3>
        <p style="margin-bottom: 0; font-size: 18px;">
            订单管理模块作为CRM核心模块，设计时充分借鉴了客户、联系人、商机模块的成熟架构，
            <br>确保了功能的完整性和用户体验的一致性。通过详细的时序安排和质量保证措施，
            <br>确保项目能够按期高质量交付，为CRM系统的完善奠定坚实基础。
        </p>
    </div>
</body>
</html>