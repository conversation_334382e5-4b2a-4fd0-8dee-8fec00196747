package com.ruoyi.common.domain.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.domain.entity.CrmOrderItem;

import lombok.Data;

/**
 * 订单数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
public class CrmOrderDTO {

    /** 订单ID */
    private Long id;

    /** 订单编号 */
    private String orderNo;

    /** 订单标题 */
    private String orderTitle;

    /** 询价单号 */
    private String quoteNo;

    /** 客户ID */
    private Long customerId;

    /** 客户名称 */
    private String customerName;

    /** 联系人ID */
    private Long contactId;

    /** 联系人姓名 */
    private String contactPerson;

    /** 联系电话 */
    private String contactPhone;

    /** 收货地址 */
    private String deliveryAddress;

    /** 关联商机ID */
    private Long opportunityId;

    /** 关联合同ID */
    private Long contractId;

    /** 订单来源 */
    private String orderSource;

    /** 订单类型 */
    private String orderType;

    /** 优先级 */
    private String priorityLevel;

    /** 紧急标志 */
    private Integer urgentFlag;

    /** 客户类型 */
    private String customerType;

    /** 订单总金额 */
    private BigDecimal totalAmount;

    /** 币种 */
    private String currency;

    /** 已付金额 */
    private BigDecimal paidAmount;

    /** 订单状态 */
    private String status;

    /** 分配状态 */
    private String assignmentStatus;

    /** 负责人ID */
    private Long ownerId;

    /** 负责人姓名 */
    private String ownerName;

    /** 分配人ID */
    private Long assignedBy;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignedTime;

    /** 下单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderDate;

    /** 预期交付日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedDeliveryDate;

    /** 实际交付日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualDeliveryDate;

    /** 备注 */
    private String remarks;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 创建人 */
    private String createBy;

    /** 更新人 */
    private String updateBy;

    /** 订单项列表 */
    private List<CrmOrderItem> orderItems;

    /** 文件URL列表 */
    private List<String> fileUrls;

    // ========== 统计字段 ==========
    
    /** 订单项数量 */
    private Integer itemCount;

    /** 订单总数量 */
    private Integer totalQuantity;

    /** 订单进度百分比 */
    private Integer progressPercent;

    /** 剩余金额 */
    private BigDecimal remainingAmount;

    /** 付款进度百分比 */
    private Integer paymentProgress;

    // ========== 关联信息 ==========
    
    /** 客户信息 */
    private CustomerInfo customerInfo;

    /** 联系人信息 */
    private ContactInfo contactInfo;

    /** 商机信息 */
    private OpportunityInfo opportunityInfo;

    /** 负责人信息 */
    private OwnerInfo ownerInfo;

    /**
     * 客户信息内部类
     */
    @Data
    public static class CustomerInfo {
        private Long id;
        private String customerName;
        private String customerLevel;
        private String customerIndustry;
        private String customerAddress;
        private String phone;
        private String email;
    }

    /**
     * 联系人信息内部类
     */
    @Data
    public static class ContactInfo {
        private Long id;
        private String name;
        private String position;
        private String phone;
        private String email;
        private String isKeyDecisionMaker;
    }

    /**
     * 商机信息内部类
     */
    @Data
    public static class OpportunityInfo {
        private Long id;
        private String opportunityName;
        private BigDecimal opportunityAmount;
        private String opportunityStage;
        private BigDecimal winRate;
        private Date expectedCloseDate;
    }

    /**
     * 负责人信息内部类
     */
    @Data
    public static class OwnerInfo {
        private Long id;
        private String userName;
        private String nickName;
        private String deptName;
        private String phone;
        private String email;
    }
}
