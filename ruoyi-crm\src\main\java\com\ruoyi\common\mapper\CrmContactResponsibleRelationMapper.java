package com.ruoyi.common.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.common.domain.entity.CrmContactResponsibleRelation;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.crm.domain.vo.PoolQueryRequest;

/**
 * 联系人-业务员关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface CrmContactResponsibleRelationMapper {
    
    /**
     * 查询联系人-业务员关系
     * 
     * @param id 联系人-业务员关系主键
     * @return 联系人-业务员关系
     */
    CrmContactResponsibleRelation selectCrmContactResponsibleRelationById(Long id);
    
    /**
     * 查询联系人-业务员关系列表
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 联系人-业务员关系集合
     */
    List<CrmContactResponsibleRelation> selectCrmContactResponsibleRelationList(CrmContactResponsibleRelation crmContactResponsibleRelation);
    
    /**
     * 新增联系人-业务员关系
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 结果
     */
    int insertCrmContactResponsibleRelation(CrmContactResponsibleRelation crmContactResponsibleRelation);
    
    /**
     * 批量新增联系人-业务员关系
     * 
     * @param list 联系人-业务员关系列表
     * @return 结果
     */
    int batchInsert(@Param("list") List<CrmContactResponsibleRelation> list);
    
    /**
     * 修改联系人-业务员关系
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 结果
     */
    int updateCrmContactResponsibleRelation(CrmContactResponsibleRelation crmContactResponsibleRelation);
    
    /**
     * 删除联系人-业务员关系
     * 
     * @param id 联系人-业务员关系主键
     * @return 结果
     */
    int deleteCrmContactResponsibleRelationById(Long id);
    
    /**
     * 批量删除联系人-业务员关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCrmContactResponsibleRelationByIds(Long[] ids);
    
    /**
     * 根据联系人ID查询所有负责人关系
     * 
     * @param contactId 联系人ID
     * @return 负责人关系列表
     */
    List<CrmContactResponsibleRelation> selectByContactId(@Param("contactId") Long contactId);
    
    /**
     * 根据负责人ID查询所有联系人关系
     * 
     * @param responsiblePersonId 负责人ID
     * @return 联系人关系列表
     */
    List<CrmContactResponsibleRelation> selectByResponsiblePersonId(@Param("responsiblePersonId") Long responsiblePersonId);
    
    /**
     * 根据用户ID查询其所属的团队成员信息
     * 
     * @param userId 用户ID
     * @return 团队成员列表
     */
    List<CrmContactResponsibleRelation> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据团队ID查询团队管理的联系人关系
     * 
     * @param teamId 团队ID
     * @return 联系人关系列表
     */
    List<CrmContactResponsibleRelation> selectByTeamId(@Param("teamId") Long teamId);
    
    /**
     * 检查指定关系是否存在
     * 
     * @param contactId 联系人ID
     * @param responsiblePersonId 负责人ID
     * @param businessType 业务类型
     * @return 存在的记录数
     */
    int checkRelationExists(@Param("contactId") Long contactId, 
                           @Param("responsiblePersonId") Long responsiblePersonId,
                           @Param("businessType") String businessType);
    
    /**
     * 检查活跃关系是否存在
     * 
     * @param contactId 联系人ID
     * @param responsiblePersonId 负责人ID
     * @param businessType 业务类型
     * @return 存在的活跃记录数
     */
    int checkActiveRelationExists(@Param("contactId") Long contactId, 
                                 @Param("responsiblePersonId") Long responsiblePersonId,
                                 @Param("businessType") String businessType);
    
    /**
     * 更新关系状态
     * 
     * @param contactId 联系人ID
     * @param responsiblePersonId 负责人ID
     * @param businessType 业务类型
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @return 更新记录数
     */
    int updateRelationStatus(@Param("contactId") Long contactId,
                            @Param("responsiblePersonId") Long responsiblePersonId,
                            @Param("businessType") String businessType,
                            @Param("oldStatus") String oldStatus,
                            @Param("newStatus") String newStatus);
    
    /**
     * 批量更新关系状态
     * 
     * @param contactIds 联系人ID列表
     * @param responsiblePersonId 负责人ID
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param endDate 结束时间
     * @param updateBy 更新人
     * @return 更新记录数
     */
    int batchUpdateRelationStatus(@Param("contactIds") List<Long> contactIds,
                                 @Param("responsiblePersonId") Long responsiblePersonId,
                                 @Param("oldStatus") String oldStatus,
                                 @Param("newStatus") String newStatus,
                                 @Param("endDate") Date endDate,
                                 @Param("updateBy") String updateBy);
    
    /**
     * 统计团队联系人数量
     * 
     * @param teamId 团队ID
     * @return 联系人数量
     */
    int countTeamContacts(@Param("teamId") Long teamId);
    
    /**
     * 按业务类型和团队统计
     * 
     * @param teamId 团队ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 统计结果列表
     */
    List<Map<String, Object>> countByBusinessTypeAndTeam(@Param("teamId") Long teamId,
                                                        @Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate);
    
    /**
     * 按成员统计联系人数量
     * 
     * @param teamId 团队ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 统计结果列表
     */
    List<Map<String, Object>> countByMemberAndDateRange(@Param("teamId") Long teamId,
                                                       @Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate);
    
    /**
     * 查询无负责人的联系人（指定业务类型）
     * 
     * @param businessType 业务类型
     * @return 联系人ID列表
     */
    List<Long> selectUnassignedContactIds(@Param("businessType") String businessType);
    
    /**
     * 查询团队可认领的联系人
     * 
     * @param teamId 团队ID
     * @param businessType 业务类型
     * @return 联系人ID列表
     */
    List<Long> selectTeamClaimableContactIds(@Param("teamId") Long teamId,
                                           @Param("businessType") String businessType);
    
    /**
     * 根据多个条件统计联系人关系
     * 
     * @param params 查询参数
     * @return 统计结果
     */
    List<Map<String, Object>> countByMultiConditions(@Param("params") Map<String, Object> params);
    
    /**
     * 查询即将过期的关系（如果有时间限制的话）
     * 
     * @param days 天数
     * @return 即将过期的关系列表
     */
    List<CrmContactResponsibleRelation> selectExpiringRelations(@Param("days") int days);
    
    /**
     * 根据联系人ID和业务类型查询活跃的负责人
     * 
     * @param contactId 联系人ID
     * @param businessType 业务类型
     * @return 负责人关系
     */
    CrmContactResponsibleRelation selectActiveResponsibleByContactAndBusiness(@Param("contactId") Long contactId,
                                                                            @Param("businessType") String businessType);
    
    /**
     * 批量查询联系人的负责人信息
     * 
     * @param contactIds 联系人ID列表
     * @param businessType 业务类型（可为空）
     * @return 负责人关系列表
     */
    List<CrmContactResponsibleRelation> selectResponsibleByContactIds(@Param("contactIds") List<Long> contactIds,
                                                                    @Param("businessType") String businessType);
    
    /**
     * 查询负责人的联系人数量统计
     * 
     * @param responsiblePersonId 负责人ID
     * @return 统计结果
     */
    Map<String, Object> countContactsByResponsible(@Param("responsiblePersonId") Long responsiblePersonId);
    
    /**
     * 查询公海联系人（多维度查询）
     * 
     * @param request 查询请求
     * @return 公海联系人列表
     */
    List<CrmContacts> selectPoolContacts(PoolQueryRequest request);
}