package com.ruoyi.common.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.ruoyi.common.domain.entity.CrmNewCustomerNotification;

/**
 * 新客户通知Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Mapper
public interface CrmNewCustomerNotificationMapper {
    
    /**
     * 查询新客户通知
     * 
     * @param id 新客户通知主键
     * @return 新客户通知
     */
    public CrmNewCustomerNotification selectCrmNewCustomerNotificationById(Long id);

    /**
     * 查询新客户通知列表
     * 
     * @param crmNewCustomerNotification 新客户通知
     * @return 新客户通知集合
     */
    public List<CrmNewCustomerNotification> selectCrmNewCustomerNotificationList(CrmNewCustomerNotification crmNewCustomerNotification);

    /**
     * 根据客户ID查询通知
     * 
     * @param customerId 客户ID
     * @return 通知列表
     */
    public List<CrmNewCustomerNotification> selectCrmNewCustomerNotificationByCustomerId(Long customerId);

    /**
     * 根据订单ID查询通知
     * 
     * @param orderId 订单ID
     * @return 通知列表
     */
    public List<CrmNewCustomerNotification> selectCrmNewCustomerNotificationByOrderId(Long orderId);

    /**
     * 根据通知状态查询通知
     * 
     * @param notificationStatus 通知状态
     * @return 通知列表
     */
    public List<CrmNewCustomerNotification> selectCrmNewCustomerNotificationByStatus(String notificationStatus);

    /**
     * 根据分配给的用户查询通知
     * 
     * @param assignedTo 分配给的用户ID
     * @return 通知列表
     */
    public List<CrmNewCustomerNotification> selectCrmNewCustomerNotificationByAssignedTo(Long assignedTo);

    /**
     * 查询待处理的通知
     * 
     * @return 待处理通知列表
     */
    public List<CrmNewCustomerNotification> selectPendingNotifications();

    /**
     * 查询超时未处理的通知
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时通知列表
     */
    public List<CrmNewCustomerNotification> selectTimeoutNotifications(@Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 查询用户的通知统计
     * 
     * @param assignedTo 分配给的用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public List<CrmNewCustomerNotification> selectNotificationStatsByUser(@Param("assignedTo") Long assignedTo, 
                                                                          @Param("startTime") String startTime, 
                                                                          @Param("endTime") String endTime);

    /**
     * 查询通知处理效率统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 效率统计
     */
    public List<CrmNewCustomerNotification> selectNotificationEfficiencyStats(@Param("startTime") String startTime, 
                                                                               @Param("endTime") String endTime);

    /**
     * 查询未发送企业微信通知的记录
     * 
     * @return 未发送通知列表
     */
    public List<CrmNewCustomerNotification> selectUnsentWechatNotifications();

    /**
     * 查询未发送邮件通知的记录
     * 
     * @return 未发送邮件列表
     */
    public List<CrmNewCustomerNotification> selectUnsentEmailNotifications();

    /**
     * 新增新客户通知
     * 
     * @param crmNewCustomerNotification 新客户通知
     * @return 结果
     */
    public int insertCrmNewCustomerNotification(CrmNewCustomerNotification crmNewCustomerNotification);

    /**
     * 修改新客户通知
     * 
     * @param crmNewCustomerNotification 新客户通知
     * @return 结果
     */
    public int updateCrmNewCustomerNotification(CrmNewCustomerNotification crmNewCustomerNotification);

    /**
     * 更新通知状态
     * 
     * @param id 通知ID
     * @param status 新状态
     * @param processedBy 处理人ID
     * @param processResult 处理结果
     * @return 结果
     */
    public int updateNotificationStatus(@Param("id") Long id, 
                                        @Param("status") String status, 
                                        @Param("processedBy") Long processedBy, 
                                        @Param("processResult") String processResult);

    /**
     * 更新企业微信发送状态
     * 
     * @param id 通知ID
     * @param wechatSent 发送状态
     * @return 结果
     */
    public int updateWechatSentStatus(@Param("id") Long id, @Param("wechatSent") Integer wechatSent);

    /**
     * 更新邮件发送状态
     * 
     * @param id 通知ID
     * @param emailSent 发送状态
     * @return 结果
     */
    public int updateEmailSentStatus(@Param("id") Long id, @Param("emailSent") Integer emailSent);

    /**
     * 批量更新通知状态
     * 
     * @param ids 通知ID列表
     * @param status 新状态
     * @param processedBy 处理人ID
     * @return 结果
     */
    public int batchUpdateNotificationStatus(@Param("ids") List<Long> ids, 
                                             @Param("status") String status, 
                                             @Param("processedBy") Long processedBy);

    /**
     * 删除新客户通知
     * 
     * @param id 新客户通知主键
     * @return 结果
     */
    public int deleteCrmNewCustomerNotificationById(Long id);

    /**
     * 批量删除新客户通知
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmNewCustomerNotificationByIds(Long[] ids);

    /**
     * 根据客户ID删除通知
     * 
     * @param customerId 客户ID
     * @return 结果
     */
    public int deleteCrmNewCustomerNotificationByCustomerId(Long customerId);

    /**
     * 根据订单ID删除通知
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int deleteCrmNewCustomerNotificationByOrderId(Long orderId);

    /**
     * 批量插入新客户通知
     * 
     * @param notifications 通知列表
     * @return 结果
     */
    public int batchInsertCrmNewCustomerNotification(List<CrmNewCustomerNotification> notifications);

    /**
     * 清理过期通知
     * 
     * @param expireDays 过期天数
     * @return 清理数量
     */
    public int cleanExpiredNotifications(@Param("expireDays") Integer expireDays);
}
