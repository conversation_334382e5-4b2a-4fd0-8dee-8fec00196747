<template>
    <div class="customer-activity-tab">
        <!-- 添加跟进记录区域 -->
        <div class="add-activity-section">
            <el-card class="activity-form-card">
                <template #header>
                    <div class="card-header" @click="toggleFormExpanded" style="cursor: pointer;">
                        <el-icon class="header-icon"><Plus /></el-icon>
                        <span>添加跟进记录</span>
                        <el-icon class="expand-icon" :class="{ 'expanded': isFormExpanded }">
                            <ArrowDown />
                        </el-icon>
                    </div>
                </template>
                
                <el-collapse-transition>
                    <div v-show="isFormExpanded">
                        <el-form :model="newActivity" label-width="100px" size="default">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="跟进类型" required>
                                        <el-select v-model="newActivity.followupType" placeholder="请选择跟进类型" style="width: 100%">
                                            <el-option 
                                                v-for="option in followupTypeOptions" 
                                                :key="option.value" 
                                                :label="option.label" 
                                                :value="option.value" 
                                            />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="跟进时间" required>
                                        <el-date-picker 
                                            v-model="newActivity.followupTime" 
                                            type="datetime" 
                                            placeholder="选择跟进时间"
                                            style="width: 100%"
                                            format="YYYY-MM-DD HH:mm"
                                            value-format="YYYY-MM-DD HH:mm:ss"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-form-item label="跟进内容" required>
                                <el-input 
                                    v-model="newActivity.content" 
                                    type="textarea" 
                                    :rows="4" 
                                    placeholder="请详细描述本次跟进的内容、客户反馈和下步计划..."
                                    maxlength="500"
                                    show-word-limit
                                />
                            </el-form-item>
                            
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="下次跟进时间">
                                        <el-date-picker 
                                            v-model="newActivity.nextFollowupTime" 
                                            type="datetime" 
                                            placeholder="选择下次跟进时间"
                                            style="width: 100%"
                                            format="YYYY-MM-DD HH:mm"
                                            value-format="YYYY-MM-DD HH:mm:ss"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="跟进结果">
                                        <el-select v-model="newActivity.result" placeholder="请选择跟进结果" style="width: 100%">
                                            <el-option label="有意向" value="interested" />
                                            <el-option label="需要考虑" value="considering" />
                                            <el-option label="暂无需求" value="no_need" />
                                            <el-option label="确认合作" value="confirmed" />
                                            <el-option label="暂时拒绝" value="refused" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="是否重要">
                                        <el-switch 
                                            v-model="newActivity.isImportant" 
                                            active-text="重要" 
                                            inactive-text="普通"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="客户满意度">
                                        <el-rate 
                                            v-model="newActivity.satisfaction" 
                                            :max="5"
                                            show-text
                                            :texts="['很不满意', '不满意', '一般', '满意', '很满意']"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <div class="form-actions">
                                <el-button @click="resetForm">清空</el-button>
                                <el-button type="primary" @click="handleAddActivity" :loading="addLoading">
                                    <el-icon><Check /></el-icon>
                                    保存记录
                                </el-button>
                            </div>
                        </el-form>
                    </div>
                </el-collapse-transition>
            </el-card>
        </div>

        <!-- 跟进记录列表 -->
        <div class="activity-list-section">
            <div class="section-header">
                <h3>跟进记录</h3>
                <div class="activity-stats">
                    <el-tag size="small" type="info">
                        共 {{ activities.length }} 条记录
                    </el-tag>
                </div>
            </div>

            <div v-loading="loading" class="activity-content">
                <div v-if="activities.length === 0" class="empty-state">
                    <el-empty description="暂无跟进记录" />
                </div>
                
                <el-timeline v-else>
                    <el-timeline-item
                        v-for="activity in activities"
                        :key="activity.id"
                        :timestamp="activity.followupTime || activity.createTime"
                        :type="activity.isImportant ? 'danger' : 'primary'"
                        :hollow="!activity.isImportant"
                    >
                        <el-card shadow="hover" class="activity-card">
                            <template #header>
                                <div class="activity-header">
                                    <div class="activity-info">
                                        <el-tag 
                                            size="small" 
                                            :type="getFollowupTypeTagType(activity.followupType)"
                                        >
                                            {{ getFollowupTypeName(activity.followupType) }}
                                        </el-tag>
                                        <span v-if="activity.result" class="activity-result">
                                            · {{ getFollowupResultName(activity.result) }}
                                        </span>
                                        <el-tag 
                                            v-if="activity.isImportant" 
                                            size="small" 
                                            type="danger"
                                            class="important-tag"
                                        >
                                            重要
                                        </el-tag>
                                    </div>
                                    <div class="activity-actions">
                                        <el-button 
                                            size="small" 
                                            text 
                                            @click="handleEditActivity(activity)"
                                        >
                                            <el-icon><Edit /></el-icon>
                                        </el-button>
                                        <el-button 
                                            size="small" 
                                            text 
                                            type="danger"
                                            @click="handleDeleteActivity(activity)"
                                        >
                                            <el-icon><Delete /></el-icon>
                                        </el-button>
                                    </div>
                                </div>
                            </template>

                            <div class="activity-content">
                                <p class="activity-text">{{ activity.content }}</p>
                                
                                <div v-if="activity.nextFollowupTime" class="next-followup">
                                    <el-icon><Clock /></el-icon>
                                    <span>下次跟进：{{ activity.nextFollowupTime }}</span>
                                </div>

                                <div v-if="activity.satisfaction" class="satisfaction">
                                    <span>客户满意度：</span>
                                    <el-rate 
                                        :model-value="activity.satisfaction" 
                                        disabled 
                                        size="small"
                                        :max="5"
                                    />
                                </div>
                            </div>

                            <template #extra>
                                <div class="activity-meta">
                                    <span class="activity-author">{{ activity.createBy || '系统' }}</span>
                                </div>
                            </template>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { Plus, ArrowDown, Check, Edit, Delete, Clock } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { CustomerData } from '../types';
import request from '@/utils/request';

interface FollowupRecord {
    id: string;
    customerId: string;
    followupType: string;
    followupTime: string;
    content: string;
    result?: string;
    nextFollowupTime?: string;
    isImportant: boolean;
    satisfaction?: number;
    createTime: string;
    createBy: string;
}

interface Props {
    entityData: CustomerData | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    'refresh-data': [];
}>();

// 状态管理
const loading = ref(false);
const addLoading = ref(false);
const isFormExpanded = ref(false);
const activities = ref<FollowupRecord[]>([]);

// 新增跟进记录表单
const newActivity = reactive({
    followupType: '',
    followupTime: '',
    content: '',
    result: '',
    nextFollowupTime: '',
    isImportant: false,
    satisfaction: 0
});

// 跟进类型选项
const followupTypeOptions = [
    { label: '电话沟通', value: 'call' },
    { label: '邮件联系', value: 'email' },
    { label: '面对面会议', value: 'meeting' },
    { label: '上门拜访', value: 'visit' },
    { label: '产品演示', value: 'demo' },
    { label: '方案讲解', value: 'proposal' },
    { label: '合同洽谈', value: 'contract' },
    { label: '其他', value: 'other' }
];

// 切换表单展开状态
const toggleFormExpanded = () => {
    isFormExpanded.value = !isFormExpanded.value;
};

// 重置表单
const resetForm = () => {
    Object.assign(newActivity, {
        followupType: '',
        followupTime: '',
        content: '',
        result: '',
        nextFollowupTime: '',
        isImportant: false,
        satisfaction: 0
    });
};

// 添加跟进记录
const handleAddActivity = async () => {
    if (!newActivity.followupType || !newActivity.followupTime || !newActivity.content) {
        ElMessage.warning('请填写必填项');
        return;
    }

    if (!props.entityData?.id) {
        ElMessage.error('客户信息不存在');
        return;
    }

    addLoading.value = true;
    try {
        console.log('准备添加跟进记录，客户ID:', props.entityData.id);
        console.log('跟进记录数据:', newActivity);
        
        const response = await request({
            url: '/front/crm/customer/followup',
            method: 'post',
            data: {
                customerId: props.entityData.id,
                followupType: newActivity.followupType,
                followupContent: newActivity.content
            }
        });
        
        console.log('API响应:', response);
        
        if (response.code === 200) {
            ElMessage.success('添加跟进记录成功');
            resetForm();
            isFormExpanded.value = false;
            await loadActivities();
            emit('refresh-data');
        } else {
            ElMessage.error(response.msg || '添加跟进记录失败');
        }
    } catch (error) {
        console.error('添加跟进记录失败:', error);
        ElMessage.error('添加跟进记录失败');
    } finally {
        addLoading.value = false;
    }
};

// 编辑跟进记录
const handleEditActivity = (activity: FollowupRecord) => {
    // TODO: 实现编辑功能
    ElMessage.info('编辑功能开发中');
};

// 删除跟进记录
const handleDeleteActivity = async (activity: FollowupRecord) => {
    try {
        await ElMessageBox.confirm('确定要删除这条跟进记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        // TODO: 调用API删除跟进记录
        // await deleteCustomerActivity(activity.id);
        
        ElMessage.success('删除成功');
        await loadActivities();
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败');
        }
    }
};

// 加载跟进记录
const loadActivities = async () => {
    if (!props.entityData?.id) {
        activities.value = [];
        return;
    }
    
    loading.value = true;
    try {
        console.log('加载客户跟进记录，客户ID:', props.entityData.id);
        
        const response = await request({
            url: `/front/crm/customer/followup/customer/${props.entityData.id}`,
            method: 'get'
        });
        
        console.log('跟进记录响应:', response);
        
        if (response.code === 200) {
            // 转换数据格式以匹配组件期望的格式
            activities.value = (response.data || []).map((item: any) => ({
                id: String(item.id),
                customerId: String(item.customerId),
                followupType: item.followupType || 'other',
                followupTime: item.createTime,
                content: item.followupContent || '',
                result: '',
                nextFollowupTime: '',
                isImportant: false,
                satisfaction: 0,
                createTime: item.createTime,
                createBy: item.userName || item.userNickName || '系统'
            }));
        } else {
            activities.value = [];
        }
    } catch (error) {
        console.error('加载跟进记录失败:', error);
        ElMessage.error('加载跟进记录失败');
        activities.value = [];
    } finally {
        loading.value = false;
    }
};

// 获取跟进类型名称
const getFollowupTypeName = (type: string) => {
    const option = followupTypeOptions.find(opt => opt.value === type);
    return option?.label || type;
};

// 获取跟进类型标签类型
const getFollowupTypeTagType = (type: string) => {
    const typeMap: Record<string, string> = {
        'call': 'success',
        'email': 'info',
        'meeting': 'warning',
        'visit': 'danger',
        'demo': 'primary',
        'proposal': 'success',
        'contract': 'warning'
    };
    return typeMap[type] || 'info';
};

// 获取跟进结果名称  
const getFollowupResultName = (result: string) => {
    const resultMap: Record<string, string> = {
        'interested': '有意向',
        'considering': '需要考虑',
        'no_need': '暂无需求',
        'confirmed': '确认合作',
        'refused': '暂时拒绝'
    };
    return resultMap[result] || result;
};

// 组件挂载时加载数据
onMounted(() => {
    if (props.entityData) {
        loadActivities();
    }
});
</script>

<style scoped lang="scss">
.customer-activity-tab {
    .add-activity-section {
        margin-bottom: 24px;

        .activity-form-card {
            .card-header {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 500;

                .header-icon {
                    color: #409eff;
                }

                .expand-icon {
                    margin-left: auto;
                    transition: transform 0.3s ease;

                    &.expanded {
                        transform: rotate(180deg);
                    }
                }
            }

            .form-actions {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                margin-top: 20px;
                padding-top: 20px;
                border-top: 1px solid #e4e7ed;
            }
        }
    }

    .activity-list-section {
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

            h3 {
                margin: 0;
                color: #303133;
                font-size: 18px;
                font-weight: 600;
            }

            .activity-stats {
                display: flex;
                gap: 8px;
            }
        }

        .activity-content {
            .empty-state {
                text-align: center;
                padding: 60px 0;
            }

            .activity-card {
                margin-bottom: 16px;

                .activity-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .activity-info {
                        display: flex;
                        align-items: center;
                        gap: 8px;

                        .activity-result {
                            color: #606266;
                            font-size: 14px;
                        }

                        .important-tag {
                            margin-left: 8px;
                        }
                    }

                    .activity-actions {
                        display: flex;
                        gap: 4px;
                    }
                }

                .activity-content {
                    .activity-text {
                        margin: 12px 0;
                        line-height: 1.6;
                        color: #303133;
                    }

                    .next-followup {
                        display: flex;
                        align-items: center;
                        gap: 6px;
                        color: #409eff;
                        font-size: 14px;
                        margin: 8px 0;
                    }

                    .satisfaction {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin: 8px 0;
                        font-size: 14px;
                        color: #606266;
                    }
                }

                .activity-meta {
                    display: flex;
                    justify-content: flex-end;
                    color: #909399;
                    font-size: 12px;

                    .activity-author {
                        &::before {
                            content: '记录人：';
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .customer-activity-tab {
        .add-activity-section {
            .activity-form-card {
                .el-form {
                    .el-row {
                        .el-col {
                            &:not(:last-child) {
                                margin-bottom: 16px;
                            }
                        }
                    }
                }
            }
        }

        .activity-list-section {
            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .activity-content {
                .activity-card {
                    .activity-header {
                        flex-direction: column;
                        gap: 12px;
                        align-items: flex-start;

                        .activity-actions {
                            align-self: flex-end;
                        }
                    }
                }
            }
        }
    }
}
</style>