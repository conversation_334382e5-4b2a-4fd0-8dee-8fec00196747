<template>
    <div class="customer-opportunity-tab">
        <!-- 商机统计 -->
        <div class="opportunity-stats-section">
            <el-row :gutter="16">
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ opportunities.length }}</div>
                            <div class="stat-label">商机总数</div>
                        </div>
                        <el-icon class="stat-icon"><Opportunity /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ activeOpportunities.length }}</div>
                            <div class="stat-label">进行中</div>
                        </div>
                        <el-icon class="stat-icon active"><TrendCharts /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ wonOpportunities.length }}</div>
                            <div class="stat-label">已成交</div>
                        </div>
                        <el-icon class="stat-icon won"><SuccessFilled /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ formatAmount(totalAmount) }}</div>
                            <div class="stat-label">总金额</div>
                        </div>
                        <el-icon class="stat-icon amount"><Money /></el-icon>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 操作区域 -->
        <div class="opportunity-actions-section">
            <div class="section-header">
                <h3>关联商机</h3>
                <div class="header-actions">
                    <el-button type="primary" size="small" @click="showCreateOpportunityDialog">
                        <el-icon><Plus /></el-icon>
                        新建商机
                    </el-button>
                    <el-button size="small" @click="showLinkOpportunityDialog">
                        <el-icon><Link /></el-icon>
                        关联商机
                    </el-button>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="filter-controls">
                <el-select 
                    v-model="filterStage" 
                    placeholder="商机阶段" 
                    clearable 
                    size="small"
                    style="width: 150px;"
                    @change="handleFilterChange"
                >
                    <el-option label="全部" value="" />
                    <el-option label="初步接触" value="initial" />
                    <el-option label="需求确认" value="qualified" />
                    <el-option label="方案报价" value="proposal" />
                    <el-option label="商务谈判" value="negotiation" />
                    <el-option label="合同签署" value="contract" />
                    <el-option label="已成交" value="won" />
                    <el-option label="已失败" value="lost" />
                </el-select>
                
                <el-input
                    v-model="searchKeyword"
                    placeholder="搜索商机名称"
                    size="small"
                    style="width: 200px;"
                    clearable
                    @input="handleSearch"
                >
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>

                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    @change="handleDateChange"
                />
            </div>
        </div>

        <!-- 商机列表 -->
        <div class="opportunity-list-section" v-loading="loading">
            <div v-if="filteredOpportunities.length === 0" class="empty-state">
                <el-empty description="暂无关联商机" />
            </div>

            <div v-else class="opportunity-timeline">
                <el-timeline>
                    <el-timeline-item
                        v-for="opportunity in filteredOpportunities"
                        :key="opportunity.id"
                        :timestamp="opportunity.expectedCloseDate"
                        :type="getTimelineType(opportunity.stage)"
                        :hollow="opportunity.stage === 'lost'"
                    >
                        <el-card class="opportunity-card" shadow="hover">
                            <template #header>
                                <div class="opportunity-header">
                                    <div class="opportunity-title">
                                        <h4 class="opportunity-name">{{ opportunity.name }}</h4>
                                        <div class="opportunity-tags">
                                            <el-tag 
                                                :type="getStageTagType(opportunity.stage)"
                                                size="small"
                                            >
                                                {{ getStageName(opportunity.stage) }}
                                            </el-tag>
                                            <el-tag 
                                                v-if="opportunity.priority"
                                                :type="getPriorityTagType(opportunity.priority)"
                                                size="small"
                                            >
                                                {{ getPriorityName(opportunity.priority) }}
                                            </el-tag>
                                        </div>
                                    </div>
                                    <div class="opportunity-actions">
                                        <el-dropdown @command="handleOpportunityAction">
                                            <el-button size="small" text>
                                                <el-icon><MoreFilled /></el-icon>
                                            </el-button>
                                            <template #dropdown>
                                                <el-dropdown-menu>
                                                    <el-dropdown-item :command="{ action: 'view', opportunity }">
                                                        <el-icon><View /></el-icon>
                                                        查看详情
                                                    </el-dropdown-item>
                                                    <el-dropdown-item :command="{ action: 'edit', opportunity }">
                                                        <el-icon><Edit /></el-icon>
                                                        编辑商机
                                                    </el-dropdown-item>
                                                    <el-dropdown-item :command="{ action: 'stage', opportunity }">
                                                        <el-icon><Rank /></el-icon>
                                                        更新阶段
                                                    </el-dropdown-item>
                                                    <el-dropdown-item 
                                                        :command="{ action: 'unlink', opportunity }"
                                                        divided
                                                    >
                                                        <el-icon><Link /></el-icon>
                                                        取消关联
                                                    </el-dropdown-item>
                                                </el-dropdown-menu>
                                            </template>
                                        </el-dropdown>
                                    </div>
                                </div>
                            </template>

                            <div class="opportunity-content">
                                <div class="opportunity-info">
                                    <el-row :gutter="16">
                                        <el-col :span="12">
                                            <div class="info-item">
                                                <span class="info-label">预计金额：</span>
                                                <span class="info-value amount">{{ formatAmount(opportunity.expectedAmount) }}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">成交概率：</span>
                                                <span class="info-value">
                                                    <el-progress 
                                                        :percentage="opportunity.winProbability" 
                                                        :stroke-width="6"
                                                        :show-text="false"
                                                        :color="getProgressColor(opportunity.winProbability)"
                                                    />
                                                    <span class="probability-text">{{ opportunity.winProbability }}%</span>
                                                </span>
                                            </div>
                                        </el-col>
                                        <el-col :span="12">
                                            <div class="info-item">
                                                <span class="info-label">负责人：</span>
                                                <span class="info-value">{{ opportunity.ownerName || '-' }}</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">创建时间：</span>
                                                <span class="info-value">{{ opportunity.createTime }}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>

                                <div v-if="opportunity.description" class="opportunity-description">
                                    <p>{{ opportunity.description }}</p>
                                </div>

                                <div class="opportunity-metrics">
                                    <div class="metric-item">
                                        <el-icon><Timer /></el-icon>
                                        <span>跟进 {{ opportunity.followupCount || 0 }} 次</span>
                                    </div>
                                    <div class="metric-item">
                                        <el-icon><ChatDotRound /></el-icon>
                                        <span>沟通 {{ opportunity.communicationCount || 0 }} 次</span>
                                    </div>
                                    <div class="metric-item">
                                        <el-icon><Document /></el-icon>
                                        <span>附件 {{ opportunity.attachmentCount || 0 }} 个</span>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </div>

        <!-- 新建商机对话框 -->
        <el-dialog 
            v-model="createOpportunityVisible" 
            title="新建商机" 
            width="800px"
            :before-close="closeCreateOpportunityDialog"
        >
            <el-form :model="createOpportunityForm" :rules="createOpportunityRules" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="商机名称" prop="name">
                            <el-input 
                                v-model="createOpportunityForm.name" 
                                placeholder="请输入商机名称"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="预计金额" prop="expectedAmount">
                            <el-input-number 
                                v-model="createOpportunityForm.expectedAmount" 
                                :min="0"
                                :precision="2"
                                style="width: 100%"
                                placeholder="请输入预计金额"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="商机阶段" prop="stage">
                            <el-select 
                                v-model="createOpportunityForm.stage" 
                                placeholder="请选择商机阶段"
                                style="width: 100%"
                            >
                                <el-option label="初步接触" value="initial" />
                                <el-option label="需求确认" value="qualified" />
                                <el-option label="方案报价" value="proposal" />
                                <el-option label="商务谈判" value="negotiation" />
                                <el-option label="合同签署" value="contract" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="成交概率" prop="winProbability">
                            <el-slider 
                                v-model="createOpportunityForm.winProbability"
                                :min="0"
                                :max="100"
                                show-input
                                input-size="small"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="预计成交日期" prop="expectedCloseDate">
                            <el-date-picker 
                                v-model="createOpportunityForm.expectedCloseDate" 
                                type="date"
                                placeholder="选择预计成交日期"
                                style="width: 100%"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="优先级" prop="priority">
                            <el-select 
                                v-model="createOpportunityForm.priority" 
                                placeholder="请选择优先级"
                                style="width: 100%"
                            >
                                <el-option label="低" value="low" />
                                <el-option label="中" value="medium" />
                                <el-option label="高" value="high" />
                                <el-option label="紧急" value="urgent" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="商机描述">
                    <el-input 
                        v-model="createOpportunityForm.description" 
                        type="textarea"
                        :rows="4"
                        placeholder="请描述商机的具体情况、客户需求等"
                        maxlength="500"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeCreateOpportunityDialog">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="handleCreateOpportunity" 
                        :loading="createOpportunityLoading"
                    >
                        创建商机
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { 
    Plus,
    Link,

    Search,
    View,
    Edit,
    Rank,
    MoreFilled,
    Timer,
    ChatDotRound,
    Document,
    Opportunity,
    TrendCharts,
    SuccessFilled,
    Money
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { CustomerData } from '../types';

interface OpportunityData {
    id: string;
    name: string;
    expectedAmount: number;
    stage: string;
    winProbability: number;
    priority: string;
    expectedCloseDate: string;
    description: string;
    ownerName: string;
    createTime: string;
    followupCount: number;
    communicationCount: number;
    attachmentCount: number;
}

interface Props {
    entityData: CustomerData | null;
}

const props = defineProps<Props>();

// 状态管理
const loading = ref(false);
const opportunities = ref<OpportunityData[]>([]);
const filterStage = ref('');
const searchKeyword = ref('');
const dateRange = ref<[string, string] | null>(null);

// 新建商机相关
const createOpportunityVisible = ref(false);
const createOpportunityLoading = ref(false);
const createOpportunityForm = reactive({
    name: '',
    expectedAmount: 0,
    stage: 'initial',
    winProbability: 50,
    expectedCloseDate: '',
    priority: 'medium',
    description: ''
});

const createOpportunityRules = {
    name: [{ required: true, message: '请输入商机名称', trigger: 'blur' }],
    expectedAmount: [{ required: true, message: '请输入预计金额', trigger: 'blur' }],
    stage: [{ required: true, message: '请选择商机阶段', trigger: 'change' }],
    expectedCloseDate: [{ required: true, message: '请选择预计成交日期', trigger: 'change' }]
};

// 计算属性
const activeOpportunities = computed(() => 
    opportunities.value.filter(o => !['won', 'lost'].includes(o.stage))
);

const wonOpportunities = computed(() => 
    opportunities.value.filter(o => o.stage === 'won')
);

const totalAmount = computed(() => 
    opportunities.value.reduce((sum, o) => sum + o.expectedAmount, 0)
);

const filteredOpportunities = computed(() => {
    let result = opportunities.value;
    
    // 按阶段过滤
    if (filterStage.value) {
        result = result.filter(o => o.stage === filterStage.value);
    }
    
    // 按关键词搜索
    if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(o => 
            o.name.toLowerCase().includes(keyword) ||
            (o.description && o.description.toLowerCase().includes(keyword))
        );
    }
    
    // 按日期范围过滤
    if (dateRange.value && dateRange.value.length === 2) {
        const [startDate, endDate] = dateRange.value;
        result = result.filter(o => 
            o.expectedCloseDate >= startDate && o.expectedCloseDate <= endDate
        );
    }
    
    return result.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
});

// 格式化金额
const formatAmount = (amount: number) => {
    if (amount >= 10000) {
        return (amount / 10000).toFixed(1) + '万';
    }
    return amount.toLocaleString();
};

// 获取阶段名称
const getStageName = (stage: string) => {
    const stageMap = {
        initial: '初步接触',
        qualified: '需求确认',
        proposal: '方案报价',
        negotiation: '商务谈判',
        contract: '合同签署',
        won: '已成交',
        lost: '已失败'
    };
    return stageMap[stage as keyof typeof stageMap] || stage;
};

// 获取阶段标签类型
const getStageTagType = (stage: string) => {
    const typeMap = {
        initial: 'info',
        qualified: 'primary',
        proposal: 'warning',
        negotiation: 'danger',
        contract: 'success',
        won: 'success',
        lost: 'info'
    };
    return typeMap[stage as keyof typeof typeMap] || 'info';
};

// 获取优先级名称
const getPriorityName = (priority: string) => {
    const priorityMap = {
        low: '低',
        medium: '中',
        high: '高',
        urgent: '紧急'
    };
    return priorityMap[priority as keyof typeof priorityMap] || priority;
};

// 获取优先级标签类型
const getPriorityTagType = (priority: string) => {
    const typeMap = {
        low: 'info',
        medium: 'primary',
        high: 'warning',
        urgent: 'danger'
    };
    return typeMap[priority as keyof typeof typeMap] || 'info';
};

// 获取时间线类型
const getTimelineType = (stage: string) => {
    if (stage === 'won') return 'success';
    if (stage === 'lost') return 'danger';
    return 'primary';
};

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
    if (percentage < 30) return '#f56c6c';
    if (percentage < 70) return '#e6a23c';
    return '#67c23a';
};

// 处理筛选变化
const handleFilterChange = () => {
    // 筛选逻辑在computed中处理
};

// 处理搜索
const handleSearch = () => {
    // 搜索逻辑在computed中处理
};

// 处理日期变化
const handleDateChange = () => {
    // 日期筛选逻辑在computed中处理
};

// 处理商机操作
const handleOpportunityAction = async (command: { action: string; opportunity: OpportunityData }) => {
    const { action, opportunity } = command;
    
    switch (action) {
        case 'view':
            ElMessage.info('查看商机详情功能开发中');
            break;
        case 'edit':
            ElMessage.info('编辑商机功能开发中');
            break;
        case 'stage':
            ElMessage.info('更新商机阶段功能开发中');
            break;
        case 'unlink':
            await handleUnlinkOpportunity(opportunity);
            break;
    }
};

// 取消关联商机
const handleUnlinkOpportunity = async (opportunity: OpportunityData) => {
    try {
        await ElMessageBox.confirm(
            `确定要取消与商机 "${opportunity.name}" 的关联吗？`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        // TODO: 调用API取消关联
        // await unlinkOpportunity(props.entityData.id, opportunity.id);
        
        ElMessage.success('取消关联成功');
        await loadOpportunities();
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('取消关联失败');
        }
    }
};

// 显示关联商机对话框
const showLinkOpportunityDialog = () => {
    ElMessage.info('关联商机功能开发中');
};

// 显示新建商机对话框
const showCreateOpportunityDialog = () => {
    createOpportunityVisible.value = true;
};

// 关闭新建商机对话框
const closeCreateOpportunityDialog = () => {
    createOpportunityVisible.value = false;
    Object.assign(createOpportunityForm, {
        name: '',
        expectedAmount: 0,
        stage: 'initial',
        winProbability: 50,
        expectedCloseDate: '',
        priority: 'medium',
        description: ''
    });
};

// 创建商机
const handleCreateOpportunity = async () => {
    createOpportunityLoading.value = true;
    try {
        // TODO: 调用API创建商机
        // await createOpportunity({
        //     ...createOpportunityForm,
        //     customerId: props.entityData?.id
        // });
        
        ElMessage.success('创建商机成功');
        closeCreateOpportunityDialog();
        await loadOpportunities();
    } catch (error) {
        ElMessage.error('创建商机失败');
    } finally {
        createOpportunityLoading.value = false;
    }
};

// 加载商机列表
const loadOpportunities = async () => {
    loading.value = true;
    try {
        // TODO: 调用API加载商机列表
        // const response = await getCustomerOpportunities(props.entityData?.id);
        // opportunities.value = response.data;
        
        // 模拟数据
        opportunities.value = [];
    } catch (error) {
        ElMessage.error('加载商机列表失败');
    } finally {
        loading.value = false;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    if (props.entityData) {
        loadOpportunities();
    }
});
</script>

<style scoped lang="scss">
.customer-opportunity-tab {
    .opportunity-stats-section {
        margin-bottom: 24px;

        .stat-card {
            text-align: center;
            position: relative;
            overflow: hidden;

            .stat-content {
                .stat-number {
                    font-size: 28px;
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 8px;
                }

                .stat-label {
                    font-size: 14px;
                    color: #606266;
                }
            }

            .stat-icon {
                position: absolute;
                right: 16px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 32px;
                color: #409eff;
                opacity: 0.3;

                &.active {
                    color: #e6a23c;
                }

                &.won {
                    color: #67c23a;
                }

                &.amount {
                    color: #f56c6c;
                }
            }
        }
    }

    .opportunity-actions-section {
        margin-bottom: 24px;

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            h3 {
                margin: 0;
                color: #303133;
                font-size: 18px;
                font-weight: 600;
            }

            .header-actions {
                display: flex;
                gap: 12px;
            }
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }
    }

    .opportunity-list-section {
        .empty-state {
            text-align: center;
            padding: 60px 0;
        }

        .opportunity-timeline {
            .opportunity-card {
                margin-bottom: 16px;

                .opportunity-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;

                    .opportunity-title {
                        flex: 1;

                        .opportunity-name {
                            margin: 0 0 8px 0;
                            font-size: 16px;
                            font-weight: 600;
                            color: #303133;
                        }

                        .opportunity-tags {
                            display: flex;
                            gap: 8px;
                        }
                    }
                }

                .opportunity-content {
                    .opportunity-info {
                        margin-bottom: 16px;

                        .info-item {
                            display: flex;
                            align-items: center;
                            margin-bottom: 8px;
                            font-size: 14px;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            .info-label {
                                flex-shrink: 0;
                                width: 80px;
                                color: #606266;
                                font-weight: 500;
                            }

                            .info-value {
                                flex: 1;
                                color: #303133;

                                &.amount {
                                    font-weight: 600;
                                    color: #f56c6c;
                                }

                                .el-progress {
                                    width: 100px;
                                    margin-right: 8px;
                                }

                                .probability-text {
                                    font-weight: 500;
                                }
                            }
                        }
                    }

                    .opportunity-description {
                        margin-bottom: 16px;
                        padding: 12px;
                        background: #f8f9fa;
                        border-radius: 6px;

                        p {
                            margin: 0;
                            font-size: 14px;
                            line-height: 1.6;
                            color: #606266;
                        }
                    }

                    .opportunity-metrics {
                        display: flex;
                        gap: 20px;

                        .metric-item {
                            display: flex;
                            align-items: center;
                            gap: 4px;
                            font-size: 12px;
                            color: #909399;

                            .el-icon {
                                font-size: 14px;
                            }
                        }
                    }
                }
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .customer-opportunity-tab {
        .opportunity-stats-section {
            .el-row {
                .el-col {
                    margin-bottom: 16px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }

        .opportunity-actions-section {
            .section-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;

                .header-actions {
                    justify-content: center;
                }
            }

            .filter-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        .opportunity-list-section {
            .opportunity-timeline {
                .opportunity-card {
                    .opportunity-header {
                        flex-direction: column;
                        gap: 12px;
                        align-items: stretch;

                        .opportunity-actions {
                            align-self: flex-end;
                        }
                    }

                    .opportunity-content {
                        .opportunity-metrics {
                            flex-wrap: wrap;
                            gap: 12px;
                        }
                    }
                }
            }
        }
    }
}
</style>