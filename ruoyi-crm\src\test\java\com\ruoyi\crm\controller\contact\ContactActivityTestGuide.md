# 联系人活动记录集成测试使用指南

## 概述

本测试用例基于前端 `ContactActivityTab.vue` 组件的功能，对后端联系人活动记录相关的接口进行集成测试。测试覆盖了前端所有的活动记录操作功能。

## 测试文件

- `ContactActivityIntegrationTestSimple.java` - 主要的集成测试文件

## 测试的接口

基于前端代码分析，测试覆盖以下后端接口：

### 1. 获取联系人活动记录列表
- **接口**: `GET /front/crm/contacts/{contactId}/activities`
- **对应前端方法**: `getContactActivities(contactId)`
- **测试方法**: `testGetContactActivities()`

### 2. 创建联系人活动记录
- **接口**: `POST /front/crm/contacts/activities`
- **对应前端方法**: `createContactActivity(activityData)`
- **测试方法**: `testCreateContactActivity()`

### 3. 编辑联系人活动记录
- **接口**: `PUT /front/crm/contacts/{contactId}/activities/{id}`
- **对应前端方法**: `updateContactActivity(contactId, activityId, activityData)`
- **测试方法**: `testUpdateContactActivity()`

### 4. 删除联系人活动记录
- **接口**: `DELETE /front/crm/contacts/{contactId}/activities/{id}`
- **对应前端方法**: `deleteContactActivity(contactId, activityId)`
- **测试方法**: `testDeleteContactActivity()`

### 5. 获取活动记录统计
- **接口**: `GET /front/crm/contacts/{contactId}/activities/stats`
- **测试方法**: `testGetContactActivityStats()`

## 测试的数据字段

测试涵盖前端使用的所有字段：

```javascript
// 前端数据结构
{
    activityType: "phone|email|meeting|visit|demo|other",
    content: "活动内容",
    activityTime: "活动时间",
    nextFollowTime: "下次联系时间",
    result: "interested|considering|no_need|interested_deep|refused",
    contactId: "联系人ID"
}
```

对应后端实体类字段：
```java
// 后端 CrmBusinessFollowUpRecords 实体
{
    relatedContactId: "联系人ID",
    activityType: "活动类型",
    content: "活动内容", 
    followUpContent: "跟进内容",
    followUpMethod: "跟进方式",
    activityTime: "活动时间",
    nextFollowTime: "下次跟进时间",
    result: "活动结果",
    contactResult: "联系结果"
}
```

## 测试用例说明

### 基础功能测试

1. **testGetContactActivities()** - 测试获取活动记录列表
   - 创建测试数据
   - 验证接口返回正确的记录数量和格式

2. **testCreateContactActivity()** - 测试创建活动记录
   - 测试所有必填字段
   - 验证创建成功响应

3. **testUpdateContactActivity()** - 测试编辑活动记录
   - 先创建记录，然后更新
   - 验证数据确实被更新

4. **testDeleteContactActivity()** - 测试删除活动记录
   - 先创建记录，然后删除
   - 验证记录确实被删除

5. **testGetContactActivityStats()** - 测试获取统计信息
   - 创建多条记录
   - 验证统计数据正确

### 边界条件测试

6. **testNonExistentContactId()** - 测试不存在的联系人ID
   - 使用不存在的联系人ID查询
   - 验证返回空列表

7. **testUpdateNonExistentActivity()** - 测试编辑不存在的活动记录
   - 尝试更新不存在的记录
   - 验证返回错误信息

8. **testAllActivityTypes()** - 测试所有活动类型
   - 测试前端支持的所有活动类型
   - 验证每种类型都能正确创建

### 综合测试

9. **testCompleteActivityWorkflow()** - 测试完整工作流程
   - 创建 → 查询 → 编辑 → 统计 → 删除
   - 验证整个生命周期

10. **testActivityDataValidation()** - 测试数据验证
    - 测试各种边界条件
    - 测试无效数据处理

11. **testConcurrentActivityCreation()** - 测试并发创建
    - 模拟并发创建多个记录
    - 验证并发安全性

## 运行测试

### 前置条件

1. 确保数据库连接正常
2. 确保相关服务类已正确注入
3. 确保测试环境配置正确

### 运行单个测试

```bash
# 运行特定测试方法
mvn test -Dtest=ContactActivityIntegrationTestSimple#testGetContactActivities

# 运行整个测试类
mvn test -Dtest=ContactActivityIntegrationTestSimple
```

### 运行所有测试

```bash
mvn test
```

### 在IDE中运行

- 右键点击测试类或测试方法
- 选择 "Run" 或 "Debug"

## 测试数据清理

测试使用了以下注解确保数据清理：

- `@Transactional` - 事务管理
- `@Rollback` - 自动回滚测试数据
- 每个测试方法都在独立事务中运行

## 预期结果

### 成功场景

- 所有基础CRUD操作都应该返回 `code: 200`
- 返回的数据格式应该符合前端期望
- 业务逻辑应该正确执行

### 错误场景

- 不存在的资源应该返回适当的错误码
- 无效数据应该被正确处理
- 错误信息应该清晰明了

## 故障排除

### 常见问题

1. **数据库连接问题**
   - 检查 `application-test.yml` 配置
   - 确保测试数据库可访问

2. **依赖注入失败**
   - 检查服务类是否正确配置
   - 确保 Spring Boot 测试配置正确

3. **权限问题**
   - 检查认证配置
   - 确保测试用户有足够权限

### 调试技巧

1. 使用 `@Slf4j` 添加日志
2. 在测试方法中添加断点
3. 查看测试输出的请求和响应内容

## 扩展测试

如需添加更多测试场景：

1. **性能测试** - 测试大量数据场景
2. **安全测试** - 测试权限控制
3. **业务规则测试** - 测试特定业务逻辑

## 注意事项

1. 测试数据会在每个测试方法执行后自动清理
2. 测试使用的联系人和活动记录都是临时创建的
3. 测试不会影响生产数据
4. 建议在代码变更后运行完整测试套件

## 测试覆盖范围

本测试套件覆盖了前端 `ContactActivityTab.vue` 组件的所有核心功能：

- ✅ 活动记录列表展示
- ✅ 新增活动记录
- ✅ 编辑活动记录  
- ✅ 删除活动记录
- ✅ 活动统计信息
- ✅ 所有活动类型 (phone, email, meeting, visit, demo, other)
- ✅ 所有联系结果 (interested, considering, no_need, interested_deep, refused)
- ✅ 边界条件和错误处理
- ✅ 完整工作流程验证
