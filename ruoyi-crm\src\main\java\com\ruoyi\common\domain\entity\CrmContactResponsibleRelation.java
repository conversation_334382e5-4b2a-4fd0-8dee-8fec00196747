package com.ruoyi.common.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;

/**
 * 联系人-业务员关系对象 crm_contact_responsible_relation
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class CrmContactResponsibleRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 联系人ID */
    @NotNull(message = "联系人ID不能为空")
    @Excel(name = "联系人ID")
    private Long contactId;

    /** 负责人ID(业务员) */
    @NotNull(message = "负责人ID不能为空")
    @Excel(name = "负责人ID")
    private Long responsiblePersonId;

    /** 业务类型 */
    @Excel(name = "业务类型", readConverterExp = "GENERAL=通用,PACKAGING=包装,PROTOTYPE=原型,3D_PRINTING=3D打印,MOLD=模具,DESIGN=设计")
    private String businessType;

    /** 关系状态 */
    @Excel(name = "关系状态", readConverterExp = "ACTIVE=活跃,INACTIVE=非活跃,TRANSFERRED=已转移")
    private String relationStatus;

    /** 开始负责时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始负责时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 结束负责时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束负责时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /** 所属团队ID */
    @Excel(name = "所属团队ID")
    private Long teamId;

    /** 分配方式 */
    @Excel(name = "分配方式", readConverterExp = "MANUAL=手动分配,TEAM_ASSIGN=团队分配,POOL_CLAIM=公海认领,MIGRATED=数据迁移")
    private String assignType;

    /** 分配人ID */
    @Excel(name = "分配人ID")
    private Long assignBy;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分配时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    // 关联查询字段
    /** 联系人姓名 */
    private String contactName;
    
    /** 负责人姓名 */
    private String responsiblePersonName;
    
    /** 团队名称 */
    private String teamName;
    
    /** 分配人姓名 */
    private String assignByName;
    
    /** 联系人手机号 */
    private String contactMobile;
    
    /** 联系人邮箱 */
    private String contactEmail;
    
    /** 联系人公司 */
    private String contactCompany;

    /**
     * 业务类型常量
     */
    public static class BusinessType {
        /** 通用 */
        public static final String GENERAL = "GENERAL";
        /** 包装 */
        public static final String PACKAGING = "PACKAGING";
        /** 原型 */
        public static final String PROTOTYPE = "PROTOTYPE";
        /** 3D打印 */
        public static final String PRINTING_3D = "3D_PRINTING";
        /** 模具 */
        public static final String MOLD = "MOLD";
        /** 设计 */
        public static final String DESIGN = "DESIGN";
    }

    /**
     * 关系状态常量
     */
    public static class RelationStatus {
        /** 活跃 */
        public static final String ACTIVE = "ACTIVE";
        /** 非活跃 */
        public static final String INACTIVE = "INACTIVE";
        /** 已转移 */
        public static final String TRANSFERRED = "TRANSFERRED";
    }

    /**
     * 分配方式常量
     */
    public static class AssignType {
        /** 手动分配 */
        public static final String MANUAL = "MANUAL";
        /** 团队分配 */
        public static final String TEAM_ASSIGN = "TEAM_ASSIGN";
        /** 公海认领 */
        public static final String POOL_CLAIM = "POOL_CLAIM";
        /** 数据迁移 */
        public static final String MIGRATED = "MIGRATED";
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getResponsiblePersonId() {
        return responsiblePersonId;
    }

    public void setResponsiblePersonId(Long responsiblePersonId) {
        this.responsiblePersonId = responsiblePersonId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getRelationStatus() {
        return relationStatus;
    }

    public void setRelationStatus(String relationStatus) {
        this.relationStatus = relationStatus;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getAssignType() {
        return assignType;
    }

    public void setAssignType(String assignType) {
        this.assignType = assignType;
    }

    public Long getAssignBy() {
        return assignBy;
    }

    public void setAssignBy(Long assignBy) {
        this.assignBy = assignBy;
    }

    public Date getAssignTime() {
        return assignTime;
    }

    public void setAssignTime(Date assignTime) {
        this.assignTime = assignTime;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getResponsiblePersonName() {
        return responsiblePersonName;
    }

    public void setResponsiblePersonName(String responsiblePersonName) {
        this.responsiblePersonName = responsiblePersonName;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getAssignByName() {
        return assignByName;
    }

    public void setAssignByName(String assignByName) {
        this.assignByName = assignByName;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getContactCompany() {
        return contactCompany;
    }

    public void setContactCompany(String contactCompany) {
        this.contactCompany = contactCompany;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("contactId", getContactId())
            .append("responsiblePersonId", getResponsiblePersonId())
            .append("businessType", getBusinessType())
            .append("relationStatus", getRelationStatus())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("teamId", getTeamId())
            .append("assignType", getAssignType())
            .append("assignBy", getAssignBy())
            .append("assignTime", getAssignTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    /**
     * 便捷方法：判断是否为活跃状态
     */
    public boolean isActive() {
        return RelationStatus.ACTIVE.equals(this.relationStatus);
    }

    /**
     * 便捷方法：判断是否为团队分配
     */
    public boolean isTeamAssigned() {
        return AssignType.TEAM_ASSIGN.equals(this.assignType);
    }

    /**
     * 便捷方法：判断是否为公海认领
     */
    public boolean isPoolClaimed() {
        return AssignType.POOL_CLAIM.equals(this.assignType);
    }

    /**
     * 便捷方法：获取业务类型中文名称
     */
    public String getBusinessTypeName() {
        switch (this.businessType) {
            case BusinessType.GENERAL: return "通用";
            case BusinessType.PACKAGING: return "包装";
            case BusinessType.PROTOTYPE: return "原型";
            case BusinessType.PRINTING_3D: return "3D打印";
            case BusinessType.MOLD: return "模具";
            case BusinessType.DESIGN: return "设计";
            default: return this.businessType;
        }
    }

    /**
     * 便捷方法：获取关系状态中文名称
     */
    public String getRelationStatusName() {
        switch (this.relationStatus) {
            case RelationStatus.ACTIVE: return "活跃";
            case RelationStatus.INACTIVE: return "非活跃";
            case RelationStatus.TRANSFERRED: return "已转移";
            default: return this.relationStatus;
        }
    }
}