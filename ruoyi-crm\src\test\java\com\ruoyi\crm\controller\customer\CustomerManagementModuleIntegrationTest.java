package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord;
import com.ruoyi.common.domain.entity.CrmVisitPlan;
import com.ruoyi.common.mapper.CrmCustomerFollowupRecordMapper;
import com.ruoyi.common.mapper.CrmCustomerMapper;
import com.ruoyi.common.mapper.CrmCustomerPoolMapper;
import com.ruoyi.common.mapper.CrmVisitPlanMapper;
import com.ruoyi.common.service.ICrmCustomerFollowupRecordService;
import com.ruoyi.common.service.ICrmCustomerService;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmCustomerPoolService;
import com.ruoyi.crm.service.ICrmVisitPlanService;

/**
 * 客户管理模块全面集成测试
 * 
 * 覆盖四个子模块：
 * 1. 客户管理 - CRUD、关注、分配等
 * 2. 公海管理 - 认领、放回、批量操作
 * 3. 跟进记录 - 增删改查、搜索、关联
 * 4. 拜访计划 - 状态管理、统计、提醒
 * 
 * 包含边界条件、异常场景、并发操作、性能测试
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CustomerManagementModuleIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmCustomerService customerService;

    @Autowired
    private ICrmCustomerPoolService customerPoolService;

    @Autowired
    private ICrmCustomerFollowupRecordService followupRecordService;

    @Autowired
    private ICrmVisitPlanService visitPlanService;

    @Autowired
    private CrmCustomerMapper customerMapper;

    @Autowired
    private CrmCustomerPoolMapper customerPoolMapper;

    @Autowired
    private CrmCustomerFollowupRecordMapper followupRecordMapper;

    @Autowired
    private CrmVisitPlanMapper visitPlanMapper;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    // 测试数据
    private CrmCustomer testCustomer1;
    private CrmCustomer testCustomer2;
    private CrmCustomerFollowupRecord testFollowupRecord;
    private CrmVisitPlan testVisitPlan;

    @BeforeEach
    void setUp() {
        super.setUpBase();
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
        
        // 初始化测试数据
        initTestData();
    }

    @AfterEach
    void tearDown() {
        super.tearDownBase();
    }

    // ==================== 客户管理模块测试 ====================

    @Test
    @Order(1)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("客户管理 - 查询客户列表")
    void testCustomerManagement_List() throws Exception {
        mockMvc.perform(get("/crm/customer/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @Order(2)
    @WithMockUser(username = "admin", authorities = {"crm:customer:add"})
    @DisplayName("客户管理 - 创建新客户")
    void testCustomerManagement_Create() throws Exception {
        CrmCustomer newCustomer = createTestCustomer("新建客户", "13900000001", "<EMAIL>");
        String requestBody = objectMapper.writeValueAsString(newCustomer);

        mockMvc.perform(post("/crm/customer")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(3)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("客户管理 - 更新客户信息")
    void testCustomerManagement_Update() throws Exception {
        testCustomer1.setCustomerName("更新后的客户名称");
        testCustomer1.setEmail("<EMAIL>");
        String requestBody = objectMapper.writeValueAsString(testCustomer1);

        mockMvc.perform(put("/crm/customer")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 公海管理模块测试 ====================

    @Test
    @Order(10)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("公海管理 - 查询公海客户")
    void testCustomerPool_GetPoolCustomers() throws Exception {
        // 先将客户放入公海
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "测试放入公海");

        mockMvc.perform(get("/crm/customer/pool")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @Order(11)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("公海管理 - 认领客户")
    void testCustomerPool_ClaimCustomer() throws Exception {
        // 确保客户在公海中
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "测试");

        mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("认领成功"));
    }

    @Test
    @Order(12)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("公海管理 - 放回公海")
    void testCustomerPool_ReturnToPool() throws Exception {
        mockMvc.perform(post("/crm/customer/{id}/return", testCustomer2.getId())
                .param("reason", "MANUAL")
                .param("remark", "测试放入公海")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("放入公海成功"));
    }

    @Test
    @Order(13)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("公海管理 - 批量认领")
    void testCustomerPool_BatchClaim() throws Exception {
        // 先将多个客户放入公海
        List<Long> customerIds = Arrays.asList(testCustomer1.getId(), testCustomer2.getId());
        customerPoolService.returnToPool(customerIds, "MANUAL", "批量测试");

        String requestBody = objectMapper.writeValueAsString(customerIds);
        mockMvc.perform(post("/crm/customer/batch/claim")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 跟进记录模块测试 ====================

    @Test
    @Order(20)
    @WithMockUser(username = "admin", authorities = {"crm:followup:list"})
    @DisplayName("跟进记录 - 查询跟进记录列表")
    void testFollowupRecord_List() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @Order(21)
    @WithMockUser(username = "admin", authorities = {"crm:followup:add"})
    @DisplayName("跟进记录 - 创建跟进记录")
    void testFollowupRecord_Create() throws Exception {
        CrmCustomerFollowupRecord newRecord = createTestFollowupRecord(testCustomer1.getId());
        String requestBody = objectMapper.writeValueAsString(newRecord);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(22)
    @WithMockUser(username = "admin", authorities = {"crm:followup:edit"})
    @DisplayName("跟进记录 - 更新跟进记录")
    void testFollowupRecord_Update() throws Exception {
        // 先创建跟进记录
        followupRecordMapper.insertCrmCustomerFollowupRecord(testFollowupRecord);

        testFollowupRecord.setFollowupContent("更新后的跟进标题：更新后的跟进内容");
        String requestBody = objectMapper.writeValueAsString(testFollowupRecord);

        mockMvc.perform(put("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(23)
    @WithMockUser(username = "admin", authorities = {"crm:followup:list"})
    @DisplayName("跟进记录 - 按客户ID查询")
    void testFollowupRecord_GetByCustomerId() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("customerId", testCustomer1.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    // ==================== 拜访计划模块测试 ====================

    @Test
    @Order(30)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:list"})
    @DisplayName("拜访计划 - 查询拜访计划列表")
    void testVisitPlan_List() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @Order(31)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:add"})
    @DisplayName("拜访计划 - 创建拜访计划")
    void testVisitPlan_Create() throws Exception {
        CrmVisitPlan newPlan = createTestVisitPlan("新建拜访计划", testCustomer1.getId());
        String requestBody = objectMapper.writeValueAsString(newPlan);

        mockMvc.perform(post("/crm/visitPlan")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(32)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:postpone"})
    @DisplayName("拜访计划 - 延期拜访")
    void testVisitPlan_Postpone() throws Exception {
        // 先创建拜访计划
        testVisitPlan.setStatus("PENDING");
        visitPlanMapper.insertCrmVisitPlan(testVisitPlan);

        Map<String, Object> params = new HashMap<>();
        params.put("reason", "客户临时有事");
        params.put("remark", "延期一天");
        params.put("newVisitTime", "2024-12-31 10:00:00");

        String requestBody = objectMapper.writeValueAsString(params);

        mockMvc.perform(post("/crm/visitPlan/postpone/{id}", testVisitPlan.getId())
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(33)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:statistics"})
    @DisplayName("拜访计划 - 获取统计信息")
    void testVisitPlan_Statistics() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/statistics")
                .param("dateRange", "2024-01-01,2024-12-31")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isMap());
    }

    // ==================== 跨模块业务流程测试 ====================

    @Test
    @Order(40)
    @WithMockUser(username = "admin", authorities = {"crm:customer:add", "crm:followup:add", "crm:visitPlan:add"})
    @DisplayName("业务流程 - 客户创建->跟进记录->拜访计划")
    void testBusinessFlow_CustomerToFollowupToVisit() throws Exception {
        // 1. 创建客户
        CrmCustomer newCustomer = createTestCustomer("流程测试客户", "***********", "<EMAIL>");
        String customerBody = objectMapper.writeValueAsString(newCustomer);

        String customerResponse = mockMvc.perform(post("/crm/customer")
                .content(customerBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn()
                .getResponse()
                .getContentAsString();

        // 2. 创建跟进记录
        CrmCustomerFollowupRecord followup = createTestFollowupRecord(testCustomer1.getId());
        followup.setFollowupContent("流程测试跟进");
        String followupBody = objectMapper.writeValueAsString(followup);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(followupBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 创建拜访计划
        CrmVisitPlan visitPlan = createTestVisitPlan("流程测试拜访", testCustomer1.getId());
        String visitBody = objectMapper.writeValueAsString(visitPlan);

        mockMvc.perform(post("/crm/visitPlan")
                .content(visitBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(41)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("业务流程 - 公海认领后创建跟进记录")
    void testBusinessFlow_ClaimThenFollowup() throws Exception {
        // 1. 将客户放入公海
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "流程测试");

        // 2. 认领客户
        mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 立即创建跟进记录
        CrmCustomerFollowupRecord followup = createTestFollowupRecord(testCustomer1.getId());
        followup.setFollowupContent("认领后跟进");
        String followupBody = objectMapper.writeValueAsString(followup);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(followupBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 边界条件和异常测试 ====================

    @Test
    @Order(50)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("边界测试 - 认领不存在的客户")
    void testBoundary_ClaimNonExistentCustomer() throws Exception {
        mockMvc.perform(post("/crm/customer/{id}/claim", 99999L)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @Order(51)
    @WithMockUser(username = "admin", authorities = {"crm:followup:add"})
    @DisplayName("边界测试 - 为不存在的客户创建跟进记录")
    void testBoundary_FollowupForNonExistentCustomer() throws Exception {
        CrmCustomerFollowupRecord record = createTestFollowupRecord(99999L);
        String requestBody = objectMapper.writeValueAsString(record);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @Order(52)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("边界测试 - 超大分页查询")
    void testBoundary_LargePagination() throws Exception {
        mockMvc.perform(get("/crm/customer/list")
                .param("pageNum", "999999")
                .param("pageSize", "1000")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isEmpty());
    }

    // ==================== 并发操作测试 ====================

    @Test
    @Order(60)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("并发测试 - 多用户同时认领同一客户")
    void testConcurrency_SimultaneousClaim() throws Exception {
        // 将客户放入公海
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "并发测试");

        ExecutorService executor = Executors.newFixedThreadPool(5);
        List<CompletableFuture<String>> futures = new ArrayList<>();

        // 5个并发请求同时认领同一客户
        for (int i = 0; i < 5; i++) {
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                            .contentType(MediaType.APPLICATION_JSON))
                            .andReturn()
                            .getResponse()
                            .getContentAsString();
                } catch (Exception e) {
                    return "error: " + e.getMessage();
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有请求完成
        List<String> results = new ArrayList<>();
        futures.forEach(future -> {
            try {
                results.add(future.get());
            } catch (Exception e) {
                results.add("exception: " + e.getMessage());
            }
        });

        // 验证只有一个请求成功
        long successCount = results.stream()
                .filter(result -> result.contains("\"code\":200"))
                .count();

        assertEquals(1, successCount, "并发认领同一客户，只应该有一个成功");
        executor.shutdown();
    }

    @Test
    @Order(61)
    @WithMockUser(username = "admin", authorities = {"crm:followup:add"})
    @DisplayName("并发测试 - 同时为同一客户创建多个跟进记录")
    void testConcurrency_SimultaneousFollowup() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 10个并发请求为同一客户创建跟进记录
        for (int i = 0; i < 10; i++) {
            final int index = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    CrmCustomerFollowupRecord record = createTestFollowupRecord(testCustomer1.getId());
                    record.setFollowupContent("并发跟进记录 " + index);
                    String requestBody = objectMapper.writeValueAsString(record);

                    mockMvc.perform(post("/front/crm/customer/followup")
                            .content(requestBody)
                            .contentType(MediaType.APPLICATION_JSON))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.code").value(200));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有操作完成
        assertDoesNotThrow(() -> 
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join()
        );
        executor.shutdown();
    }

    // ==================== 性能测试 ====================

    @Test
    @Order(70)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("性能测试 - 大量客户数据查询")
    void testPerformance_LargeCustomerQuery() throws Exception {
        // 创建大量测试客户数据
        createManyTestCustomers(500);

        long startTime = System.currentTimeMillis();

        mockMvc.perform(get("/crm/customer/list")
                .param("pageNum", "1")
                .param("pageSize", "100")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 3000, "大量客户数据查询应该在3秒内完成，实际用时: " + duration + "ms");
    }

    @Test
    @Order(71)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("性能测试 - 复杂条件查询")
    void testPerformance_ComplexQuery() throws Exception {
        long startTime = System.currentTimeMillis();

        mockMvc.perform(get("/crm/customer/list")
                .param("customerName", "测试")
                .param("customerIndustry", "测试行业")
                .param("customerLevel", "A级")
                .param("responsiblePersonId", "1")
                .param("pageNum", "1")
                .param("pageSize", "50")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 2000, "复杂条件查询应该在2秒内完成，实际用时: " + duration + "ms");
    }

    // ==================== 数据一致性测试 ====================

    @Test
    @Order(80)
    @WithMockUser(username = "admin", authorities = {"crm:customer:add", "crm:customer:list"})
    @DisplayName("数据一致性 - 创建后立即查询")
    void testDataConsistency_CreateAndQuery() throws Exception {
        CrmCustomer newCustomer = createTestCustomer("一致性测试客户", "13900000003", "<EMAIL>");
        String requestBody = objectMapper.writeValueAsString(newCustomer);

        // 创建客户
        mockMvc.perform(post("/crm/customer")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 立即查询验证
        mockMvc.perform(get("/crm/customer/list")
                .param("customerName", "一致性测试客户")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows[?(@.customerName == '一致性测试客户')]").exists());
    }

    // ==================== 辅助方法 ====================

    private void initTestData() {
        // 创建测试客户
        testCustomer1 = createTestCustomer("测试客户1", "13800138001", "<EMAIL>");
        testCustomer2 = createTestCustomer("测试客户2", "13800138002", "<EMAIL>");

        customerMapper.insertCrmCustomer(testCustomer1);
        customerMapper.insertCrmCustomer(testCustomer2);

        // 创建测试跟进记录
        testFollowupRecord = createTestFollowupRecord(testCustomer1.getId());

        // 创建测试拜访计划
        testVisitPlan = createTestVisitPlan("测试拜访计划", testCustomer1.getId());
    }

    private CrmCustomer createTestCustomer(String name, String mobile, String email) {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerName(name);
        customer.setMobile(mobile);
        customer.setEmail(email);
        customer.setResponsiblePersonId("1");
        customer.setCustomerIndustry("测试行业");
        customer.setCustomerLevel("A级");
        customer.setCustomerSource("测试来源");
        customer.setStatus("1");
        customer.setDelFlag("0");
        customer.setCreateTime(new Date());
        customer.setUpdateTime(new Date());
        return customer;
    }

    private CrmCustomerFollowupRecord createTestFollowupRecord(Long customerId) {
        CrmCustomerFollowupRecord record = new CrmCustomerFollowupRecord();
        record.setCustomerId(customerId);
        record.setFollowupType("call");
        record.setFollowupContent("测试跟进记录：这是一条测试跟进记录的内容");
        record.setCreatorId(1L);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        return record;
    }

    private CrmVisitPlan createTestVisitPlan(String planName, Long customerId) {
        CrmVisitPlan plan = new CrmVisitPlan();
        plan.setVisitPlanName(planName);
        plan.setCustomerId(customerId);
        plan.setCustomerName(testCustomer1.getCustomerName());
        plan.setVisitTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
        plan.setVisitPurpose("测试拜访目的");
        plan.setRemindTime(30);
        plan.setRemark("测试备注");
        plan.setOwnerId(1L);
        plan.setOwnerName("测试用户");
        plan.setDeptId(100L);
        plan.setDeptName("测试部门");
        plan.setStatus("PENDING");
        plan.setDelFlag("0");
        plan.setCreateTime(new Date());
        plan.setUpdateTime(new Date());
        return plan;
    }

    private void createManyTestCustomers(int count) {
        for (int i = 0; i < count; i++) {
            CrmCustomer customer = createTestCustomer("批量测试客户" + i, "138000000" + String.format("%02d", i), "batch" + i + "@test.com");
            customerMapper.insertCrmCustomer(customer);
        }
    }
}