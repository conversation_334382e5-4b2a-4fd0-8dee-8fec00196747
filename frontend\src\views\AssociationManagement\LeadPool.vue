<template>
  <div class="lead-pool-container">
    <!-- 头部统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ stats.totalCount || 0 }}</div>
              <div class="stats-label">总线索数</div>
            </div>
            <el-icon class="stats-icon"><Box /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card available">
            <div class="stats-content">
              <div class="stats-number">{{ stats.availableCount || 0 }}</div>
              <div class="stats-label">可用线索</div>
            </div>
            <el-icon class="stats-icon"><CircleCheck /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card assigned">
            <div class="stats-content">
              <div class="stats-number">{{ stats.assignedCount || 0 }}</div>
              <div class="stats-label">已分配</div>
            </div>
            <el-icon class="stats-icon"><User /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card quality">
            <div class="stats-content">
              <div class="stats-number">{{ getHighQualityCount() }}</div>
              <div class="stats-label">优质线索</div>
            </div>
            <el-icon class="stats-icon"><Star /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
        <el-form-item label="池状态" prop="poolStatus">
          <el-select v-model="queryParams.poolStatus" placeholder="请选择池状态" clearable>
            <el-option label="可用" value="available" />
            <el-option label="已分配" value="assigned" />
            <el-option label="锁定" value="locked" />
          </el-select>
        </el-form-item>
        <el-form-item label="质量等级" prop="qualityLevel">
          <el-select v-model="queryParams.qualityLevel" placeholder="请选择质量等级" clearable>
            <el-option label="A级-优质" value="A" />
            <el-option label="B级-良好" value="B" />
            <el-option label="C级-一般" value="C" />
            <el-option label="D级-较差" value="D" />
          </el-select>
        </el-form-item>
        <el-form-item label="地区" prop="region">
          <el-input v-model="queryParams.region" placeholder="请输入地区" clearable />
        </el-form-item>
        <el-form-item label="行业" prop="industry">
          <el-input v-model="queryParams.industry" placeholder="请输入行业" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="operation-card">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['crm:leadPool:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="User"
            :disabled="!multiple"
            @click="handleAssign"
            v-hasPermi="['crm:leadPool:assign']"
          >分配</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            :disabled="!multiple"
            @click="handleGrab"
            v-hasPermi="['crm:leadPool:grab']"
          >抢单</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="RefreshRight"
            :disabled="!multiple"
            @click="handleRecycle"
            v-hasPermi="['crm:leadPool:recycle']"
          >回收</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="!multiple"
            @click="handleDelete"
            v-hasPermi="['crm:leadPool:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['crm:leadPool:export']"
          >导出</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="leadPoolList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="线索名称" align="center" prop="leadName" min-width="120">
          <template #default="scope">
            <el-link type="primary" @click="handleDetail(scope.row)">
              {{ scope.row.leadName || '未关联线索' }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="客户名称" align="center" prop="customerName" min-width="120" />
        <el-table-column label="手机号码" align="center" prop="mobile" min-width="120" />
        <el-table-column label="池状态" align="center" prop="poolStatus" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.poolStatus)"
              disable-transitions
            >
              {{ getStatusText(scope.row.poolStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="质量等级" align="center" prop="qualityLevel" width="100">
          <template #default="scope">
            <el-tag
              :type="getQualityTagType(scope.row.qualityLevel)"
              disable-transitions
            >
              {{ scope.row.qualityLevel }}级
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="优先级" align="center" prop="priority" width="80">
          <template #default="scope">
            <el-rate
              v-model="scope.row.priority"
              :max="10"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>
        </el-table-column>
        <el-table-column label="地区" align="center" prop="region" width="100" />
        <el-table-column label="行业" align="center" prop="industry" width="100" />
        <el-table-column label="预估价值" align="center" prop="estimatedValue" width="120">
          <template #default="scope">
            <span v-if="scope.row.estimatedValue">
              ¥{{ Number(scope.row.estimatedValue).toLocaleString() }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="进入池时间" align="center" prop="enterPoolTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.enterPoolTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分配次数" align="center" prop="assignCount" width="100" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="View"
              @click="handleDetail(scope.row)"
              v-hasPermi="['crm:leadPool:query']"
            >详情</el-button>
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['crm:leadPool:edit']"
            >修改</el-button>
            <el-button
              v-if="scope.row.poolStatus === 'available'"
              link
              type="success"
              icon="User"
              @click="handleSingleAssign(scope.row)"
              v-hasPermi="['crm:leadPool:assign']"
            >分配</el-button>
            <el-button
              v-if="scope.row.poolStatus === 'available'"
              link
              type="warning"
              icon="Download"
              @click="handleSingleGrab(scope.row)"
              v-hasPermi="['crm:leadPool:grab']"
            >抢单</el-button>
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['crm:leadPool:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改线索池对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="leadPoolRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="关联线索ID" prop="leadId">
          <el-input v-model="form.leadId" placeholder="请输入关联线索ID（可为空）" />
        </el-form-item>
        <el-form-item label="质量等级" prop="qualityLevel">
          <el-select v-model="form.qualityLevel" placeholder="请选择质量等级">
            <el-option label="A级-优质" value="A" />
            <el-option label="B级-良好" value="B" />
            <el-option label="C级-一般" value="C" />
            <el-option label="D级-较差" value="D" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-slider v-model="form.priority" :min="1" :max="10" show-stops show-input />
        </el-form-item>
        <el-form-item label="地区" prop="region">
          <el-input v-model="form.region" placeholder="请输入地区" />
        </el-form-item>
        <el-form-item label="行业" prop="industry">
          <el-input v-model="form.industry" placeholder="请输入行业" />
        </el-form-item>
        <el-form-item label="预估价值" prop="estimatedValue">
          <el-input v-model="form.estimatedValue" placeholder="请输入预估价值" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Box, CircleCheck, User, Star, Search, Refresh, Plus, Download, RefreshRight, Delete, View, Edit } from '@element-plus/icons-vue';
import { parseTime } from '@/utils/ruoyi';
import {
  listLeadPool,
  getLeadPool,
  addLeadPool,
  updateLeadPool,
  deleteLeadPool,
  getLeadPoolStats,
  exportLeadPool,
  type LeadPool,
  type LeadPoolQuery
} from './api/leadPool';

// 响应式数据
const loading = ref(true);
const ids = ref<number[]>([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const leadPoolList = ref<LeadPool[]>([]);
const title = ref('');
const open = ref(false);
const stats = ref<any>({});

// 查询参数
const queryParams = reactive<LeadPoolQuery>({
  pageNum: 1,
  pageSize: 10,
  poolStatus: undefined,
  qualityLevel: undefined,
  region: undefined,
  industry: undefined
});

// 表单数据
const form = ref<LeadPool>({
  poolStatus: 'available',
  qualityLevel: 'C',
  priority: 5,
  sourceType: 'new',
  assignCount: 0
} as LeadPool);

// 表单校验
const rules = reactive({
  qualityLevel: [
    { required: true, message: '质量等级不能为空', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '优先级不能为空', trigger: 'blur' }
  ]
});

// 获取统计中的优质线索数量
const getHighQualityCount = () => {
  if (stats.value.qualityStats) {
    const aLevel = stats.value.qualityStats.find((item: any) => item.qualityLevel === 'A');
    return aLevel ? aLevel.assignCount : 0;
  }
  return 0;
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'available': return 'success';
    case 'assigned': return 'warning';
    case 'locked': return 'danger';
    default: return 'info';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'available': return '可用';
    case 'assigned': return '已分配';
    case 'locked': return '锁定';
    default: return '未知';
  }
};

// 获取质量等级标签类型
const getQualityTagType = (level: string) => {
  switch (level) {
    case 'A': return 'danger';
    case 'B': return 'warning';
    case 'C': return 'success';
    case 'D': return 'info';
    default: return 'info';
  }
};

// 查询线索池列表
const getList = async () => {
  loading.value = true;
  try {
    const response = await listLeadPool(queryParams);
    leadPoolList.value = response.rows || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取线索池列表失败:', error);
    ElMessage.error('获取线索池列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取统计信息
const getStats = async () => {
  try {
    const response = await getLeadPoolStats();
    stats.value = response.data || {};
  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  queryParams.poolStatus = undefined;
  queryParams.qualityLevel = undefined;
  queryParams.region = undefined;
  queryParams.industry = undefined;
  handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection: LeadPool[]) => {
  ids.value = selection.map(item => item.id!);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 行点击事件
const handleRowClick = (row: LeadPool) => {
  // 可以在这里添加行点击逻辑
};

// 新增按钮操作
const handleAdd = () => {
  reset();
  open.value = true;
  title.value = '添加线索池';
};

// 修改按钮操作
const handleUpdate = async (row?: LeadPool) => {
  reset();
  const leadPoolId = row?.id || ids.value[0];
  try {
    const response = await getLeadPool(leadPoolId);
    form.value = response.data;
    open.value = true;
    title.value = '修改线索池';
  } catch (error) {
    ElMessage.error('获取线索池信息失败');
  }
};

// 提交按钮
const submitForm = () => {
  // 这里需要实现表单提交逻辑
  ElMessage.success('功能开发中...');
};

// 取消按钮
const cancel = () => {
  open.value = false;
  reset();
};

// 表单重置
const reset = () => {
  form.value = {
    poolStatus: 'available',
    qualityLevel: 'C',
    priority: 5,
    sourceType: 'new',
    assignCount: 0
  } as LeadPool;
};

// 删除按钮操作
const handleDelete = (row?: LeadPool) => {
  ElMessage.success('删除功能开发中...');
};

// 分配按钮操作
const handleAssign = () => {
  ElMessage.success('分配功能开发中...');
};

// 单个分配操作
const handleSingleAssign = (row: LeadPool) => {
  ElMessage.success('单个分配功能开发中...');
};

// 抢单按钮操作
const handleGrab = () => {
  ElMessage.success('抢单功能开发中...');
};

// 单个抢单操作
const handleSingleGrab = (row: LeadPool) => {
  ElMessage.success('单个抢单功能开发中...');
};

// 回收按钮操作
const handleRecycle = () => {
  ElMessage.success('回收功能开发中...');
};

// 详情按钮操作
const handleDetail = (row: LeadPool) => {
  ElMessage.success('详情功能开发中...');
};

// 导出按钮操作
const handleExport = () => {
  ElMessage.success('导出功能开发中...');
};

// 初始化
onMounted(() => {
  getList();
  getStats();
});
</script>

<style scoped>
.lead-pool-container {
  padding: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card.available {
  border-left: 4px solid #67c23a;
}

.stats-card.assigned {
  border-left: 4px solid #e6a23c;
}

.stats-card.quality {
  border-left: 4px solid #f56c6c;
}

.stats-content {
  padding: 20px;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.stats-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #e4e7ed;
}

.filter-card,
.operation-card,
.table-card {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
