package com.ruoyi.crm.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.dto.CrmOrderAssignmentDTO;
import com.ruoyi.common.domain.dto.CrmOrderDTO;
import com.ruoyi.common.domain.dto.CrmOrderQueryDTO;
import com.ruoyi.common.domain.entity.CrmOrder;
import com.ruoyi.common.domain.entity.CrmOrderItem;
import com.ruoyi.common.service.ICrmCustomerMatchingService;
import com.ruoyi.common.service.ICrmOrderService;

import lombok.extern.slf4j.Slf4j;

/**
 * CRM订单服务测试类
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CrmOrderServiceTest {

    @Autowired
    private ICrmOrderService crmOrderService;

    @Autowired
    private ICrmCustomerMatchingService customerMatchingService;

    @Test
    public void testCreateOrder() {
        log.info("=== 测试创建订单 ===");
        
        // 创建订单DTO
        CrmOrderDTO orderDTO = new CrmOrderDTO();
        orderDTO.setOrderNo("TEST-" + System.currentTimeMillis());
        orderDTO.setCustomerName("测试客户");
        orderDTO.setContactPhone("13800138000");
        orderDTO.setOrderSource("3D_PRINTING");
        orderDTO.setOrderType("3D_PRINTING");
        orderDTO.setPriorityLevel("HIGH");
        orderDTO.setTotalAmount(new BigDecimal("5000.00"));
        orderDTO.setOrderDate(new Date());
        orderDTO.setRemarks("测试订单创建");
        
        // 创建订单
        int result = crmOrderService.createCrmOrder(orderDTO);
        log.info("创建订单结果: {}", result);
        
        assert result > 0 : "订单创建失败";
    }

    @Test
    public void testOrderQuery() {
        log.info("=== 测试订单查询 ===");
        
        // 创建查询条件
        CrmOrderQueryDTO queryDTO = new CrmOrderQueryDTO();
        queryDTO.setOrderSource("3D_PRINTING");
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        // 执行查询
        TableDataInfo result = crmOrderService.selectCrmOrderPage(queryDTO);
        log.info("查询结果: 总数={}, 当前页数据量={}", result.getTotal(), result.getRows().size());
        
        assert result != null : "查询结果不能为空";
        assert result.getCode() == 200 : "查询状态码应为200";
    }

    @Test
    public void testOrderAssignment() {
        log.info("=== 测试订单分配 ===");
        
        // 先创建一个测试订单
        CrmOrder order = createTestOrder();
        
        // 创建分配DTO
        CrmOrderAssignmentDTO assignmentDTO = new CrmOrderAssignmentDTO();
        assignmentDTO.setOrderId(order.getId());
        assignmentDTO.setToUserId(1L);
        assignmentDTO.setToUserName("管理员");
        assignmentDTO.setOperatorId(1L);
        assignmentDTO.setOperatorName("系统管理员");
        assignmentDTO.setReason("测试分配");
        assignmentDTO.setIpAddress("127.0.0.1");
        
        // 执行分配
        int result = crmOrderService.assignOrder(assignmentDTO);
        log.info("分配订单结果: {}", result);
        
        assert result > 0 : "订单分配失败";
        
        // 验证分配结果
        CrmOrder updatedOrder = crmOrderService.selectCrmOrderById(order.getId());
        assert updatedOrder.getOwnerId().equals(1L) : "订单负责人应为1";
        assert "ASSIGNED".equals(updatedOrder.getAssignmentStatus()) : "分配状态应为ASSIGNED";
    }

    @Test
    public void testOrderTransfer() {
        log.info("=== 测试订单转移 ===");
        
        // 先创建并分配一个订单
        CrmOrder order = createTestOrder();
        assignOrderToUser(order.getId(), 1L, "管理员");
        
        // 创建转移DTO
        CrmOrderAssignmentDTO transferDTO = new CrmOrderAssignmentDTO();
        transferDTO.setOrderId(order.getId());
        transferDTO.setFromUserId(1L);
        transferDTO.setFromUserName("管理员");
        transferDTO.setToUserId(2L);
        transferDTO.setToUserName("测试用户");
        transferDTO.setOperatorId(1L);
        transferDTO.setOperatorName("管理员");
        transferDTO.setReason("测试转移");
        
        // 执行转移
        int result = crmOrderService.transferOrder(transferDTO);
        log.info("转移订单结果: {}", result);
        
        assert result > 0 : "订单转移失败";
        
        // 验证转移结果
        CrmOrder updatedOrder = crmOrderService.selectCrmOrderById(order.getId());
        assert updatedOrder.getOwnerId().equals(2L) : "订单负责人应为2";
    }

    @Test
    public void testOrderGrab() {
        log.info("=== 测试订单抢单 ===");
        
        // 创建未分配的订单
        CrmOrder order = createTestOrder();
        
        // 执行抢单
        int result = crmOrderService.grabOrder(order.getId(), 1L);
        log.info("抢单结果: {}", result);
        
        assert result > 0 : "抢单失败";
        
        // 验证抢单结果
        CrmOrder updatedOrder = crmOrderService.selectCrmOrderById(order.getId());
        assert updatedOrder.getOwnerId().equals(1L) : "订单负责人应为1";
        assert "ASSIGNED".equals(updatedOrder.getAssignmentStatus()) : "分配状态应为ASSIGNED";
    }

    @Test
    public void testOrderReclaim() {
        log.info("=== 测试订单回收 ===");
        
        // 先创建并分配一个订单
        CrmOrder order = createTestOrder();
        assignOrderToUser(order.getId(), 1L, "管理员");
        
        // 执行回收
        int result = crmOrderService.reclaimOrder(order.getId(), 1L, "测试回收");
        log.info("回收订单结果: {}", result);
        
        assert result > 0 : "订单回收失败";
        
        // 验证回收结果
        CrmOrder updatedOrder = crmOrderService.selectCrmOrderById(order.getId());
        assert updatedOrder.getOwnerId() == null : "订单负责人应为空";
        assert "UNASSIGNED".equals(updatedOrder.getAssignmentStatus()) : "分配状态应为UNASSIGNED";
    }

    @Test
    public void testOrderStatusUpdate() {
        log.info("=== 测试订单状态更新 ===");
        
        // 创建测试订单
        CrmOrder order = createTestOrder();
        
        // 更新状态
        int result = crmOrderService.updateOrderStatus(order.getId(), "PROCESSING", 1L);
        log.info("更新状态结果: {}", result);
        
        assert result > 0 : "状态更新失败";
        
        // 验证状态更新
        CrmOrder updatedOrder = crmOrderService.selectCrmOrderById(order.getId());
        assert "PROCESSING".equals(updatedOrder.getStatus()) : "订单状态应为PROCESSING";
    }

    @Test
    public void testCustomerMatching() {
        log.info("=== 测试客户匹配 ===");
        
        // 测试电话号码匹配
        ICrmCustomerMatchingService.CustomerMatchResult phoneResult = 
                customerMatchingService.matchCustomerByPhone("13800138000");
        log.info("电话匹配结果: 是否匹配={}, 匹配类型={}", 
                phoneResult.isMatched(), phoneResult.getMatchType());
        
        // 测试客户名称匹配
        List<ICrmCustomerMatchingService.CustomerMatchResult> nameResults = 
                customerMatchingService.matchCustomerByName("测试客户");
        log.info("名称匹配结果数量: {}", nameResults.size());
        
        // 测试综合匹配
        ICrmCustomerMatchingService.CustomerMatchResult comprehensiveResult = 
                customerMatchingService.matchCustomerComprehensive("13800138000", "<EMAIL>", "测试客户");
        log.info("综合匹配结果: 是否匹配={}, 置信度={}", 
                comprehensiveResult.isMatched(), comprehensiveResult.getConfidence());
        
        // 测试新客户检查
        boolean isNewCustomer = customerMatchingService.isNewCustomer("13900139000", "<EMAIL>", "新客户");
        log.info("是否为新客户: {}", isNewCustomer);
        
        assert isNewCustomer : "应该识别为新客户";
    }

    @Test
    public void testPhoneNormalization() {
        log.info("=== 测试电话号码标准化 ===");
        
        String[] testPhones = {
            "13800138000",
            "+86 138 0013 8000",
            "86-138-0013-8000",
            "0138-0013-8000",
            "(138) 0013-8000"
        };
        
        for (String phone : testPhones) {
            String normalized = customerMatchingService.normalizePhone(phone);
            log.info("原始电话: {}, 标准化后: {}", phone, normalized);
            assert normalized.equals("13800138000") : "标准化结果应为13800138000";
        }
    }

    @Test
    public void testCustomerNameNormalization() {
        log.info("=== 测试客户名称标准化 ===");
        
        String[] testNames = {
            "北京测试科技有限公司",
            "上海测试集团",
            "深圳测试企业",
            "Test Company Ltd",
            "Example Corp"
        };
        
        for (String name : testNames) {
            String normalized = customerMatchingService.normalizeCustomerName(name);
            log.info("原始名称: {}, 标准化后: {}", name, normalized);
            assert normalized.length() > 0 : "标准化后的名称不能为空";
        }
    }

    @Test
    public void testAssignmentValidation() {
        log.info("=== 测试分配条件验证 ===");
        
        // 创建测试订单
        CrmOrder order = createTestOrder();
        
        // 测试分配条件
        boolean canAssign = crmOrderService.canAssignOrder(order.getId(), 1L);
        log.info("是否可以分配: {}", canAssign);
        
        boolean canGrab = crmOrderService.canGrabOrder(order.getId(), 1L);
        log.info("是否可以抢单: {}", canGrab);
        
        assert canAssign : "应该可以分配";
        assert canGrab : "应该可以抢单";
    }

    /**
     * 创建测试订单
     */
    private CrmOrder createTestOrder() {
        CrmOrder order = new CrmOrder();
        order.setOrderNo("TEST-" + System.currentTimeMillis());
        order.setCustomerName("测试客户");
        order.setContactPhone("13800138000");
        order.setOrderSource("3D_PRINTING");
        order.setOrderType("3D_PRINTING");
        order.setPriorityLevel("NORMAL");
        order.setAssignmentStatus("UNASSIGNED");
        order.setStatus("PENDING");
        order.setTotalAmount(new BigDecimal("1000.00"));
        order.setOrderDate(new Date());
        order.setCreateBy("test");
        
        // 添加订单项
        CrmOrderItem item = new CrmOrderItem();
        item.setProductName("测试产品");
        item.setQuantity(1);
        item.setUnitPrice(new BigDecimal("1000.00"));
        item.setSubtotal(new BigDecimal("1000.00"));
        
        if (order.getOrderItems() == null) {
            order.setOrderItems(new java.util.ArrayList<>());
        }
        order.getOrderItems().add(item);
        
        // 插入订单
        crmOrderService.insertCrmOrder(order);
        
        return order;
    }

    /**
     * 分配订单给用户
     */
    private void assignOrderToUser(Long orderId, Long userId, String userName) {
        CrmOrderAssignmentDTO assignmentDTO = new CrmOrderAssignmentDTO();
        assignmentDTO.setOrderId(orderId);
        assignmentDTO.setToUserId(userId);
        assignmentDTO.setToUserName(userName);
        assignmentDTO.setOperatorId(1L);
        assignmentDTO.setOperatorName("系统管理员");
        assignmentDTO.setReason("测试分配");
        
        crmOrderService.assignOrder(assignmentDTO);
    }
}
