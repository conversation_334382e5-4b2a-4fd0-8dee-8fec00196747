package com.ruoyi.common.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.ruoyi.common.domain.entity.CrmBusinessConversionLog;

/**
 * 业务转化日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Mapper
public interface CrmBusinessConversionLogMapper {
    
    /**
     * 查询业务转化日志
     * 
     * @param id 业务转化日志主键
     * @return 业务转化日志
     */
    public CrmBusinessConversionLog selectCrmBusinessConversionLogById(Long id);

    /**
     * 查询业务转化日志列表
     * 
     * @param crmBusinessConversionLog 业务转化日志
     * @return 业务转化日志集合
     */
    public List<CrmBusinessConversionLog> selectCrmBusinessConversionLogList(CrmBusinessConversionLog crmBusinessConversionLog);

    /**
     * 根据转化类型查询转化日志
     * 
     * @param conversionType 转化类型
     * @return 转化日志列表
     */
    public List<CrmBusinessConversionLog> selectCrmBusinessConversionLogByType(String conversionType);

    /**
     * 根据源实体查询转化日志
     * 
     * @param sourceType 源实体类型
     * @param sourceId 源实体ID
     * @return 转化日志列表
     */
    public List<CrmBusinessConversionLog> selectCrmBusinessConversionLogBySource(@Param("sourceType") String sourceType, 
                                                                                 @Param("sourceId") Long sourceId);

    /**
     * 根据目标实体查询转化日志
     * 
     * @param targetType 目标实体类型
     * @param targetId 目标实体ID
     * @return 转化日志列表
     */
    public List<CrmBusinessConversionLog> selectCrmBusinessConversionLogByTarget(@Param("targetType") String targetType, 
                                                                                 @Param("targetId") Long targetId);

    /**
     * 根据操作人查询转化日志
     * 
     * @param operatorId 操作人ID
     * @return 转化日志列表
     */
    public List<CrmBusinessConversionLog> selectCrmBusinessConversionLogByOperator(Long operatorId);

    /**
     * 查询转化成功率统计
     * 
     * @param conversionType 转化类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率统计
     */
    public List<CrmBusinessConversionLog> selectConversionSuccessRate(@Param("conversionType") String conversionType, 
                                                                      @Param("startTime") String startTime, 
                                                                      @Param("endTime") String endTime);

    /**
     * 查询转化金额统计
     * 
     * @param conversionType 转化类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 金额统计
     */
    public List<CrmBusinessConversionLog> selectConversionAmountStats(@Param("conversionType") String conversionType, 
                                                                      @Param("startTime") String startTime, 
                                                                      @Param("endTime") String endTime);

    /**
     * 查询用户转化统计
     * 
     * @param operatorId 操作人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户转化统计
     */
    public List<CrmBusinessConversionLog> selectUserConversionStats(@Param("operatorId") Long operatorId, 
                                                                    @Param("startTime") String startTime, 
                                                                    @Param("endTime") String endTime);

    /**
     * 查询转化漏斗数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 漏斗数据
     */
    public List<CrmBusinessConversionLog> selectConversionFunnelData(@Param("startTime") String startTime, 
                                                                     @Param("endTime") String endTime);

    /**
     * 新增业务转化日志
     * 
     * @param crmBusinessConversionLog 业务转化日志
     * @return 结果
     */
    public int insertCrmBusinessConversionLog(CrmBusinessConversionLog crmBusinessConversionLog);

    /**
     * 修改业务转化日志
     * 
     * @param crmBusinessConversionLog 业务转化日志
     * @return 结果
     */
    public int updateCrmBusinessConversionLog(CrmBusinessConversionLog crmBusinessConversionLog);

    /**
     * 删除业务转化日志
     * 
     * @param id 业务转化日志主键
     * @return 结果
     */
    public int deleteCrmBusinessConversionLogById(Long id);

    /**
     * 批量删除业务转化日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCrmBusinessConversionLogByIds(Long[] ids);

    /**
     * 根据源实体删除转化日志
     * 
     * @param sourceType 源实体类型
     * @param sourceId 源实体ID
     * @return 结果
     */
    public int deleteCrmBusinessConversionLogBySource(@Param("sourceType") String sourceType, 
                                                      @Param("sourceId") Long sourceId);

    /**
     * 批量插入转化日志
     * 
     * @param conversionLogs 转化日志列表
     * @return 结果
     */
    public int batchInsertCrmBusinessConversionLog(List<CrmBusinessConversionLog> conversionLogs);

    /**
     * 查询转化趋势数据
     * 
     * @param conversionType 转化类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组方式：DAY-按天,WEEK-按周,MONTH-按月
     * @return 趋势数据
     */
    public List<CrmBusinessConversionLog> selectConversionTrendData(@Param("conversionType") String conversionType, 
                                                                    @Param("startTime") String startTime, 
                                                                    @Param("endTime") String endTime, 
                                                                    @Param("groupBy") String groupBy);

    /**
     * 查询转化路径分析
     * 
     * @param sourceType 源实体类型
     * @param targetType 目标实体类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 路径分析数据
     */
    public List<CrmBusinessConversionLog> selectConversionPathAnalysis(@Param("sourceType") String sourceType, 
                                                                       @Param("targetType") String targetType, 
                                                                       @Param("startTime") String startTime, 
                                                                       @Param("endTime") String endTime);
}
