package com.ruoyi.crm.integration;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord;
import com.ruoyi.common.domain.entity.CrmLeadPool;
import com.ruoyi.common.mapper.CrmLeadAssignmentRecordMapper;
import com.ruoyi.common.mapper.CrmLeadPoolMapper;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmLeadAssignmentRecordService;
import com.ruoyi.crm.service.ICrmLeadPoolService;

/**
 * 线索池功能集成测试
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */

@AutoConfigureWebMvc
@DisplayName("线索池功能集成测试")
public class CrmLeadPoolIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmLeadPoolService leadPoolService;

    @Autowired
    private ICrmLeadAssignmentRecordService assignmentRecordService;

    @Autowired
    private CrmLeadPoolMapper leadPoolMapper;

    @Autowired
    private CrmLeadAssignmentRecordMapper assignmentRecordMapper;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 清理测试数据
        cleanupTestData();
        
        // 准备测试数据
        prepareTestData();
    }

    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        // 清理分配记录
        assignmentRecordMapper.deleteCrmLeadAssignmentRecordByIds(new Long[]{1L, 2L, 3L, 4L, 5L});
        
        // 清理线索池数据
        leadPoolMapper.deleteCrmLeadPoolByIds(new Long[]{1L, 2L, 3L, 4L, 5L});
    }

    /**
     * 准备测试数据
     */
    private void prepareTestData() {
        // 创建测试线索池数据
        CrmLeadPool pool1 = new CrmLeadPool();
        pool1.setLeadId(1001L);
        pool1.setPoolStatus("available");
        pool1.setQualityLevel("A");
        pool1.setPriority(8);
        pool1.setSourceType("new");
        pool1.setRegion("北京");
        pool1.setIndustry("IT");
        pool1.setEstimatedValue(new BigDecimal("50000"));
        pool1.setEnterPoolTime(new Date());
        pool1.setCreateBy("testUser");
        pool1.setCreateTime(new Date());
        leadPoolMapper.insertCrmLeadPool(pool1);

        CrmLeadPool pool2 = new CrmLeadPool();
        pool2.setLeadId(1002L);
        pool2.setPoolStatus("available");
        pool2.setQualityLevel("B");
        pool2.setPriority(6);
        pool2.setSourceType("imported");
        pool2.setRegion("上海");
        pool2.setIndustry("教育");
        pool2.setEstimatedValue(new BigDecimal("30000"));
        pool2.setEnterPoolTime(new Date());
        pool2.setCreateBy("testUser");
        pool2.setCreateTime(new Date());
        leadPoolMapper.insertCrmLeadPool(pool2);
    }

    @Test
    @DisplayName("测试查询线索池列表API")
    void testGetLeadPoolList() throws Exception {
        mockMvc.perform(get("/front/crm/leadPool/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @DisplayName("测试查询可用线索池列表API")
    void testGetAvailableLeadPoolList() throws Exception {
        mockMvc.perform(get("/front/crm/leadPool/available")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .param("qualityLevel", "A")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @DisplayName("测试新增线索池API")
    void testAddLeadPool() throws Exception {
        CrmLeadPool newPool = new CrmLeadPool();
        newPool.setLeadId(1003L);
        newPool.setQualityLevel("C");
        newPool.setPriority(5);
        newPool.setRegion("深圳");
        newPool.setIndustry("制造业");
        newPool.setEstimatedValue(new BigDecimal("25000"));
        newPool.setRemarks("测试新增线索池");

        mockMvc.perform(post("/front/crm/leadPool")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newPool)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证数据是否插入成功
        CrmLeadPool query = new CrmLeadPool();
        query.setLeadId(1003L);
        List<CrmLeadPool> pools = leadPoolService.selectCrmLeadPoolList(query);
        assertFalse(pools.isEmpty());
        assertEquals("深圳", pools.get(0).getRegion());
    }

    @Test
    @DisplayName("测试手动分配线索API")
    void testAssignLeads() throws Exception {
        // 准备分配请求数据
        String assignRequest = """
            {
                "poolIds": [1, 2],
                "toUserId": 100,
                "reason": "手动分配测试"
            }
            """;

        mockMvc.perform(post("/front/crm/leadPool/assign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(assignRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证分配结果
        CrmLeadPool pool = leadPoolService.selectCrmLeadPoolById(1L);
        assertEquals("assigned", pool.getPoolStatus());
        assertTrue(pool.getAssignCount() > 0);

        // 验证分配记录
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByToUserId(100L);
        assertFalse(records.isEmpty());
        assertEquals("manual", records.get(0).getAssignmentType());
    }

    @Test
    @DisplayName("测试批量分配线索API")
    void testBatchAssignLeads() throws Exception {
        String batchAssignRequest = """
            {
                "poolIds": [1, 2],
                "userIds": [101, 102],
                "reason": "批量分配测试"
            }
            """;

        mockMvc.perform(post("/front/crm/leadPool/batchAssign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(batchAssignRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证批量分配结果
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByAssignmentType("manual");
        assertTrue(records.size() >= 2);
    }

    @Test
    @DisplayName("测试抢单功能API")
    void testGrabLead() throws Exception {
        String grabRequest = """
            {
                "userId": 103,
                "reason": "抢单测试"
            }
            """;

        mockMvc.perform(post("/front/crm/leadPool/grab/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(grabRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证抢单结果
        CrmLeadPool pool = leadPoolService.selectCrmLeadPoolById(1L);
        assertEquals("assigned", pool.getPoolStatus());

        // 验证抢单记录
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByAssignmentType("grab");
        assertFalse(records.isEmpty());
        assertEquals(103L, records.get(0).getToUserId());
    }

    @Test
    @DisplayName("测试线索回收API")
    void testRecycleLeads() throws Exception {
        // 先分配一个线索
        leadPoolService.assignLeads(new Long[]{1L}, 104L, "测试分配");

        String recycleRequest = """
            {
                "leadIds": [1001],
                "reason": "回收测试"
            }
            """;

        mockMvc.perform(post("/front/crm/leadPool/recycle")
                .contentType(MediaType.APPLICATION_JSON)
                .content(recycleRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证回收结果
        CrmLeadPool pool = leadPoolService.getLeadPoolByLeadId(1001L);
        assertEquals("available", pool.getPoolStatus());
        assertEquals("recycled", pool.getSourceType());

        // 验证回收记录
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByAssignmentType("recycle");
        assertFalse(records.isEmpty());
    }

    @Test
    @DisplayName("测试获取统计信息API")
    void testGetLeadPoolStats() throws Exception {
        mockMvc.perform(get("/front/crm/leadPool/stats")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalCount").isNumber())
                .andExpect(jsonPath("$.data.availableCount").isNumber())
                .andExpect(jsonPath("$.data.statusStats").isArray())
                .andExpect(jsonPath("$.data.qualityStats").isArray());

        // 验证统计数据的正确性
        Map<String, Object> stats = leadPoolService.getLeadPoolStats();
        assertTrue((Integer) stats.get("totalCount") >= 2);
        assertTrue((Integer) stats.get("availableCount") >= 0);
    }

    @Test
    @DisplayName("测试按质量等级查询API")
    void testGetLeadPoolByQualityLevel() throws Exception {
        mockMvc.perform(get("/front/crm/leadPool/quality/A")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 验证业务逻辑
        List<CrmLeadPool> pools = leadPoolService.getLeadPoolByQualityLevel("A");
        for (CrmLeadPool pool : pools) {
            assertEquals("A", pool.getQualityLevel());
            assertEquals("available", pool.getPoolStatus());
        }
    }

    @Test
    @DisplayName("测试线索池服务层业务逻辑")
    void testLeadPoolServiceLogic() {
        // 测试添加线索到池中
        int result = leadPoolService.addToPool(1004L, "B", 7, "广州", "金融", "40000", "服务层测试");
        assertEquals(1, result);

        // 测试检查线索是否在池中
        boolean inPool = leadPoolService.isLeadInPool(1004L);
        assertTrue(inPool);

        // 测试更新池状态
        CrmLeadPool pool = leadPoolService.getLeadPoolByLeadId(1004L);
        int updateResult = leadPoolService.updatePoolStatus(pool.getId(), "locked");
        assertEquals(1, updateResult);

        // 验证状态更新
        pool = leadPoolService.selectCrmLeadPoolById(pool.getId());
        assertEquals("locked", pool.getPoolStatus());
    }

    @Test
    @DisplayName("测试分配记录服务层业务逻辑")
    void testAssignmentRecordServiceLogic() {
        // 创建手动分配记录
        int result = assignmentRecordService.createManualAssignmentRecord(1001L, 1L, 105L, "服务层测试分配");
        assertEquals(1, result);

        // 创建抢单记录
        result = assignmentRecordService.createGrabAssignmentRecord(1002L, 2L, 106L, "服务层测试抢单");
        assertEquals(1, result);

        // 创建回收记录
        result = assignmentRecordService.createRecycleRecord(1001L, 1L, 105L, "服务层测试回收");
        assertEquals(1, result);

        // 验证记录查询
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByLeadId(1001L);
        assertTrue(records.size() >= 2);

        // 验证统计功能
        Map<String, Object> stats = assignmentRecordService.getAssignmentRecordStats();
        assertTrue((Integer) stats.get("totalCount") >= 3);
    }

    @Test
    @DisplayName("测试数据完整性和约束")
    void testDataIntegrityAndConstraints() {
        // 测试必填字段验证
        CrmLeadPool invalidPool = new CrmLeadPool();
        // 不设置必填字段，应该抛出异常或返回错误
        
        // 测试状态转换逻辑
        CrmLeadPool pool = new CrmLeadPool();
        pool.setPoolStatus("available");
        assertTrue(pool.isAvailable());
        assertFalse(pool.isAssigned());
        assertFalse(pool.isLocked());

        // 测试状态设置方法
        pool.setAssigned();
        assertTrue(pool.isAssigned());
        assertNotNull(pool.getLastAssignTime());
        assertTrue(pool.getAssignCount() > 0);

        pool.setAvailable();
        assertTrue(pool.isAvailable());

        pool.setLocked();
        assertTrue(pool.isLocked());
    }

    @Test
    @DisplayName("测试并发分配场景")
    void testConcurrentAssignment() {
        // 模拟并发分配同一个线索
        Long poolId = 1L;
        
        // 第一次分配应该成功
        int result1 = leadPoolService.assignLeads(new Long[]{poolId}, 107L, "并发测试1");
        assertEquals(1, result1);
        
        // 第二次分配同一个线索应该失败（因为已经被分配）
        int result2 = leadPoolService.assignLeads(new Long[]{poolId}, 108L, "并发测试2");
        assertEquals(0, result2);
        
        // 验证线索状态
        CrmLeadPool pool = leadPoolService.selectCrmLeadPoolById(poolId);
        assertEquals("assigned", pool.getPoolStatus());
    }
}
