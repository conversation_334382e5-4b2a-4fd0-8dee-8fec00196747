package com.ruoyi.crm.service;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.domain.entity.CrmContactResponsibleRelation;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.crm.domain.vo.TeamAssignRequest;
import com.ruoyi.crm.domain.vo.PoolQueryRequest;
import com.ruoyi.crm.domain.vo.TeamPerformanceVO;

/**
 * 联系人-业务员关系Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface ICrmContactResponsibleRelationService {
    
    /**
     * 查询联系人-业务员关系
     * 
     * @param id 联系人-业务员关系主键
     * @return 联系人-业务员关系
     */
    CrmContactResponsibleRelation selectCrmContactResponsibleRelationById(Long id);
    
    /**
     * 查询联系人-业务员关系列表
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 联系人-业务员关系集合
     */
    List<CrmContactResponsibleRelation> selectCrmContactResponsibleRelationList(CrmContactResponsibleRelation crmContactResponsibleRelation);
    
    /**
     * 新增联系人-业务员关系
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 结果
     */
    int insertCrmContactResponsibleRelation(CrmContactResponsibleRelation crmContactResponsibleRelation);
    
    /**
     * 修改联系人-业务员关系
     * 
     * @param crmContactResponsibleRelation 联系人-业务员关系
     * @return 结果
     */
    int updateCrmContactResponsibleRelation(CrmContactResponsibleRelation crmContactResponsibleRelation);
    
    /**
     * 批量删除联系人-业务员关系
     * 
     * @param ids 需要删除的联系人-业务员关系主键集合
     * @return 结果
     */
    int deleteCrmContactResponsibleRelationByIds(Long[] ids);
    
    /**
     * 删除联系人-业务员关系信息
     * 
     * @param id 联系人-业务员关系主键
     * @return 结果
     */
    int deleteCrmContactResponsibleRelationById(Long id);
    
    /**
     * 团队负责人分配联系人给团队成员
     * 
     * @param request 分配请求
     * @return 成功分配的数量
     */
    int assignContactsToTeamMember(TeamAssignRequest request);
    
    /**
     * 从公海认领联系人
     * 
     * @param contactIds 联系人ID数组
     * @param businessType 业务类型
     * @return 成功认领的数量
     */
    int claimContactsFromPool(Long[] contactIds, String businessType);
    
    /**
     * 将联系人退回公海
     * 
     * @param contactIds 联系人ID数组
     * @param responsiblePersonId 负责人ID（可为空，默认当前用户）
     * @param reason 退回原因
     * @return 成功退回的关系数量
     */
    int returnContactsToPool(Long[] contactIds, Long responsiblePersonId, String reason);
    
    /**
     * 转移联系人负责人
     * 
     * @param contactIds 联系人ID数组
     * @param fromUserId 原负责人ID
     * @param toUserId 新负责人ID
     * @param businessType 业务类型
     * @param remark 转移备注
     * @return 成功转移的数量
     */
    int transferContacts(Long[] contactIds, Long fromUserId, Long toUserId, String businessType, String remark);
    
    /**
     * 查询公海联系人（多维度）
     * 
     * @param request 查询请求
     * @return 公海联系人列表
     */
    List<CrmContacts> selectPoolContacts(PoolQueryRequest request);
    
    /**
     * 获取团队业绩统计
     * 
     * @param teamId 团队ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 团队业绩统计
     */
    TeamPerformanceVO getTeamPerformance(Long teamId, Date startDate, Date endDate);
    
    /**
     * 获取个人负责的联系人（按业务类型）
     * 
     * @param userId 用户ID
     * @param businessType 业务类型（可为空）
     * @return 联系人关系列表
     */
    List<CrmContactResponsibleRelation> getMyContacts(Long userId, String businessType);
    
    /**
     * 批量更新团队信息
     * 
     * @param oldTeamId 原团队ID
     * @param newTeamId 新团队ID
     * @return 更新数量
     */
    int updateTeamInfo(Long oldTeamId, Long newTeamId);
    
    /**
     * 检查联系人在指定业务类型下是否有负责人
     * 
     * @param contactId 联系人ID
     * @param businessType 业务类型
     * @return 是否有负责人
     */
    boolean hasResponsiblePerson(Long contactId, String businessType);
    
    /**
     * 获取联系人在指定业务类型下的负责人
     * 
     * @param contactId 联系人ID
     * @param businessType 业务类型
     * @return 负责人关系信息
     */
    CrmContactResponsibleRelation getResponsiblePerson(Long contactId, String businessType);
    
    /**
     * 根据联系人ID查询所有活跃的负责人关系
     * 
     * @param contactId 联系人ID
     * @return 负责人关系列表
     */
    List<CrmContactResponsibleRelation> getActiveResponsiblePersons(Long contactId);
    
    /**
     * 批量查询联系人的负责人信息
     * 
     * @param contactIds 联系人ID列表
     * @param businessType 业务类型（可为空）
     * @return 负责人关系列表
     */
    List<CrmContactResponsibleRelation> getContactsResponsiblePersons(List<Long> contactIds, String businessType);
}