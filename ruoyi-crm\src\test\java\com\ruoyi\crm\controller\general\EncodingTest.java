package com.ruoyi.crm.controller;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;

import org.junit.jupiter.api.Test;

/**
 * 编码测试类 - 验证 Debug Console 中文显示
 */
public class EncodingTest {

    @Test
    public void testChineseOutput() {
        System.out.println("=== 编码测试开始 ===");
        System.out.println("当前默认字符集: " + Charset.defaultCharset());
        System.out.println("file.encoding: " + System.getProperty("file.encoding"));
        System.out.println("console.encoding: " + System.getProperty("console.encoding"));
        System.out.println("sun.stdout.encoding: " + System.getProperty("sun.stdout.encoding"));
        System.out.println("sun.stderr.encoding: " + System.getProperty("sun.stderr.encoding"));
        System.out.println("sun.jnu.encoding: " + System.getProperty("sun.jnu.encoding"));
        
        System.out.println("=== 中文测试 ===");
        
        // 方法1: 直接输出
        System.out.println("方法1-直接输出: 你好世界！");
        System.out.println("方法1-业务术语: 线索管理系统");
        System.out.println("方法1-特殊字符: 客户、联系人、商机");
        
        // 方法2: 使用字节数组强制 UTF-8 输出
        try {
            String[] testStrings = {
                "方法2-UTF8字节: 你好世界！",
                "方法2-业务术语: 线索管理系统",
                "方法2-特殊字符: 客户、联系人、商机",
                "方法2-表情符号: ✅ ❌ ⚠️ 💡"
            };
            
            for (String text : testStrings) {
                byte[] utf8Bytes = text.getBytes("UTF-8");
                System.out.write(utf8Bytes);
                System.out.println(); // 换行
            }
            System.out.flush();
        } catch (Exception e) {
            System.out.println("UTF-8字节输出失败: " + e.getMessage());
        }
        
        // 方法3: 使用 UTF-8 PrintStream
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PrintStream utf8Out = new PrintStream(baos, true, "UTF-8");
            utf8Out.println("方法3-PrintStream: 这是通过UTF-8 PrintStream输出的中文");
            utf8Out.println("方法3-表情符号: 😀 🎉 🚀");
            
            // 输出到控制台
            System.out.write(baos.toByteArray());
            System.out.flush();
        } catch (UnsupportedEncodingException e) {
            System.out.println("UTF-8 PrintStream 创建失败: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("PrintStream 输出失败: " + e.getMessage());
        }
        
        // 方法4: 测试简单英文（应该正常显示）
        System.out.println("Method4-English: Hello World! CRM Lead Management System");
        
        System.out.println("=== 编码测试结束 ===");
    }
    
    @Test
    public void testSystemProperties() {
        System.out.println("=== 系统属性测试 ===");
        System.out.println("os.name: " + System.getProperty("os.name"));
        System.out.println("user.language: " + System.getProperty("user.language"));
        System.out.println("user.country: " + System.getProperty("user.country"));
        System.out.println("user.timezone: " + System.getProperty("user.timezone"));
        System.out.println("java.version: " + System.getProperty("java.version"));
        System.out.println("java.vendor: " + System.getProperty("java.vendor"));
        
        // 测试字符集相关
        System.out.println("=== 字符集信息 ===");
        System.out.println("Default Charset: " + Charset.defaultCharset().name());
        System.out.println("Available Charsets: ");
        Charset.availableCharsets().keySet().stream()
            .filter(name -> name.contains("UTF") || name.contains("GBK") || name.contains("GB"))
            .forEach(name -> System.out.println("  - " + name));
            
        System.out.println("=== 系统属性测试结束 ===");
    }
    
    @Test
    public void testSimpleOutput() {
        // 最简单的测试，只输出基本中文
        System.out.println("简单测试: 中文");
        System.out.println("Simple test: Chinese characters");
        System.out.println("测试完成");
    }
} 