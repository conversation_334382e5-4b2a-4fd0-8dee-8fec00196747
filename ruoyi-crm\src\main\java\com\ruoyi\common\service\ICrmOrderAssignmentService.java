package com.ruoyi.common.service;

import java.util.List;

import com.ruoyi.common.domain.dto.CrmOrderAssignmentDTO;
import com.ruoyi.common.domain.entity.CrmOrderAssignmentLog;

/**
 * CRM订单分配服务接口
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
public interface ICrmOrderAssignmentService {
    
    /**
     * 自动分配订单
     * 
     * @param orderId 订单ID
     * @param assignmentStrategy 分配策略
     * @return 分配结果
     */
    public AssignmentResult autoAssignOrder(Long orderId, String assignmentStrategy);
    
    /**
     * 手动分配订单
     * 
     * @param assignmentDTO 分配信息
     * @return 分配结果
     */
    public AssignmentResult manualAssignOrder(CrmOrderAssignmentDTO assignmentDTO);
    
    /**
     * 批量分配订单
     * 
     * @param assignmentDTO 分配信息
     * @return 分配结果
     */
    public BatchAssignmentResult batchAssignOrders(CrmOrderAssignmentDTO assignmentDTO);
    
    /**
     * 转移订单
     * 
     * @param assignmentDTO 转移信息
     * @return 转移结果
     */
    public AssignmentResult transferOrder(CrmOrderAssignmentDTO assignmentDTO);
    
    /**
     * 抢单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param reason 抢单原因
     * @return 抢单结果
     */
    public AssignmentResult grabOrder(Long orderId, Long userId, String reason);
    
    /**
     * 回收订单到公海池
     * 
     * @param orderId 订单ID
     * @param operatorId 操作人ID
     * @param reason 回收原因
     * @return 回收结果
     */
    public AssignmentResult reclaimOrder(Long orderId, Long operatorId, String reason);
    
    /**
     * 智能分配订单（基于客户匹配）
     * 
     * @param orderId 订单ID
     * @param customerPhone 客户电话
     * @return 分配结果
     */
    public AssignmentResult smartAssignOrder(Long orderId, String customerPhone);
    
    /**
     * 获取可分配的用户列表
     * 
     * @param orderId 订单ID
     * @return 可分配用户列表
     */
    public List<AssignableUser> getAssignableUsers(Long orderId);
    
    /**
     * 获取用户工作量统计
     * 
     * @param userId 用户ID
     * @return 工作量统计
     */
    public WorkloadStatistics getUserWorkload(Long userId);
    
    /**
     * 获取部门工作量统计
     * 
     * @param deptId 部门ID
     * @return 工作量统计列表
     */
    public List<WorkloadStatistics> getDeptWorkload(Long deptId);
    
    /**
     * 检查分配条件
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param assignmentType 分配类型
     * @return 检查结果
     */
    public AssignmentValidation validateAssignment(Long orderId, Long userId, String assignmentType);
    
    /**
     * 获取分配历史
     * 
     * @param orderId 订单ID
     * @return 分配历史列表
     */
    public List<CrmOrderAssignmentLog> getAssignmentHistory(Long orderId);
    
    /**
     * 获取用户分配统计
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分配统计
     */
    public AssignmentStatistics getUserAssignmentStats(Long userId, String startDate, String endDate);
    
    /**
     * 获取分配效率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 效率统计
     */
    public List<AssignmentEfficiency> getAssignmentEfficiency(String startDate, String endDate);
    
    /**
     * 分配结果
     */
    public static class AssignmentResult {
        private boolean success;
        private String message;
        private Long orderId;
        private Long fromUserId;
        private Long toUserId;
        private String assignmentType;
        private Long assignmentLogId;
        
        public static AssignmentResult success(Long orderId, Long toUserId, String assignmentType) {
            AssignmentResult result = new AssignmentResult();
            result.setSuccess(true);
            result.setMessage("分配成功");
            result.setOrderId(orderId);
            result.setToUserId(toUserId);
            result.setAssignmentType(assignmentType);
            return result;
        }
        
        public static AssignmentResult failure(String message) {
            AssignmentResult result = new AssignmentResult();
            result.setSuccess(false);
            result.setMessage(message);
            return result;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public Long getOrderId() { return orderId; }
        public void setOrderId(Long orderId) { this.orderId = orderId; }
        
        public Long getFromUserId() { return fromUserId; }
        public void setFromUserId(Long fromUserId) { this.fromUserId = fromUserId; }
        
        public Long getToUserId() { return toUserId; }
        public void setToUserId(Long toUserId) { this.toUserId = toUserId; }
        
        public String getAssignmentType() { return assignmentType; }
        public void setAssignmentType(String assignmentType) { this.assignmentType = assignmentType; }
        
        public Long getAssignmentLogId() { return assignmentLogId; }
        public void setAssignmentLogId(Long assignmentLogId) { this.assignmentLogId = assignmentLogId; }
    }
    
    /**
     * 批量分配结果
     */
    public static class BatchAssignmentResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<AssignmentResult> results;
        private String summary;
        
        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        
        public List<AssignmentResult> getResults() { return results; }
        public void setResults(List<AssignmentResult> results) { this.results = results; }
        
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
    }
    
    /**
     * 可分配用户
     */
    public static class AssignableUser {
        private Long userId;
        private String userName;
        private String nickName;
        private String deptName;
        private boolean isOnline;
        private int currentWorkload;
        private int maxWorkload;
        private double workloadRate;
        private String status;
        private int todayAssignedCount;
        private String lastActiveTime;
        
        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getNickName() { return nickName; }
        public void setNickName(String nickName) { this.nickName = nickName; }
        
        public String getDeptName() { return deptName; }
        public void setDeptName(String deptName) { this.deptName = deptName; }
        
        public boolean isOnline() { return isOnline; }
        public void setOnline(boolean online) { isOnline = online; }
        
        public int getCurrentWorkload() { return currentWorkload; }
        public void setCurrentWorkload(int currentWorkload) { this.currentWorkload = currentWorkload; }
        
        public int getMaxWorkload() { return maxWorkload; }
        public void setMaxWorkload(int maxWorkload) { this.maxWorkload = maxWorkload; }
        
        public double getWorkloadRate() { return workloadRate; }
        public void setWorkloadRate(double workloadRate) { this.workloadRate = workloadRate; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public int getTodayAssignedCount() { return todayAssignedCount; }
        public void setTodayAssignedCount(int todayAssignedCount) { this.todayAssignedCount = todayAssignedCount; }
        
        public String getLastActiveTime() { return lastActiveTime; }
        public void setLastActiveTime(String lastActiveTime) { this.lastActiveTime = lastActiveTime; }
    }
    
    /**
     * 工作量统计
     */
    public static class WorkloadStatistics {
        private Long userId;
        private String userName;
        private int totalOrders;
        private int pendingOrders;
        private int processingOrders;
        private int completedOrders;
        private double completionRate;
        private double avgProcessTime;
        
        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public int getTotalOrders() { return totalOrders; }
        public void setTotalOrders(int totalOrders) { this.totalOrders = totalOrders; }
        
        public int getPendingOrders() { return pendingOrders; }
        public void setPendingOrders(int pendingOrders) { this.pendingOrders = pendingOrders; }
        
        public int getProcessingOrders() { return processingOrders; }
        public void setProcessingOrders(int processingOrders) { this.processingOrders = processingOrders; }
        
        public int getCompletedOrders() { return completedOrders; }
        public void setCompletedOrders(int completedOrders) { this.completedOrders = completedOrders; }
        
        public double getCompletionRate() { return completionRate; }
        public void setCompletionRate(double completionRate) { this.completionRate = completionRate; }
        
        public double getAvgProcessTime() { return avgProcessTime; }
        public void setAvgProcessTime(double avgProcessTime) { this.avgProcessTime = avgProcessTime; }
    }
    
    /**
     * 分配验证结果
     */
    public static class AssignmentValidation {
        private boolean valid;
        private String reason;
        private List<String> warnings;
        
        public static AssignmentValidation valid() {
            AssignmentValidation validation = new AssignmentValidation();
            validation.setValid(true);
            return validation;
        }
        
        public static AssignmentValidation invalid(String reason) {
            AssignmentValidation validation = new AssignmentValidation();
            validation.setValid(false);
            validation.setReason(reason);
            return validation;
        }
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings; }
    }
    
    /**
     * 分配统计
     */
    public static class AssignmentStatistics {
        private Long userId;
        private String userName;
        private int assignedCount;
        private int transferredCount;
        private int grabbedCount;
        private int reclaimedCount;
        private double avgResponseTime;
        
        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public int getAssignedCount() { return assignedCount; }
        public void setAssignedCount(int assignedCount) { this.assignedCount = assignedCount; }
        
        public int getTransferredCount() { return transferredCount; }
        public void setTransferredCount(int transferredCount) { this.transferredCount = transferredCount; }
        
        public int getGrabbedCount() { return grabbedCount; }
        public void setGrabbedCount(int grabbedCount) { this.grabbedCount = grabbedCount; }
        
        public int getReclaimedCount() { return reclaimedCount; }
        public void setReclaimedCount(int reclaimedCount) { this.reclaimedCount = reclaimedCount; }
        
        public double getAvgResponseTime() { return avgResponseTime; }
        public void setAvgResponseTime(double avgResponseTime) { this.avgResponseTime = avgResponseTime; }
    }
    
    /**
     * 分配效率
     */
    public static class AssignmentEfficiency {
        private String date;
        private int totalAssignments;
        private double avgAssignmentTime;
        private double successRate;
        private int autoAssignments;
        private int manualAssignments;
        
        // Getters and Setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        
        public int getTotalAssignments() { return totalAssignments; }
        public void setTotalAssignments(int totalAssignments) { this.totalAssignments = totalAssignments; }
        
        public double getAvgAssignmentTime() { return avgAssignmentTime; }
        public void setAvgAssignmentTime(double avgAssignmentTime) { this.avgAssignmentTime = avgAssignmentTime; }
        
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
        
        public int getAutoAssignments() { return autoAssignments; }
        public void setAutoAssignments(int autoAssignments) { this.autoAssignments = autoAssignments; }
        
        public int getManualAssignments() { return manualAssignments; }
        public void setManualAssignments(int manualAssignments) { this.manualAssignments = manualAssignments; }
    }
    
    // 分配策略常量
    public static final String STRATEGY_ROUND_ROBIN = "ROUND_ROBIN";
    public static final String STRATEGY_WORKLOAD_BASED = "WORKLOAD_BASED";
    public static final String STRATEGY_CUSTOMER_BASED = "CUSTOMER_BASED";
    public static final String STRATEGY_SKILL_BASED = "SKILL_BASED";
    public static final String STRATEGY_RANDOM = "RANDOM";
}
