package com.ruoyi.crm.controller;

import static com.ruoyi.crm.controller.TestAssertionHelper.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmContract;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmContractService;

/**
 * CrmContractController 集成测试类
 * 使用真实的数据库和完整的Spring上下文进行测试
 * 
 * <AUTHOR>
 * @date 2024-06-01
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("合同控制器集成测试")
class CrmContractControllerIntegrationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(CrmContractControllerIntegrationTest.class);
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmContractService contractService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .alwaysDo(print())
                .build();
    }

    private CrmContract createTestContract(String contractName, String contractNumber) {
        CrmContract contract = new CrmContract();
        contract.setManagerId(1L);
        contract.setContractNumber(contractNumber);
        contract.setContractName(contractName);
        contract.setCustomerName("测试客户");
        contract.setContractAmount(new BigDecimal("10000.00"));
        contract.setOrderDate(new Date());
        contract.setStartDate(new Date());
        contract.setEndDate(new Date(System.currentTimeMillis() + 86400000L));
        contract.setStatus("1");
        contract.setRemarks("集成测试合同");
        contract.setQuotationNumber("QT-001");
        contract.setOpportunityName("测试商机");
        contract.setCustomerSignatory("客户签约人");
        contract.setCompanySignatory("公司签约人");
        contract.setContractType("标准合同");
        contract.setProfit(new BigDecimal("1000.00"));
        contract.setTotalProductCost(new BigDecimal("9000.00"));
        contract.setDelFlag("0");
        contractService.insertContract(contract);
        assertNotNull(contract.getId(), "测试合同创建失败");
        return contract;
    }

    private void cleanupTestContract(Long contractId) {
        if (contractId != null) {
            try {
                contractService.deleteContractById(contractId);
            } catch (Exception e) {
                // 忽略清理错误
            }
        }
    }

    @Nested
    @DisplayName("合同CRUD集成测试")
    class CrudIntegrationTests {
        @Test
        @DisplayName("完整的CRUD流程测试")
        void testFullCrudFlow() throws Exception {
            // 1. 创建合同
            CrmContract newContract = new CrmContract();
            newContract.setManagerId(1L);
            newContract.setContractNumber("HT-CRUD-001");
            newContract.setContractName("CRUD测试合同");
            newContract.setCustomerName("测试客户");
            newContract.setContractAmount(new BigDecimal("8888.88"));
            newContract.setOrderDate(new Date());
            newContract.setStartDate(new Date());
            newContract.setEndDate(new Date(System.currentTimeMillis() + 86400000L));
            newContract.setStatus("1");
            newContract.setRemarks("CRUD测试合同");
            newContract.setQuotationNumber("QT-CRUD-001");
            newContract.setOpportunityName("测试商机");
            newContract.setCustomerSignatory("客户签约人");
            newContract.setCompanySignatory("公司签约人");
            newContract.setContractType("标准合同");
            newContract.setProfit(new BigDecimal("1000.00"));
            newContract.setTotalProductCost(new BigDecimal("7888.88"));
            newContract.setDelFlag("0");

            MvcResult createResult = mockMvc.perform(post("/front/crm/contracts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newContract)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdContractId = rootNode.path("data").path("id").asLong();
            assertNotNull(createdContractId, "创建合同后应返回ID");

            try {
                logger.warn("查询合同新创建的id: " + createdContractId);
                // 2. 查询单个合同
                MvcResult queryResult = mockMvc.perform(get("/front/crm/contracts/{id}", createdContractId))
                        .andExpect(status().isOk())
                        .andReturn();
                String queryJson = queryResult.getResponse().getContentAsString();
                logger.warn("查询合同返回内容: " + queryJson);
                assertTrue(queryJson.contains("CRUD测试合同"), "返回内容中应包含合同名称");
                // 保留原有断言
                assertEquals(200, objectMapper.readTree(queryJson).path("code").asInt());
                assertEquals("CRUD测试合同", objectMapper.readTree(queryJson).path("data").path("contractName").asText());

                // 3. 修改合同
                newContract.setId(createdContractId);
                newContract.setContractName("修改后的合同名称");
                newContract.setRemarks("已修改");

                mockMvc.perform(put("/front/crm/contracts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newContract)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证修改结果
                mockMvc.perform(get("/front/crm/contracts/{id}", createdContractId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.data.contractName").value("修改后的合同名称"))
                        .andExpect(jsonPath("$.data.remarks").value("已修改"));
            } finally {
                // 5. 删除合同
                mockMvc.perform(delete("/front/crm/contracts/{ids}", createdContractId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));
                // 6. 验证删除结果
                CrmContract deleted = contractService.selectContractById(createdContractId);
                assertNull(deleted, "删除后查询应为null");
            }
        }


        // 查询合同列表
        @Test
        @DisplayName("查询合同列表 - 带分页和筛选")
        void testGetContractListWithFilters() throws Exception {
            CrmContract testContract = createTestContract("列表测试合同", "HT-LIST-001");
            Long testContractId = testContract.getId();
            try {
                MvcResult result = mockMvc.perform(get("/front/crm/contracts/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .param("contractName", "列表测试"))
                        .andExpect(status().isOk())
                        .andReturn();
                String responseContent = result.getResponse().getContentAsString();
                TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
                assertNotNull(response);
                assertEquals(200, response.getCode());
                assertNotNull(response.getRows());
                assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
            } finally {
                cleanupTestContract(testContractId);
            }
        }
    }

    @Nested
    @DisplayName("导出功能集成测试")
    class ExportIntegrationTests {
        @Test
        @DisplayName("导出合同数据")
        void testExportContracts() throws Exception {
            CrmContract queryContract = new CrmContract();
            queryContract.setStatus("1");
            MvcResult result = mockMvc.perform(get("/front/crm/contracts/export")
                    .param("status", "1"))
                    .andExpect(status().isOk())
                    .andReturn();
            String responseContent = result.getResponse().getContentAsString();
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            assertNotNull(response);
            assertEquals(200, (Integer) response.get("code"));
        }
    }
    
    @Nested
    @DisplayName("搜索功能集成测试")
    class SearchIntegrationTests {
        private Long testContractId;
        
        @BeforeEach
        void setUp() {
            CrmContract testContract = createTestContract("搜索测试合同", "HT-SEARCH-001");
            testContractId = testContract.getId();
        }
        
        @AfterEach
        void tearDown() {
            cleanupTestContract(testContractId);
        }
        
        @Test
        @DisplayName("按合同名称搜索")
        void testSearchByContractName() throws Exception {
            MvcResult result = mockMvc.perform(get("/front/crm/contracts/list")
                    .param("contractName", "搜索测试"))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
        }
        
        @Test
        @DisplayName("按客户名称搜索")
        void testSearchByCustomerName() throws Exception {
            MvcResult result = mockMvc.perform(get("/front/crm/contracts/list")
                    .param("customerName", "测试客户"))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
        }
        
        @Test
        @DisplayName("按合同编号搜索")
        void testSearchByContractNumber() throws Exception {
            MvcResult result = mockMvc.perform(get("/front/crm/contracts/list")
                    .param("contractNumber", "HT-SEARCH"))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
        }
    }
    
    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {
        @Test
        @DisplayName("查询不存在的合同")
        void testGetNonExistentContract() throws Exception {
            Long nonExistentId = 99999L; // 假设这个ID不存在
            
            MvcResult result = mockMvc.perform(get("/front/crm/contracts/{id}", nonExistentId))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(responseContent);
            
            // 验证返回的数据为空或错误信息
            assertTrue(rootNode.path("data").isNull() || rootNode.path("data").isEmpty(), 
                    "不存在的合同应返回空数据");
        }
        
        @Test
        @DisplayName("删除不存在的合同")
        void testDeleteNonExistentContract() throws Exception {
            Long nonExistentId = 99999L; // 假设这个ID不存在
            
            MvcResult result = mockMvc.perform(delete("/front/crm/contracts/{ids}", nonExistentId))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            // 验证操作是否成功完成（即使没有实际删除任何记录）
            assertEquals(200, (Integer) response.get("code"), "删除不存在的记录应该返回成功状态");
        }
        
        @Test
        @DisplayName("使用无效数据创建合同")
        void testCreateContractWithInvalidData() throws Exception {
            CrmContract invalidContract = new CrmContract();
            // 故意不设置必要字段
            
            MvcResult result = mockMvc.perform(post("/front/crm/contracts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidContract)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(responseContent);
            
            // 验证是否返回错误信息
            // 注意：具体的错误代码和消息可能需要根据实际应用的错误处理逻辑进行调整
            if (rootNode.has("code") && rootNode.path("code").asInt() != 200) {
                // 如果返回错误代码
                assertNotEquals(200, rootNode.path("code").asInt(), "无效数据应该返回错误状态");
            } else {
                // 如果返回成功代码，但操作可能在服务层失败
                // 这种情况下，可能需要检查其他指标来确定操作是否真的失败
                logger.warn("创建无效合同返回了成功状态码，但操作可能在服务层失败");
            }
        }
    }
}