<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终架构设计 - 去业务线简化版</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- Prism.js for code highlighting -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #27ae60;
            border-bottom: 3px solid #27ae60;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .insight-box {
            background-color: #e8f5e8;
            border: 2px solid #27ae60;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .solution-box {
            background-color: #f0f8ff;
            border: 2px solid #4169e1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .comparison-box {
            background-color: #fff8dc;
            border: 2px solid #ffd700;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .mermaid-diagram {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        .code-section {
            margin: 20px 0;
        }
        .code-title {
            background-color: #495057;
            color: white;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin: 0;
            font-weight: bold;
            font-size: 14px;
        }
        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0 0 5px 5px !important;
            font-size: 13px !important;
        }
        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .scenario-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .scenario-card h4 {
            color: #007bff;
            margin-top: 0;
        }
        .feature-list {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .feature-item {
            margin: 10px 0;
            padding: 10px;
            background-color: white;
            border-left: 3px solid #28a745;
            border-radius: 3px;
        }
        .old-vs-new {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .old-design {
            background-color: #fee;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e74c3c;
        }
        .new-design {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 最终架构设计 - 去业务线简化版</h1>
        
        <div class="insight-box">
            <h3>💡 设计理念</h3>
            <p><strong>核心思想：</strong>保持客户和联系人的双主体设计，去除业务线的僵化约束，实现最大的业务灵活性。</p>
            <ul>
                <li><strong>客户表：</strong>统计分析和业务单据的主体</li>
                <li><strong>联系人表：</strong>实际业务对接和沟通的主体</li>
                <li><strong>关系表：</strong>维护组织归属，支持统计汇总</li>
                <li><strong>负责人：</strong>在联系人级别灵活分配，不受客户约束</li>
            </ul>
        </div>

        <h2>📊 三种业务场景的统一处理</h2>

        <div class="scenario-grid">
            <div class="scenario-card">
                <h4>🏢 大公司场景</h4>
                <p><strong>A公司玩具制造商</strong></p>
                <ul>
                    <li>客户：A公司（统计主体）</li>
                    <li>联系人1：王五-研发部 → 张三负责</li>
                    <li>联系人2：赵六-市场部 → 李四负责</li>
                </ul>
                <p><strong>优势：</strong>客户表用于报表统计，联系人表用于具体业务对接</p>
            </div>

            <div class="scenario-card">
                <h4>🏬 小公司场景</h4>
                <p><strong>B公司小工厂</strong></p>
                <ul>
                    <li>客户：B公司</li>
                    <li>联系人：老板张总 → 李四负责</li>
                </ul>
                <p><strong>优势：</strong>保持业务一致性，不需要特殊处理逻辑</p>
            </div>

            <div class="scenario-card">
                <h4>👤 个人客户场景</h4>
                <p><strong>个人发明家</strong></p>
                <ul>
                    <li>客户：王先生（业务主体）</li>
                    <li>联系人：王先生本人 → 张三负责</li>
                </ul>
                <p><strong>优势：</strong>业务单据可选客户或联系人，保持流程统一</p>
            </div>
        </div>

        <h2>🏗️ 最终架构设计</h2>

        <div class="mermaid-diagram">
            <div class="mermaid">
erDiagram
    CrmCustomer {
        Long id PK
        String customerName
        String customerType "企业/个人"
        String customerIndustry
        String customerLevel
        String status
        String delFlag
    }
    
    CrmCustomerResponsible {
        Long id PK
        Long customerId FK
        Long responsiblePersonId FK
        String responsibleType "PRIMARY/SECONDARY/COOPERATE"
        Date startDate
        Date endDate
        String status "ACTIVE/INACTIVE"
        Integer priority "优先级"
        String remark
    }
    
    CrmContacts {
        Long id PK
        Long responsiblePersonId FK "直接设置负责人"
        String name
        String department
        String position
        String mobile
        String email
        String status
        String delFlag
    }
    
    CrmCustomerContactRelation {
        Long id PK
        Long customerId FK
        Long contactId FK
        String relationType "主要联系人/决策人/财务联系人"
        Integer isPrimary "是否主联系人"
        Date startDate
        Date endDate
        String status
    }
    
    CrmPoolOperationLog {
        Long id PK
        String operationType "CLAIM/RETURN"
        Long targetId FK "客户ID或联系人ID"
        String targetType "CUSTOMER/CONTACT"
        Long operatorId FK
        String reason
        String remark
        Date operationTime
    }
    
    CrmCustomer ||--o{ CrmCustomerResponsible : "has multiple responsible"
    CrmCustomer ||--o{ CrmCustomerContactRelation : "has contacts"
    CrmContacts ||--o{ CrmCustomerContactRelation : "belongs to"
    CrmContacts ||--|| CrmContacts : "has responsible"
            </div>
        </div>

        <h2>🆚 设计对比：去除业务线的优势</h2>

        <div class="old-vs-new">
            <div class="old-design">
                <h4>❌ 业务线约束版</h4>
                <ul>
                    <li>联系人必须对应某个业务线</li>
                    <li>负责人必须是该业务线的负责人</li>
                    <li>业务线需要预定义和维护</li>
                    <li>跨业务线协作复杂</li>
                    <li>个人客户也要强制分业务线</li>
                </ul>
            </div>
            <div class="new-design">
                <h4>✅ 灵活负责版</h4>
                <ul>
                    <li>联系人可以有任意负责人</li>
                    <li>负责人分配完全灵活</li>
                    <li>不需要维护业务线分类</li>
                    <li>自然支持跨领域协作</li>
                    <li>所有场景统一处理</li>
                </ul>
            </div>
        </div>

        <h2>📋 核心表结构设计</h2>

        <div class="code-section">
            <div class="code-title">1. 客户负责人关系表 (crm_customer_responsible)</div>
            <pre><code class="language-sql">CREATE TABLE crm_customer_responsible (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    responsible_person_id BIGINT NOT NULL COMMENT '负责人ID',
    responsible_type VARCHAR(20) DEFAULT 'PRIMARY' COMMENT '负责类型:PRIMARY主要/SECONDARY次要/COOPERATE协作',
    start_date DATETIME NOT NULL COMMENT '负责开始时间',
    end_date DATETIME COMMENT '负责结束时间(NULL表示当前负责)',
    status VARCHAR(10) DEFAULT 'ACTIVE' COMMENT '状态:ACTIVE有效/INACTIVE无效',
    priority INT DEFAULT 1 COMMENT '优先级(数字越小优先级越高)',
    remark VARCHAR(500) COMMENT '备注说明',
    create_time DATETIME NOT NULL,
    create_by VARCHAR(64),
    update_time DATETIME,
    update_by VARCHAR(64),
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_responsible_person_id (responsible_person_id),
    INDEX idx_status_date (status, start_date, end_date),
    
    UNIQUE KEY uk_customer_responsible_active (customer_id, responsible_person_id, status)
);</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">2. 联系人表增加负责人字段 (crm_contacts)</div>
            <pre><code class="language-sql">-- 联系人直接设置负责人，不受客户负责人约束
ALTER TABLE crm_contacts 
ADD COLUMN responsible_person_id BIGINT COMMENT '负责人ID',
ADD INDEX idx_responsible_person (responsible_person_id);

-- 如果需要，也可以添加负责人变更历史
CREATE TABLE crm_contact_responsible_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    contact_id BIGINT NOT NULL COMMENT '联系人ID',
    old_responsible_id BIGINT COMMENT '原负责人ID',
    new_responsible_id BIGINT COMMENT '新负责人ID',
    change_reason VARCHAR(100) COMMENT '变更原因',
    change_time DATETIME NOT NULL COMMENT '变更时间',
    change_by VARCHAR(64) COMMENT '变更人'
);</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">3. 统一操作日志表 (crm_pool_operation_log)</div>
            <pre><code class="language-sql">CREATE TABLE crm_pool_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型:CLAIM/RETURN/TRANSFER',
    target_id BIGINT NOT NULL COMMENT '目标ID(客户ID或联系人ID)',
    target_type VARCHAR(10) NOT NULL COMMENT '目标类型:CUSTOMER/CONTACT',
    target_name VARCHAR(100) COMMENT '目标名称',
    operator_id BIGINT NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    old_responsible_id BIGINT COMMENT '原负责人ID',
    new_responsible_id BIGINT COMMENT '新负责人ID',
    reason VARCHAR(100) COMMENT '操作原因',
    remark VARCHAR(500) COMMENT '备注',
    operation_time DATETIME NOT NULL COMMENT '操作时间',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    
    INDEX idx_target (target_type, target_id),
    INDEX idx_operator (operator_id),
    INDEX idx_operation_time (operation_time)
);</code></pre>
        </div>

        <h2>💼 核心业务逻辑实现</h2>

        <div class="code-section">
            <div class="code-title">客户认领逻辑 - 支持多负责人</div>
            <pre><code class="language-java">@Override
@Transactional
public int claimCustomers(List&lt;Long&gt; customerIds) {
    // 1. 参数验证
    if (customerIds == null || customerIds.isEmpty()) {
        throw new ServiceException("请选择要认领的客户");
    }
    
    Long userId = SecurityUtils.getUserId();
    
    // 2. 检查认领限制
    if (!checkClaimLimit(userId, customerIds.size())) {
        throw new ServiceException("超出认领限制");
    }
    
    // 3. 验证客户状态 - 检查是否已有该用户作为负责人
    List&lt;Long&gt; alreadyResponsibleCustomers = customerResponsibleMapper
        .selectCustomersByResponsiblePerson(userId, customerIds);
    if (!alreadyResponsibleCustomers.isEmpty()) {
        throw new ServiceException("部分客户您已经是负责人，请重新选择");
    }
    
    // 4. 创建负责人关系
    Date now = new Date();
    List&lt;CrmCustomerResponsible&gt; responsibleList = new ArrayList&lt;&gt;();
    
    for (Long customerId : customerIds) {
        CrmCustomerResponsible responsible = new CrmCustomerResponsible();
        responsible.setCustomerId(customerId);
        responsible.setResponsiblePersonId(userId);
        responsible.setResponsibleType("PRIMARY"); // 新认领的都是主要负责人
        responsible.setStartDate(now);
        responsible.setStatus("ACTIVE");
        responsible.setPriority(1);
        responsibleList.add(responsible);
    }
    
    // 5. 批量插入负责人关系
    int result = customerResponsibleMapper.batchInsert(responsibleList);
    
    // 6. 记录操作日志
    recordOperationLog("CLAIM", "CUSTOMER", customerIds, null, userId, "客户认领");
    
    return result;
}</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">联系人认领逻辑 - 直接分配</div>
            <pre><code class="language-java">@Override
@Transactional
public int claimContacts(List&lt;Long&gt; contactIds) {
    // 1. 参数验证
    if (contactIds == null || contactIds.isEmpty()) {
        throw new ServiceException("请选择要认领的联系人");
    }
    
    Long userId = SecurityUtils.getUserId();
    
    // 2. 检查认领限制
    if (!checkClaimLimit(userId, contactIds.size())) {
        throw new ServiceException("超出认领限制");
    }
    
    // 3. 查询联系人当前状态
    List&lt;CrmContacts&gt; contacts = contactMapper.selectByIds(contactIds);
    List&lt;Long&gt; alreadyOwnedContacts = contacts.stream()
        .filter(contact -&gt; userId.equals(contact.getResponsiblePersonId()))
        .map(CrmContacts::getId)
        .collect(Collectors.toList());
    
    if (!alreadyOwnedContacts.isEmpty()) {
        throw new ServiceException("部分联系人您已经是负责人，请重新选择");
    }
    
    // 4. 记录变更历史（如果需要）
    recordContactResponsibleHistory(contacts, userId);
    
    // 5. 批量更新联系人负责人
    int result = contactMapper.batchUpdateResponsiblePerson(contactIds, userId);
    
    // 6. 记录操作日志
    recordOperationLog("CLAIM", "CONTACT", contactIds, null, userId, "联系人认领");
    
    return result;
}</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">公海查询逻辑 - 统一处理</div>
            <pre><code class="language-java">/**
 * 获取公海客户（无任何负责人的客户）
 */
@Override
public List&lt;CrmCustomerPool&gt; selectPoolCustomers(CrmCustomerPool query) {
    return customerPoolMapper.selectCustomersWithoutResponsible(query);
}

/**
 * 获取公海联系人（无负责人的联系人）
 */
@Override
public List&lt;CrmContactPool&gt; selectPoolContacts(CrmContactPool query) {
    return contactPoolMapper.selectContactsWithoutResponsible(query);
}

/**
 * 获取我负责的客户
 */
@Override
public List&lt;CrmCustomer&gt; selectMyCustomers(Long userId) {
    return customerResponsibleMapper.selectCustomersByResponsiblePerson(userId);
}

/**
 * 获取我负责的联系人
 */
@Override
public List&lt;CrmContacts&gt; selectMyContacts(Long userId) {
    CrmContacts query = new CrmContacts();
    query.setResponsiblePersonId(userId);
    return contactMapper.selectList(query);
}</code></pre>
        </div>

        <h2>🔄 数据迁移策略</h2>

        <div class="comparison-box">
            <h4>📊 现有数据平滑迁移</h4>
            <ol>
                <li><strong>客户负责人迁移：</strong>将客户表的单一负责人转换为客户负责人关系记录</li>
                <li><strong>联系人负责人继承：</strong>从关联客户继承负责人，或设置为独立负责人</li>
                <li><strong>保持现有关系：</strong>客户-联系人关系表不变，继续维护组织归属</li>
                <li><strong>历史数据完整性：</strong>确保所有现有业务单据的关联关系不受影响</li>
            </ol>
        </div>

        <div class="code-section">
            <div class="code-title">数据迁移SQL脚本</div>
            <pre><code class="language-sql">-- 1. 迁移客户负责人关系
INSERT INTO crm_customer_responsible (
    customer_id, responsible_person_id, responsible_type, 
    start_date, status, priority, create_time
)
SELECT 
    id, responsible_person_id, 'PRIMARY',
    COALESCE(create_time, NOW()), 'ACTIVE', 1, NOW()
FROM crm_customer 
WHERE responsible_person_id IS NOT NULL
AND responsible_person_id != ''
AND id NOT IN (SELECT customer_id FROM crm_customer_responsible);

-- 2. 迁移联系人负责人（从客户继承）
UPDATE crm_contacts c
INNER JOIN crm_customer_contact_relation ccr ON c.id = ccr.contact_id
INNER JOIN crm_customer cust ON ccr.customer_id = cust.id
SET c.responsible_person_id = cust.responsible_person_id
WHERE c.responsible_person_id IS NULL
AND cust.responsible_person_id IS NOT NULL;

-- 3. 清空客户表的负责人字段（可选，保留用于兼容性）
-- ALTER TABLE crm_customer DROP COLUMN responsible_person_id;</code></pre>
        </div>

        <h2>✨ 架构优势总结</h2>

        <div class="feature-list">
            <div class="feature-item">
                <strong>🎯 业务灵活性：</strong>去除业务线约束，支持任意的负责人分配组合
            </div>
            <div class="feature-item">
                <strong>📊 统计完整性：</strong>客户表保留，支持按客户维度的报表和分析
            </div>
            <div class="feature-item">
                <strong>🤝 协作便利性：</strong>同一客户的多个负责人可以自然协作
            </div>
            <div class="feature-item">
                <strong>🔄 场景统一性：</strong>大公司、小公司、个人客户统一处理逻辑
            </div>
            <div class="feature-item">
                <strong>📋 单据灵活性：</strong>业务单据可以选择客户或直接选择联系人
            </div>
            <div class="feature-item">
                <strong>📈 扩展性：</strong>未来可以轻松添加新的负责人类型和关系
            </div>
        </div>

        <h2>🚀 实施计划</h2>

        <div class="solution-box">
            <h4>📅 7-8天实施计划</h4>
            <ul>
                <li><strong>第1天：</strong>创建客户负责人关系表和相关索引</li>
                <li><strong>第2天：</strong>修改联系人表，添加负责人字段</li>
                <li><strong>第3天：</strong>数据迁移，将现有数据转换到新架构</li>
                <li><strong>第4-5天：</strong>重写业务逻辑，实现多负责人认领和查询</li>
                <li><strong>第6天：</strong>修复所有集成测试，确保功能正常</li>
                <li><strong>第7-8天：</strong>完善操作日志和异常处理，性能优化</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #e8f5e8; border-radius: 5px;">
            <h3>✅ 最终确认</h3>
            <p><strong>架构方案：</strong>多负责人 + 去业务线 + 双主体保留</p>
            <p style="color: #27ae60; font-weight: bold;">这个设计既解决了多负责人问题，又保持了最大的业务灵活性！</p>
            <p><strong>🚀 准备开始实施了吗？</strong></p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#333333',
                fill: '#ececff',
                fontSize: 12
            }
        });

        // Auto highlight code blocks
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>