package com.ruoyi.common.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.ruoyi.common.domain.dto.CustomerSearchDTO;
import com.ruoyi.common.domain.entity.CrmCustomer;

/**
 * 客户Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Mapper
public interface CrmCustomerMapper {

    /**
     * 通过ID查询客户
     * 
     * @param id 客户ID
     * @return 客户信息
     */
    CrmCustomer selectCrmCustomerById(@Param("id") Long id);

    /**
     * 查询客户列表
     * 
     * @param crmCustomer 客户信息
     * @return 客户列表
     */
    List<CrmCustomer> selectList(CrmCustomer crmCustomer);

    /**
     * 新增客户
     * 
     * @param crmCustomer 客户信息
     * @return 结果
     */ 
    int insertCrmCustomer(CrmCustomer crmCustomer);

    /**
     * 修改客户
     * 
     * @param crmCustomer 客户信息
     * @return 结果
     */
    int updateCrmCustomer(CrmCustomer crmCustomer);

    /**
     * 删除客户
     *  
     * @param id 客户ID
     * @return 结果
     */
    int deleteCrmCustomerById(@Param("id") Long id);

    /**
     * 批量删除客户
     * 
     * @param ids 需要删除的客户ID数组
     * @return 结果
     */
    int deleteCrmCustomerByIds(Long[] ids);

    /**
     * 搜索客户
     *
     * @param searchDTO 搜索条件
     * @return 客户列表
     */
    List<CrmCustomer> searchCustomers(CustomerSearchDTO searchDTO); // 假设这是一个搜索方法的声明

    /**
     * 根据电话号码查找客户
     *
     * @param phone 电话号码
     * @return 客户信息
     */
    CrmCustomer findByPhone(@Param("phone") String phone);

    /**
     * 根据标准化电话号码查找客户
     *
     * @param normalizedPhone 标准化电话号码
     * @return 客户信息
     */
    CrmCustomer findByNormalizedPhone(@Param("normalizedPhone") String normalizedPhone);

    /**
     * 根据电话号码模糊查找客户
     *
     * @param phonePattern 电话号码模式
     * @return 客户信息
     */
    CrmCustomer findByPhoneLike(@Param("phonePattern") String phonePattern);

    /**
     * 根据客户名称查找客户
     *
     * @param customerName 客户名称
     * @return 客户列表
     */
    List<CrmCustomer> findByCustomerName(@Param("customerName") String customerName);

    /**
     * 根据客户名称模糊查找客户
     *
     * @param customerNamePattern 客户名称模式
     * @return 客户列表
     */
    List<CrmCustomer> findByCustomerNameLike(@Param("customerNamePattern") String customerNamePattern);

    /**
     * 根据邮箱查找客户
     *
     * @param email 邮箱
     * @return 客户信息
     */
    CrmCustomer findByEmail(@Param("email") String email);
}