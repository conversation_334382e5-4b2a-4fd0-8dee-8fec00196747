import request from '@/utils/request'
import type { CustomerFormData } from '@/views/ThreeDPrintingQuote/components/CustomerInfoForm.vue'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 3D打印订单创建请求接口
export interface ThreeDPrintingOrderCreateRequest {
  quoteNo: string
  customerInfo: CustomerFormData
  items: OrderItem[]
  totalAmount: number
  sprayOptions?: string[]
  insertOptions?: string[]
  uploadedFiles?: { originalName: string, ossUrl: string }[]
}

// 订单项目接口
export interface OrderItem {
  modelName: string
  modelInfo: {
    dimensions: string
    volume: string
    surfaceArea: string
  }
  material: string
  materialId: string
  quantity: number
  unitPrice: number
  totalPrice: number
  processOptions?: string[]
}

// 订单创建结果接口
export interface OrderCreateResult {
  orderNo: string
  orderId: string
  customerId: string
  isNewCustomer: boolean
  contactId?: string
  isNewContact: boolean
  opportunityId: string
  message: string
}

/**
 * 创建3D打印订单并自动集成CRM（后端事务性处理）
 * 一次API调用完成：客户创建/检查、联系人创建、商机创建、订单创建
 */
export function createOrderWithCRMIntegration(data: ThreeDPrintingOrderCreateRequest): Promise<ApiResponse<OrderCreateResult>> {
  return request({
    url: '/front/crm/3d-printing/create-order',
    method: 'post',
    data
  })
}

/**
 * 检查客户是否存在（通过手机号）
 */
export function checkCustomerExists(mobile: string): Promise<ApiResponse<{ exists: boolean, customerId?: string }>> {
  return request({
    url: '/front/crm/3d-printing/check-customer',
    method: 'get',
    params: { mobile }
  })
}

/**
 * 检查联系人是否存在（通过客户ID和手机号）
 */
export function checkContactExists(customerId: string, mobile: string): Promise<ApiResponse<{ exists: boolean, contactId?: string }>> {
  return request({
    url: '/front/crm/3d-printing/check-contact',
    method: 'get',
    params: { customerId, mobile }
  })
}

export default {
  createOrderWithCRMIntegration,
  checkCustomerExists,
  checkContactExists
}