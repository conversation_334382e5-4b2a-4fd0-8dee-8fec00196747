// 联系人相关数据类型定义

// 联系人活动记录
export interface ContactActivity {
    id: number;
    contactId: number;
    moduleType?: string; // 所属模块（customer:客户,lead:线索,opportunity:商机,contract:合同,contact:联系人）
    activityType: 'phone' | 'email' | 'meeting' | 'visit' | 'demo' | 'other';
    content: string;
    activityTime: string;
    participants?: string;
    result?: string;
    nextFollowTime?: string;
    createBy: string;
    createTime: string;
}

// 联系人附件
export interface ContactAttachment {
    id: number;
    contactId: number;
    fileName: string;
    fileSize: number;
    fileType: string;
    filePath: string;
    uploadBy: string;
    uploadTime: string;
}

// 联系人操作记录
export interface ContactOperation {
    id: number;
    contactId: number;
    operationType: 'create' | 'update' | 'delete' | 'follow' | 'unfollow';
    operationDesc: string;
    operationBy: string;
    operationTime: string;
    // 扩展字段，匹配实际需求
    operationContent?: string;
    operationDescription?: string;
    operatorName?: string;
    operatorId?: number;
    changeDetails?: string;
    ipAddress?: string;
    userAgent?: string;
}

// 用户选项（用于筛选）
export interface UserOption {
    value: number | string;
    label: string;
}

// 客户信息
export interface CustomerInfo {
    id: number;
    name: string;
    industry?: string;
    scale?: string;
    address?: string;
    website?: string;
    description?: string;
}

// 用户信息（用于负责人选择）
export interface UserInfo {
    id: number;
    userName: string;
    nickName: string;
    email?: string;
    phonenumber?: string;
    dept?: {
        deptName: string;
    };
}

// 活动类型选项
export const ACTIVITY_TYPES = {
    phone: '电话联系',
    email: '邮件联系',
    meeting: '会议讨论',
    visit: '上门拜访',
    demo: '方案演示',
    other: '其他'
} as const;

// 操作类型选项 - 修复为数组格式，支持 .find() 方法
export const OPERATION_TYPES = [
    { value: 'create', label: '创建' },
    { value: 'update', label: '更新' },
    { value: 'delete', label: '删除' },
    { value: 'follow', label: '关注' },
    { value: 'unfollow', label: '取消关注' },
    { value: 'add_followup', label: '添加跟进记录' },
    { value: 'update_followup', label: '更新跟进记录' },
    { value: 'delete_followup', label: '删除跟进记录' },
    { value: 'add_visit_plan', label: '创建拜访计划' },
    { value: 'update_visit_plan', label: '更新拜访计划' },
    { value: 'delete_visit_plan', label: '删除拜访计划' },
    { value: 'postpone_visit_plan', label: '延期拜访计划' },
    { value: 'cancel_visit_plan', label: '取消拜访计划' },
    { value: 'complete_visit_plan', label: '完成拜访计划' },
    { value: 'add_call_record', label: '添加通话记录' },
    { value: 'update_call_record', label: '更新通话记录' },
    { value: 'delete_call_record', label: '删除通话记录' },
    { value: 'link_customer', label: '关联客户' },
    { value: 'unlink_customer', label: '取消关联客户' },
    { value: 'set_primary_customer', label: '设置主要客户' }
] as const;