<template>
    <el-container class="customer-management">
        <!-- 使用新的导航组件 -->
        <side-nav
            v-model="activeTab"
            :title="navConfig.title"
            :menu-items="navConfig.menuItems"
        />

        <!-- 主内容区域 -->
        <el-container class="main-container">
            <el-header class="header">
                <h1>客户管理</h1>
                <div class="header-actions">
                    <el-button 
                        type="success" 
                        plain
                        size="small"
                        :disabled="selectedCustomers.length === 0"
                        @click="handleBatchFollow"
                        class="action-btn"
                    >
                        <el-icon><Star /></el-icon>
                        批量关注
                    </el-button>
                    <el-button 
                        type="warning" 
                        plain
                        size="small"
                        :disabled="selectedCustomers.length === 0"
                        @click="handleBatchUnfollow"
                        class="action-btn"
                    >
                        <el-icon><StarFilled /></el-icon>
                        取消关注
                    </el-button>
                    <el-button 
                        type="info" 
                        plain
                        size="small"
                        :disabled="selectedCustomers.length === 0"
                        @click="handleBatchAssign"
                        class="action-btn"
                    >
                        <el-icon><Share /></el-icon>
                        批量分配
                    </el-button>
                    <el-button 
                        type="primary" 
                        size="small"
                        @click="openCustomerDialog"
                        class="action-btn primary-btn"
                    >
                        <el-icon><Plus /></el-icon>
                        新建客户
                    </el-button>
                </div>
            </el-header>

            <el-main>
                <!-- 根据activeTab显示不同内容 -->
                <div v-show="activeTab === 'customers'" class="customer-list-container">
                    <!-- 使用统一的筛选组件 -->
                    <common-filter
                        v-model:searchValue="searchInput"
                        v-model:filterValue="filterType"
                        :config="filterConfig"
                        @search="handleSearch"
                        @filter="handleFilterChange"
                    />

                    <!-- 数据表格 -->
                    <el-table 
                        ref="customerTable" 
                        :data="customers" 
                        v-loading="loading"
                        border 
                        sortable 
                        tooltip-effect="dark"
                        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                        style="width: 100%; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1); height: 450px;"
                        @selection-change="handleSelectionChange">
                        
                        <el-table-column type="selection" width="55" />
                        
                        <el-table-column prop="customerName" label="客户名称" min-width="150">
                            <template #default="scope">
                                <el-button link type="primary" class="link-button" @click="openDrawer(scope.row)">
                                    {{ scope.row.customerName }}
                                </el-button>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="mobile" label="手机号" min-width="130" />
                        
                        <el-table-column prop="phone" label="电话" min-width="130" />
                        
                        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
                        
                        <el-table-column prop="customerIndustry" label="所属行业" min-width="120" />
                        
                        <el-table-column prop="customerLevel" label="客户级别" min-width="120" />
                        
                        <el-table-column prop="customerSource" label="客户来源" min-width="120" />
                        
                        <el-table-column prop="status" label="状态" min-width="100">
                            <template #default="scope">
                                <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
                                    {{ scope.row.status === '1' ? '正常' : '禁用' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="isFollowing" label="关注状态" min-width="120">
                            <template #default="scope">
                                <el-tag :type="scope.row.isFollowing ? 'success' : 'info'" size="small">
                                    <el-icon style="margin-right: 4px;">
                                        <StarFilled v-if="scope.row.isFollowing" />
                                        <Star v-else />
                                    </el-icon>
                                    {{ scope.row.isFollowing ? '已关注' : '未关注' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="createTime" label="创建时间" min-width="150" />
                        
                        <el-table-column label="操作" width="200" fixed="right">
                            <template #default="scope">
                                <el-button 
                                    type="primary" 
                                    link 
                                    size="small" 
                                    @click="openDrawer(scope.row)"
                                >
                                    <el-icon><Edit /></el-icon>
                                    详情
                                </el-button>
                                <el-button 
                                    :type="scope.row.isFollowing ? 'warning' : 'success'" 
                                    link 
                                    size="small" 
                                    @click="handleToggleFollow(scope.row)"
                                >
                                    <el-icon>
                                        <StarFilled v-if="scope.row.isFollowing" />
                                        <Star v-else />
                                    </el-icon>
                                    {{ scope.row.isFollowing ? '取关' : '关注' }}
                                </el-button>
                                <el-button 
                                    type="danger" 
                                    link 
                                    size="small" 
                                    @click="handleDelete(scope.row)"
                                >
                                    <el-icon><Delete /></el-icon>
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    
                    <!-- 分页组件 -->
                    <pagination v-show="totalCustomers > 0" 
                        :total="totalCustomers" 
                        :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize"
                        @pagination="handlePagination" />
                </div>

                <!-- 公海管理标签页 -->
                <div v-show="activeTab === 'publicPool'">
                    <public-pool-tab />
                </div>

                <!-- 跟进记录标签页 -->
                <div v-show="activeTab === 'followup'">
                    <followup-tab />
                </div>

                <!-- 拜访计划标签页 -->
                <div v-show="activeTab === 'visitPlan'">
                    <visit-plan-tab />
                </div>
            </el-main>
        </el-container>

        <!-- 客户详情抽屉 -->
        <common-drawer 
            v-if="selectedCustomer"
            v-model="drawerVisible" 
            entity-type="customer" 
            :model-name="'客户'" 
            :entity-data="selectedCustomer" 
            :drawer-config="localDrawerConfig"
            :header-component="CustomerHeaderTab" 
            :header-props="{ userOptions }"
            :actions="drawerActions"
            @update:entity-data="handleCustomerUpdate"
            @toggle-follow="handleDrawerToggleFollow" 
        />

        <!-- 新建/编辑客户对话框 -->
        <el-dialog 
            v-model="dialogVisible" 
            :title="dialogTitle"
            width="800px"
            :before-close="handleClose"
        >
            <el-form :model="currentCustomer" :rules="rules" ref="customerForm" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="客户名称" prop="customerName">
                            <el-input v-model="currentCustomer.customerName" placeholder="请输入客户名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机号" prop="mobile">
                            <el-input v-model="currentCustomer.mobile" placeholder="请输入手机号" />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="电话" prop="phone">
                            <el-input v-model="currentCustomer.phone" placeholder="请输入电话" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="邮箱" prop="email">
                            <el-input v-model="currentCustomer.email" placeholder="请输入邮箱" />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="所属行业" prop="customerIndustry">
                            <el-select v-model="currentCustomer.customerIndustry" placeholder="请选择所属行业" style="width: 100%">
                                <el-option label="互联网" value="互联网" />
                                <el-option label="金融" value="金融" />
                                <el-option label="教育" value="教育" />
                                <el-option label="医疗" value="医疗" />
                                <el-option label="制造业" value="制造业" />
                                <el-option label="其他" value="其他" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="客户级别" prop="customerLevel">
                            <el-select v-model="currentCustomer.customerLevel" placeholder="请选择客户级别" style="width: 100%">
                                <el-option label="A级" value="A级" />
                                <el-option label="B级" value="B级" />
                                <el-option label="C级" value="C级" />
                                <el-option label="D级" value="D级" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-form-item label="客户地址" prop="customerAddress">
                    <el-input v-model="currentCustomer.customerAddress" type="textarea" :rows="3" placeholder="请输入客户地址" />
                </el-form-item>
                
                <el-form-item label="备注" prop="remarks">
                    <el-input v-model="currentCustomer.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="handleClose">取消</el-button>
                    <el-button type="primary" @click="handleSave" :loading="saving">
                        {{ isEdit ? '更新' : '创建' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 跟进记录对话框 -->
        <el-dialog v-model="followupDialogVisible" title="新增跟进记录" width="600px">
            <el-form :model="followupForm" :rules="followupRules" ref="followupFormRef" label-width="100px">
                <el-form-item label="跟进类型" prop="followupType">
                    <el-select v-model="followupForm.followupType" placeholder="请选择跟进类型" style="width: 100%">
                        <el-option label="电话沟通" value="call" />
                        <el-option label="面访" value="visit" />
                        <el-option label="邮件" value="email" />
                        <el-option label="微信" value="wechat" />
                        <el-option label="其他" value="other" />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="跟进标题" prop="title">
                    <el-input v-model="followupForm.title" placeholder="请输入跟进标题" />
                </el-form-item>
                
                <el-form-item label="跟进内容" prop="content">
                    <el-input v-model="followupForm.content" type="textarea" :rows="4" placeholder="请输入跟进内容" />
                </el-form-item>
                
                <el-form-item label="跟进结果" prop="followupResult">
                    <el-select v-model="followupForm.followupResult" placeholder="请选择跟进结果" style="width: 100%">
                        <el-option label="成功" value="success" />
                        <el-option label="失败" value="failed" />
                        <el-option label="待定" value="pending" />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="下次跟进时间">
                    <el-date-picker 
                        v-model="followupForm.nextFollowupTime" 
                        type="datetime" 
                        placeholder="选择下次跟进时间"
                        style="width: 100%"
                    />
                </el-form-item>
                
                <el-form-item>
                    <el-checkbox v-model="followupForm.isImportant">重要跟进</el-checkbox>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="followupDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSaveFollowup" :loading="followupSaving">保存</el-button>
                </span>
            </template>
        </el-dialog>
    </el-container>
</template>

<script setup lang="ts">
import {
    Delete,
    Edit,
    Plus,
    Share,
    Star,
    StarFilled
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, onMounted, reactive, ref } from 'vue';

// 导入组件
import type { Action } from '@/components/CommonDrawer/index.vue';
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import CommonFilter from '@/components/CommonFilter/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import SideNav from '@/components/SideNav/index.vue';

// 导入配置和API
import {
    createCustomer as addCustomer,
    createFollowupRecord as addCustomerFollowupRecord,
    deleteCustomer,
    deleteFollowupRecords as deleteCustomerFollowupRecord,
    followCustomer,
    getFollowupRecordsByCustomerId as getCustomerFollowupRecords,
    getFollowStatus,
    listCustomers,
    unfollowCustomer,
    updateCustomer
} from '@/api/crm/customer';
import { drawerConfig, navConfig } from './config';
import { customerFilterConfig } from './config/filterConfig';

// 导入Tab组件
import CustomerHeaderTab from './tabs/CustomerHeaderTab.vue';
import FollowupTab from './tabs/FollowupTab.vue';
import PublicPoolTab from './tabs/PublicPoolTab.vue';
import VisitPlanTab from './tabs/VisitPlanTab.vue';

// 导入类型
import type { CustomerData, FollowupRecord } from '@/api/crm/customer/types';

// 状态管理
const loading = ref(false);
const saving = ref(false);
const followupLoading = ref(false);
const followupSaving = ref(false);
const detailLoading = ref(false);

// 客户数据
const customers = ref<CustomerData[]>([]);
const totalCustomers = ref(0);
const selectedCustomers = ref<CustomerData[]>([]);
const selectedCustomer = ref<CustomerData | null>(null);

// 弹窗状态
const dialogVisible = ref(false);
const drawerVisible = ref(false);
const followupDialogVisible = ref(false);

// 表单数据
const currentCustomer = ref<CustomerData>({
    customerName: '',
    mobile: '',
    phone: '',
    email: '',
    customerIndustry: '',
    customerLevel: '',
    customerSource: '',
    customerAddress: '',
    remarks: '',
    status: '1'
});

// 跟进记录
const followupRecords = ref<FollowupRecord[]>([]);
const followupForm = ref<FollowupRecord>({
    customerId: 0,
    followupType: '',
    title: '',
    content: '',
    followupResult: '',
    nextFollowupTime: '',
    isImportant: false
});

// 导航和筛选
const activeTab = ref('customers');
const activeDrawerTab = ref('basic');
const searchInput = ref('');
const filterType = ref('all');

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    customerName: '',
    mobile: '',
    email: '',
    customerIndustry: '',
    customerLevel: '',
    status: ''
});

// 抽屉配置
const localDrawerConfig = reactive({
    ...drawerConfig
});

// 用户选项
const userOptions = ref<any[]>([]);

// 计算属性
const isEdit = computed(() => !!currentCustomer.value.id);
const dialogTitle = computed(() => isEdit.value ? '编辑客户' : '新建客户');

// 抽屉操作按钮配置
const drawerActions = computed<Action[]>(() => [
    {
        label: '编辑',
        icon: 'Edit',
        handler: (data: CustomerData) => {
            openCustomerDialog(data);
        }
    },
    {
        label: selectedCustomer.value?.isFollowing ? '取消关注' : '关注',
        icon: selectedCustomer.value?.isFollowing ? 'StarFilled' : 'Star',
        type: (selectedCustomer.value?.isFollowing ? 'warning' : 'success') as 'warning' | 'success',
        handler: (data: CustomerData) => {
            handleToggleFollow(data);
        }
    },
    {
        label: '分配团队',
        icon: 'UserFilled',
        type: 'primary' as 'primary',
        handler: (data: CustomerData) => {
            handleAssignTeam(data);
        }
    },
    {
        label: '打印',
        icon: 'Printer',
        handler: (data: CustomerData) => {
            console.log('打印', data);
        }
    },
    {
        label: '删除',
        type: 'danger' as 'danger',
        icon: 'Delete',
        handler: (data: CustomerData) => {
            handleDelete(data);
        }
    }
]);

// 使用导入的筛选配置
const filterConfig = customerFilterConfig;

// 表单验证规则
const rules = {
    customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
    mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }]
};

const followupRules = {
    followupType: [{ required: true, message: '请选择跟进类型', trigger: 'change' }],
    title: [{ required: true, message: '请输入跟进标题', trigger: 'blur' }],
    content: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }],
    followupResult: [{ required: true, message: '请选择跟进结果', trigger: 'change' }]
};

// 生命周期
onMounted(() => {
    getList();
});

// 方法
const getList = async () => {
    loading.value = true;
    try {
        const response = await listCustomers(queryParams);
        if (response.code === 200) {
            customers.value = response.rows || [];
            totalCustomers.value = response.total || 0;
            
            // 批量查询关注状态
            await Promise.all(customers.value.map(async (customer) => {
                try {
                    const followResponse = await getFollowStatus(customer.id!);
                    customer.isFollowing = followResponse.data || false;
                } catch (error) {
                    customer.isFollowing = false;
                }
            }));
        }
    } catch (error) {
        ElMessage.error('获取客户列表失败');
    } finally {
        loading.value = false;
    }
};

const handleSelectionChange = (selection: CustomerData[]) => {
    selectedCustomers.value = selection;
};

const openCustomerDialog = (customer?: CustomerData) => {
    if (customer) {
        currentCustomer.value = { ...customer };
    } else {
        currentCustomer.value = {
            customerName: '',
            mobile: '',
            phone: '',
            email: '',
            customerIndustry: '',
            customerLevel: '',
            customerSource: '',
            customerAddress: '',
            remarks: '',
            status: '1'
        };
    }
    dialogVisible.value = true;
};

const openDrawer = async (customer: CustomerData) => {
    selectedCustomer.value = customer;
    drawerVisible.value = true;
    
    // 加载跟进记录
    await loadFollowupRecords(customer.id!);
};

const handleClose = () => {
    dialogVisible.value = false;
    currentCustomer.value = {
        customerName: '',
        mobile: '',
        phone: '',
        email: '',
        customerIndustry: '',
        customerLevel: '',
        customerSource: '',
        customerAddress: '',
        remarks: '',
        status: '1'
    };
};

const handleSave = async () => {
    saving.value = true;
    try {
        const customerData = { ...currentCustomer.value };
        let response;
        
        if (isEdit.value) {
            response = await updateCustomer(customerData);
        } else {
            response = await addCustomer(customerData);
        }
        
        if (response.code === 200) {
            ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
            handleClose();
            getList();
        } else {
            ElMessage.error(response.msg || '操作失败');
        }
    } catch (error) {
        ElMessage.error('操作失败');
    } finally {
        saving.value = false;
    }
};

const handleDelete = async (customer: CustomerData) => {
    try {
        await ElMessageBox.confirm('确认删除该客户吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });
        
        const response = await deleteCustomer(customer.id!);
        if (response.code === 200) {
            ElMessage.success('删除成功');
            getList();
        } else {
            ElMessage.error(response.msg || '删除失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败');
        }
    }
};

const handleToggleFollow = async (customer: CustomerData) => {
    try {
        let response;
        if (customer.isFollowing) {
            response = await unfollowCustomer(customer.id!);
        } else {
            response = await followCustomer(customer.id!);
        }
        
        if (response.code === 200) {
            customer.isFollowing = !customer.isFollowing;
            ElMessage.success(customer.isFollowing ? '关注成功' : '取消关注成功');
            
            // 更新selectedCustomer如果是同一个客户
            if (selectedCustomer.value?.id === customer.id) {
                selectedCustomer.value.isFollowing = customer.isFollowing;
            }
        } else {
            ElMessage.error(response.msg || '操作失败');
        }
    } catch (error) {
        ElMessage.error('操作失败');
    }
};

const handleBatchFollow = async () => {
    if (selectedCustomers.value.length === 0) {
        ElMessage.warning('请选择要关注的客户');
        return;
    }
    
    try {
        for (const customer of selectedCustomers.value) {
            if (!customer.isFollowing) {
                await followCustomer(customer.id!);
                customer.isFollowing = true;
            }
        }
        ElMessage.success('批量关注成功');
    } catch (error) {
        ElMessage.error('批量关注失败');
    }
};

const handleBatchUnfollow = async () => {
    if (selectedCustomers.value.length === 0) {
        ElMessage.warning('请选择要取消关注的客户');
        return;
    }
    
    try {
        for (const customer of selectedCustomers.value) {
            if (customer.isFollowing) {
                await unfollowCustomer(customer.id!);
                customer.isFollowing = false;
            }
        }
        ElMessage.success('批量取消关注成功');
    } catch (error) {
        ElMessage.error('批量取消关注失败');
    }
};

const handleBatchAssign = () => {
    ElMessage.info('批量分配功能开发中');
};

const handleAssignTeam = (customer: CustomerData) => {
    ElMessage.info('分配团队功能开发中');
};

const handleSearch = (value: string) => {
    queryParams.customerName = value;
    queryParams.pageNum = 1;
    getList();
};

const handleFilterChange = (value: string) => {
    // 根据筛选类型设置查询参数
    Object.assign(queryParams, {
        customerLevel: '',
        status: '',
        pageNum: 1
    });
    
    if (value === 'A级' || value === 'B级' || value === 'C级' || value === 'D级') {
        queryParams.customerLevel = value;
    }
    
    getList();
};

const handlePagination = (val: { page: number; limit: number }) => {
    queryParams.pageNum = val.page;
    queryParams.pageSize = val.limit;
    getList();
};

// 客户更新处理
const handleCustomerUpdate = async (newData: CustomerData) => {
    try {
        const response = await updateCustomer(newData);
        if (response.code === 200) {
            ElMessage.success('更新客户成功');
            // 更新当前客户数据
            Object.assign(selectedCustomer.value!, newData);
            // 刷新列表数据
            await getList();
        } else {
            ElMessage.error(response.msg || '更新客户失败');
        }
    } catch (error) {
        ElMessage.error('更新客户失败');
    }
};

// 抽屉中的关注状态切换
const handleDrawerToggleFollow = async () => {
    if (selectedCustomer.value) {
        await handleToggleFollow(selectedCustomer.value);
    }
};

// 跟进记录相关方法
const loadFollowupRecords = async (customerId: number) => {
    followupLoading.value = true;
    try {
        const response = await getCustomerFollowupRecords(customerId);
        if (response.code === 200) {
            followupRecords.value = response.data || [];
        }
    } catch (error) {
        ElMessage.error('加载跟进记录失败');
    } finally {
        followupLoading.value = false;
    }
};

const openFollowupDialog = () => {
    if (!selectedCustomer.value?.id) {
        ElMessage.warning('请先选择客户');
        return;
    }
    
    followupForm.value = {
        customerId: selectedCustomer.value.id,
        followupType: '',
        title: '',
        content: '',
        followupResult: '',
        nextFollowupTime: '',
        isImportant: false
    };
    followupDialogVisible.value = true;
};

const handleSaveFollowup = async () => {
    followupSaving.value = true;
    try {
        const response = await addCustomerFollowupRecord(followupForm.value);
        if (response.code === 200) {
            ElMessage.success('添加跟进记录成功');
            followupDialogVisible.value = false;
            if (selectedCustomer.value?.id) {
                await loadFollowupRecords(selectedCustomer.value.id);
            }
        } else {
            ElMessage.error(response.msg || '添加跟进记录失败');
        }
    } catch (error) {
        ElMessage.error('添加跟进记录失败');
    } finally {
        followupSaving.value = false;
    }
};

const deleteFollowupRecord = async (recordId: number) => {
    try {
        await ElMessageBox.confirm('确认删除该跟进记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });
        
        const response = await deleteCustomerFollowupRecord(recordId);
        if (response.code === 200) {
            ElMessage.success('删除成功');
            if (selectedCustomer.value?.id) {
                await loadFollowupRecords(selectedCustomer.value.id);
            }
        } else {
            ElMessage.error(response.msg || '删除失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败');
        }
    }
};

// 跟进类型和结果名称映射
const getFollowupTypeName = (type: string) => {
    const typeMap: Record<string, string> = {
        call: '电话沟通',
        visit: '面访',
        email: '邮件',
        wechat: '微信',
        other: '其他'
    };
    return typeMap[type] || type;
};

const getFollowupResultName = (result: string) => {
    const resultMap: Record<string, string> = {
        success: '成功',
        failed: '失败',
        pending: '待定'
    };
    return resultMap[result] || result;
};
</script>

<style scoped>
.customer-management {
    height: 100vh;
    background-color: #f5f7fa;
}

.main-container {
    flex: 1;
    padding: 0 20px;
    background-color: #fff;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.main-container .el-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 0;
    overflow: auto;
}

/* 客户标签页容器 */
.main-container .el-main > div[v-show] {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 表格容器 */
.main-container .el-table {
    margin-bottom: 16px;
    height: 450px !important;
    overflow: auto;
    flex-shrink: 0;
}

/* 分页组件容器 */
.main-container .pagination-container {
    flex-shrink: 0;
    padding: 12px 0;
    border-top: 1px solid #f0f2f5;
}

/* 筛选组件 */
.main-container .common-filter {
    flex-shrink: 0;
    margin-bottom: 16px;
}

/* 确保分页组件可见 */
:deep(.pagination-container) {
    display: block !important;
    visibility: visible !important;
    margin-top: 16px;
    margin-bottom: 20px;
    min-height: 60px;
}

/* 确保客户标签页内容正确布局 */
.main-container .el-main > div[v-show="activeTab === 'customers'"] {
    display: flex !important;
    flex-direction: column;
    min-height: 0; /* 允许内容收缩 */
}

/* 客户列表容器 */
.customer-list-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

.link-button {
    padding: 0;
    height: auto;
    font-weight: normal;
}

.link-button:hover {
    text-decoration: underline;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>