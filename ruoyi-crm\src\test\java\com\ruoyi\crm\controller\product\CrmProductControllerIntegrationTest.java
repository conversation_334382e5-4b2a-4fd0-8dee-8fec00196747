package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.math.BigDecimal;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmProduct;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmProductService;

/**
 * CrmProductController 集成测试类
 * 使用真实的数据库和完整的Spring上下文进行测试
 * 
 * <AUTHOR>
 * @date 2024-08-16
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("产品控制器集成测试")
class CrmProductControllerIntegrationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(CrmProductControllerIntegrationTest.class);
    
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmProductService crmProductService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .alwaysDo(print())
                .build();
    }

    private CrmProduct createTestProduct(String productName, BigDecimal price) {
        CrmProduct product = new CrmProduct();
        product.setName(productName);
        product.setPrice(price);
        product.setType("测试类型");
        product.setMaterialProperties("测试材料属性");
        product.setMaterialProcess("测试材料工艺");
        product.setTechSpecs("测试技术规格");
        product.setAdvantages("测试优点");
        product.setDisadvantages("测试缺点");
        product.setApplicationAreas("测试应用领域");
        crmProductService.insertCrmProduct(product);
        assertNotNull(product.getId(), "测试产品创建失败");
        return product;
    }

    private void cleanupTestProduct(Long productId) {
        if (productId != null) {
            try {
                Long[] ids = { productId };
                crmProductService.deleteCrmProductByIds(ids);
            } catch (Exception e) {
                // 忽略清理错误
                logger.warn("清理测试数据失败: {}", e.getMessage());
            }
        }
    }

    @Nested
    @DisplayName("产品CRUD集成测试")
    class CrudIntegrationTests {

        @Test
        @DisplayName("完整的CRUD流程测试")
        void testFullCrudFlow() throws Exception {
            // 1. 创建产品
            CrmProduct newProduct = new CrmProduct();
            newProduct.setName("CRUD测试产品");
            newProduct.setPrice(new BigDecimal("888.88"));
            newProduct.setType("测试类型");
            newProduct.setMaterialProperties("测试材料属性");
            newProduct.setMaterialProcess("测试材料工艺");
            newProduct.setTechSpecs("测试技术规格");
            newProduct.setAdvantages("测试优点");
            newProduct.setDisadvantages("测试缺点");
            newProduct.setApplicationAreas("测试应用领域");

            MvcResult createResult = mockMvc.perform(post("/crm/product")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newProduct)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            int code = rootNode.path("code").asInt();
            assertEquals(200, code, "创建产品应返回成功状态码");
            
            // 获取创建的产品ID
            Long createdProductId = null;
            try {
                // 查询刚创建的产品
                CrmProduct query = new CrmProduct();
                query.setName("CRUD测试产品");
                List<CrmProduct> products = crmProductService.selectCrmProductList(query);
                assertFalse(products.isEmpty(), "应该能查询到刚创建的产品");
                createdProductId = products.get(0).getId();
                assertNotNull(createdProductId, "创建的产品应有ID");
                
                // 2. 查询单个产品
                MvcResult queryResult = mockMvc.perform(get("/crm/product/{id}", createdProductId))
                        .andExpect(status().isOk())
                        .andReturn();
                String queryJson = queryResult.getResponse().getContentAsString();
                assertTrue(queryJson.contains("CRUD测试产品"), "返回内容中应包含产品名称");
                assertEquals(200, objectMapper.readTree(queryJson).path("code").asInt());
                assertEquals("CRUD测试产品", objectMapper.readTree(queryJson).path("data").path("name").asText());

                // 3. 修改产品
                newProduct.setId(createdProductId);
                newProduct.setName("修改后的产品名称");
                newProduct.setPrice(new BigDecimal("999.99"));

                mockMvc.perform(put("/crm/product")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newProduct)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证修改结果
                mockMvc.perform(get("/crm/product/{id}", createdProductId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.data.name").value("修改后的产品名称"));
            } finally {
                // 5. 删除产品
                if (createdProductId != null) {
                    mockMvc.perform(delete("/crm/product/{ids}", createdProductId))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.code").value(200));
                    // 6. 验证删除结果
                    CrmProduct deleted = crmProductService.selectCrmProductById(createdProductId);
                    assertNull(deleted, "删除后查询应为null");
                }
            }
        }

        // 查询产品列表
        @Test
        @DisplayName("查询产品列表 - 带分页和筛选")
        void testGetProductListWithFilters() throws Exception {
            CrmProduct testProduct = createTestProduct("列表测试产品", new BigDecimal("666.66"));
            Long testProductId = testProduct.getId();
            try {
                MvcResult result = mockMvc.perform(get("/crm/product/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .param("name", "列表测试"))
                        .andExpect(status().isOk())
                        .andReturn();
                String responseContent = result.getResponse().getContentAsString();
                TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
                assertNotNull(response);
                assertEquals(200, response.getCode());
                assertNotNull(response.getRows());
                assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
            } finally {
                cleanupTestProduct(testProductId);
            }
        }
    }

    @Nested
    @DisplayName("导出功能集成测试")
    class ExportIntegrationTests {
        @Test
        @DisplayName("导出产品数据")
        void testExportProducts() throws Exception {
            CrmProduct testProduct = createTestProduct("导出测试产品", new BigDecimal("777.77"));
            Long testProductId = testProduct.getId();
            try {
                MvcResult result = mockMvc.perform(get("/crm/product/export")
                        .param("name", "导出测试"))
                        .andExpect(status().isOk())
                        .andReturn();
                String responseContent = result.getResponse().getContentAsString();
                AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
                assertNotNull(response);
                assertEquals(200, (Integer) response.get("code"));
            } finally {
                cleanupTestProduct(testProductId);
            }
        }
    }
    
    @Nested
    @DisplayName("搜索功能集成测试")
    class SearchIntegrationTests {
        private Long testProductId;
        
        @BeforeEach
        void setUp() {
            CrmProduct testProduct = createTestProduct("搜索测试产品", new BigDecimal("555.55"));
            testProductId = testProduct.getId();
        }
        
        @AfterEach
        void tearDown() {
            cleanupTestProduct(testProductId);
        }
        
        @Test
        @DisplayName("按产品名称搜索")
        void testSearchByProductName() throws Exception {
            MvcResult result = mockMvc.perform(get("/crm/product/list")
                    .param("name", "搜索测试"))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
        }
        
        @Test
        @DisplayName("按产品类型搜索")
        void testSearchByProductType() throws Exception {
            MvcResult result = mockMvc.perform(get("/crm/product/list")
                    .param("type", "测试类型"))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
        }
    }
    
    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {
        @Test
        @DisplayName("查询不存在的产品")
        void testGetNonExistentProduct() throws Exception {
            Long nonExistentId = 99999L; // 假设这个ID不存在
            
            MvcResult result = mockMvc.perform(get("/crm/product/{id}", nonExistentId))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(responseContent);
            
            // 验证返回的数据为空或错误信息
            assertTrue(rootNode.path("data").isNull() || rootNode.path("data").isEmpty(), 
                    "不存在的产品应返回空数据");
        }
        
        @Test
        @DisplayName("删除不存在的产品")
        void testDeleteNonExistentProduct() throws Exception {
            Long nonExistentId = 99999L; // 假设这个ID不存在
            
            MvcResult result = mockMvc.perform(delete("/crm/product/{ids}", nonExistentId))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            // 验证操作是否成功完成（即使没有实际删除任何记录）
            assertEquals(200, (Integer) response.get("code"), "删除不存在的记录应该返回成功状态");
        }
        
        @Test
        @DisplayName("使用无效数据创建产品")
        void testCreateProductWithInvalidData() throws Exception {
            CrmProduct invalidProduct = new CrmProduct();
            // 故意不设置必要字段
            
            MvcResult result = mockMvc.perform(post("/crm/product")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidProduct)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(responseContent);
            
            // 验证是否返回错误信息
            // 注意：具体的错误代码和消息可能需要根据实际应用的错误处理逻辑进行调整
            if (rootNode.has("code") && rootNode.path("code").asInt() != 200) {
                // 如果返回错误代码
                assertNotEquals(200, rootNode.path("code").asInt(), "无效数据应该返回错误状态");
            } else {
                // 如果返回成功代码，但操作可能在服务层失败
                // 这种情况下，可能需要检查其他指标来确定操作是否真的失败
                logger.warn("创建无效产品返回了成功状态码，但操作可能在服务层失败");
            }
        }
    }
    
    @Nested
    @DisplayName("产品类型API测试")
    class ProductTypeApiTests {
        @Test
        @DisplayName("获取产品类型列表")
        void testGetProductTypes() throws Exception {
            // 先创建一个特定类型的产品
            CrmProduct testProduct = createTestProduct("类型测试产品", new BigDecimal("444.44"));
            testProduct.setType("特殊测试类型");
            crmProductService.updateCrmProduct(testProduct);
            Long testProductId = testProduct.getId();
            
            try {
                MvcResult result = mockMvc.perform(get("/crm/product/types"))
                        .andExpect(status().isOk())
                        .andReturn();
                
                String responseContent = result.getResponse().getContentAsString();
                AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
                
                assertNotNull(response);
                assertEquals(200, (Integer) response.get("code"));
                
                // 验证返回的类型列表中包含我们创建的特殊类型
                Object data = response.get("data");
                assertNotNull(data, "返回的数据不应为null");
                
                List<String> types = objectMapper.convertValue(data, new TypeReference<List<String>>() {});
                assertTrue(types.contains("特殊测试类型"), "类型列表应包含测试添加的类型");
            } finally {
                cleanupTestProduct(testProductId);
            }
        }
    }
}