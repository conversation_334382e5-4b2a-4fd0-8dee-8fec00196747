package com.ruoyi.crm.controller;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.crm.BaseTestCase;

/**
 * CrmContactsController 集成测试类
 * 使用真实的数据库和完整的Spring上下文进行测试
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("联系人控制器集成测试")
class CrmContactsControllerIntegrationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(CrmContactsControllerIntegrationTest.class);
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmContactsService crmContactsService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        // 只设置MockMvc，不创建测试数据
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .alwaysDo(print())
                .build();
    }

    // 保留工具方法，供需要的测试使用
    private CrmContacts createTestContact(String contactName, String customerName) {
        CrmContacts testContact = new CrmContacts();
        testContact.setName(contactName);
      
        testContact.setMobile("13900139000");
        testContact.setPhone("010-12345678");
        testContact.setEmail("<EMAIL>");
        testContact.setPosition("产品经理");
        testContact.setIsKeyDecisionMaker("1");
        testContact.setResponsiblePersonId("1");
        testContact.setGender("M");
        testContact.setAddress("北京市朝阳区");
        testContact.setDetailedAddress("朝阳区望京街道");
        testContact.setRemarks("集成测试用例");

        // 插入测试数据
        crmContactsService.insertCrmContacts(testContact);
        assertNotNull(testContact.getId(), "测试数据创建失败");
        return testContact;
    }

    private void cleanupTestContact(Long contactId) {
        if (contactId != null) {
            try {
                crmContactsService.deleteCrmContactsById(contactId);
            } catch (Exception e) {
                // 忽略清理错误
            }
        }
    }

    @Nested
    @DisplayName("联系人CRUD集成测试")
    class CrudIntegrationTests {

        @Test
        @DisplayName("完整的CRUD流程测试")
        void testFullCrudFlow() throws Exception {
            // 这个测试自己创建数据，不需要预置数据
            // 1. 创建联系人
            CrmContacts newContact = new CrmContacts();
            newContact.setName("CRUD测试联系人");
 
            newContact.setMobile("13800138001");
            newContact.setPhone("010-12345679");
            newContact.setEmail("<EMAIL>");
            newContact.setPosition("测试经理");
            newContact.setIsKeyDecisionMaker("1");
            newContact.setResponsiblePersonId("1");
            newContact.setGender("M");
            newContact.setAddress("上海市浦东区");
            newContact.setDetailedAddress("浦东新区张江高科技园");
            newContact.setRemarks("CRUD测试联系人");

            MvcResult createResult = mockMvc.perform(post("/front/crm/contacts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newContact)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            AjaxResult createResponse = objectMapper.readValue(createResponseContent, AjaxResult.class);
            
            // 从响应中获取创建的联系人ID
            Long createdContactId = null;
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            createdContactId = rootNode.path("data").path("id").asLong();
            assertNotNull(createdContactId, "创建联系人后应返回ID");

            try {
                                 // 2. 查询单个联系人
                 mockMvc.perform(get("/front/crm/contacts/{id}", createdContactId))
                         .andExpect(status().isOk())
                         .andExpect(jsonPath("$.code").value(200))
                         .andExpect(jsonPath("$.data.name").value("CRUD测试联系人"))
                         .andExpect(jsonPath("$.data.customerName").value("CRUD测试客户"));

                // 3. 修改联系人
                newContact.setId(createdContactId);
                newContact.setName("修改后的联系人名称");
                newContact.setPosition("高级测试经理");
                newContact.setRemarks("已修改");

                mockMvc.perform(put("/front/crm/contacts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newContact)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证修改结果
                mockMvc.perform(get("/front/crm/contacts/{id}", createdContactId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.data.name").value("修改后的联系人名称"))
                        .andExpect(jsonPath("$.data.position").value("高级测试经理"))
                        .andExpect(jsonPath("$.data.remarks").value("已修改"));

            } finally {
                logger.warn("···createdContactId: {}", createdContactId);
                // 5. 删除联系人
                mockMvc.perform(delete("/front/crm/contacts/{ids}", createdContactId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.msg").value("操作成功"))
                        .andExpect(jsonPath("$.code").value(200));

                // 6. 验证删除结果
                MvcResult deleteVerifyResult = mockMvc.perform(get("/front/crm/contacts/{id}", createdContactId))
                        .andExpect(status().isOk())
                        .andReturn();
                
                String deleteVerifyContent = deleteVerifyResult.getResponse().getContentAsString();
                AjaxResult deleteVerifyResponse = objectMapper.readValue(deleteVerifyContent, AjaxResult.class);

                logger.warn("···deleteVerifyResponse: {}", deleteVerifyResponse);
                assertEquals(200, deleteVerifyResponse.get("code"));
                assertEquals("操作成功", deleteVerifyResponse.get("msg"));
                logger.warn("···deleteVerifyResponse.get(\"data\"): {}", deleteVerifyResponse.get("data"));
            }
        }

        @Test
        @DisplayName("查询联系人列表 - 带分页和筛选")
        void testGetContactListWithFilters() throws Exception {
            CrmContacts testContact = createTestContact("列表测试联系人", "列表测试客户");
            Long testContactId = testContact.getId();
            
            try {
                MvcResult result = mockMvc.perform(get("/front/crm/contacts/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .param("name", "列表测试")
                        .param("customerName", "列表测试客户"))
                        .andExpect(status().isOk())
                        .andReturn();

                String responseContent = result.getResponse().getContentAsString();
                TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
                
                assertNotNull(response);
                assertEquals(200, response.getCode());
                assertNotNull(response.getRows());
                assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
            } finally {
                cleanupTestContact(testContactId);
            }
        }

        @Test
        @DisplayName("联系人更新 - 全字段测试")
        void testUpdateContactAllFields() throws Exception {
            // 1. 先创建一个测试联系人
            CrmContacts originalContact = new CrmContacts();
            originalContact.setName("原始联系人");
            originalContact.setMobile("13800000001");
            originalContact.setPhone("010-11111111");
            originalContact.setEmail("<EMAIL>");
            originalContact.setPosition("初级工程师");
            originalContact.setIsKeyDecisionMaker("0");
            originalContact.setResponsiblePersonId("1");
            originalContact.setGender("M");
            originalContact.setAddress("北京市海淀区");
            originalContact.setDetailedAddress("海淀区中关村");
            originalContact.setRemarks("原始备注");

            MvcResult createResult = mockMvc.perform(post("/front/crm/contacts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(originalContact)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            // 获取创建的联系人ID
            String createResponseContent = createResult.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdContactId = rootNode.path("data").path("id").asLong();
            assertNotNull(createdContactId, "创建联系人后应返回ID");

            try {
                // 2. 准备全字段更新数据
                CrmContacts updateContact = new CrmContacts();
                updateContact.setId(createdContactId);
                updateContact.setName("更新后联系人姓名");
                updateContact.setMobile("13900000002");
                updateContact.setPhone("021-22222222");
                updateContact.setEmail("<EMAIL>");
                updateContact.setPosition("高级架构师");
                updateContact.setIsKeyDecisionMaker("1");
                updateContact.setResponsiblePersonId("2");
                updateContact.setGender("F");
                updateContact.setAddress("上海市浦东新区");
                updateContact.setDetailedAddress("浦东新区陆家嘴");
                updateContact.setRemarks("更新后的详细备注信息");
                updateContact.setDirectSuperior("更新后的直属上级");
                // 设置下次联系时间 - 需要使用Date对象
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    updateContact.setNextContactTime(sdf.parse("2025-07-15 14:30:00"));
                } catch (Exception e) {
                    logger.warn("日期解析失败", e);
                }

                // 3. 执行更新操作
                MvcResult updateResult = mockMvc.perform(put("/front/crm/contacts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateContact)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200))
                        .andReturn();

                String updateResponseContent = updateResult.getResponse().getContentAsString();
                logger.info("更新响应内容: {}", updateResponseContent);

                // 4. 验证所有字段都已正确更新
                MvcResult verifyResult = mockMvc.perform(get("/front/crm/contacts/{id}", createdContactId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.name").value("更新后联系人姓名"))
                        .andExpect(jsonPath("$.data.mobile").value("13900000002"))
                        .andExpect(jsonPath("$.data.phone").value("021-22222222"))
                        .andExpect(jsonPath("$.data.email").value("<EMAIL>"))
                        .andExpect(jsonPath("$.data.position").value("高级架构师"))
                        .andExpect(jsonPath("$.data.isKeyDecisionMaker").value("1"))
                        .andExpect(jsonPath("$.data.responsiblePersonId").value("2"))
                        .andExpect(jsonPath("$.data.gender").value("F"))
                        .andExpect(jsonPath("$.data.address").value("上海市浦东新区"))
                        .andExpect(jsonPath("$.data.detailedAddress").value("浦东新区陆家嘴"))
                        .andExpect(jsonPath("$.data.remarks").value("更新后的详细备注信息"))
                        .andExpect(jsonPath("$.data.directSuperior").value("更新后的直属上级"))
                        .andReturn();

                String verifyResponseContent = verifyResult.getResponse().getContentAsString();
                logger.info("验证响应内容: {}", verifyResponseContent);

                // 5. 额外验证：确保更新时间字段有变化
                JsonNode verifyNode = objectMapper.readTree(verifyResponseContent);
                JsonNode dataNode = verifyNode.path("data");
                
                assertNotNull(dataNode.path("updateTime").asText(), "更新时间应该存在");
                assertNotEquals("", dataNode.path("updateTime").asText(), "更新时间不应为空");

            } finally {
                // 6. 清理测试数据
                mockMvc.perform(delete("/front/crm/contacts/{ids}", createdContactId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));
                
                logger.info("测试数据清理完成，联系人ID: {}", createdContactId);
            }
        }

        @Test
        @DisplayName("联系人更新 - 部分字段测试")
        void testUpdateContactPartialFields() throws Exception {
            // 1. 创建测试联系人
            CrmContacts originalContact = new CrmContacts();
            originalContact.setName("部分更新测试联系人");
            originalContact.setMobile("13800000003");
            originalContact.setEmail("<EMAIL>");
            originalContact.setPosition("测试工程师");
            originalContact.setRemarks("原始备注");

            MvcResult createResult = mockMvc.perform(post("/front/crm/contacts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(originalContact)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdContactId = rootNode.path("data").path("id").asLong();

            try {
                // 2. 只更新部分字段
                CrmContacts partialUpdate = new CrmContacts();
                partialUpdate.setId(createdContactId);
                partialUpdate.setName("部分更新后的姓名");
                partialUpdate.setPosition("高级测试工程师");
                partialUpdate.setRemarks("部分更新后的备注");
                // 其他字段保持不变

                // 3. 执行部分更新
                mockMvc.perform(put("/front/crm/contacts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(partialUpdate)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证更新结果
                MvcResult verifyResult = mockMvc.perform(get("/front/crm/contacts/{id}", createdContactId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.name").value("部分更新后的姓名"))
                        .andExpect(jsonPath("$.data.position").value("高级测试工程师"))
                        .andExpect(jsonPath("$.data.remarks").value("部分更新后的备注"))
                        .andExpect(jsonPath("$.data.mobile").value("13800000003")) // 原始值应保持不变
                        .andExpect(jsonPath("$.data.email").value("<EMAIL>")) // 原始值应保持不变
                        .andReturn();

                String verifyContent = verifyResult.getResponse().getContentAsString();
                logger.info("部分更新验证结果: {}", verifyContent);

            } finally {
                // 5. 清理测试数据
                mockMvc.perform(delete("/front/crm/contacts/{ids}", createdContactId))
                        .andExpect(status().isOk());
            }
        }

        @Test
        @DisplayName("联系人更新 - 边界条件测试")
        void testUpdateContactBoundaryConditions() throws Exception {
            // 1. 创建测试联系人
            CrmContacts originalContact = new CrmContacts();
            originalContact.setName("边界测试联系人");
            originalContact.setMobile("13800000004");
            originalContact.setEmail("<EMAIL>");

            MvcResult createResult = mockMvc.perform(post("/front/crm/contacts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(originalContact)))
                    .andExpect(status().isOk())
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdContactId = rootNode.path("data").path("id").asLong();

            try {
                // 2. 测试极长文本
                String longText = "这是一个非常长的文本内容".repeat(50); // 大约1000个字符
                CrmContacts longTextUpdate = new CrmContacts();
                longTextUpdate.setId(createdContactId);
                longTextUpdate.setName("长文本测试");
                longTextUpdate.setRemarks(longText);

                mockMvc.perform(put("/front/crm/contacts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(longTextUpdate)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 3. 测试特殊字符
                CrmContacts specialCharUpdate = new CrmContacts();
                specialCharUpdate.setId(createdContactId);
                specialCharUpdate.setName("特殊字符测试@#$%^&*()");
                specialCharUpdate.setRemarks("包含特殊字符：<>&\"'");

                mockMvc.perform(put("/front/crm/contacts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(specialCharUpdate)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证特殊字符更新结果
                mockMvc.perform(get("/front/crm/contacts/{id}", createdContactId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.data.name").value("特殊字符测试@#$%^&*()"))
                        .andExpect(jsonPath("$.data.remarks").value("包含特殊字符：<>&\"'"));

            } finally {
                // 5. 清理测试数据
                mockMvc.perform(delete("/front/crm/contacts/{ids}", createdContactId))
                        .andExpect(status().isOk());
            }
        }

        @Test
        @DisplayName("联系人更新 - 错误场景测试")
        void testUpdateContactErrorScenarios() throws Exception {
            // 1. 测试不存在的联系人ID更新
            CrmContacts nonExistentContact = new CrmContacts();
            nonExistentContact.setId(999999L);
            nonExistentContact.setName("不存在的联系人");

            MvcResult nonExistentResult = mockMvc.perform(put("/front/crm/contacts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(nonExistentContact)))
                    .andExpect(status().isOk())
                    .andReturn();

            String nonExistentContent = nonExistentResult.getResponse().getContentAsString();
            AjaxResult nonExistentResponse = objectMapper.readValue(nonExistentContent, AjaxResult.class);
            
            // 验证返回错误码或相应的处理结果
            logger.info("不存在联系人更新结果: {}", nonExistentResponse);

            // 2. 测试无效数据格式
            String invalidJson = "{\"id\":1,\"name\":\"\",\"email\":\"invalid-email\"}";
            
            MvcResult invalidResult = mockMvc.perform(put("/front/crm/contacts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(invalidJson))
                    .andExpect(status().isOk())
                    .andReturn();

            String invalidContent = invalidResult.getResponse().getContentAsString();
            logger.info("无效数据更新结果: {}", invalidContent);
        }

    }

    @Nested
    @DisplayName("导出功能集成测试")
    class ExportIntegrationTests {

        @Test
        @DisplayName("导出联系人数据")
        void testExportContacts() throws Exception {
            CrmContacts queryContact = new CrmContacts();
            queryContact.setIsKeyDecisionMaker("1");

                MvcResult result = mockMvc.perform(post("/front/crm/contacts/export")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(queryContact)))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            assertNotNull(response);
            assertEquals(200, (Integer) response.get("code"));
        }
    }
}