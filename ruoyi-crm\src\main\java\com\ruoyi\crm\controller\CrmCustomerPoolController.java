package com.ruoyi.crm.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.domain.entity.CrmCustomerPool;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.service.ICrmCustomerService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.service.ICrmCustomerPoolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户公海Controller
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@RestController
@RequestMapping("/crm/customer")
public class CrmCustomerPoolController extends BaseController {
    
    @Autowired
    private ICrmCustomerPoolService customerPoolService;
    
    @Autowired
    private ICrmCustomerService customerService;

    /**
     * 查询公海客户列表
     */
    @PreAuthorize("@ss.hasPermi('crm:customer:list')")
    @GetMapping("/pool")
    public TableDataInfo poolList(CrmCustomerPool crmCustomerPool) {
        startPage();
        List<CrmCustomerPool> list = customerPoolService.selectPoolCustomerList(crmCustomerPool);
        return getDataTable(list);
    }

    /**
     * 查询我的客户列表
     */
    @PreAuthorize("@ss.hasPermi('crm:customer:list')")
    @GetMapping("/own")
    public TableDataInfo ownList() {
        startPage();
        Long userId = SecurityUtils.getUserId();
        List<CrmCustomerPool> list = customerPoolService.selectMyCustomerList(userId);
        return getDataTable(list);
    }

    /**
     * 将客户放入公海
     */
    @PreAuthorize("@ss.hasPermi('crm:customer:edit')")
    @Log(title = "客户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/return")
    public AjaxResult returnToPool(@PathVariable("id") Long customerId,
                                  @RequestParam("reason") String reason,
                                  @RequestParam(value = "remark", required = false) String remark) {
        List<Long> customerIds = List.of(customerId);
        int result = customerPoolService.returnToPool(customerIds, reason, remark);
        return result > 0 ? success("放入公海成功") : error("放入公海失败");
    }

    /**
     * 批量将客户放入公海
     */
    @PreAuthorize("@ss.hasPermi('crm:customer:edit')")
    @Log(title = "客户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/return")
    public AjaxResult batchReturnToPool(@RequestBody ReturnToPoolRequest request) {
        int result = customerPoolService.returnToPool(request.getCustomerIds(), 
                                                      request.getReason(), 
                                                      request.getRemark());
        return result > 0 ? success("成功放入公海 " + result + " 个客户") : error("放入公海失败");
    }

    /**
     * 认领客户
     */
    @PreAuthorize("@ss.hasPermi('crm:customer:edit')")
    @Log(title = "客户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/claim")
    public AjaxResult claimCustomer(@PathVariable("id") Long customerId) {
        List<Long> customerIds = List.of(customerId);
        try {
            int result = customerPoolService.claimCustomers(customerIds);
            return result > 0 ? success("认领成功") : error("认领失败");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 批量认领客户
     */
    @PreAuthorize("@ss.hasPermi('crm:customer:edit')")
    @Log(title = "客户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/claim")
    public AjaxResult batchClaimCustomers(@RequestBody List<Long> customerIds) {
        try {
            int result = customerPoolService.claimCustomers(customerIds);
            return result > 0 ? success("成功认领 " + result + " 个客户") : error("认领失败");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 搜索客户
     */
    @PreAuthorize("@ss.hasPermi('crm:customer:list')")
    @GetMapping("/search")
    public AjaxResult searchCustomers(@RequestParam("customerName") String customerName) {
        List<CrmCustomer> customers = customerService.selectCrmCustomerByName(customerName);
        return success(customers);
    }

    /**
     * 放入公海请求参数
     */
    public static class ReturnToPoolRequest {
        private List<Long> customerIds;
        private String reason;
        private String remark;

        // Getters and Setters
        public List<Long> getCustomerIds() {
            return customerIds;
        }

        public void setCustomerIds(List<Long> customerIds) {
            this.customerIds = customerIds;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}