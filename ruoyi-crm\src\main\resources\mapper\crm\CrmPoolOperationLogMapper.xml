<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmPoolOperationLogMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmPoolOperationLog" id="CrmPoolOperationLogResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="operationType"    column="operation_type"    />
        <result property="fromUserId"    column="from_user_id"    />
        <result property="toUserId"    column="to_user_id"    />
        <result property="operationReason"    column="operation_reason"    />
        <result property="operationRemark"    column="operation_remark"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <insert id="insert" parameterType="com.ruoyi.common.domain.entity.CrmPoolOperationLog" useGeneratedKeys="true" keyProperty="id">
        insert into crm_pool_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="operationType != null">operation_type,</if>
            <if test="fromUserId != null">from_user_id,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="operationReason != null">operation_reason,</if>
            <if test="operationRemark != null">operation_remark,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="operationType != null">#{operationType},</if>
            <if test="fromUserId != null">#{fromUserId},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="operationReason != null">#{operationReason},</if>
            <if test="operationRemark != null">#{operationRemark},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <select id="selectList" parameterType="com.ruoyi.common.domain.entity.CrmPoolOperationLog" resultMap="CrmPoolOperationLogResult">
        select * from crm_pool_operation_log
        <where>
            <if test="customerId != null">and customer_id = #{customerId}</if>
            <if test="operationType != null and operationType != ''">and operation_type = #{operationType}</if>
            <if test="fromUserId != null">and from_user_id = #{fromUserId}</if>
            <if test="toUserId != null">and to_user_id = #{toUserId}</if>
        </where>
        order by operation_time desc
    </select>

    <select id="selectById" parameterType="Long" resultMap="CrmPoolOperationLogResult">
        select * from crm_pool_operation_log where id = #{id}
    </select>

</mapper>