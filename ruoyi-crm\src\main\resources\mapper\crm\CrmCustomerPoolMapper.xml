<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmCustomerPoolMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmCustomerPool" id="CrmCustomerPoolResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="poolType"    column="pool_type"    />
        <result property="departmentId"    column="department_id"    />
        <result property="previousOwnerId"    column="previous_owner_id"    />
        <result property="previousOwnerName"    column="previous_owner_name"    />
        <result property="returnReason"    column="return_reason"    />
        <result property="returnRemark"    column="return_remark"    />
        <result property="returnTime"    column="return_time"    />
        <result property="returnBy"    column="return_by"    />
        <result property="daysInPool"    column="days_in_pool"    />
        <result property="status"    column="status"    />
        <result property="claimTime"    column="claim_time"    />
        <result property="claimBy"    column="claim_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <!-- 扩展字段 -->
        <result property="mobile"    column="mobile"    />
        <result property="email"    column="email"    />
        <result property="customerIndustry"    column="customer_industry"    />
        <result property="customerLevel"    column="customer_level"    />
        <result property="lastFollowupTime"    column="last_followup_time"    />
    </resultMap>

    <sql id="selectPoolCustomerVo">
        select p.*, 
               c.customer_name,
               c.mobile,
               c.email,
               c.customer_industry,
               c.customer_level,
               c.last_followup_time
        from crm_customer_pool p
        left join crm_business_customers c on p.customer_id = c.id
    </sql>

    <select id="selectPoolCustomerList" parameterType="com.ruoyi.common.domain.entity.CrmCustomerPool" resultMap="CrmCustomerPoolResult">
        <include refid="selectPoolCustomerVo"/>
        <where>
            c.del_flag = '0'
            <if test="poolType != null  and poolType != ''"> and p.pool_type = #{poolType}</if>
            <if test="departmentId != null"> and p.department_id = #{departmentId}</if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
            <if test="returnReason != null  and returnReason != ''"> and p.return_reason = #{returnReason}</if>
            <if test="customerName != null  and customerName != ''"> and c.customer_name like concat('%', #{customerName}, '%')</if>
            <if test="mobile != null  and mobile != ''"> and c.mobile like concat('%', #{mobile}, '%')</if>
            <if test="email != null  and email != ''"> and c.email like concat('%', #{email}, '%')</if>
            <if test="customerIndustry != null  and customerIndustry != ''"> and c.customer_industry = #{customerIndustry}</if>
            <if test="customerLevel != null  and customerLevel != ''"> and c.customer_level = #{customerLevel}</if>
        </where>
        order by p.return_time desc
    </select>
    
    <update id="batchUpdateStatus">
        update crm_customer_pool
        set status = #{status},
            claim_time = case when #{status} = 'CLAIMED' then now() else claim_time end,
            claim_by = case when #{status} = 'CLAIMED' then #{claimBy} else claim_by end,
            update_time = now()
        where customer_id in
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </update>
    
    <select id="selectTodayClaimCount" resultType="int">
        select count(1) 
        from crm_pool_operation_log
        where operation_type = 'CLAIM'
          and to_user_id = #{userId}
          and date(operation_time) = curdate()
    </select>
    
    <select id="selectUserCustomerCount" resultType="int">
        select count(1)
        from crm_business_customers
        where responsible_person_id = #{userId}
          and del_flag = '0'
    </select>
    
    <update id="updateDaysInPool">
        update crm_customer_pool
        set days_in_pool = datediff(now(), return_time)
        where status = 'IN_POOL'
    </update>

</mapper>