-- =============================================
-- 线索池功能相关数据表
-- 创建时间: 2025-01-24
-- 说明: 线索池管理、分配记录等功能的数据表
-- =============================================

-- ----------------------------
-- 1. 线索池表 (crm_lead_pool)
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_pool`;
CREATE TABLE `crm_lead_pool` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `lead_id` bigint(20) DEFAULT NULL COMMENT '线索ID，关联crm_business_leads表',
  `pool_status` varchar(50) NOT NULL DEFAULT 'available' COMMENT '池状态：available-可用，assigned-已分配，locked-锁定',
  `quality_level` varchar(20) DEFAULT 'C' COMMENT '质量等级：A-优质，B-良好，C-一般，D-较差',
  `priority` int(11) DEFAULT 5 COMMENT '优先级：1-10，数字越大优先级越高',
  `source_type` varchar(50) DEFAULT 'new' COMMENT '来源类型：new-新增，recycled-回收，imported-导入',
  `enter_pool_time` datetime NOT NULL COMMENT '进入池时间',
  `last_assign_time` datetime DEFAULT NULL COMMENT '最后分配时间',
  `assign_count` int(11) DEFAULT 0 COMMENT '分配次数',
  `region` varchar(100) DEFAULT NULL COMMENT '地区，用于地区优先分配',
  `industry` varchar(100) DEFAULT NULL COMMENT '行业，用于行业专业分配',
  `estimated_value` decimal(15,2) DEFAULT NULL COMMENT '预估价值',
  `remarks` text COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_lead_id` (`lead_id`) USING BTREE,
  KEY `idx_pool_status` (`pool_status`) USING BTREE,
  KEY `idx_quality_level` (`quality_level`) USING BTREE,
  KEY `idx_enter_pool_time` (`enter_pool_time`) USING BTREE,
  KEY `idx_region` (`region`) USING BTREE,
  KEY `idx_industry` (`industry`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线索池表';

-- ----------------------------
-- 2. 线索分配记录表 (crm_lead_assignment_records)
-- ----------------------------
DROP TABLE IF EXISTS `crm_lead_assignment_records`;
CREATE TABLE `crm_lead_assignment_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `lead_id` bigint(20) NOT NULL COMMENT '线索ID',
  `pool_id` bigint(20) DEFAULT NULL COMMENT '线索池ID，关联crm_lead_pool表',
  `from_user_id` bigint(20) DEFAULT NULL COMMENT '原负责人ID，可为空（从池中分配）',
  `to_user_id` bigint(20) NOT NULL COMMENT '新负责人ID',
  `assignment_type` varchar(50) NOT NULL COMMENT '分配类型：manual-手动分配，grab-抢单，recycle-回收',
  `assignment_reason` varchar(200) DEFAULT NULL COMMENT '分配原因：手动分配/抢单/回收等',
  `assignment_time` datetime NOT NULL COMMENT '分配时间',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(64) DEFAULT NULL COMMENT '操作人姓名',
  `remarks` text COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_lead_id` (`lead_id`) USING BTREE,
  KEY `idx_pool_id` (`pool_id`) USING BTREE,
  KEY `idx_from_user_id` (`from_user_id`) USING BTREE,
  KEY `idx_to_user_id` (`to_user_id`) USING BTREE,
  KEY `idx_assignment_type` (`assignment_type`) USING BTREE,
  KEY `idx_assignment_time` (`assignment_time`) USING BTREE,
  KEY `idx_operator_id` (`operator_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='线索分配记录表';

-- ----------------------------
-- 3. 插入测试数据（可选）
-- ----------------------------

-- 插入一些测试线索池数据
INSERT INTO `crm_lead_pool` (`lead_id`, `pool_status`, `quality_level`, `priority`, `source_type`, `enter_pool_time`, `region`, `industry`, `estimated_value`, `remarks`, `create_by`, `create_time`) VALUES
(NULL, 'available', 'A', 8, 'new', NOW(), '北京', 'IT', 50000.00, '高质量IT行业线索', 'admin', NOW()),
(NULL, 'available', 'B', 6, 'new', NOW(), '上海', '教育', 30000.00, '教育行业潜在客户', 'admin', NOW()),
(NULL, 'available', 'C', 4, 'imported', NOW(), '深圳', '制造业', 20000.00, '制造业导入线索', 'admin', NOW());

-- 注意：这里的lead_id设为NULL是因为线索池中的线索可能还没有对应的crm_business_leads记录
-- 实际使用时，可以根据业务需求决定是否需要关联现有线索

-- ----------------------------
-- 4. 权限相关SQL（如果需要）
-- ----------------------------

-- 线索池管理权限
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
-- ('线索池管理', 线索管理菜单ID, 2, 'leadPool', 'crm/leadPool/index', 1, 0, 'C', '0', '0', 'crm:leadPool:list', 'pool', 'admin', NOW(), '', NULL, '线索池管理菜单');

-- 分配记录权限  
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
-- ('分配记录', 线索管理菜单ID, 3, 'assignmentRecords', 'crm/assignmentRecords/index', 1, 0, 'C', '0', '0', 'crm:assignmentRecords:list', 'log', 'admin', NOW(), '', NULL, '分配记录菜单');
