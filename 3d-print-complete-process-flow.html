<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D打印服务完整流程 - 从模型上传到商机转化</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        h1 {
            margin: 0;
            font-size: 2.8em;
            font-weight: 700;
        }
        
        .subtitle {
            font-size: 1.4em;
            opacity: 0.95;
            margin-top: 15px;
            font-weight: 300;
        }
        
        .overview {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
        }
        
        .overview-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .overview-text {
            flex: 1;
            min-width: 300px;
        }
        
        .overview-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .stat-card {
            background: #f1f3f4;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            min-width: 150px;
        }
        
        .stat-number {
            font-size: 2.2em;
            font-weight: 700;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .flow-container {
            display: flex;
            flex-direction: column;
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .step {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }
        
        .step:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 6px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }
        
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            margin-right: 20px;
            flex-shrink: 0;
        }
        
        .step-title {
            font-size: 1.8em;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .step-content {
            padding-left: 62px;
        }
        
        .step-description {
            margin-bottom: 25px;
            font-size: 1.15em;
            color: #555;
            line-height: 1.7;
        }
        
        .diagram-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .diagram-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 25px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 15px;
            overflow-x: auto;
            margin: 20px 0;
            line-height: 1.5;
        }
        
        .code-block pre {
            margin: 0;
            white-space: pre-wrap;
        }
        
        .component-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 25px 0;
        }
        
        .component-card {
            background: #e3f2fd;
            border-left: 5px solid #2196f3;
            padding: 20px;
            border-radius: 8px;
            flex: 1;
            min-width: 250px;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
        }
        
        .component-title {
            font-weight: 600;
            color: #0d47a1;
            margin-top: 0;
            font-size: 1.2em;
        }
        
        .data-flow {
            background: #e8f5e9;
            border-left: 5px solid #4caf50;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
        }
        
        .api-endpoint {
            background: #fff3e0;
            border-left: 5px solid #ff9800;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
            line-height: 1.6;
        }
        
        .business-value {
            background: #f3e5f5;
            border-left: 5px solid #9c27b0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(156, 39, 176, 0.1);
        }
        
        .next-step {
            text-align: center;
            margin-top: 35px;
            padding: 25px;
            background: #e1f5fe;
            border-radius: 12px;
            font-size: 1.3em;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.15);
        }
        
        footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            color: #7f8c8d;
            font-size: 1em;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .mermaid {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            min-height: 200px;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }
            
            h1 {
                font-size: 2.2em;
            }
            
            .step-content {
                padding-left: 0;
            }
            
            .overview-content {
                flex-direction: column;
            }
        }
    </style>
    <script src="./bin/工作计划/js/mermaid.min.js"></script>
</head>
<body>
    <header>
        <h1>3D打印服务完整流程</h1>
        <div class="subtitle">从模型上传到商机转化的端到端业务流程</div>
    </header>
    
    <div class="overview">
        <div class="overview-content">
            <div class="overview-text">
                <h2>流程概览</h2>
                <p>本流程描述了客户从上传3D模型到系统生成商机的完整业务过程。该流程整合了文件上传、模型分析、参数选择、报价生成、订单创建和商机转化等多个环节，实现了从潜在客户到销售机会的完整转化路径。</p>
            </div>
            <div class="overview-stats">
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">核心步骤</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">3+</div>
                    <div class="stat-label">系统模块</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2</div>
                    <div class="stat-label">业务转化</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="flow-container">
        <div class="step">
            <div class="step-header">
                <div class="step-number">1</div>
                <h2 class="step-title">创建询价单并上传3D模型</h2>
            </div>
            <div class="step-content">
                <p class="step-description">
                    客户访问3D打印报价页面，系统自动创建一个空白的询价单。客户通过上传区域上传3D模型文件（支持STL、OBJ、FBX等格式），每上传一个模型，询价单中就会增加一行记录。上传的模型会显示缩略图，客户可以点击缩略图查看详细的3D模型展示。
                </p>
                
                <div class="diagram-container">
                    <h3 class="diagram-title">询价单创建与模型上传流程</h3>
                    <div class="mermaid">
flowchart TD
    A[客户访问报价页面] --> B[系统创建空白询价单]
    B --> C[显示上传区域]
    C --> D[客户上传3D模型文件]
    D --> E{文件格式检查}
    E -->|格式正确| F[解析模型信息]
    E -->|格式错误| G[显示错误提示]
    F --> H[在询价单中添加模型行]
    H --> I[显示模型缩略图]
    I --> J[客户可继续上传更多模型]
    J --> K{客户操作选择}
    K -->|点击缩略图| L[查看详细3D模型展示]
    K -->|继续上传| D
    K -->|选择参数| M[进入参数选择阶段]
                    </div>
                </div>
                
                <div class="component-list">
                    <div class="component-card">
                        <h3 class="component-title">前端组件</h3>
                        <p>UploadSection.vue - 文件上传区域</p>
                        <p>QuoteTable.vue - 询价单表格</p>
                        <p>ModelViewer.vue - 3D模型查看器</p>
                    </div>
                    <div class="component-card">
                        <h3 class="component-title">后端处理</h3>
                        <p>模型解析服务 - 提取尺寸、体积、表面积</p>
                        <p>文件处理服务 - 生成缩略图</p>
                    </div>
                </div>
                
                <div class="code-block">
<pre>// 上传成功后的数据结构示例
{
  index: 1,
  modelInfo: {
    name: '2.外壳',
    unit: 'mm',
    dimensions: '275.00×103.96×103.92mm',
    volume: '130384.30mm³',
    surfaceArea: '174166.02mm²'
  },
  thumbnail: 'path/to/thumbnail.jpg',
  quantity: 1,
  unitPrice: 0, // 初始价格为0，待参数选择后计算
  totalPrice: 0
}</pre>
                </div>
                
                <div class="data-flow">
                    <strong>数据流:</strong> 客户访问 → 创建空白询价单 → 上传模型文件 → 解析模型信息 → 更新询价单 → 显示缩略图
                </div>
                
                <div class="business-value">
                    <strong>业务价值:</strong> 通过创建空白询价单并逐步添加模型的方式，让客户可以清晰地看到当前的询价内容。缩略图展示和详细3D预览功能增强了用户体验，便于客户确认模型信息。
                </div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-header">
                <div class="step-number">2</div>
                <h2 class="step-title">选择打印参数</h2>
            </div>
            <div class="step-content">
                <p class="step-description">
                    客户为询价单中的每个模型选择打印参数，包括材料、颜色、工艺等。这是必选步骤，客户必须为每个模型选择参数后才能生成报价。系统根据选择的材料和模型体积计算价格。
                </p>
                
                <div class="diagram-container">
                    <h3 class="diagram-title">参数选择流程</h3>
                    <div class="mermaid">
flowchart TD
    A[客户点击选择参数] --> B[打开参数选择对话框]
    B --> C[显示材料类型选择]
    C --> D[客户选择材料类型]
    D --> E[加载该类型材料列表]
    E --> F[显示材料详细信息]
    F --> G[客户选择具体材料]
    G --> H[显示后处理选项]
    H --> I[客户选择后处理工艺]
    I --> J[系统计算价格]
    J --> K[更新询价单价格]
    K --> L[客户确认选择]
                    </div>
                </div>
                
                <div class="component-list">
                    <div class="component-card">
                        <h3 class="component-title">前端组件</h3>
                        <p>参数选择对话框 - el-dialog</p>
                        <p>材料类型选择 - el-button-group</p>
                        <p>材料列表 - el-table</p>
                        <p>后处理选项 - el-checkbox-group</p>
                    </div>
                    <div class="component-card">
                        <h3 class="component-title">后端处理</h3>
                        <p>材料参数表 - print materials</p>
                        <p>价格计算引擎 - 基于体积和材料单价</p>
                        <p>技术规格数据 - tech specs</p>
                    </div>
                </div>
                
                <div class="code-block">
<pre>// 价格计算逻辑示例
// 1. 提取模型体积（从mm³转换为cm³）
const volumeInCm3 = volumeInMm3 / 1000;

// 2. 计算单价（体积 × 材料单价）
unitPrice = volumeInCm3 × materialPrice;

// 3. 计算总价（单价 × 数量）
totalPrice = unitPrice × quantity;</pre>
                </div>
                
                <div class="data-flow">
                    <strong>数据流:</strong> 客户选择参数 → 系统匹配材料 → 计算价格 → 更新询价单 → 显示价格明细
                </div>
                
                <div class="business-value">
                    <strong>业务价值:</strong> 通过强制参数选择确保每个模型都有明确的报价基础。基于体积和材料单价的自动计算保证了报价的准确性和一致性，提高了报价效率。
                </div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-header">
                <div class="step-number">3</div>
                <h2 class="step-title">生成报价并提交订单</h2>
            </div>
            <div class="step-content">
                <p class="step-description">
                    系统根据客户选择的参数和模型信息生成详细的报价单，显示在询价单右侧。客户确认报价信息后点击"立即下单"按钮提交订单，系统创建正式的3D打印订单记录。
                </p>
                
                <div class="diagram-container">
                    <h3 class="diagram-title">报价生成与订单提交流程</h3>
                    <div class="mermaid">
sequenceDiagram
    actor C as 客户
    participant Q as 询价单页面
    participant P as 参数选择对话框
    participant O as 订单服务
    participant S as 存储服务
    
    C->>P: 为所有模型选择参数
    P->>Q: 更新询价单价格
    Q->>C: 显示完整报价明细
    C->>Q: 点击"立即下单"
    Q->>O: 提交订单数据
    activate O
    O->>S: 上传模型文件到OSS
    activate S
    S-->>O: 返回文件存储URL
    deactivate S
    O->>O: 创建订单记录
    O-->>Q: 返回订单号
    deactivate O
    Q->>C: 显示订单提交成功
                    </div>
                </div>
                
                <div class="component-list">
                    <div class="component-card">
                        <h3 class="component-title">前端操作</h3>
                        <p>handlePlaceOrder - 处理订单提交</p>
                        <p>createPrintOrder API 调用</p>
                        <p>报价明细展示</p>
                    </div>
                    <div class="component-card">
                        <h3 class="component-title">后端处理</h3>
                        <p>CrmOrderController.createPrintOrder - 接收订单请求</p>
                        <p>CrmOrderServiceImpl.createPrintOrder - 创建订单记录</p>
                        <p>OSS文件存储服务</p>
                    </div>
                </div>
                
                <div class="code-block">
<pre>// 报价数据结构
quoteData: {
  quoteNo: 'SQ' + Date.now(), // 生成询价单号
  items: [
    {
      modelName: '2.外壳',
      modelInfo: {
        dimensions: '275.00×103.96×103.92mm',
        volume: '130384.30mm³',
        surfaceArea: '174166.02mm²'
      },
      material: 'PLA材料',
      quantity: 1,
      unitPrice: 13.04,
      totalPrice: 13.04,
      processOptions: ['喷漆', '丝印']
    }
  ],
  totalAmount: 13.04
}</pre>
                </div>
                
                <div class="api-endpoint">
                    // 后端订单创建核心逻辑<br>
                    CrmOrder order = new CrmOrder();<br>
                    order.setOrderNo(generateOrderNo());<br>
                    order.setQuoteNo(quoteJson.getString("quoteNo"));<br>
                    order.setTotalAmount(quoteJson.getBigDecimal("totalAmount"));<br>
                    order.setStatus("pending");<br>
                    // 插入订单记录<br>
                    crmOrderMapper.insertCrmOrder(order);
                </div>
                
                <div class="data-flow">
                    <strong>数据流:</strong> 客户确认报价 → 提交订单数据 → 上传文件到OSS → 创建订单记录 → 返回订单号
                </div>
                
                <div class="business-value">
                    <strong>业务价值:</strong> 自动生成详细的报价单，确保客户清楚了解费用构成。订单提交后文件永久存储，保障数据安全，为后续生产提供准确依据。
                </div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-header">
                <div class="step-number">4</div>
                <h2 class="step-title">系统创建商机记录</h2>
            </div>
            <div class="step-content">
                <p class="step-description">
                    系统自动将订单转化为商机记录，便于销售团队跟进和管理。商机包含客户信息、预计金额、订单详情等关键信息。
                </p>
                
                <div class="diagram-container">
                    <h3 class="diagram-title">商机转化流程</h3>
                    <div class="mermaid">
flowchart TD
    A[订单创建完成] --> B{是否为新客户}
    B -->|是| C[创建新客户档案]
    B -->|否| D[关联现有客户]
    C --> E[生成商机记录]
    D --> E[生成商机记录]
    E --> F[设置商机状态为新商机]
    F --> G[分配给销售团队]
    G --> H[发送通知给销售人员]
                    </div>
                </div>
                
                <div class="component-list">
                    <div class="component-card">
                        <h3 class="component-title">商机数据</h3>
                        <p>商机名称 - 基于订单信息</p>
                        <p>客户信息 - 关联客户</p>
                        <p>预计金额 - 订单总金额</p>
                        <p>来源 - 3D打印订单</p>
                    </div>
                    <div class="component-card">
                        <h3 class="component-title">业务流程</h3>
                        <p>订单 → 商机转化</p>
                        <p>客户档案管理</p>
                        <p>销售分配机制</p>
                    </div>
                </div>
                
                <div class="code-block">
<pre>// 商机创建逻辑（概念性）
BusinessOpportunity opportunity = new BusinessOpportunity();
opportunity.setName("3D打印订单-" + order.getOrderNo());
opportunity.setCustomerId(order.getCustomerId());
opportunity.setCustomerName(order.getCustomerName());
opportunity.setEstimatedAmount(order.getTotalAmount());
opportunity.setSource("3D_PRINT_ORDER");
opportunity.setStatus("new"); // 新商机状态
// 保存商机记录
businessOpportunityService.insert(opportunity);</pre>
                </div>
                
                <div class="data-flow">
                    <strong>数据流:</strong> 订单创建 → 商机生成 → 销售团队通知 → 商机池管理 → 销售跟进
                </div>
                
                <div class="business-value">
                    <strong>业务价值:</strong> 自动生成销售商机，提高销售转化率，完善CRM业务闭环。系统化商机管理提升销售效率。
                </div>
            </div>
        </div>
        
        <div class="step">
            <div class="step-header">
                <div class="step-number">5</div>
                <h2 class="step-title">后续流程跟进</h2>
            </div>
            <div class="step-content">
                <p class="step-description">
                    订单进入生产排程阶段，通知生产部门准备打印材料和设备。销售团队开始跟进商机，推进客户关系发展。
                </p>
                
                <div class="diagram-container">
                    <h3 class="diagram-title">完整业务流程</h3>
                    <div class="mermaid">
flowchart TD
    A[客户访问3D打印报价页面] --> B[创建空白询价单]
    B --> C[上传3D模型文件]
    C --> D[选择打印参数]
    D --> E[生成报价单]
    E --> F[客户确认并提交订单]
    F --> G[系统创建订单记录]
    G --> H[系统创建商机]
    H --> I[销售团队跟进]
    G --> J[生产部门排程]
    J --> K[打印生产]
    K --> L[质量检测]
    L --> M[发货交付]
    I --> N[商机推进]
    N --> O[合同签订]
    O --> P[回款管理]
                    </div>
                </div>
                
                <div class="component-list">
                    <div class="component-card">
                        <h3 class="component-title">生产流程</h3>
                        <p>生产排程系统</p>
                        <p>打印任务分配</p>
                        <p>质量控制系统</p>
                    </div>
                    <div class="component-card">
                        <h3 class="component-title">销售流程</h3>
                        <p>商机跟进管理</p>
                        <p>合同签署流程</p>
                        <p>回款管理系统</p>
                    </div>
                </div>
                
                <div class="next-step">
                    <p>下一步：订单进入生产排程阶段，通知生产部门准备打印材料和设备</p>
                    <p>销售团队收到新商机通知，开始客户跟进</p>
                </div>
            </div>
        </div>
    </div>
    
    <footer>
        <p>3D打印服务完整流程文档 | 基于CRM4系统架构</p>
        <p>此流程确保从客户上传模型到生成商机的完整业务闭环，实现端到端的数字化管理</p>
        <p>版本: 1.0 | 更新时间: 2025年7月</p>
    </footer>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                wrap: true
            }
        });
    </script>
</body>
</html>