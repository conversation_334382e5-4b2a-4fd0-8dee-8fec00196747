<template>
  <div>
    <div class="upload-section">
      <slot name="header">
        <div class="section-header">
          <h2>上传图纸以及选择加工参数</h2>
          <p class="sub-title">请上传您的3D模型文件，我们将为您提供精准报价</p>
        </div>
      </slot>

      <p class="warning-text">
        尊敬的客户，严禁利用本平台下单生产违禁产品，如管制刀具、枪支弹药等。一经发现，立即封号并上报公安局。感谢您的支持与合作！
      </p>

      <el-upload
        class="upload-area"
        drag
        multiple
        :limit="20"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :http-request="handleFileUpload"
        :on-exceed="handleExceed"
        accept=".stl,.obj,.fbx,.gltf,.glb,.ply,.dae,.step,.stp,.zip,.rar"
      >
        <div class="upload-content">
          <el-icon class="upload-icon">
            <Upload />
          </el-icon>
          <div class="upload-text">拖拽或者上传3D图纸文件或压缩包</div>
          <el-button type="primary" class="upload-button">选择3D图纸文件</el-button>
        </div>

        <div class="upload-tips">
          <p>速加将对您的文件绝对保密，保护您的知识产权</p>
          <p>支持格式：STL(.stl)、OBJ(.obj)、FBX(.fbx)、GLTF(.gltf, .glb)、PLY(.ply)、COLLADA(.dae)、STEP(.step, .stp)</p>
          <p class="format-note">推荐使用STL或GLTF格式，具有更好的兼容性</p>
          <p class="format-note">注意：STEP格式可上传但暂不支持3D预览，建议转换为STL格式</p>
          <p>可压缩包（zip、rar）直接上传</p>
        </div>

        <div class="upload-limit">
          单次上传文件 ≤20 个，单个文件大小<100M
        </div>
      </el-upload>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FileProcessor, SUPPORTED_3D_FORMATS } from '@/utils/fileProcessor';
import { Upload } from '@element-plus/icons-vue';
import { ElMessage, type UploadRequestOptions } from 'element-plus';

// 定义上传成功事件的数据类型
interface UploadSuccessData {
  url: string // 本地文件的临时URL (blob:)
  thumbnail: string
  file: File
  processedFile: {
    name: string
    dimensions: string
    volume: string
    surfaceArea: string
  }
}

const emit = defineEmits<{
  (e: 'upload-success', data: UploadSuccessData): void
  (e: 'error', error: unknown): void
}>()

// 检查文件类型和大小
const beforeUpload = (file: File): boolean => {
  // 检查文件大小
  const maxSize = 100 * 1024 * 1024 // 100MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过 100MB!')
    return false
  }

  // 检查文件类型 - 允许STEP格式上传，但会在后续处理中给出提示
  const extension = file.name.toLowerCase().split('.').pop()
  const allowedFormats = [...SUPPORTED_3D_FORMATS, 'step', 'stp']
  if (!extension || !allowedFormats.includes(extension)) {
    ElMessage.error(`不支持的文件格式: ${extension}，请上传 ${allowedFormats.join(', ')} 格式的文件`)
    return false
  }

  return true
}

// 处理文件超出限制
const handleExceed = (files: File[]) => {
  ElMessage.warning(`当前限制选择 20 个文件，本次选择了 ${files.length} 个文件，已忽略超出部分`)
}

// 自定义文件上传处理（不实际上传，只在本地处理）
const handleFileUpload = async (options: UploadRequestOptions) => {
  const file = options.file
  console.log('[UploadSection] 开始本地文件处理:', file.name)
  
  try {
    // 创建本地临时URL
    const localUrl = URL.createObjectURL(file)
    
    // 处理文件信息
    const processedFile = await FileProcessor.process(file)
    const modelInfo = await FileProcessor.extractModelInfo(
      file,
      file.name.split('.').pop() || ''
    )

    // 构建完整的响应数据
    const result: UploadSuccessData = {
      url: localUrl, // 使用本地临时URL
      thumbnail: processedFile.thumbnail || '',
      file: file,
      processedFile: {
        name: file.name,
        dimensions: modelInfo.dimensions || '待计算',
        volume: modelInfo.volume || '待计算',
        surfaceArea: modelInfo.surfaceArea || '待计算'
      }
    }

    // 发送成功事件到父组件
    emit('upload-success', result)
    console.log('[UploadSection] 本地文件处理完成，已发送数据到父组件:', result)
  } catch (error) {
    console.error('[UploadSection] 本地文件处理失败:', error)
    handleUploadError(error)
  }
}

// 处理上传错误
const handleUploadError = (error: unknown) => {
  console.error('[UploadSection] 上传失败:', error)
  ElMessage.error('上传失败: ' + (error instanceof Error ? error.message : '未知错误'))
  emit('error', error)
}
</script>

<style scoped>
.upload-section {
  padding: 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-header h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 10px;
}

.section-header .sub-title {
  font-size: 14px;
  color: #909399;
}

.warning-text {
  color: #f02b2b;
  background-color: #feeaea;
  text-align: center;
  margin-bottom: 20px;
  padding: 10px;
}

.format-note {
  color: #E6A23C;
  font-size: 12px;
  font-style: italic;
}

.upload-area {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #409EFF;
  background: #f5f7fa;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.upload-icon {
  font-size: 48px;
  color: #909399;
}

.upload-text {
  font-size: 16px;
  color: #606266;
}

.upload-button {
  padding: 12px 24px;
  font-size: 14px;
}

.upload-tips {
  margin-top: 20px;
  color: #909399;
  font-size: 13px;
  line-height: 1.8;
}

.upload-limit {
  margin-top: 15px;
  color: #909399;
  font-size: 12px;
}
</style>
