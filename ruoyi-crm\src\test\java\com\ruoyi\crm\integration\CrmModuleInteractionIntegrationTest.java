package com.ruoyi.crm.integration;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.domain.entity.CrmContract;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord;
import com.ruoyi.common.domain.entity.CrmLeads;
import com.ruoyi.common.domain.entity.CrmOpportunity;
import com.ruoyi.common.domain.entity.CrmPaymentRecord;
import com.ruoyi.common.domain.entity.CrmTeam;
import com.ruoyi.common.domain.entity.CrmVisitPlan;
import com.ruoyi.common.mapper.CrmContactsMapper;
import com.ruoyi.common.mapper.CrmContractMapper;
import com.ruoyi.common.mapper.CrmCustomerFollowupRecordMapper;
import com.ruoyi.common.mapper.CrmCustomerMapper;
import com.ruoyi.common.mapper.CrmLeadsMapper;
import com.ruoyi.common.mapper.CrmOpportunityMapper;
import com.ruoyi.common.mapper.CrmPaymentRecordMapper;
import com.ruoyi.common.mapper.CrmVisitPlanMapper;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.common.service.ICrmCustomerFollowupRecordService;
import com.ruoyi.common.service.ICrmCustomerService;
import com.ruoyi.common.service.ICrmTeamService;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmContractService;
import com.ruoyi.crm.service.ICrmCustomerPoolService;
import com.ruoyi.crm.service.ICrmLeadService;
import com.ruoyi.crm.service.ICrmOpportunityService;
import com.ruoyi.crm.service.ICrmPaymentService;
import com.ruoyi.crm.service.ICrmVisitPlanService;

/**
 * CRM模块交互集成测试
 * 
 * 测试不同CRM模块之间的数据流转和业务协作：
 * 1. 线索(Lead) -> 客户(Customer) -> 商机(Opportunity) -> 合同(Contract) -> 回款(Payment)
 * 2. 客户管理 <-> 联系人管理 <-> 拜访计划
 * 3. 客户跟进记录 <-> 拜访计划 <-> 商机管理
 * 4. 合同管理 <-> 回款管理 <-> 对账管理
 * 5. 团队管理 <-> 客户分配 <-> 权限控制
 * 6. 公海管理 <-> 客户流转 <-> 业务日志
 * 
 * 验证跨模块的数据一致性、事务完整性、业务规则执行
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CrmModuleInteractionIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    // 各模块服务
    @Autowired private ICrmLeadService leadService;
    @Autowired private ICrmCustomerService customerService;
    @Autowired private ICrmContactsService contactsService;
    @Autowired private ICrmOpportunityService opportunityService;
    @Autowired private ICrmContractService contractService;
    @Autowired private ICrmPaymentService paymentService;
    @Autowired private ICrmVisitPlanService visitPlanService;
    @Autowired private ICrmCustomerFollowupRecordService followupRecordService;
    @Autowired private ICrmCustomerPoolService customerPoolService;
    @Autowired private ICrmTeamService teamService;

    // 各模块映射器
    @Autowired private CrmLeadsMapper leadMapper;
    @Autowired private CrmCustomerMapper customerMapper;
    @Autowired private CrmContactsMapper contactsMapper;
    @Autowired private CrmOpportunityMapper opportunityMapper;
    @Autowired private CrmContractMapper contractMapper;
    @Autowired private CrmPaymentRecordMapper paymentMapper;
    @Autowired private CrmVisitPlanMapper visitPlanMapper;
    @Autowired private CrmCustomerFollowupRecordMapper followupRecordMapper;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    // 测试数据 - 完整的业务流程数据
    private CrmLeads testLead;
    private CrmCustomer testCustomer;
    private CrmContacts testContact;
    private CrmOpportunity testOpportunity;
    private CrmContract testContract;
    private CrmPaymentRecord testPayment;
    private CrmVisitPlan testVisitPlan;
    private CrmCustomerFollowupRecord testFollowupRecord;

    @BeforeEach
    void setUp() {
        super.setUpBase();
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
        
        // 初始化完整的测试数据链路
        initTestDataChain();
    }

    @AfterEach
    void tearDown() {
        super.tearDownBase();
    }

    // ==================== 核心业务流程测试 ====================

    @Test
    @Order(1)
    @WithMockUser(username = "admin", authorities = {"crm:lead:convert", "crm:customer:add"})
    @DisplayName("业务流程1 - 线索转换为客户")
    void testBusinessFlow_LeadToCustomer() throws Exception {
        // 1. 创建线索
        leadMapper.insertCrmLeads(testLead);

        // 2. 线索转换为客户
        mockMvc.perform(post("/crm/lead/{id}/convert", testLead.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value(containsString("转换成功")));

        // 3. 验证客户已创建
        mockMvc.perform(get("/crm/customer/list")
                .param("customerName", testLead.getCustomerName())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows[0].customerName").value(testLead.getCustomerName()));

        // 4. 验证线索状态已更新为已转换
        mockMvc.perform(get("/crm/lead/{id}", testLead.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.status").value("CONVERTED"));
    }

    @Test
    @Order(2)
    @WithMockUser(username = "admin", authorities = {"crm:customer:add", "crm:opportunity:add"})
    @DisplayName("业务流程2 - 客户创建商机")
    void testBusinessFlow_CustomerToOpportunity() throws Exception {
        // 1. 创建客户
        customerMapper.insertCrmCustomer(testCustomer);

        // 2. 为客户创建商机
        CrmOpportunity opportunity = createTestOpportunity(testCustomer.getId(), testCustomer.getCustomerName());
        String requestBody = objectMapper.writeValueAsString(opportunity);

        mockMvc.perform(post("/crm/opportunity")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 验证商机与客户关联
        mockMvc.perform(get("/crm/opportunity/list")
                .param("customerId", testCustomer.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows[0].customerId").value(testCustomer.getId()));

        // 4. 验证客户的商机统计更新
        mockMvc.perform(get("/crm/customer/{id}", testCustomer.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(3)
    @WithMockUser(username = "admin", authorities = {"crm:opportunity:add", "crm:contract:add"})
    @DisplayName("业务流程3 - 商机转换为合同")
    void testBusinessFlow_OpportunityToContract() throws Exception {
        // 1. 创建客户和商机
        customerMapper.insertCrmCustomer(testCustomer);
        testOpportunity.setCustomerId(testCustomer.getId());
        testOpportunity.setCustomerName(testCustomer.getCustomerName());
        opportunityMapper.insertCrmOpportunity(testOpportunity);

        // 2. 商机转换为合同
        CrmContract contract = createTestContract(testCustomer.getId(), testOpportunity.getId());
        String requestBody = objectMapper.writeValueAsString(contract);

        mockMvc.perform(post("/crm/contract")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 更新商机状态为已成交
        testOpportunity.setOpportunityStage("WON");
        testOpportunity.setExpectedCloseDate(new Date());
        String opportunityBody = objectMapper.writeValueAsString(testOpportunity);

        mockMvc.perform(put("/crm/opportunity")
                .content(opportunityBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 4. 验证合同与商机关联
        mockMvc.perform(get("/crm/contract/list")
                .param("opportunityId", testOpportunity.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows[0].opportunityId").value(testOpportunity.getId()));
    }

    @Test
    @Order(4)
    @WithMockUser(username = "admin", authorities = {"crm:contract:add", "crm:payment:add"})
    @DisplayName("业务流程4 - 合同生成回款计划")
    void testBusinessFlow_ContractToPayment() throws Exception {
        // 1. 创建完整的业务链路
        customerMapper.insertCrmCustomer(testCustomer);
        testOpportunity.setCustomerId(testCustomer.getId());
        opportunityMapper.insertCrmOpportunity(testOpportunity);
        testContract.setCustomerName(testCustomer.getCustomerName());
        testContract.setOpportunityName(testOpportunity.getOpportunityName());
        contractMapper.insertContract(testContract);

        // 2. 基于合同创建回款记录
        CrmPaymentRecord payment = createTestPayment(testContract.getId(), testCustomer.getId());
        String requestBody = objectMapper.writeValueAsString(payment);

        mockMvc.perform(post("/crm/payment")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 验证回款与合同关联
        mockMvc.perform(get("/crm/payment/list")
                .param("contractId", testContract.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows[0].contractId").value(testContract.getId()));

        // 4. 验证合同回款统计更新
        mockMvc.perform(get("/crm/contract/{id}", testContract.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 客户关系管理交互测试 ====================

    @Test
    @Order(10)
    @WithMockUser(username = "admin", authorities = {"crm:customer:add", "crm:contacts:add", "crm:visitPlan:add"})
    @DisplayName("客户关系 - 客户+联系人+拜访计划联动")
    void testCustomerRelationship_CustomerContactVisit() throws Exception {
        // 1. 创建客户
        customerMapper.insertCrmCustomer(testCustomer);

        // 2. 为客户添加联系人
        CrmContacts contact = createTestContact(testCustomer.getId());
        String contactBody = objectMapper.writeValueAsString(contact);

        mockMvc.perform(post("/crm/contacts")
                .content(contactBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 基于客户和联系人创建拜访计划
        CrmVisitPlan visitPlan = createTestVisitPlan(testCustomer.getId(), contact.getId());
        String visitBody = objectMapper.writeValueAsString(visitPlan);

        mockMvc.perform(post("/crm/visitPlan")
                .content(visitBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 4. 验证拜访计划关联了正确的客户和联系人
        mockMvc.perform(get("/crm/visitPlan/listByObject")
                .param("objectType", "customer")
                .param("objectId", testCustomer.getId().toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0].customerId").value(testCustomer.getId()))
                .andExpect(jsonPath("$.data[0].contactId").value(contact.getId()));

        // 5. 查询客户的所有相关信息（联系人、拜访计划）
        mockMvc.perform(get("/crm/customer/{id}/details", testCustomer.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(11)
    @WithMockUser(username = "admin", authorities = {"crm:followup:add", "crm:visitPlan:add"})
    @DisplayName("跟进管理 - 跟进记录+拜访计划联动")
    void testFollowupManagement_FollowupAndVisit() throws Exception {
        customerMapper.insertCrmCustomer(testCustomer);

        // 1. 创建跟进记录
        CrmCustomerFollowupRecord followup = createTestFollowupRecord(testCustomer.getId());
        followupRecordMapper.insertCrmCustomerFollowupRecord(followup);

        // 2. 基于跟进记录创建拜访计划
        CrmVisitPlan visitPlan = createTestVisitPlan(testCustomer.getId(), null);
        visitPlan.setRemark("基于跟进记录：" + followup.getFollowupContent());
        String visitBody = objectMapper.writeValueAsString(visitPlan);

        mockMvc.perform(post("/crm/visitPlan")
                .content(visitBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 完成拜访后更新跟进记录
        Map<String, Object> completeParams = new HashMap<>();
        completeParams.put("followupContent", "拜访完成，客户表示满意，计划下次深入沟通");

        String completeBody = objectMapper.writeValueAsString(completeParams);

        mockMvc.perform(post("/crm/visitPlan/complete/{id}", visitPlan.getId())
                .content(completeBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 4. 验证跟进记录和拜访计划的关联关系
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("customerId", testCustomer.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 公海流转交互测试 ====================

    @Test
    @Order(20)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit", "crm:followup:add"})
    @DisplayName("公海流转 - 客户流转+跟进记录+业务日志")
    void testCustomerPool_FlowWithLogs() throws Exception {
        customerMapper.insertCrmCustomer(testCustomer);

        // 1. 客户放入公海
        mockMvc.perform(post("/crm/customer/{id}/return", testCustomer.getId())
                .param("reason", "LONG_TERM_NO_FOLLOW")
                .param("remark", "超过30天未跟进，自动放入公海")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 2. 其他用户认领客户
        mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 认领后立即创建跟进记录
        CrmCustomerFollowupRecord followup = createTestFollowupRecord(testCustomer.getId());
        followup.setFollowupContent("认领后首次跟进：刚从公海认领该客户，开始建立联系");
        String followupBody = objectMapper.writeValueAsString(followup);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(followupBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 4. 验证公海操作日志记录
        mockMvc.perform(get("/crm/customer/pool/operationLogs")
                .param("customerId", testCustomer.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 5. 验证客户所有权变更历史
        mockMvc.perform(get("/crm/customer/{id}/ownershipHistory", testCustomer.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 团队协作交互测试 ====================

    @Test
    @Order(30)
    @WithMockUser(username = "admin", authorities = {"crm:team:manage", "crm:customer:assign"})
    @DisplayName("团队协作 - 团队管理+客户分配+权限控制")
    void testTeamCollaboration_TeamCustomerPermission() throws Exception {
        customerMapper.insertCrmCustomer(testCustomer);

        // 1. 创建团队
        CrmTeam team = createTestTeam();
        String teamBody = objectMapper.writeValueAsString(team);

        mockMvc.perform(post("/crm/team")
                .content(teamBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 2. 将客户分配给团队成员
        Map<String, Object> assignParams = new HashMap<>();
        assignParams.put("customerId", testCustomer.getId());
        assignParams.put("newOwnerId", 2L);
        assignParams.put("reason", "团队负载均衡");

        String assignBody = objectMapper.writeValueAsString(assignParams);

        mockMvc.perform(post("/crm/customer/assign")
                .content(assignBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 验证团队成员权限
        mockMvc.perform(get("/crm/customer/list")
                .param("teamId", team.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 4. 验证客户分配历史
        mockMvc.perform(get("/crm/customer/{id}/assignmentHistory", testCustomer.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 数据一致性交互测试 ====================

    @Test
    @Order(40)
    @WithMockUser(username = "admin", authorities = {"crm:customer:remove", "crm:opportunity:list", "crm:contract:list"})
    @DisplayName("数据一致性 - 删除客户的级联影响")
    void testDataConsistency_CustomerDeletionCascade() throws Exception {
        // 1. 创建完整的业务链路
        customerMapper.insertCrmCustomer(testCustomer);
        testOpportunity.setCustomerId(testCustomer.getId());
        opportunityMapper.insertCrmOpportunity(testOpportunity);
        testContract.setCustomerName(testCustomer.getCustomerName());
        testContract.setOpportunityName(testOpportunity.getOpportunityName());
        contractMapper.insertContract(testContract);

        // 2. 尝试删除有关联数据的客户（应该失败或提供警告）
        mockMvc.perform(delete("/crm/customer/{ids}", testCustomer.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(result -> {
                    String response = result.getResponse().getContentAsString();
                    // 验证是否有适当的错误消息或警告
                    assertTrue(response.contains("关联") || response.contains("影响") || response.contains("code\":500"),
                            "删除有关联数据的客户应该有适当的提示");
                });

        // 3. 验证关联数据仍然存在
        mockMvc.perform(get("/crm/opportunity/{id}", testOpportunity.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.id").value(testOpportunity.getId()));

        mockMvc.perform(get("/crm/contract/{id}", testContract.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.id").value(testContract.getId()));
    }

    @Test
    @Order(41)
    @WithMockUser(username = "admin", authorities = {"crm:opportunity:edit", "crm:contract:list"})
    @DisplayName("数据一致性 - 商机状态变更的影响")
    void testDataConsistency_OpportunityStatusChange() throws Exception {
        // 1. 创建商机和合同
        customerMapper.insertCrmCustomer(testCustomer);
        testOpportunity.setCustomerId(testCustomer.getId());
        opportunityMapper.insertCrmOpportunity(testOpportunity);
        testContract.setOpportunityName(testOpportunity.getOpportunityName());
        contractMapper.insertContract(testContract);

        // 2. 将商机状态改为失败
        testOpportunity.setOpportunityStage("LOST");
        testOpportunity.setRemarks("价格太高");
        String opportunityBody = objectMapper.writeValueAsString(testOpportunity);

        mockMvc.perform(put("/crm/opportunity")
                .content(opportunityBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 验证关联合同状态是否受影响
        mockMvc.perform(get("/crm/contract/{id}", testContract.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.id").value(testContract.getId()));

        // 4. 检查是否有业务逻辑验证或警告
        mockMvc.perform(get("/crm/contract/list")
                .param("opportunityId", testOpportunity.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 性能和并发交互测试 ====================

    @Test
    @Order(50)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list", "crm:opportunity:list", "crm:contract:list"})
    @DisplayName("性能测试 - 跨模块关联查询")
    void testPerformance_CrossModuleQueries() throws Exception {
        // 创建一些测试数据
        setupPerformanceTestData();

        long startTime = System.currentTimeMillis();

        // 1. 查询客户及其关联的商机、合同信息
        mockMvc.perform(get("/crm/customer/{id}/fullInfo", testCustomer.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 2000, "跨模块关联查询应该在2秒内完成，实际用时: " + duration + "ms");
    }

    @Test
    @Order(51)
    @WithMockUser(username = "admin", authorities = {"crm:dashboard:view"})
    @DisplayName("性能测试 - 仪表板统计数据")
    void testPerformance_DashboardStatistics() throws Exception {
        long startTime = System.currentTimeMillis();

        // 查询仪表板所需的各种统计数据
        mockMvc.perform(get("/crm/dashboard/statistics")
                .param("dateRange", "2024-01-01,2024-12-31")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isMap());

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 3000, "仪表板统计查询应该在3秒内完成，实际用时: " + duration + "ms");
    }

    // ==================== 辅助方法 ====================

    private void initTestDataChain() {
        // 创建完整的业务数据链路
        testLead = createTestLead();
        testCustomer = createTestCustomer("测试客户公司", "13800138001", "<EMAIL>");
        testContact = createTestContact(null); // 客户ID将在运行时设置
        testOpportunity = createTestOpportunity(null, null); // 客户信息将在运行时设置
        testContract = createTestContract(null, null); // 客户和商机ID将在运行时设置
        testPayment = createTestPayment(null, null); // 合同和客户ID将在运行时设置
        testVisitPlan = createTestVisitPlan(null, null); // 客户和联系人ID将在运行时设置
        testFollowupRecord = createTestFollowupRecord(null); // 客户ID将在运行时设置
    }

    private CrmLeads createTestLead() {
        CrmLeads lead = new CrmLeads();
        lead.setLeadName("张三");
        lead.setCustomerName("测试公司");
        lead.setPhone("13800138001");
        lead.setEmail("<EMAIL>");
        lead.setLeadSource("网站询盘");
        lead.setCustomerIndustry("制造业");
        lead.setStatus("NEW");
        lead.setCreateTime(new Date());
        return lead;
    }

    private CrmCustomer createTestCustomer(String name, String mobile, String email) {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerName(name);
        customer.setMobile(mobile);
        customer.setEmail(email);
        customer.setResponsiblePersonId("1");
        customer.setCustomerIndustry("制造业");
        customer.setCustomerLevel("A级");
        customer.setCustomerSource("线索转换");
        customer.setStatus("1");
        customer.setDelFlag("0");
        customer.setCreateTime(new Date());
        return customer;
    }

    private CrmContacts createTestContact(Long customerId) {
        CrmContacts contact = new CrmContacts();
        // 注意：customerId通过关联表管理，这里不直接设置
        contact.setName("李四");
        contact.setPosition("技术总监");
        contact.setPhone("13900139001");
        contact.setEmail("<EMAIL>");
        contact.setIsKeyDecisionMaker("1"); // 字符串类型，1表示是
        contact.setCreateTime(new Date());
        return contact;
    }

    private CrmOpportunity createTestOpportunity(Long customerId, String customerName) {
        CrmOpportunity opportunity = new CrmOpportunity();
        opportunity.setCustomerId(customerId);
        opportunity.setCustomerName(customerName);
        opportunity.setOpportunityName("设备采购项目");
        opportunity.setOpportunityAmount(new BigDecimal("500000.00"));
        opportunity.setOpportunityStage("PROPOSAL");
        opportunity.setWinRate(new BigDecimal("60"));
        opportunity.setExpectedCloseDate(new Date(System.currentTimeMillis() + 30 * 24 * 60 * 60 * 1000L));
        opportunity.setRemarks("大型生产设备采购需求");
        opportunity.setCreateTime(new Date());
        return opportunity;
    }

    private CrmContract createTestContract(Long customerId, Long opportunityId) {
        CrmContract contract = new CrmContract();
        // 注意：CrmContract没有customerId和opportunityId字段，使用名称字段
        contract.setCustomerName("测试客户");
        contract.setOpportunityName("设备采购项目");
        contract.setContractName("设备采购合同");
        contract.setContractAmount(new BigDecimal("480000.00"));
        contract.setStartDate(new Date());
        contract.setEndDate(new Date(System.currentTimeMillis() + 365 * 24 * 60 * 60 * 1000L));
        contract.setStatus("SIGNED");
        contract.setCreateTime(new Date());
        return contract;
    }

    private CrmPaymentRecord createTestPayment(Long contractId, Long customerId) {
        CrmPaymentRecord payment = new CrmPaymentRecord();
        // 注意：CrmPaymentRecord没有contractId字段，但有customerId
        payment.setCustomerId(customerId);
        payment.setPaymentAmount(new BigDecimal("150000.00"));
        payment.setPaymentDate(new Date());
        payment.setPaymentMethod("银行转账");
        payment.setPaymentStatus("PAID");
        payment.setCreateTime(new Date());
        return payment;
    }

    private CrmVisitPlan createTestVisitPlan(Long customerId, Long contactId) {
        CrmVisitPlan plan = new CrmVisitPlan();
        plan.setCustomerId(customerId);
        plan.setContactId(contactId);
        plan.setVisitPlanName("项目跟进拜访");
        plan.setVisitTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000L));
        plan.setVisitPurpose("跟进项目进展，确认需求细节");
        plan.setRemindTime(30);
        plan.setStatus("PENDING");
        plan.setOwnerId(1L);
        plan.setOwnerName("测试用户");
        plan.setCreateTime(new Date());
        return plan;
    }

    private CrmCustomerFollowupRecord createTestFollowupRecord(Long customerId) {
        CrmCustomerFollowupRecord record = new CrmCustomerFollowupRecord();
        record.setCustomerId(customerId);
        record.setFollowupType("call");
        record.setFollowupContent("与客户电话沟通，确认设备技术参数和预算范围");
        record.setCreatorId(1L);
        record.setCreateTime(new Date());
        return record;
    }

    private CrmTeam createTestTeam() {
        CrmTeam team = new CrmTeam();
        team.setTeamName("销售一组");
        team.setDescription("负责华东地区销售业务");
        team.setLeaderId(1L);
        team.setLeaderName("团队负责人");
        team.setStatus("ACTIVE");
        team.setCreateTime(new Date());
        return team;
    }

    private void setupPerformanceTestData() {
        // 为性能测试创建一些关联数据
        if (testCustomer.getId() == null) {
            customerMapper.insertCrmCustomer(testCustomer);
        }
        
        testOpportunity.setCustomerId(testCustomer.getId());
        testOpportunity.setCustomerName(testCustomer.getCustomerName());
        opportunityMapper.insertCrmOpportunity(testOpportunity);
        
        testContract.setCustomerName(testCustomer.getCustomerName());
        testContract.setOpportunityName(testOpportunity.getOpportunityName());
        contractMapper.insertContract(testContract);
        
        // 注意：CrmPaymentRecord没有contractId字段
        testPayment.setCustomerId(testCustomer.getId());
        paymentMapper.insertCrmPaymentRecord(testPayment);
    }
}