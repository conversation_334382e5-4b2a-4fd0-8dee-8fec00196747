package com.ruoyi.crm.integration;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmLeadAssignmentRecord;
import com.ruoyi.common.mapper.CrmLeadAssignmentRecordMapper;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmLeadAssignmentRecordService;

/**
 * 线索分配记录功能集成测试
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */

@AutoConfigureWebMvc
@DisplayName("线索分配记录功能集成测试")
public class CrmLeadAssignmentRecordIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmLeadAssignmentRecordService assignmentRecordService;

    @Autowired
    private CrmLeadAssignmentRecordMapper assignmentRecordMapper;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 清理测试数据
        cleanupTestData();
        
        // 准备测试数据
        prepareTestData();
    }

    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        assignmentRecordMapper.deleteCrmLeadAssignmentRecordByIds(new Long[]{1L, 2L, 3L, 4L, 5L});
    }

    /**
     * 准备测试数据
     */
    private void prepareTestData() {
        // 创建手动分配记录
        CrmLeadAssignmentRecord record1 = CrmLeadAssignmentRecord.createManualAssignment(
            2001L, 1L, 201L, "测试手动分配", 1L, "testAdmin");
        record1.setCreateBy("testUser");
        record1.setCreateTime(new Date());
        assignmentRecordMapper.insertCrmLeadAssignmentRecord(record1);

        // 创建抢单记录
        CrmLeadAssignmentRecord record2 = CrmLeadAssignmentRecord.createGrabAssignment(
            2002L, 2L, 202L, "测试抢单", 202L, "testUser2");
        record2.setCreateBy("testUser");
        record2.setCreateTime(new Date());
        assignmentRecordMapper.insertCrmLeadAssignmentRecord(record2);

        // 创建回收记录
        CrmLeadAssignmentRecord record3 = CrmLeadAssignmentRecord.createRecycleRecord(
            2003L, 3L, 203L, "测试回收", 1L, "testAdmin");
        record3.setCreateBy("testUser");
        record3.setCreateTime(new Date());
        assignmentRecordMapper.insertCrmLeadAssignmentRecord(record3);
    }

    @Test
    @DisplayName("测试查询分配记录列表API")
    void testGetAssignmentRecordsList() throws Exception {
        mockMvc.perform(get("/front/crm/assignmentRecords/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @DisplayName("测试按分配类型查询API")
    void testGetRecordsByAssignmentType() throws Exception {
        mockMvc.perform(get("/front/crm/assignmentRecords/type/manual")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 验证业务逻辑
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByAssignmentType("manual");
        for (CrmLeadAssignmentRecord record : records) {
            assertEquals("manual", record.getAssignmentType());
            assertTrue(record.isManualAssignment());
        }
    }

    @Test
    @DisplayName("测试按线索ID查询分配记录API")
    void testGetRecordsByLeadId() throws Exception {
        mockMvc.perform(get("/front/crm/assignmentRecords/lead/2001")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 验证业务逻辑
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByLeadId(2001L);
        assertFalse(records.isEmpty());
        for (CrmLeadAssignmentRecord record : records) {
            assertEquals(2001L, record.getLeadId());
        }
    }

    @Test
    @DisplayName("测试按用户ID查询分配记录API")
    void testGetRecordsByUserId() throws Exception {
        // 测试作为分配对象的查询
        mockMvc.perform(get("/front/crm/assignmentRecords/toUser/201")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 测试作为原负责人的查询
        mockMvc.perform(get("/front/crm/assignmentRecords/fromUser/203")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 验证业务逻辑
        List<CrmLeadAssignmentRecord> toUserRecords = assignmentRecordService.getRecordsByToUserId(201L);
        for (CrmLeadAssignmentRecord record : toUserRecords) {
            assertEquals(201L, record.getToUserId());
        }

        List<CrmLeadAssignmentRecord> fromUserRecords = assignmentRecordService.getRecordsByFromUserId(203L);
        for (CrmLeadAssignmentRecord record : fromUserRecords) {
            assertEquals(203L, record.getFromUserId());
        }
    }

    @Test
    @DisplayName("测试按操作人查询分配记录API")
    void testGetRecordsByOperator() throws Exception {
        mockMvc.perform(get("/front/crm/assignmentRecords/operator/1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 验证业务逻辑
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByOperatorId(1L);
        for (CrmLeadAssignmentRecord record : records) {
            assertEquals(1L, record.getOperatorId());
        }
    }

    @Test
    @DisplayName("测试按时间范围查询分配记录API")
    void testGetRecordsByTimeRange() throws Exception {
        String startTime = "2025-01-01";
        String endTime = "2025-12-31";

        mockMvc.perform(get("/front/crm/assignmentRecords/timeRange")
                .param("startTime", startTime)
                .param("endTime", endTime)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("测试获取分配记录统计信息API")
    void testGetAssignmentRecordStats() throws Exception {
        mockMvc.perform(get("/front/crm/assignmentRecords/stats")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalCount").isNumber())
                .andExpect(jsonPath("$.data.manualCount").isNumber())
                .andExpect(jsonPath("$.data.grabCount").isNumber())
                .andExpect(jsonPath("$.data.recycleCount").isNumber())
                .andExpect(jsonPath("$.data.typeStats").isArray())
                .andExpect(jsonPath("$.data.userStats").isArray())
                .andExpect(jsonPath("$.data.operatorStats").isArray());

        // 验证统计数据的正确性
        Map<String, Object> stats = assignmentRecordService.getAssignmentRecordStats();
        assertTrue((Integer) stats.get("totalCount") >= 3);
        assertTrue((Integer) stats.get("manualCount") >= 1);
        assertTrue((Integer) stats.get("grabCount") >= 1);
        assertTrue((Integer) stats.get("recycleCount") >= 1);
    }

    @Test
    @DisplayName("测试各类型统计API")
    void testStatisticsAPIs() throws Exception {
        // 测试按分配类型统计
        mockMvc.perform(get("/front/crm/assignmentRecords/stats/type")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 测试按用户统计
        mockMvc.perform(get("/front/crm/assignmentRecords/stats/user")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 测试按操作人统计
        mockMvc.perform(get("/front/crm/assignmentRecords/stats/operator")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());

        // 测试时间范围统计
        mockMvc.perform(get("/front/crm/assignmentRecords/stats/timeRange")
                .param("startTime", "2025-01-01")
                .param("endTime", "2025-12-31")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isNumber());
    }

    @Test
    @DisplayName("测试获取最近分配记录API")
    void testGetLatestRecord() throws Exception {
        mockMvc.perform(get("/front/crm/assignmentRecords/latest/2001")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isNotEmpty());

        // 验证业务逻辑
        CrmLeadAssignmentRecord latestRecord = assignmentRecordService.getLatestRecordByLeadId(2001L);
        assertNotNull(latestRecord);
        assertEquals(2001L, latestRecord.getLeadId());
    }

    @Test
    @DisplayName("测试新增分配记录API")
    void testAddAssignmentRecord() throws Exception {
        CrmLeadAssignmentRecord newRecord = new CrmLeadAssignmentRecord();
        newRecord.setLeadId(2004L);
        newRecord.setPoolId(4L);
        newRecord.setToUserId(204L);
        newRecord.setAssignmentType("manual");
        newRecord.setAssignmentReason("API测试新增");
        newRecord.setOperatorId(1L);
        newRecord.setOperatorName("testAdmin");

        mockMvc.perform(post("/front/crm/assignmentRecords")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newRecord)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证数据是否插入成功
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByLeadId(2004L);
        assertFalse(records.isEmpty());
        assertEquals("API测试新增", records.get(0).getAssignmentReason());
    }

    @Test
    @DisplayName("测试修改分配记录API")
    void testUpdateAssignmentRecord() throws Exception {
        // 先获取一个记录
        List<CrmLeadAssignmentRecord> records = assignmentRecordService.getRecordsByLeadId(2001L);
        assertFalse(records.isEmpty());
        
        CrmLeadAssignmentRecord record = records.get(0);
        record.setRemarks("API测试修改");

        mockMvc.perform(put("/front/crm/assignmentRecords")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(record)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证修改结果
        CrmLeadAssignmentRecord updatedRecord = assignmentRecordService.selectCrmLeadAssignmentRecordById(record.getId());
        assertEquals("API测试修改", updatedRecord.getRemarks());
    }

    @Test
    @DisplayName("测试删除分配记录API")
    void testDeleteAssignmentRecord() throws Exception {
        // 先创建一个记录用于删除
        CrmLeadAssignmentRecord recordToDelete = new CrmLeadAssignmentRecord();
        recordToDelete.setLeadId(2005L);
        recordToDelete.setToUserId(205L);
        recordToDelete.setAssignmentType("manual");
        recordToDelete.setOperatorId(1L);
        recordToDelete.setOperatorName("testAdmin");
        recordToDelete.setCreateBy("testUser");
        recordToDelete.setCreateTime(new Date());
        
        assignmentRecordMapper.insertCrmLeadAssignmentRecord(recordToDelete);
        Long recordId = recordToDelete.getId();

        mockMvc.perform(delete("/front/crm/assignmentRecords/" + recordId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 验证删除结果
        CrmLeadAssignmentRecord deletedRecord = assignmentRecordService.selectCrmLeadAssignmentRecordById(recordId);
        assertNull(deletedRecord);
    }

    @Test
    @DisplayName("测试分配记录服务层业务逻辑")
    void testAssignmentRecordServiceLogic() {
        // 测试创建各种类型的分配记录
        int manualResult = assignmentRecordService.createManualAssignmentRecord(3001L, 10L, 301L, "服务层手动分配测试");
        assertEquals(1, manualResult);

        int grabResult = assignmentRecordService.createGrabAssignmentRecord(3002L, 11L, 302L, "服务层抢单测试");
        assertEquals(1, grabResult);

        int recycleResult = assignmentRecordService.createRecycleRecord(3003L, 12L, 303L, "服务层回收测试");
        assertEquals(1, recycleResult);

        // 验证记录类型判断方法
        List<CrmLeadAssignmentRecord> manualRecords = assignmentRecordService.getRecordsByAssignmentType("manual");
        for (CrmLeadAssignmentRecord record : manualRecords) {
            assertTrue(record.isManualAssignment());
            assertFalse(record.isGrabAssignment());
            assertFalse(record.isRecycleRecord());
        }

        List<CrmLeadAssignmentRecord> grabRecords = assignmentRecordService.getRecordsByAssignmentType("grab");
        for (CrmLeadAssignmentRecord record : grabRecords) {
            assertFalse(record.isManualAssignment());
            assertTrue(record.isGrabAssignment());
            assertFalse(record.isRecycleRecord());
        }

        List<CrmLeadAssignmentRecord> recycleRecords = assignmentRecordService.getRecordsByAssignmentType("recycle");
        for (CrmLeadAssignmentRecord record : recycleRecords) {
            assertFalse(record.isManualAssignment());
            assertFalse(record.isGrabAssignment());
            assertTrue(record.isRecycleRecord());
        }
    }

    @Test
    @DisplayName("测试分配记录统计功能")
    void testAssignmentRecordStatistics() {
        // 测试各种统计方法
        List<CrmLeadAssignmentRecord> typeStats = assignmentRecordService.countRecordsByAssignmentType();
        assertFalse(typeStats.isEmpty());

        List<CrmLeadAssignmentRecord> userStats = assignmentRecordService.countRecordsByToUser();
        assertFalse(userStats.isEmpty());

        List<CrmLeadAssignmentRecord> operatorStats = assignmentRecordService.countRecordsByOperator();
        assertFalse(operatorStats.isEmpty());

        // 测试时间范围统计
        Date startTime = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000); // 昨天
        Date endTime = new Date(); // 今天
        int timeRangeCount = assignmentRecordService.countRecordsByTimeRange(startTime, endTime);
        assertTrue(timeRangeCount >= 0);
    }

    @Test
    @DisplayName("测试分配记录工厂方法")
    void testAssignmentRecordFactoryMethods() {
        // 测试手动分配记录创建
        CrmLeadAssignmentRecord manualRecord = CrmLeadAssignmentRecord.createManualAssignment(
            4001L, 20L, 401L, "工厂方法测试", 1L, "testAdmin");
        assertEquals("manual", manualRecord.getAssignmentType());
        assertEquals(4001L, manualRecord.getLeadId());
        assertEquals(401L, manualRecord.getToUserId());
        assertNull(manualRecord.getFromUserId());

        // 测试抢单记录创建
        CrmLeadAssignmentRecord grabRecord = CrmLeadAssignmentRecord.createGrabAssignment(
            4002L, 21L, 402L, "抢单测试", 402L, "testUser");
        assertEquals("grab", grabRecord.getAssignmentType());
        assertEquals(4002L, grabRecord.getLeadId());
        assertEquals(402L, grabRecord.getToUserId());

        // 测试回收记录创建
        CrmLeadAssignmentRecord recycleRecord = CrmLeadAssignmentRecord.createRecycleRecord(
            4003L, 22L, 403L, "回收测试", 1L, "testAdmin");
        assertEquals("recycle", recycleRecord.getAssignmentType());
        assertEquals(4003L, recycleRecord.getLeadId());
        assertEquals(403L, recycleRecord.getFromUserId());
        assertNull(recycleRecord.getToUserId());
    }
}
