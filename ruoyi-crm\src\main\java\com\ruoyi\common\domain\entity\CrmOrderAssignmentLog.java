package com.ruoyi.common.domain.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单分配历史实体类
 * 对应数据库表：crm_order_assignment_log
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmOrderAssignmentLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 订单ID */
    private Long orderId;

    /** 操作类型 */
    @Excel(name = "操作类型", readConverterExp = "ASSIGN=分配,TRANSFER=转移,RECLAIM=回收,GRAB=抢单")
    private String actionType;

    /** 原负责人ID */
    private Long fromUserId;

    /** 原负责人姓名 */
    @Excel(name = "原负责人")
    private String fromUserName;

    /** 新负责人ID */
    private Long toUserId;

    /** 新负责人姓名 */
    @Excel(name = "新负责人")
    private String toUserName;

    /** 操作人ID */
    private Long operatorId;

    /** 操作人姓名 */
    @Excel(name = "操作人")
    private String operatorName;

    /** 操作原因 */
    @Excel(name = "操作原因")
    private String reason;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    /** IP地址 */
    private String ipAddress;

    /** 用户代理 */
    private String userAgent;

    // ========== 关联对象 ==========
    
    /** 关联订单对象 */
    private CrmOrder order;

    /** 原负责人对象 */
    private Object fromUser;

    /** 新负责人对象 */
    private Object toUser;

    /** 操作人对象 */
    private Object operator;

    // ========== 查询条件字段 ==========
    
    /** 开始时间（查询用） */
    private String startTime;

    /** 结束时间（查询用） */
    private String endTime;

    /** 订单编号（查询用） */
    private String orderNo;

    /** 客户名称（查询用） */
    private String customerName;

    // ========== 常量定义 ==========
    
    /** 操作类型：分配 */
    public static final String ACTION_TYPE_ASSIGN = "ASSIGN";
    
    /** 操作类型：转移 */
    public static final String ACTION_TYPE_TRANSFER = "TRANSFER";
    
    /** 操作类型：回收 */
    public static final String ACTION_TYPE_RECLAIM = "RECLAIM";
    
    /** 操作类型：抢单 */
    public static final String ACTION_TYPE_GRAB = "GRAB";
}
