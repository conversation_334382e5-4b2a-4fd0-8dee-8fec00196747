package com.ruoyi.crm.controller;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.entity.CrmContactFollowupRecords;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmContactFollowupRecordsService;

/**
 * 联系人活动记录集成测试 - 重构版本
 * 测试新的联系人跟进记录接口功能
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@AutoConfigureWebMvc
@Transactional
@Rollback
@DisplayName("联系人活动记录集成测试 - 重构版本")
public class ContactActivityIntegrationTestRefactored extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmContactsService crmContactsService;

    @Autowired
    private ICrmContactFollowupRecordsService followUpRecordsService;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private Long testContactId;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .build();
        
        objectMapper = new ObjectMapper();
        
        // 创建测试联系人
        setupTestContact();
        
        // 设置简单的认证用户
        setupAuthentication();
    }

    /**
     * 创建测试联系人数据
     */
    private void setupTestContact() {
        CrmContacts testContact = new CrmContacts();
        testContact.setName("测试联系人");
        testContact.setPhone("13800138000");
        testContact.setEmail("<EMAIL>");
        testContact.setPosition("测试职位");
        testContact.setResponsiblePersonId("1");
        testContact.setCreateTime(new Date());
        testContact.setDelFlag("0");
        
        int result = crmContactsService.insertCrmContacts(testContact);
        assertTrue(result > 0, "创建测试联系人失败");
        
        testContactId = testContact.getId();
        assertNotNull(testContactId, "测试联系人ID不能为空");
    }

    /**
     * 设置简单的认证用户
     */
    private void setupAuthentication() {
        UsernamePasswordAuthenticationToken authToken = 
            new UsernamePasswordAuthenticationToken(
                "admin", 
                "password", 
                Arrays.asList(new SimpleGrantedAuthority("ROLE_USER"))
            );
        SecurityContextHolder.getContext().setAuthentication(authToken);
    }

    @Test
    @DisplayName("测试获取联系人活动记录列表")
    public void testGetContactActivities() throws Exception {
        // 先创建一些测试数据
        createTestActivity("电话", "测试电话跟进内容", "high");
        createTestActivity("邮件", "测试邮件跟进内容", "normal");
        
        // 执行测试
        MvcResult result = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andExpect(jsonPath("$.data", hasSize(greaterThanOrEqualTo(2))))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("获取活动记录响应: " + responseContent);
    }

    @Test
    @DisplayName("测试创建联系人活动记录")
    public void testCreateContactActivity() throws Exception {
        CrmContactFollowupRecords newActivity = new CrmContactFollowupRecords();
        newActivity.setContactId(testContactId);
        newActivity.setFollowUpContent("这是一次重要的电话沟通");
        newActivity.setFollowUpMethod("电话");
        newActivity.setNextContactMethod("邮件");
        newActivity.setNextContactTime(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L)); // 一周后
        newActivity.setCommunicationResult("客户很感兴趣");
        newActivity.setMeetingSummary("电话跟进详细内容");
        newActivity.setContactQuality("high");
        
        String requestJson = objectMapper.writeValueAsString(newActivity);
        System.out.println("创建活动记录请求: " + requestJson);

        MvcResult result = mockMvc.perform(post("/front/crm/contacts/activities")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("创建活动记录响应: " + responseContent);
    }

    @Test
    @DisplayName("测试编辑联系人活动记录")
    public void testUpdateContactActivity() throws Exception {
        // 先创建一个活动记录
        Long activityId = createTestActivity("会议", "原始会议内容", "normal");
        
        // 准备更新数据
        CrmContactFollowupRecords updateActivity = new CrmContactFollowupRecords();
        updateActivity.setFollowUpContent("更新后的会议内容");
        updateActivity.setFollowUpMethod("会议");
        updateActivity.setCommunicationResult("客户表现出强烈兴趣");
        updateActivity.setContactQuality("high");
        updateActivity.setNextContactTime(new Date(System.currentTimeMillis() + 3 * 24 * 60 * 60 * 1000L)); // 3天后
        
        String requestJson = objectMapper.writeValueAsString(updateActivity);
        System.out.println("更新活动记录请求: " + requestJson);

        MvcResult result = mockMvc.perform(put("/front/crm/contacts/{contactId}/activities/{id}", testContactId, activityId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("更新活动记录响应: " + responseContent);
        
        // 验证数据是否真的更新了
        CrmContactFollowupRecords updatedRecord = followUpRecordsService.selectCrmContactFollowupRecordsById(activityId);
        assertNotNull(updatedRecord);
        assertEquals("更新后的会议内容", updatedRecord.getFollowUpContent());
        assertEquals("high", updatedRecord.getContactQuality());
    }

    @Test
    @DisplayName("测试删除联系人活动记录")
    public void testDeleteContactActivity() throws Exception {
        // 先创建一个活动记录
        Long activityId = createTestActivity("拜访", "客户拜访记录", "normal");
        
        // 执行删除
        MvcResult result = mockMvc.perform(delete("/front/crm/contacts/{contactId}/activities/{id}", testContactId, activityId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("删除活动记录响应: " + responseContent);
        
        // 验证记录是否被删除
        CrmContactFollowupRecords deletedRecord = followUpRecordsService.selectCrmContactFollowupRecordsById(activityId);
        assertNull(deletedRecord, "记录应该已被删除");
    }

    @Test
    @DisplayName("测试获取联系人活动记录统计")
    public void testGetContactActivityStats() throws Exception {
        // 创建多种类型的活动记录
        createTestActivity("电话", "电话跟进1", "high");
        createTestActivity("邮件", "邮件跟进1", "normal");
        createTestActivity("会议", "会议跟进1", "high");
        
        MvcResult result = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities/stats", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg", containsString("成功")))
                .andExpect(jsonPath("$.data.totalCount").value(greaterThanOrEqualTo(3)))
                .andExpect(jsonPath("$.data.lastActivityTime").exists())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("获取活动统计响应: " + responseContent);
    }

    @Test
    @DisplayName("测试不存在的联系人ID")
    public void testNonExistentContactId() throws Exception {
        Long nonExistentContactId = 999999L;
        
        mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", nonExistentContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(0))); // 应该返回空列表
    }

    @Test
    @DisplayName("测试编辑不存在的活动记录")
    public void testUpdateNonExistentActivity() throws Exception {
        Long nonExistentActivityId = 999999L;
        
        CrmContactFollowupRecords updateActivity = new CrmContactFollowupRecords();
        updateActivity.setFollowUpContent("尝试更新不存在的记录");
        
        String requestJson = objectMapper.writeValueAsString(updateActivity);

        mockMvc.perform(put("/front/crm/contacts/{contactId}/activities/{id}", testContactId, nonExistentActivityId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg", containsString("不存在")));
    }

    @Test
    @DisplayName("测试活动记录的所有质量等级")
    public void testAllQualityLevels() throws Exception {
        String[] qualityLevels = {"high", "normal", "low"};
        String[] followUpMethods = {"电话", "邮件", "会议", "拜访"};
        
        for (int i = 0; i < qualityLevels.length; i++) {
            String quality = qualityLevels[i];
            String method = followUpMethods[i % followUpMethods.length];
            
            CrmContactFollowupRecords activity = new CrmContactFollowupRecords();
            activity.setContactId(testContactId);
            activity.setFollowUpContent("测试" + quality + "质量活动");
            activity.setFollowUpMethod(method);
            activity.setContactQuality(quality);
            activity.setCommunicationResult("联系结果 - " + quality);
            activity.setNextContactTime(new Date(System.currentTimeMillis() + (i + 1) * 24 * 60 * 60 * 1000L));
            
            String requestJson = objectMapper.writeValueAsString(activity);

            mockMvc.perform(post("/front/crm/contacts/activities")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestJson))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
        }
        
        // 验证所有记录都创建成功
        mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(greaterThanOrEqualTo(qualityLevels.length))));
    }

    @Test
    @DisplayName("测试完整的活动记录工作流程")
    public void testCompleteActivityWorkflow() throws Exception {
        // 1. 创建活动记录
        CrmContactFollowupRecords newActivity = new CrmContactFollowupRecords();
        newActivity.setContactId(testContactId);
        newActivity.setFollowUpContent("初次电话联系");
        newActivity.setFollowUpMethod("电话");
        newActivity.setCommunicationResult("客户表示感兴趣");
        newActivity.setContactQuality("normal");
        newActivity.setNextContactTime(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
        
        String createJson = objectMapper.writeValueAsString(newActivity);
        
        mockMvc.perform(post("/front/crm/contacts/activities")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
        
        // 2. 查询活动记录列表，找到刚创建的记录
        MvcResult listResult = mockMvc.perform(get("/front/crm/contacts/{contactId}/activities", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(greaterThan(0))))
                .andReturn();
        
        // 解析响应获取活动ID
        String listResponse = listResult.getResponse().getContentAsString();
        AjaxResult listAjaxResult = objectMapper.readValue(listResponse, AjaxResult.class);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> activities = (List<Map<String, Object>>) listAjaxResult.get("data");
        Long activityId = Long.valueOf(activities.get(0).get("id").toString());
        
        // 3. 编辑活动记录
        CrmContactFollowupRecords updateActivity = new CrmContactFollowupRecords();
        updateActivity.setFollowUpContent("更新后的电话内容");
        updateActivity.setContactQuality("high");
        updateActivity.setCommunicationResult("客户非常感兴趣，要求详细方案");
        
        String updateJson = objectMapper.writeValueAsString(updateActivity);
        
        mockMvc.perform(put("/front/crm/contacts/{contactId}/activities/{id}", testContactId, activityId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(updateJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
        
        // 4. 获取统计信息
        mockMvc.perform(get("/front/crm/contacts/{contactId}/activities/stats", testContactId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.totalCount").value(greaterThan(0)));
        
        // 5. 删除活动记录
        mockMvc.perform(delete("/front/crm/contacts/{contactId}/activities/{id}", testContactId, activityId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
        
        System.out.println("完整工作流程测试完成");
    }

    /**
     * 创建测试活动记录的辅助方法
     */
    private Long createTestActivity(String followUpMethod, String content, String quality) {
        CrmContactFollowupRecords activity = new CrmContactFollowupRecords();
        activity.setContactId(testContactId);
        activity.setFollowUpMethod(followUpMethod);
        activity.setFollowUpContent(content);
        activity.setContactQuality(quality);
        activity.setCommunicationResult("测试联系结果");
        activity.setNextContactTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000L)); // 明天
        activity.setCreatorId(1L);
        activity.setCreatedAt(new Date());
        activity.setUpdatedAt(new Date());
        
        int insertResult = followUpRecordsService.insertCrmContactFollowupRecords(activity);
        assertTrue(insertResult > 0, "创建测试活动记录失败");
        
        return activity.getId();
    }
}
