package com.ruoyi.crm.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.math.BigDecimal;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.dto.CrmOrderAssignmentDTO;
import com.ruoyi.common.domain.dto.CrmOrderDTO;
import com.ruoyi.common.domain.dto.CrmOrderQueryDTO;
import com.ruoyi.common.domain.entity.CrmOrderItem;

import lombok.extern.slf4j.Slf4j;

/**
 * CRM订单控制器测试类
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Transactional
public class CrmOrderControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:list"})
    public void testGetOrderList() throws Exception {
        log.info("=== 测试获取订单列表 ===");
        
        mockMvc.perform(get("/crm/order/list")
                .param("pageNum", "1")
                .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:list"})
    public void testPageQuery() throws Exception {
        log.info("=== 测试分页查询订单 ===");
        
        CrmOrderQueryDTO queryDTO = new CrmOrderQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        queryDTO.setOrderSource("3D_PRINTING");
        
        String jsonContent = objectMapper.writeValueAsString(queryDTO);
        
        mockMvc.perform(post("/crm/order/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:add"})
    public void testCreateOrder() throws Exception {
        log.info("=== 测试创建订单 ===");
        
        CrmOrderDTO orderDTO = new CrmOrderDTO();
        orderDTO.setOrderNo("TEST-" + System.currentTimeMillis());
        orderDTO.setCustomerName("测试客户");
        orderDTO.setContactPhone("13800138000");
        orderDTO.setOrderSource("3D_PRINTING");
        orderDTO.setOrderType("3D_PRINTING");
        orderDTO.setPriorityLevel("NORMAL");
        orderDTO.setTotalAmount(new BigDecimal("1000.00"));
        orderDTO.setOrderDate(new Date());
        orderDTO.setRemarks("API测试订单");
        
        // 添加订单项
        CrmOrderItem item = new CrmOrderItem();
        item.setProductName("测试产品");
        item.setQuantity(1);
        item.setUnitPrice(new BigDecimal("1000.00"));
        item.setSubtotal(new BigDecimal("1000.00"));

        if (orderDTO.getOrderItems() == null) {
            orderDTO.setOrderItems(new java.util.ArrayList<>());
        }
        orderDTO.getOrderItems().add(item);
        
        String jsonContent = objectMapper.writeValueAsString(orderDTO);
        
        mockMvc.perform(post("/crm/order/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:assign"})
    public void testAssignOrder() throws Exception {
        log.info("=== 测试分配订单 ===");
        
        CrmOrderAssignmentDTO assignmentDTO = new CrmOrderAssignmentDTO();
        assignmentDTO.setOrderId(1L);
        assignmentDTO.setToUserId(1L);
        assignmentDTO.setToUserName("管理员");
        assignmentDTO.setReason("API测试分配");
        
        String jsonContent = objectMapper.writeValueAsString(assignmentDTO);
        
        mockMvc.perform(post("/crm/order/assign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:grab"})
    public void testGrabOrder() throws Exception {
        log.info("=== 测试抢单 ===");
        
        mockMvc.perform(post("/crm/order/grab/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:list"})
    public void testGetMyOrders() throws Exception {
        log.info("=== 测试获取我的订单 ===");
        
        CrmOrderQueryDTO queryDTO = new CrmOrderQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        String jsonContent = objectMapper.writeValueAsString(queryDTO);
        
        mockMvc.perform(post("/crm/order/my")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:list"})
    public void testGetUnassignedOrders() throws Exception {
        log.info("=== 测试获取未分配订单 ===");
        
        CrmOrderQueryDTO queryDTO = new CrmOrderQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        String jsonContent = objectMapper.writeValueAsString(queryDTO);
        
        mockMvc.perform(post("/crm/order/unassigned")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:edit"})
    public void testUpdateOrderStatus() throws Exception {
        log.info("=== 测试更新订单状态 ===");
        
        mockMvc.perform(put("/crm/order/status/1")
                .param("status", "PROCESSING"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:list"})
    public void testCheckCanAssign() throws Exception {
        log.info("=== 测试检查是否可以分配 ===");
        
        mockMvc.perform(get("/crm/order/check/assign")
                .param("orderId", "1")
                .param("userId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isBoolean());
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:list"})
    public void testCheckCanGrab() throws Exception {
        log.info("=== 测试检查是否可以抢单 ===");
        
        mockMvc.perform(get("/crm/order/check/grab/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isBoolean());
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:transfer"})
    public void testTransferOrder() throws Exception {
        log.info("=== 测试转移订单 ===");
        
        CrmOrderAssignmentDTO transferDTO = new CrmOrderAssignmentDTO();
        transferDTO.setOrderId(1L);
        transferDTO.setFromUserId(1L);
        transferDTO.setFromUserName("管理员");
        transferDTO.setToUserId(2L);
        transferDTO.setToUserName("测试用户");
        transferDTO.setReason("API测试转移");
        
        String jsonContent = objectMapper.writeValueAsString(transferDTO);
        
        mockMvc.perform(post("/crm/order/transfer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:reclaim"})
    public void testReclaimOrder() throws Exception {
        log.info("=== 测试回收订单 ===");
        
        mockMvc.perform(post("/crm/order/reclaim")
                .param("orderId", "1")
                .param("reason", "API测试回收"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:query"})
    public void testGetOrderDetail() throws Exception {
        log.info("=== 测试获取订单详情 ===");
        
        mockMvc.perform(get("/crm/order/detail/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:list"})
    public void testGetDeptOrders() throws Exception {
        log.info("=== 测试获取部门订单 ===");
        
        CrmOrderQueryDTO queryDTO = new CrmOrderQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        
        String jsonContent = objectMapper.writeValueAsString(queryDTO);
        
        mockMvc.perform(post("/crm/order/dept/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"crm:order:edit"})
    public void testBatchUpdateStatus() throws Exception {
        log.info("=== 测试批量更新状态 ===");
        
        mockMvc.perform(put("/crm/order/batch/status")
                .param("ids", "1,2,3")
                .param("status", "PROCESSING"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
