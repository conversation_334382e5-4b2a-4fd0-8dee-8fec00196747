package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.ruoyi.crm.service.ICustomerNotificationService;

/**
 * 客户通知服务实现类
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Service
public class CustomerNotificationServiceImpl implements ICustomerNotificationService {
    
    private static final Logger log = LoggerFactory.getLogger(CustomerNotificationServiceImpl.class);
    
    @Override
    public void notifyNewCustomerFromThreeD(Long customerId, String customerName, String contactPhone, String quoteNo) {
        log.info("=== 新客户通知 ===");
        log.info("通过3D打印系统创建了新客户:");
        log.info("- 客户ID: {}", customerId);
        log.info("- 客户名称: {}", customerName);
        log.info("- 联系电话: {}", contactPhone);
        log.info("- 询价单号: {}", quoteNo);
        log.info("- 状态: 客户暂未分配负责人，已放入公海池");
        log.info("- 建议: 请管理员及时分配负责人跟进");
        log.info("===============");
        
        // TODO: 在这里集成实际的通知机制：
        // 1. 系统内消息通知
        // 2. 邮件通知
        // 3. 企业微信通知
        // 4. 短信通知
        
        // 示例：发送系统消息给管理员
        sendSystemNotification("新客户待分配", 
            String.format("3D打印系统新增客户：%s（%s），询价单号：%s，请及时分配负责人", 
                customerName, contactPhone, quoteNo));
    }
    
    @Override
    public void notifyManagerNewOrder(Long managerId, Long customerId, String customerName, String orderNo, BigDecimal orderAmount) {
        log.info("=== 负责人订单通知 ===");
        log.info("您负责的客户有新订单:");
        log.info("- 负责人ID: {}", managerId);
        log.info("- 客户ID: {}", customerId);
        log.info("- 客户名称: {}", customerName);
        log.info("- 订单号: {}", orderNo);
        log.info("- 订单金额: ¥{}", orderAmount);
        log.info("- 来源: 3D打印报价系统");
        log.info("===================");
        
        // TODO: 发送通知给具体的负责人
        sendSystemNotification("客户新订单", 
            String.format("您的客户 %s 通过3D打印系统下了新订单，订单号：%s，金额：¥%s", 
                customerName, orderNo, orderAmount));
    }
    
    /**
     * 发送系统通知（占位方法）
     */
    private void sendSystemNotification(String title, String content) {
        // TODO: 集成实际的系统通知功能
        log.info("系统通知 - {}: {}", title, content);
    }
}