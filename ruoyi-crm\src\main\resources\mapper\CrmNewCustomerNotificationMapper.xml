<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmNewCustomerNotificationMapper">
    
    <resultMap type="CrmNewCustomerNotification" id="CrmNewCustomerNotificationResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="customerPhone"    column="customer_phone"    />
        <result property="customerSource"    column="customer_source"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="orderAmount"    column="order_amount"    />
        <result property="notificationType"    column="notification_type"    />
        <result property="notificationStatus"    column="notification_status"    />
        <result property="priorityLevel"    column="priority_level"    />
        <result property="assignedTo"    column="assigned_to"    />
        <result property="assignedBy"    column="assigned_by"    />
        <result property="assignedTime"    column="assigned_time"    />
        <result property="processedBy"    column="processed_by"    />
        <result property="processedTime"    column="processed_time"    />
        <result property="processResult"    column="process_result"    />
        <result property="notificationChannels"    column="notification_channels"    />
        <result property="wechatSent"    column="wechat_sent"    />
        <result property="emailSent"    column="email_sent"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectCrmNewCustomerNotificationVo">
        select id, customer_id, customer_name, customer_phone, customer_source, order_id, order_no, order_amount,
               notification_type, notification_status, priority_level, assigned_to, assigned_by, assigned_time,
               processed_by, processed_time, process_result, notification_channels, wechat_sent, email_sent,
               create_time, update_time, create_by, update_by, del_flag
        from crm_new_customer_notifications
    </sql>

    <select id="selectCrmNewCustomerNotificationList" parameterType="CrmNewCustomerNotification" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        <where>  
            del_flag = '0'
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="customerPhone != null  and customerPhone != ''"> and customer_phone like concat('%', #{customerPhone}, '%')</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="notificationType != null  and notificationType != ''"> and notification_type = #{notificationType}</if>
            <if test="notificationStatus != null  and notificationStatus != ''"> and notification_status = #{notificationStatus}</if>
            <if test="priorityLevel != null  and priorityLevel != ''"> and priority_level = #{priorityLevel}</if>
            <if test="assignedTo != null "> and assigned_to = #{assignedTo}</if>
            <if test="processedBy != null "> and processed_by = #{processedBy}</if>
            <if test="startTime != null and startTime != ''"> and create_time &gt;= #{startTime}</if>
            <if test="endTime != null and endTime != ''"> and create_time &lt;= #{endTime}</if>
            <if test="statusList != null and statusList.size() > 0">
                and notification_status in
                <foreach item="status" collection="statusList" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="assignedToList != null and assignedToList.size() > 0">
                and assigned_to in
                <foreach item="userId" collection="assignedToList" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCrmNewCustomerNotificationById" parameterType="Long" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where id = #{id}
    </select>

    <select id="selectCrmNewCustomerNotificationByCustomerId" parameterType="Long" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where customer_id = #{customerId} and del_flag = '0'
        order by create_time desc
    </select>

    <select id="selectCrmNewCustomerNotificationByOrderId" parameterType="Long" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where order_id = #{orderId} and del_flag = '0'
        order by create_time desc
    </select>

    <select id="selectCrmNewCustomerNotificationByStatus" parameterType="String" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where notification_status = #{notificationStatus} and del_flag = '0'
        order by create_time desc
    </select>

    <select id="selectCrmNewCustomerNotificationByAssignedTo" parameterType="Long" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where assigned_to = #{assignedTo} and del_flag = '0'
        order by create_time desc
    </select>

    <select id="selectPendingNotifications" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where notification_status = 'PENDING' and del_flag = '0'
        order by priority_level desc, create_time asc
    </select>

    <select id="selectTimeoutNotifications" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where notification_status in ('PENDING', 'PROCESSING') 
        and del_flag = '0'
        and timestampdiff(minute, create_time, now()) > #{timeoutMinutes}
        order by create_time asc
    </select>

    <select id="selectNotificationStatsByUser" resultMap="CrmNewCustomerNotificationResult">
        select assigned_to, notification_status, count(*) as notification_count,
               avg(timestampdiff(minute, create_time, processed_time)) as avg_process_time
        from crm_new_customer_notifications
        where del_flag = '0'
        <if test="assignedTo != null">and assigned_to = #{assignedTo}</if>
        <if test="startTime != null and startTime != ''">and create_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and create_time &lt;= #{endTime}</if>
        group by assigned_to, notification_status
        order by notification_count desc
    </select>

    <select id="selectNotificationEfficiencyStats" resultMap="CrmNewCustomerNotificationResult">
        select 
            count(*) as total_count,
            sum(case when notification_status = 'COMPLETED' then 1 else 0 end) as completed_count,
            sum(case when notification_status = 'PENDING' then 1 else 0 end) as pending_count,
            sum(case when notification_status = 'PROCESSING' then 1 else 0 end) as processing_count,
            avg(case when processed_time is not null then timestampdiff(minute, create_time, processed_time) else null end) as avg_process_time,
            max(case when processed_time is not null then timestampdiff(minute, create_time, processed_time) else null end) as max_process_time,
            min(case when processed_time is not null then timestampdiff(minute, create_time, processed_time) else null end) as min_process_time
        from crm_new_customer_notifications
        where del_flag = '0'
        <if test="startTime != null and startTime != ''">and create_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and create_time &lt;= #{endTime}</if>
    </select>

    <select id="selectUnsentWechatNotifications" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where wechat_sent = 0 
        and notification_channels like '%WECHAT%'
        and del_flag = '0'
        order by create_time asc
        limit 100
    </select>

    <select id="selectUnsentEmailNotifications" resultMap="CrmNewCustomerNotificationResult">
        <include refid="selectCrmNewCustomerNotificationVo"/>
        where email_sent = 0 
        and notification_channels like '%EMAIL%'
        and del_flag = '0'
        order by create_time asc
        limit 100
    </select>
        
    <insert id="insertCrmNewCustomerNotification" parameterType="CrmNewCustomerNotification" useGeneratedKeys="true" keyProperty="id">
        insert into crm_new_customer_notifications
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="customerPhone != null">customer_phone,</if>
            <if test="customerSource != null">customer_source,</if>
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="orderAmount != null">order_amount,</if>
            <if test="notificationType != null">notification_type,</if>
            <if test="notificationStatus != null">notification_status,</if>
            <if test="priorityLevel != null">priority_level,</if>
            <if test="assignedTo != null">assigned_to,</if>
            <if test="assignedBy != null">assigned_by,</if>
            <if test="assignedTime != null">assigned_time,</if>
            <if test="processedBy != null">processed_by,</if>
            <if test="processedTime != null">processed_time,</if>
            <if test="processResult != null">process_result,</if>
            <if test="notificationChannels != null">notification_channels,</if>
            <if test="wechatSent != null">wechat_sent,</if>
            <if test="emailSent != null">email_sent,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="customerPhone != null">#{customerPhone},</if>
            <if test="customerSource != null">#{customerSource},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="notificationType != null">#{notificationType},</if>
            <if test="notificationStatus != null">#{notificationStatus},</if>
            <if test="priorityLevel != null">#{priorityLevel},</if>
            <if test="assignedTo != null">#{assignedTo},</if>
            <if test="assignedBy != null">#{assignedBy},</if>
            <if test="assignedTime != null">#{assignedTime},</if>
            <if test="processedBy != null">#{processedBy},</if>
            <if test="processedTime != null">#{processedTime},</if>
            <if test="processResult != null">#{processResult},</if>
            <if test="notificationChannels != null">#{notificationChannels},</if>
            <if test="wechatSent != null">#{wechatSent},</if>
            <if test="emailSent != null">#{emailSent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            now(),
         </trim>
    </insert>

    <update id="updateCrmNewCustomerNotification" parameterType="CrmNewCustomerNotification">
        update crm_new_customer_notifications
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="customerPhone != null">customer_phone = #{customerPhone},</if>
            <if test="customerSource != null">customer_source = #{customerSource},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="orderAmount != null">order_amount = #{orderAmount},</if>
            <if test="notificationType != null">notification_type = #{notificationType},</if>
            <if test="notificationStatus != null">notification_status = #{notificationStatus},</if>
            <if test="priorityLevel != null">priority_level = #{priorityLevel},</if>
            <if test="assignedTo != null">assigned_to = #{assignedTo},</if>
            <if test="assignedBy != null">assigned_by = #{assignedBy},</if>
            <if test="assignedTime != null">assigned_time = #{assignedTime},</if>
            <if test="processedBy != null">processed_by = #{processedBy},</if>
            <if test="processedTime != null">processed_time = #{processedTime},</if>
            <if test="processResult != null">process_result = #{processResult},</if>
            <if test="notificationChannels != null">notification_channels = #{notificationChannels},</if>
            <if test="wechatSent != null">wechat_sent = #{wechatSent},</if>
            <if test="emailSent != null">email_sent = #{emailSent},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <update id="updateNotificationStatus">
        update crm_new_customer_notifications
        set notification_status = #{status},
            processed_by = #{processedBy},
            processed_time = now(),
            process_result = #{processResult},
            update_time = now()
        where id = #{id}
    </update>

    <update id="updateWechatSentStatus">
        update crm_new_customer_notifications
        set wechat_sent = #{wechatSent}, update_time = now()
        where id = #{id}
    </update>

    <update id="updateEmailSentStatus">
        update crm_new_customer_notifications
        set email_sent = #{emailSent}, update_time = now()
        where id = #{id}
    </update>

    <update id="batchUpdateNotificationStatus">
        update crm_new_customer_notifications
        set notification_status = #{status},
            processed_by = #{processedBy},
            processed_time = now(),
            update_time = now()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteCrmNewCustomerNotificationById" parameterType="Long">
        update crm_new_customer_notifications set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteCrmNewCustomerNotificationByIds" parameterType="String">
        update crm_new_customer_notifications set del_flag = '1' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCrmNewCustomerNotificationByCustomerId" parameterType="Long">
        update crm_new_customer_notifications set del_flag = '1' where customer_id = #{customerId}
    </delete>

    <delete id="deleteCrmNewCustomerNotificationByOrderId" parameterType="Long">
        update crm_new_customer_notifications set del_flag = '1' where order_id = #{orderId}
    </delete>

    <insert id="batchInsertCrmNewCustomerNotification" parameterType="java.util.List">
        insert into crm_new_customer_notifications(customer_id, customer_name, customer_phone, customer_source, order_id, order_no, order_amount,
                                                    notification_type, notification_status, priority_level, assigned_to, assigned_by, assigned_time,
                                                    notification_channels, wechat_sent, email_sent, create_time, create_by, del_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId}, #{item.customerName}, #{item.customerPhone}, #{item.customerSource}, #{item.orderId}, #{item.orderNo}, #{item.orderAmount},
             #{item.notificationType}, #{item.notificationStatus}, #{item.priorityLevel}, #{item.assignedTo}, #{item.assignedBy}, #{item.assignedTime},
             #{item.notificationChannels}, #{item.wechatSent}, #{item.emailSent}, now(), #{item.createBy}, '0')
        </foreach>
    </insert>

    <delete id="cleanExpiredNotifications">
        delete from crm_new_customer_notifications 
        where notification_status = 'COMPLETED' 
        and datediff(now(), processed_time) > #{expireDays}
    </delete>

</mapper>
