-- ----------------------------
-- 1. 团队与业务对象关联表
-- ----------------------------
DROP TABLE IF EXISTS `crm_team_relation`;
CREATE TABLE `crm_team_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID (关联crm_teams.id)',
  `relation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联对象类型 (例如: CONTACT, OPPORTUNITY, CUSTOMER)',
  `relation_id` bigint(20) NOT NULL COMMENT '关联对象ID',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_team_relation` (`team_id`,`relation_type`,`relation_id`) USING BTREE COMMENT '团队与业务对象的唯一关联'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='CRM团队与业务对象关联表';
