package com.ruoyi.crm.controller.customer;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.domain.entity.CrmCustomerFollowupRecord;
import com.ruoyi.common.mapper.CrmCustomerFollowupRecordMapper;
import com.ruoyi.common.mapper.CrmCustomerMapper;
import com.ruoyi.common.service.ICrmCustomerFollowupRecordService;
import com.ruoyi.crm.BaseTestCase;

/**
 * 客户跟进记录全面集成测试
 * 
 * 测试覆盖：
 * 1. CRUD基础功能测试
 * 2. 数据验证和边界条件测试
 * 3. 搜索和过滤功能测试
 * 4. 异常场景和错误处理测试
 * 5. 并发操作测试
 * 6. 性能压力测试
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CrmCustomerFollowupRecordControllerIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmCustomerFollowupRecordService followupRecordService;

    @Autowired
    private CrmCustomerMapper customerMapper;

    @Autowired
    private CrmCustomerFollowupRecordMapper followupRecordMapper;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    // 测试数据
    private CrmCustomer testCustomer;
    private CrmCustomerFollowupRecord testRecord;
    private List<CrmCustomerFollowupRecord> batchTestRecords;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
        
        // 初始化测试数据
        initTestData();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据（事务回滚会自动处理）
    }

    // ==================== CRUD基础功能测试 ====================

    @Test
    @Order(1)
    @WithMockUser(username = "admin")
    @DisplayName("查询跟进记录列表 - 正常情况")
    void testGetFollowupRecordList_Normal() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @Order(2)
    @WithMockUser(username = "admin")
    @DisplayName("根据客户ID查询跟进记录")
    void testGetFollowupRecordsByCustomerId() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("customerId", testCustomer.getId().toString())
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @Order(3)
    @WithMockUser(username = "admin")
    @DisplayName("创建跟进记录 - 正常情况")
    void testCreateFollowupRecord_Normal() throws Exception {
        CrmCustomerFollowupRecord newRecord = createTestFollowupRecord(testCustomer.getId());
        String requestBody = objectMapper.writeValueAsString(newRecord);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(4)
    @WithMockUser(username = "admin")
    @DisplayName("更新跟进记录 - 正常情况")
    void testUpdateFollowupRecord_Normal() throws Exception {
        // 先创建一条记录
        followupRecordMapper.insertCrmCustomerFollowupRecord(testRecord);

        // 更新记录
        testRecord.setFollowupContent("更新后的标题：更新后的内容");
        String requestBody = objectMapper.writeValueAsString(testRecord);

        mockMvc.perform(put("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(5)
    @WithMockUser(username = "admin")
    @DisplayName("删除跟进记录 - 正常情况")
    void testDeleteFollowupRecord_Normal() throws Exception {
        // 先创建一条记录
        followupRecordMapper.insertCrmCustomerFollowupRecord(testRecord);

        mockMvc.perform(delete("/front/crm/customer/followup/{id}", testRecord.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 数据验证和边界条件测试 ====================

    @Test
    @Order(10)
    @WithMockUser(username = "admin")
    @DisplayName("数据验证 - 必填字段缺失")
    void testCreateFollowupRecord_MissingRequiredFields() throws Exception {
        CrmCustomerFollowupRecord invalidRecord = new CrmCustomerFollowupRecord();
        // 不设置必填字段
        String requestBody = objectMapper.writeValueAsString(invalidRecord);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @Order(11)
    @WithMockUser(username = "admin")
    @DisplayName("数据验证 - 字段长度边界测试")
    void testCreateFollowupRecord_FieldLengthBoundary() throws Exception {
        CrmCustomerFollowupRecord record = createTestFollowupRecord(testCustomer.getId());
        
        // 测试超长内容
        record.setFollowupContent("a".repeat(256)); // 假设内容最大255字符
        String requestBody = objectMapper.writeValueAsString(record);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                // 根据实际业务逻辑验证是否应该成功或失败
    }

    @Test
    @Order(12)
    @WithMockUser(username = "admin")
    @DisplayName("数据验证 - 特殊字符处理")
    void testCreateFollowupRecord_SpecialCharacters() throws Exception {
        CrmCustomerFollowupRecord record = createTestFollowupRecord(testCustomer.getId());
        record.setFollowupContent("测试标题 <script>alert('xss')</script> - 内容包含特殊字符: !@#$%^&*()_+{}[]|\\:;\"'<>?,.`~");
        
        String requestBody = objectMapper.writeValueAsString(record);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(13)
    @WithMockUser(username = "admin")
    @DisplayName("边界测试 - 分页极限值")
    void testPagination_Boundary() throws Exception {
        // 测试页码为0
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("pageNum", "0")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // 测试页面大小为0
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("pageNum", "1")
                .param("pageSize", "0")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // 测试超大页码
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("pageNum", "999999")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rows").isEmpty());
    }

    // ==================== 搜索和过滤功能测试 ====================

    @Test
    @Order(20)
    @WithMockUser(username = "admin")
    @DisplayName("搜索功能 - 按跟进类型过滤")
    void testSearchByFollowupType() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("followupType", "call")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(21)
    @WithMockUser(username = "admin")
    @DisplayName("搜索功能 - 按跟进结果过滤")
    void testSearchByFollowupResult() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("followupResult", "success")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(22)
    @WithMockUser(username = "admin")
    @DisplayName("搜索功能 - 按时间范围过滤")
    void testSearchByDateRange() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("startTime", "2024-01-01")
                .param("endTime", "2024-12-31")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(23)
    @WithMockUser(username = "admin")
    @DisplayName("搜索功能 - 组合条件搜索")
    void testCombinedSearch() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("customerId", testCustomer.getId().toString())
                .param("followupType", "call")
                .param("followupResult", "success")
                .param("isImportant", "true")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(24)
    @WithMockUser(username = "admin")
    @DisplayName("搜索功能 - 模糊搜索")
    void testFuzzySearch() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("title", "测试")
                .param("content", "内容")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 异常场景和错误处理测试 ====================

    @Test
    @Order(30)
    @WithMockUser(username = "admin")
    @DisplayName("异常测试 - 操作不存在的记录")
    void testOperateNonExistentRecord() throws Exception {
        // 尝试更新不存在的记录
        CrmCustomerFollowupRecord nonExistentRecord = createTestFollowupRecord(testCustomer.getId());
        nonExistentRecord.setId(99999L);
        String requestBody = objectMapper.writeValueAsString(nonExistentRecord);

        mockMvc.perform(put("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));

        // 尝试删除不存在的记录
        mockMvc.perform(delete("/front/crm/customer/followup/{id}", 99999L)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @Order(31)
    @WithMockUser(username = "admin")
    @DisplayName("异常测试 - 无效的JSON格式")
    void testInvalidJsonFormat() throws Exception {
        String invalidJson = "{invalid json format}";

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(invalidJson)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @Order(32)
    @WithMockUser(username = "admin")
    @DisplayName("异常测试 - SQL注入攻击防护")
    void testSqlInjectionPrevention() throws Exception {
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("title", "'; DROP TABLE crm_customer_followup_records; --")
                .param("content", "1' OR '1'='1")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
        
        // 验证表还存在（通过查询）
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("pageNum", "1")
                .param("pageSize", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    @Order(33)
    @WithMockUser(username = "admin")
    @DisplayName("异常测试 - 关联客户不存在")
    void testCreateRecordWithNonExistentCustomer() throws Exception {
        CrmCustomerFollowupRecord record = createTestFollowupRecord(99999L); // 不存在的客户ID
        String requestBody = objectMapper.writeValueAsString(record);

        mockMvc.perform(post("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    // ==================== 并发操作测试 ====================

    @Test
    @Order(40)
    @WithMockUser(username = "admin")
    @DisplayName("并发测试 - 同时创建多条记录")
    void testConcurrentCreateRecords() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < 20; i++) {
            final int index = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    CrmCustomerFollowupRecord record = createTestFollowupRecord(testCustomer.getId());
                    record.setFollowupContent("并发测试记录 " + index);
                    String requestBody = objectMapper.writeValueAsString(record);

                    mockMvc.perform(post("/front/crm/customer/followup")
                            .content(requestBody)
                            .contentType(MediaType.APPLICATION_JSON))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.code").value(200));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有操作完成
        assertDoesNotThrow(() -> 
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join()
        );
        executor.shutdown();
    }

    @Test
    @Order(41)
    @WithMockUser(username = "admin")
    @DisplayName("并发测试 - 同时更新同一条记录")
    void testConcurrentUpdateSameRecord() throws Exception {
        // 先创建一条记录
        followupRecordMapper.insertCrmCustomerFollowupRecord(testRecord);
        
        ExecutorService executor = Executors.newFixedThreadPool(5);
        List<CompletableFuture<String>> futures = new ArrayList<>();

        for (int i = 0; i < 10; i++) {
            final int index = i;
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    CrmCustomerFollowupRecord updateRecord = new CrmCustomerFollowupRecord();
                    updateRecord.setId(testRecord.getId());
                    updateRecord.setCustomerId(testRecord.getCustomerId());
                    updateRecord.setFollowupContent("并发更新测试 " + index + " - 并发更新内容 " + index);
                    updateRecord.setFollowupType("call");
                    
                    String requestBody = objectMapper.writeValueAsString(updateRecord);

                    return mockMvc.perform(put("/front/crm/customer/followup")
                            .content(requestBody)
                            .contentType(MediaType.APPLICATION_JSON))
                            .andReturn()
                            .getResponse()
                            .getContentAsString();
                } catch (Exception e) {
                    return "error: " + e.getMessage();
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有操作完成
        List<String> results = new ArrayList<>();
        futures.forEach(future -> {
            try {
                results.add(future.get());
            } catch (Exception e) {
                results.add("exception: " + e.getMessage());
            }
        });

        // 验证至少有部分操作成功
        long successCount = results.stream()
                .filter(result -> result.contains("\"code\":200"))
                .count();
        
        assertTrue(successCount > 0, "并发更新应该至少有部分成功");
        executor.shutdown();
    }

    // ==================== 性能压力测试 ====================

    @Test
    @Order(50)
    @WithMockUser(username = "admin")
    @DisplayName("性能测试 - 大量数据查询")
    void testPerformance_LargeDataQuery() throws Exception {
        // 创建大量测试数据
        createManyTestRecords(1000);

        long startTime = System.currentTimeMillis();

        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("pageNum", "1")
                .param("pageSize", "100")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 3000, "大数据量查询应该在3秒内完成，实际用时: " + duration + "ms");
    }

    @Test
    @Order(51)
    @WithMockUser(username = "admin")
    @DisplayName("性能测试 - 复杂条件查询")
    void testPerformance_ComplexQuery() throws Exception {
        long startTime = System.currentTimeMillis();

        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("customerId", testCustomer.getId().toString())
                .param("followupType", "call")
                .param("followupResult", "success")
                .param("startTime", "2024-01-01")
                .param("endTime", "2024-12-31")
                .param("title", "测试")
                .param("isImportant", "true")
                .param("pageNum", "1")
                .param("pageSize", "50")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 2000, "复杂条件查询应该在2秒内完成，实际用时: " + duration + "ms");
    }

    @Test
    @Order(52)
    @WithMockUser(username = "admin")
    @DisplayName("压力测试 - 高并发查询")
    void testStress_HighConcurrentQueries() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(20);
        List<CompletableFuture<Long>> futures = new ArrayList<>();

        for (int i = 0; i < 100; i++) {
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                try {
                    long start = System.currentTimeMillis();
                    mockMvc.perform(get("/front/crm/customer/followup/list")
                            .param("pageNum", "1")
                            .param("pageSize", "10")
                            .contentType(MediaType.APPLICATION_JSON))
                            .andExpect(status().isOk());
                    return System.currentTimeMillis() - start;
                } catch (Exception e) {
                    return -1L;
                }
            }, executor);
            futures.add(future);
        }

        List<Long> durations = new ArrayList<>();
        futures.forEach(future -> {
            try {
                durations.add(future.get());
            } catch (Exception e) {
                durations.add(-1L);
            }
        });

        // 计算成功率和平均响应时间
        long successCount = durations.stream().filter(d -> d > 0).count();
        double successRate = (double) successCount / durations.size() * 100;
        double avgDuration = durations.stream().filter(d -> d > 0).mapToLong(Long::longValue).average().orElse(0);

        assertTrue(successRate >= 95, "高并发查询成功率应该 >= 95%，实际: " + successRate + "%");
        assertTrue(avgDuration < 1000, "高并发查询平均响应时间应该 < 1秒，实际: " + avgDuration + "ms");
        
        executor.shutdown();
    }

    // ==================== 数据一致性测试 ====================

    @Test
    @Order(60)
    @WithMockUser(username = "admin")
    @DisplayName("数据一致性测试 - 创建后立即查询")
    void testDataConsistency_CreateAndQuery() throws Exception {
        CrmCustomerFollowupRecord newRecord = createTestFollowupRecord(testCustomer.getId());
        newRecord.setFollowupContent("一致性测试记录");
        String requestBody = objectMapper.writeValueAsString(newRecord);

        // 创建记录
        String createResponse = mockMvc.perform(post("/front/crm/customer/followup")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn()
                .getResponse()
                .getContentAsString();

        // 立即查询验证数据一致性
        mockMvc.perform(get("/front/crm/customer/followup/list")
                .param("title", "一致性测试记录")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows[?(@.title == '一致性测试记录')]").exists());
    }

    // ==================== 辅助方法 ====================

    private void initTestData() {
        // 创建测试客户
        testCustomer = createTestCustomer("跟进测试客户", "13900139001", "<EMAIL>");
        customerMapper.insertCrmCustomer(testCustomer);

        // 创建测试跟进记录
        testRecord = createTestFollowupRecord(testCustomer.getId());
        
        // 创建批量测试数据
        batchTestRecords = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            CrmCustomerFollowupRecord record = createTestFollowupRecord(testCustomer.getId());
            record.setFollowupContent("批量测试记录 " + i);
            batchTestRecords.add(record);
        }
    }

    private CrmCustomer createTestCustomer(String name, String mobile, String email) {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerName(name);
        customer.setMobile(mobile);
        customer.setEmail(email);
        customer.setResponsiblePersonId("1");
        customer.setCustomerIndustry("测试行业");
        customer.setCustomerLevel("A级");
        customer.setCustomerSource("测试来源");
        customer.setStatus("1");
        customer.setDelFlag("0");
        customer.setCreateTime(new Date());
        customer.setUpdateTime(new Date());
        return customer;
    }

    private CrmCustomerFollowupRecord createTestFollowupRecord(Long customerId) {
        CrmCustomerFollowupRecord record = new CrmCustomerFollowupRecord();
        record.setCustomerId(customerId);
        record.setFollowupType("call");
        record.setFollowupContent("测试跟进记录：这是一条测试跟进记录的内容");
        record.setCreatorId(1L);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        return record;
    }

    private void createManyTestRecords(int count) {
        for (int i = 0; i < count; i++) {
            CrmCustomerFollowupRecord record = createTestFollowupRecord(testCustomer.getId());
            record.setFollowupContent("大量数据测试记录 " + i);
            record.setFollowupType(i % 2 == 0 ? "call" : "visit");
            followupRecordMapper.insertCrmCustomerFollowupRecord(record);
        }
    }
}