// ModelViewer调试工具
export class ModelViewerDebug {
  // 测试URL格式解析
  static testUrlParsing(url: string) {
    console.log('🔍 URL解析测试:');
    console.log('原始URL:', url);
    
    const cleanUrl = url.split('?')[0]; // 去除查询参数
    const extension = cleanUrl.split('.').pop()?.toLowerCase();
    
    console.log('清理后URL:', cleanUrl);
    console.log('提取的扩展名:', extension);
    console.log('是否支持该格式:', ['stl', 'obj', 'fbx', 'gltf', 'glb', 'ply', 'dae'].includes(extension || ''));
    
    return { cleanUrl, extension };
  }

  // 测试模型信息
  static testModelInfo(modelInfo: any) {
    console.log('📋 模型信息测试:');
    console.log('modelInfo 存在:', !!modelInfo);
    console.log('modelInfo 类型:', typeof modelInfo);
    console.log('modelInfo 内容:', modelInfo);
    
    if (modelInfo) {
      console.log('name 属性:', modelInfo.name);
      console.log('url 属性:', modelInfo.url);
      console.log('originalUrl 属性:', modelInfo.originalUrl);
      console.log('modelUrl 属性:', modelInfo.modelUrl);
    }
    
    return modelInfo;
  }

  // 测试文件可访问性
  static async testFileAccess(url: string): Promise<boolean> {
    try {
      console.log('🌐 文件访问测试:', url);
      const response = await fetch(url, { method: 'HEAD' });
      console.log('文件访问结果:', response.status, response.statusText);
      return response.ok;
    } catch (error) {
      console.error('文件访问失败:', error);
      return false;
    }
  }

  // 完整调试流程
  static async fullDebug(row: any, modelViewer: any) {
    console.log('🚀 开始完整调试流程...\n');
    
    // 1. 测试行数据
    console.log('1️⃣ 行数据测试:');
    console.log('row 存在:', !!row);
    console.log('row 类型:', typeof row);
    console.log('row 内容:', row);
    
    if (!row) {
      console.error('❌ 行数据为空，无法继续');
      return false;
    }
    
    // 2. 测试URL获取
    console.log('\n2️⃣ URL获取测试:');
    const modelUrl = row.originalUrl || row.modelUrl || '/models/1508523 v2.stl';
    console.log('获取到的URL:', modelUrl);
    
    // 3. 测试URL解析
    console.log('\n3️⃣ URL解析测试:');
    const { cleanUrl, extension } = ModelViewerDebug.testUrlParsing(modelUrl);
    
    // 4. 测试ModelViewer组件
    console.log('\n4️⃣ ModelViewer组件测试:');
    console.log('modelViewer ref 存在:', !!modelViewer);
    console.log('modelViewer 类型:', typeof modelViewer);
    
    if (modelViewer && typeof modelViewer.open === 'function') {
      console.log('✅ open 方法存在');
    } else {
      console.error('❌ open 方法不存在');
      return false;
    }
    
    // 5. 测试文件访问（可选，可能会有跨域问题）
    console.log('\n5️⃣ 文件访问测试:');
    try {
      const isAccessible = await ModelViewerDebug.testFileAccess(modelUrl);
      if (isAccessible) {
        console.log('✅ 文件可访问');
      } else {
        console.warn('⚠️ 文件可能不可访问');
      }
    } catch (error) {
      console.warn('⚠️ 文件访问测试跳过（可能有跨域限制）');
    }
    
    console.log('\n✨ 调试完成');
    return true;
  }

  // 模拟点击预览（用于控制台测试）
  static simulatePreview() {
    const mockRow = {
      index: 1,
      originalUrl: 'https://example.com/model.stl',
      modelUrl: 'https://example.com/model.stl',
      modelInfo: {
        name: 'test_model.stl',
        dimensions: '10x10x10',
        volume: 1000,
        surfaceArea: 600
      },
      fileData: {
        name: 'test_model.stl'
      }
    };
    
    console.log('🎯 模拟预览测试');
    console.log('使用的模拟数据:', mockRow);
    
    return mockRow;
  }
}

// 导出到全局作用域（开发环境使用）
if (typeof window !== 'undefined') {
  (window as any).ModelViewerDebug = ModelViewerDebug;
  console.log('💡 ModelViewerDebug 已添加到 window 对象，可在控制台使用');
  console.log('💡 使用方式:');
  console.log('   - ModelViewerDebug.testUrlParsing("your-url")');
  console.log('   - ModelViewerDebug.simulatePreview()');
}