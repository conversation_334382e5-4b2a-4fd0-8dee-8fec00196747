package com.ruoyi.common.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户公海操作日志对象 crm_pool_operation_log
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
public class CrmPoolOperationLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long id;

    /** 客户ID */
    private Long customerId;

    /** 客户名称 */
    private String customerName;

    /** 操作类型：RETURN(放入公海), CLAIM(认领), AUTO_RETURN(自动回收) */
    private String operationType;

    /** 操作人ID */
    private Long operatorId;

    /** 操作人姓名 */
    private String operatorName;

    /** 原负责人ID */
    private Long fromUserId;

    /** 原负责人姓名 */
    private String fromUserName;

    /** 新负责人ID */
    private Long toUserId;

    /** 新负责人姓名 */
    private String toUserName;

    /** 操作原因 */
    private String reason;

    /** 备注 */
    private String remark;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    /** IP地址 */
    private String ipAddr;
}