<template>
    <div class="customer-contacts-tab">
        <!-- 联系人统计 -->
        <div class="contacts-stats-section">
            <el-row :gutter="16">
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ contacts.length }}</div>
                            <div class="stat-label">联系人总数</div>
                        </div>
                        <el-icon class="stat-icon"><User /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ primaryContacts.length }}</div>
                            <div class="stat-label">主要联系人</div>
                        </div>
                        <el-icon class="stat-icon primary"><Star /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ activeContacts.length }}</div>
                            <div class="stat-label">活跃联系人</div>
                        </div>
                        <el-icon class="stat-icon active"><ChatDotRound /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ followedContacts.length }}</div>
                            <div class="stat-label">关注联系人</div>
                        </div>
                        <el-icon class="stat-icon followed"><StarFilled /></el-icon>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 操作区域 -->
        <div class="contacts-actions-section">
            <div class="section-header">
                <h3>关联联系人</h3>
                <div class="header-actions">
                    <el-button type="primary" size="small" @click="showLinkContactDialog">
                        <el-icon><Link /></el-icon>
                        关联联系人
                    </el-button>
                    <el-button size="small" @click="showCreateContactDialog">
                        <el-icon><Plus /></el-icon>
                        新建联系人
                    </el-button>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="filter-controls">
                <el-select 
                    v-model="filterType" 
                    placeholder="联系人类型" 
                    clearable 
                    size="small"
                    style="width: 150px;"
                    @change="handleFilterChange"
                >
                    <el-option label="全部" value="" />
                    <el-option label="主要联系人" value="primary" />
                    <el-option label="决策者" value="decision_maker" />
                    <el-option label="使用者" value="user" />
                    <el-option label="影响者" value="influencer" />
                </el-select>
                
                <el-input
                    v-model="searchKeyword"
                    placeholder="搜索联系人"
                    size="small"
                    style="width: 200px;"
                    clearable
                    @input="handleSearch"
                >
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>

                <div class="view-controls">
                    <el-button-group size="small">
                        <el-button 
                            :type="viewMode === 'card' ? 'primary' : 'default'"
                            @click="viewMode = 'card'"
                        >
                            <el-icon><Grid /></el-icon>
                        </el-button>
                        <el-button 
                            :type="viewMode === 'table' ? 'primary' : 'default'"
                            @click="viewMode = 'table'"
                        >
                            <el-icon><List /></el-icon>
                        </el-button>
                    </el-button-group>
                </div>
            </div>
        </div>

        <!-- 联系人列表 -->
        <div class="contacts-list-section" v-loading="loading">
            <div v-if="filteredContacts.length === 0" class="empty-state">
                <el-empty description="暂无关联联系人" />
            </div>

            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="contacts-grid">
                <div 
                    v-for="contact in filteredContacts" 
                    :key="contact.id"
                    class="contact-card"
                    @click="handleContactClick(contact)"
                >
                    <div class="contact-header">
                        <div class="contact-avatar">
                            <el-avatar :size="50" :src="contact.avatar">
                                {{ contact.name?.charAt(0) || '联' }}
                            </el-avatar>
                            <div v-if="contact.isPrimary" class="primary-badge">
                                <el-icon><Star /></el-icon>
                            </div>
                        </div>
                        
                        <div class="contact-actions">
                            <el-dropdown @command="handleContactAction">
                                <el-button size="small" text>
                                    <el-icon><MoreFilled /></el-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item :command="{ action: 'view', contact }">
                                            <el-icon><View /></el-icon>
                                            查看详情
                                        </el-dropdown-item>
                                        <el-dropdown-item :command="{ action: 'edit', contact }">
                                            <el-icon><Edit /></el-icon>
                                            编辑联系人
                                        </el-dropdown-item>
                                        <el-dropdown-item :command="{ action: 'setPrimary', contact }">
                                            <el-icon><Star /></el-icon>
                                            设为主要联系人
                                        </el-dropdown-item>
                                        <el-dropdown-item 
                                            :command="{ action: 'unlink', contact }"
                                            divided
                                        >
                                            <el-icon><Link /></el-icon>
                                            取消关联
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>

                    <div class="contact-info">
                        <div class="contact-name">{{ contact.name }}</div>
                        <div class="contact-position">{{ contact.position || '-' }}</div>
                        <div class="contact-role">
                            <el-tag 
                                :type="getContactRoleTagType(contact.decisionRole)" 
                                size="small"
                            >
                                {{ getContactRoleName(contact.decisionRole) }}
                            </el-tag>
                        </div>
                    </div>

                    <div class="contact-details">
                        <div class="detail-row">
                            <el-icon><Phone /></el-icon>
                            <span>{{ contact.phone || contact.mobile || '-' }}</span>
                        </div>
                        <div class="detail-row">
                            <el-icon><Message /></el-icon>
                            <span>{{ contact.email || '-' }}</span>
                        </div>
                        <div class="detail-row">
                            <el-icon><Calendar /></el-icon>
                            <span>{{ contact.lastContactTime || '未联系' }}</span>
                        </div>
                    </div>

                    <div class="contact-status">
                        <div class="status-item">
                            <el-tag 
                                :type="contact.isFollowing ? 'success' : 'info'" 
                                size="small"
                            >
                                <el-icon>
                                    <StarFilled v-if="contact.isFollowing" />
                                    <Star v-else />
                                </el-icon>
                                {{ contact.isFollowing ? '已关注' : '未关注' }}
                            </el-tag>
                        </div>
                        <div class="status-item">
                            <span class="activity-count">{{ contact.activityCount || 0 }} 次互动</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表格视图 -->
            <div v-if="viewMode === 'table'" class="contacts-table">
                <el-table :data="filteredContacts" stripe @row-click="handleContactClick">
                    <el-table-column width="60">
                        <template #default="{ row }">
                            <el-avatar :size="40" :src="row.avatar">
                                {{ row.name?.charAt(0) || '联' }}
                            </el-avatar>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="name" label="姓名" min-width="120">
                        <template #default="{ row }">
                            <div class="name-cell">
                                <span class="name">{{ row.name }}</span>
                                <el-icon v-if="row.isPrimary" class="primary-icon">
                                    <Star />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="position" label="职位" min-width="120" />
                    
                    <el-table-column prop="decisionRole" label="决策角色" min-width="120">
                        <template #default="{ row }">
                            <el-tag 
                                :type="getContactRoleTagType(row.decisionRole)" 
                                size="small"
                            >
                                {{ getContactRoleName(row.decisionRole) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="phone" label="手机号" min-width="130">
                        <template #default="{ row }">
                            {{ row.phone || row.mobile || '-' }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
                    
                    <el-table-column prop="lastContactTime" label="最后联系" min-width="120" />
                    
                    <el-table-column prop="isFollowing" label="关注状态" width="100">
                        <template #default="{ row }">
                            <el-tag :type="row.isFollowing ? 'success' : 'info'" size="small">
                                <el-icon>
                                    <StarFilled v-if="row.isFollowing" />
                                    <Star v-else />
                                </el-icon>
                                {{ row.isFollowing ? '已关注' : '未关注' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
                            <el-button size="small" text @click.stop="handleContactAction({ action: 'view', contact: row })">
                                查看
                            </el-button>
                            <el-button size="small" text @click.stop="handleContactAction({ action: 'unlink', contact: row })">
                                取消关联
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <!-- 关联联系人对话框 -->
        <el-dialog 
            v-model="linkContactVisible" 
            title="关联联系人" 
            width="800px"
            :before-close="closeLinkContactDialog"
        >
            <div class="link-contact-content">
                <el-input
                    v-model="contactSearchKeyword"
                    placeholder="搜索联系人姓名、手机号或邮箱"
                    size="default"
                    clearable
                    @input="searchContacts"
                >
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>

                <div class="available-contacts" v-loading="searchLoading">
                    <div v-if="availableContacts.length === 0" class="empty-result">
                        <el-empty description="暂无可关联的联系人" />
                    </div>

                    <div v-else class="contacts-selection">
                        <el-checkbox-group v-model="selectedContactIds">
                            <div 
                                v-for="contact in availableContacts" 
                                :key="contact.id"
                                class="contact-option"
                            >
                                <el-checkbox :value="contact.id">
                                    <div class="contact-option-content">
                                        <el-avatar :size="32" :src="contact.avatar">
                                            {{ contact.name?.charAt(0) || '联' }}
                                        </el-avatar>
                                        <div class="contact-option-info">
                                            <div class="contact-option-name">{{ contact.name }}</div>
                                            <div class="contact-option-details">
                                                <span>{{ contact.position || '未知职位' }}</span>
                                                <span>·</span>
                                                <span>{{ contact.phone || contact.mobile || '无手机号' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </el-checkbox>
                            </div>
                        </el-checkbox-group>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeLinkContactDialog">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="handleLinkContacts" 
                        :loading="linkContactLoading"
                        :disabled="selectedContactIds.length === 0"
                    >
                        关联联系人 ({{ selectedContactIds.length }})
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { 
    User, 
    Star, 
    StarFilled,
    ChatDotRound,
    Link,
    Plus,
    Search,
    Grid,
    List,
    Phone,
    Message,
    Calendar,
    View,
    Edit,
    MoreFilled
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { CustomerData } from '../types';
import { 
    getContactsByCustomerId, 
    getContactsByCustomerIdFromContacts,
    linkCustomerContact, 
    unlinkCustomerContact, 
    setPrimaryContact, 
    batchLinkCustomerContacts,
    getAvailableContacts
} from '@/api/crm/customer-contact-relation';
import { searchContacts as searchContactsApi } from '@/api/crm/contacts';

interface ContactData {
    id: number;
    name: string;
    position: string;
    phone: string;
    mobile: string;
    email: string;
    avatar?: string;
    decisionRole: string;
    isPrimary: boolean;
    isFollowing: boolean;
    lastContactTime: string;
    activityCount: number;
    // 关联关系相关字段
    relationId?: number;
    relationType?: string;
    relationStatus?: string;
}

interface Props {
    entityData: CustomerData | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    'contact-click': [contact: ContactData];
}>();

// 状态管理
const loading = ref(false);
const contacts = ref<ContactData[]>([]);
const filterType = ref('');
const searchKeyword = ref('');
const viewMode = ref<'card' | 'table'>('card');

// 关联联系人相关
const linkContactVisible = ref(false);
const linkContactLoading = ref(false);
const searchLoading = ref(false);
const contactSearchKeyword = ref('');
const availableContacts = ref<ContactData[]>([]);
const selectedContactIds = ref<number[]>([]);

// 计算属性
const primaryContacts = computed(() => 
    contacts.value.filter(c => c.isPrimary)
);

const activeContacts = computed(() => 
    contacts.value.filter(c => c.activityCount > 0)
);

const followedContacts = computed(() => 
    contacts.value.filter(c => c.isFollowing)
);

const filteredContacts = computed(() => {
    let result = contacts.value;
    
    // 按类型过滤
    if (filterType.value) {
        if (filterType.value === 'primary') {
            result = result.filter(c => c.isPrimary);
        } else {
            result = result.filter(c => c.decisionRole === filterType.value);
        }
    }
    
    // 按关键词搜索
    if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(c => 
            c.name.toLowerCase().includes(keyword) ||
            (c.phone && c.phone.includes(keyword)) ||
            (c.mobile && c.mobile.includes(keyword)) ||
            (c.email && c.email.toLowerCase().includes(keyword))
        );
    }
    
    return result;
});

// 获取联系人角色名称
const getContactRoleName = (role: string) => {
    const roleMap = {
        decision_maker: '决策者',
        user: '使用者',
        influencer: '影响者',
        gatekeeper: '把关者'
    };
    return roleMap[role as keyof typeof roleMap] || role;
};

// 获取联系人角色标签类型
const getContactRoleTagType = (role: string) => {
    const typeMap = {
        decision_maker: 'danger',
        user: 'success',
        influencer: 'warning',
        gatekeeper: 'info'
    };
    return typeMap[role as keyof typeof typeMap] || 'info';
};

// 处理筛选变化
const handleFilterChange = () => {
    // 筛选逻辑在computed中处理
};

// 处理搜索
const handleSearch = () => {
    // 搜索逻辑在computed中处理
};

// 处理联系人点击
const handleContactClick = (contact: ContactData) => {
    emit('contact-click', contact);
};

// 处理联系人操作
const handleContactAction = async (command: { action: string; contact: ContactData }) => {
    const { action, contact } = command;
    
    switch (action) {
        case 'view':
            emit('contact-click', contact);
            break;
        case 'edit':
            ElMessage.info('编辑联系人功能开发中');
            break;
        case 'setPrimary':
            await handleSetPrimaryContact(contact);
            break;
        case 'unlink':
            await handleUnlinkContact(contact);
            break;
    }
};

// 设置主要联系人
const handleSetPrimaryContact = async (contact: ContactData) => {
    if (!props.entityData?.id) return;
    
    try {
        const response = await setPrimaryContact({
            customerId: props.entityData.id,
            contactId: contact.id
        });
        
        if (response.code === 200) {
            ElMessage.success('设置主要联系人成功');
            await loadContacts();
        } else {
            ElMessage.error(response.msg || '设置主要联系人失败');
        }
    } catch (error) {
        console.error('设置主要联系人失败:', error);
        ElMessage.error('设置主要联系人失败');
    }
};

// 取消关联联系人
const handleUnlinkContact = async (contact: ContactData) => {
    if (!props.entityData?.id) return;
    
    try {
        await ElMessageBox.confirm(
            `确定要取消与联系人 "${contact.name}" 的关联吗？`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        const response = await unlinkCustomerContact({
            customerId: props.entityData.id,
            contactId: contact.id
        });
        
        if (response.code === 200) {
            ElMessage.success('取消关联成功');
            await loadContacts();
        } else {
            ElMessage.error(response.msg || '取消关联失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('取消关联失败:', error);
            ElMessage.error('取消关联失败');
        }
    }
};

// 显示关联联系人对话框
const showLinkContactDialog = () => {
    linkContactVisible.value = true;
    searchContacts();
};

// 关闭关联联系人对话框
const closeLinkContactDialog = () => {
    linkContactVisible.value = false;
    contactSearchKeyword.value = '';
    availableContacts.value = [];
    selectedContactIds.value = [];
};

// 显示新建联系人对话框
const showCreateContactDialog = () => {
    ElMessage.info('新建联系人功能开发中');
};

// 搜索可关联的联系人
const searchContacts = async () => {
    if (!props.entityData?.id) return;
    
    searchLoading.value = true;
    try {
        // 获取已关联的联系人ID列表，用于排除
        const linkedContactIds = contacts.value.map(c => c.id);
        
        // 搜索联系人，排除已关联的
        const response = await searchContactsApi(contactSearchKeyword.value, linkedContactIds);
        
        if (response.code === 200) {
            // 转换数据格式
            availableContacts.value = (response.data || []).map((contact: any) => ({
                id: contact.id,
                name: contact.name || '',
                position: contact.position || '',
                phone: contact.phone || '',
                mobile: contact.mobile || '',
                email: contact.email || '',
                decisionRole: contact.decisionRole || 'user',
                isPrimary: false,
                isFollowing: false,
                lastContactTime: '',
                activityCount: 0
            }));
        } else {
            ElMessage.error(response.msg || '搜索联系人失败');
            availableContacts.value = [];
        }
    } catch (error) {
        console.error('搜索联系人失败:', error);
        ElMessage.error('搜索联系人失败');
        availableContacts.value = [];
    } finally {
        searchLoading.value = false;
    }
};

// 关联联系人
const handleLinkContacts = async () => {
    if (!props.entityData?.id) return;
    
    if (selectedContactIds.value.length === 0) {
        ElMessage.warning('请选择要关联的联系人');
        return;
    }

    linkContactLoading.value = true;
    try {
        const response = await batchLinkCustomerContacts({
            customerId: props.entityData.id,
            contactIds: selectedContactIds.value,
            relationType: '次要联系人'
        });
        
        if (response.code === 200) {
            ElMessage.success('关联联系人成功');
            closeLinkContactDialog();
            await loadContacts();
        } else {
            ElMessage.error(response.msg || '关联联系人失败');
        }
    } catch (error) {
        console.error('关联联系人失败:', error);
        ElMessage.error('关联联系人失败');
    } finally {
        linkContactLoading.value = false;
    }
};

// 加载关联联系人
const loadContacts = async () => {
    if (!props.entityData?.id) return;
    
    loading.value = true;
    try {
        // 先尝试使用新的关联接口
        let response;
        try {
            response = await getContactsByCustomerId(props.entityData.id);
        } catch (error) {
            console.log('新接口暂不可用，使用现有接口');
            // 如果新接口不可用，使用现有接口
            response = await getContactsByCustomerIdFromContacts(props.entityData.id);
        }
        
        // 处理不同的响应格式
        let contactsData = [];
        if (response.code === 200) {
            contactsData = response.data || response.rows || [];
        } else if (response.rows) {
            // TableDataInfo 格式
            contactsData = response.rows || [];
        }
        
        // 转换数据格式，添加关联关系信息
        contacts.value = contactsData.map((contact: any) => ({
            id: contact.id,
            name: contact.name || '',
            position: contact.position || '',
            phone: contact.phone || contact.mobile || '',
            mobile: contact.mobile || contact.phone || '',
            email: contact.email || '',
            decisionRole: contact.decisionRole || 'user',
            isPrimary: contact.isPrimary === 1 || contact.isPrimary === true || false,
            isFollowing: contact.isFollowing || false,
            lastContactTime: contact.lastContactTime || '未联系',
            activityCount: contact.activityCount || 0,
            relationId: contact.relationId,
            relationType: contact.relationType || '次要联系人',
            relationStatus: contact.relationStatus || 'active'
        }));
    } catch (error) {
        console.error('加载关联联系人失败:', error);
        ElMessage.error('加载关联联系人失败');
        contacts.value = [];
    } finally {
        loading.value = false;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    if (props.entityData) {
        loadContacts();
    }
});
</script>

<style scoped lang="scss">
.customer-contacts-tab {
    .contacts-stats-section {
        margin-bottom: 24px;

        .stat-card {
            text-align: center;
            position: relative;
            overflow: hidden;

            .stat-content {
                .stat-number {
                    font-size: 28px;
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 8px;
                }

                .stat-label {
                    font-size: 14px;
                    color: #606266;
                }
            }

            .stat-icon {
                position: absolute;
                right: 16px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 32px;
                color: #409eff;
                opacity: 0.3;

                &.primary {
                    color: #f56c6c;
                }

                &.active {
                    color: #67c23a;
                }

                &.followed {
                    color: #e6a23c;
                }
            }
        }
    }

    .contacts-actions-section {
        margin-bottom: 24px;

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            h3 {
                margin: 0;
                color: #303133;
                font-size: 18px;
                font-weight: 600;
            }

            .header-actions {
                display: flex;
                gap: 12px;
            }
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;

            .view-controls {
                margin-left: auto;
            }
        }
    }

    .contacts-list-section {
        .empty-state {
            text-align: center;
            padding: 60px 0;
        }

        .contacts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;

            .contact-card {
                border: 1px solid #e4e7ed;
                border-radius: 12px;
                padding: 20px;
                background: white;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    transform: translateY(-2px);
                }

                .contact-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 16px;

                    .contact-avatar {
                        position: relative;

                        .primary-badge {
                            position: absolute;
                            top: -4px;
                            right: -4px;
                            width: 20px;
                            height: 20px;
                            background: #f56c6c;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 12px;
                        }
                    }
                }

                .contact-info {
                    text-align: center;
                    margin-bottom: 16px;

                    .contact-name {
                        font-size: 16px;
                        font-weight: 600;
                        color: #303133;
                        margin-bottom: 4px;
                    }

                    .contact-position {
                        font-size: 14px;
                        color: #606266;
                        margin-bottom: 8px;
                    }

                    .contact-role {
                        margin-bottom: 4px;
                    }
                }

                .contact-details {
                    margin-bottom: 16px;

                    .detail-row {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 8px;
                        font-size: 14px;
                        color: #606266;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .el-icon {
                            font-size: 16px;
                            color: #909399;
                        }
                    }
                }

                .contact-status {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .status-item {
                        .activity-count {
                            font-size: 12px;
                            color: #909399;
                        }
                    }
                }
            }
        }

        .contacts-table {
            .name-cell {
                display: flex;
                align-items: center;
                gap: 6px;

                .name {
                    font-weight: 500;
                }

                .primary-icon {
                    color: #f56c6c;
                    font-size: 14px;
                }
            }
        }
    }

    .link-contact-content {
        .el-input {
            margin-bottom: 20px;
        }

        .available-contacts {
            max-height: 400px;
            overflow-y: auto;

            .empty-result {
                text-align: center;
                padding: 40px 0;
            }

            .contacts-selection {
                .contact-option {
                    margin-bottom: 12px;
                    padding: 12px;
                    border: 1px solid #e4e7ed;
                    border-radius: 8px;
                    transition: all 0.3s ease;

                    &:hover {
                        background: #f8f9fa;
                    }

                    .contact-option-content {
                        display: flex;
                        align-items: center;
                        gap: 12px;

                        .contact-option-info {
                            flex: 1;

                            .contact-option-name {
                                font-weight: 500;
                                color: #303133;
                                margin-bottom: 4px;
                            }

                            .contact-option-details {
                                font-size: 12px;
                                color: #909399;

                                span {
                                    margin-right: 8px;

                                    &:last-child {
                                        margin-right: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .customer-contacts-tab {
        .contacts-stats-section {
            .el-row {
                .el-col {
                    margin-bottom: 16px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }

        .contacts-actions-section {
            .section-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;

                .header-actions {
                    justify-content: center;
                }
            }

            .filter-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        .contacts-list-section {
            .contacts-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }
    }
}
</style>