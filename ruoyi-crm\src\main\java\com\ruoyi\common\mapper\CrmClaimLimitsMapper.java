package com.ruoyi.common.mapper;

import com.ruoyi.common.domain.entity.CrmClaimLimits;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户认领限制Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface CrmClaimLimitsMapper {
    
    /**
     * 获取用户的认领限制
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 认领限制
     */
    CrmClaimLimits selectUserClaimLimits(@Param("userId") Long userId, 
                                         @Param("roleIds") List<Long> roleIds);

    /**
     * 新增客户认领限制
     *
     * @param crmClaimLimits 客户认领限制
     * @return 结果
     */
    int insertCrmClaimLimits(CrmClaimLimits crmClaimLimits);
}