package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.dto.LeadConvertDTO;
import com.ruoyi.common.domain.entity.CrmLeads;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.controller.CrmLeadController.AssignForm;
import com.ruoyi.crm.service.ICrmLeadService;
import com.ruoyi.crm.utils.TestWebContextUtils;

/**
 * CrmLeadController 纯单元测试
 * 不依赖Spring上下文，只使用Mockito
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("线索控制器纯单元测试")
class CrmLeadControllerUnitTest {

    @Mock
    private ICrmLeadService crmLeadService;

    @InjectMocks
    private CrmLeadController crmLeadController;

    private CrmLeads testLead;
    private LeadConvertDTO convertDTO;
    private AssignForm assignForm;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testLead = new CrmLeads();
        testLead.setId(1L);
        testLead.setLeadName("测试线索");
        testLead.setCustomerName("测试客户");
        testLead.setMobile("13800138000");
        testLead.setEmail("<EMAIL>");
        testLead.setStatus("1");
        testLead.setResponsiblePersonId("1");
        testLead.setDelFlag("0");

        // 初始化转化DTO
        convertDTO = new LeadConvertDTO();
        convertDTO.setLeadId(1L);
        convertDTO.setConvertType("new");
        convertDTO.setCustomerName("新客户");
        convertDTO.setIndustry("IT");

        LeadConvertDTO.ContactInfo contact = new LeadConvertDTO.ContactInfo();
        contact.setName("联系人");
        contact.setPosition("经理");
        contact.setPhone("13800138000");
        contact.setEmail("<EMAIL>");
        convertDTO.setContact(contact);

        // 初始化分配表单
        assignForm = new AssignForm();
        assignForm.setLeadId(1L);
        assignForm.setNewOwnerId(2L);
    }



    @Nested
    @DisplayName("线索转化测试")
    class ConvertLeadTests {

        @Test
        @DisplayName("转化线索 - 成功")
        void testConvertLead_Success() {
            // Arrange
            doNothing().when(crmLeadService).convertLead(any(LeadConvertDTO.class));

            // Act
            AjaxResult result = crmLeadController.convertLead(convertDTO);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmLeadService).convertLead(convertDTO);
        }

        @Test
        @DisplayName("转化线索 - 服务异常")
        void testConvertLead_ServiceException() {
            // Arrange
            doThrow(new ServiceException("线索不存在")).when(crmLeadService).convertLead(any(LeadConvertDTO.class));

            // Act
            AjaxResult result = crmLeadController.convertLead(convertDTO);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("线索不存在", result.get("msg"));
            verify(crmLeadService).convertLead(convertDTO);
        }

        @Test
        @DisplayName("转化线索 - 运行时异常")
        void testConvertLead_RuntimeException() {
            // Arrange
            doThrow(new RuntimeException("数据库连接失败")).when(crmLeadService).convertLead(any(LeadConvertDTO.class));

            // Act
            AjaxResult result = crmLeadController.convertLead(convertDTO);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("数据库连接失败", result.get("msg"));
            verify(crmLeadService).convertLead(convertDTO);
        }
    }

    @Nested
    @DisplayName("线索查询测试")
    class QueryLeadTests {

        @Nested
        @DisplayName("分页查询测试")
        class PaginationQueryTests {
            
            @BeforeEach
            void setUpWebContext() {
                // 只在分页查询测试中设置 Web 上下文
                TestWebContextUtils.setupDefaultWebContext();
            }

            @AfterEach
            void tearDownWebContext() {
                TestWebContextUtils.cleanupWebContext();
            }

            @Test
            @DisplayName("获取线索列表")
            void testGetLeadList() {
                // ✅ 需要分页，所以需要 Web 上下文
                List<CrmLeads> leadList = Arrays.asList(testLead);
                when(crmLeadService.getLeadList(any(CrmLeads.class))).thenReturn(leadList);

                // Act
                TableDataInfo result = crmLeadController.getLeadList(new CrmLeads());

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                assertEquals(1, result.getTotal());
                assertNotNull(result.getRows());
                verify(crmLeadService).getLeadList(any(CrmLeads.class));
            }

            @Test
            @DisplayName("获取我负责的线索列表")
            void testGetMyLeadList() {
                List<CrmLeads> leadList = Arrays.asList(testLead);
                when(crmLeadService.getMyLeadList(any(CrmLeads.class))).thenReturn(leadList);

                // Act
                TableDataInfo result = crmLeadController.getMyLeadList(new CrmLeads());

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                verify(crmLeadService).getMyLeadList(any(CrmLeads.class));
            }

            @Test
            @DisplayName("获取下属的线索列表")
            void testGetSubordinateLeadList() {
                List<CrmLeads> leadList = Arrays.asList(testLead);
                when(crmLeadService.getSubordinateLeadList(any(CrmLeads.class))).thenReturn(leadList);

                // Act
                TableDataInfo result = crmLeadController.getSubordinateLeadList(new CrmLeads());

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                verify(crmLeadService).getSubordinateLeadList(any(CrmLeads.class));
            }

            @Test
            @DisplayName("获取我关注的线索列表")
            void testGetFollowedLeadList() {
                List<CrmLeads> leadList = Arrays.asList(testLead);
                when(crmLeadService.getFollowedLeadList(any(CrmLeads.class))).thenReturn(leadList);

                // Act
                TableDataInfo result = crmLeadController.getFollowedLeadList(new CrmLeads());

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                verify(crmLeadService).getFollowedLeadList(any(CrmLeads.class));
            }
        }

        @Nested
        @DisplayName("单个查询测试")
        class SingleQueryTests {
            
            @Test
            @DisplayName("获取线索详细信息")
            void testGetInfo() {
                // ✅ 不需要分页，所以不设置 Web 上下文
                // Arrange
                when(crmLeadService.selectCrmLeadsById(1L)).thenReturn(testLead);

                // Act
                AjaxResult result = crmLeadController.getInfo(1L);

                // Assert
                assertNotNull(result);
                assertTrue(result.isSuccess());
                assertEquals(testLead, result.get("data"));
                verify(crmLeadService).selectCrmLeadsById(1L);
            }
        }
    }

    @Nested
    @DisplayName("线索CRUD测试")
    class CrudLeadTests {

        @Test
        @DisplayName("新增线索 - 成功")
        void testAddWithRecord_Success() {
            // Arrange
            when(crmLeadService.insertCrmLeads(any(CrmLeads.class))).thenReturn(1);

            // Act
            AjaxResult result = crmLeadController.addWithRecord(testLead);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmLeadService).insertCrmLeads(testLead);
        }

        @Test
        @DisplayName("新增线索 - 失败")
        void testAddWithRecord_Failed() {
            // Arrange
            when(crmLeadService.insertCrmLeads(any(CrmLeads.class))).thenReturn(0);

            // Act
            AjaxResult result = crmLeadController.addWithRecord(testLead);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            verify(crmLeadService).insertCrmLeads(testLead);
        }

        @Test
        @DisplayName("新增线索 - 异常")
        void testAddWithRecord_Exception() {
            // Arrange
            when(crmLeadService.insertCrmLeads(any(CrmLeads.class)))
                    .thenThrow(new RuntimeException("数据库连接失败"));

            // Act
            AjaxResult result = crmLeadController.addWithRecord(testLead);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("数据库连接失败", result.get("msg"));
            verify(crmLeadService).insertCrmLeads(testLead);
        }

        @Test
        @DisplayName("修改线索 - 成功")
        void testEditWithRecord_Success() {
            // Arrange
            when(crmLeadService.updateCrmLeads(any(CrmLeads.class))).thenReturn(1);

            // Act
            AjaxResult result = crmLeadController.editWithRecord(testLead);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmLeadService).updateCrmLeads(testLead);
        }

        @Test
        @DisplayName("修改线索 - 异常")
        void testEditWithRecord_Exception() {
            // Arrange
            when(crmLeadService.updateCrmLeads(any(CrmLeads.class)))
                    .thenThrow(new RuntimeException("更新失败"));

            // Act
            AjaxResult result = crmLeadController.editWithRecord(testLead);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("更新失败", result.get("msg"));
            verify(crmLeadService).updateCrmLeads(testLead);
        }

        @Test
        @DisplayName("删除线索 - 成功")
        void testRemoveWithRecord_Success() {
            // Arrange
            Long[] ids = {1L, 2L};
            when(crmLeadService.deleteCrmLeadsByIds(ids)).thenReturn(2);

            // Act
            AjaxResult result = crmLeadController.removeWithRecord(ids);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmLeadService).deleteCrmLeadsByIds(ids);
        }

        @Test
        @DisplayName("删除线索 - 异常")
        void testRemoveWithRecord_Exception() {
            // Arrange
            Long[] ids = {1L, 2L};
            when(crmLeadService.deleteCrmLeadsByIds(ids))
                    .thenThrow(new RuntimeException("删除失败"));

            // Act
            AjaxResult result = crmLeadController.removeWithRecord(ids);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("删除失败", result.get("msg"));
            verify(crmLeadService).deleteCrmLeadsByIds(ids);
        }
    }

    @Nested
    @DisplayName("线索分配测试")
    class AssignLeadTests {

        @Test
        @DisplayName("分配线索 - 成功")
        void testAssignLead_Success() {
            try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
                // Arrange
                mockedSecurityUtils.when(SecurityUtils::getUserId).thenReturn(1L);
                mockedSecurityUtils.when(SecurityUtils::getUsername).thenReturn("testuser");
                
                when(crmLeadService.selectCrmLeadsById(1L)).thenReturn(testLead);
                when(crmLeadService.updateCrmLeads(any(CrmLeads.class))).thenReturn(1);

                // Act
                AjaxResult result = crmLeadController.assignLead(assignForm);

                // Assert
                assertNotNull(result);
                assertTrue(result.isSuccess());
                verify(crmLeadService).selectCrmLeadsById(1L);
                verify(crmLeadService).updateCrmLeads(any(CrmLeads.class));
            }
        }

        @Test
        @DisplayName("分配线索 - 线索不存在")
        void testAssignLead_LeadNotFound() {
            try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
                // Arrange
                mockedSecurityUtils.when(SecurityUtils::getUserId).thenReturn(1L);
                mockedSecurityUtils.when(SecurityUtils::getUsername).thenReturn("testuser");
                
                when(crmLeadService.selectCrmLeadsById(1L)).thenReturn(null);

                // Act
                AjaxResult result = crmLeadController.assignLead(assignForm);

                // Assert
                assertNotNull(result);
                assertTrue(result.isError());
                assertEquals("线索不存在", result.get("msg"));
                verify(crmLeadService).selectCrmLeadsById(1L);
                verify(crmLeadService, never()).updateCrmLeads(any(CrmLeads.class));
            }
        }

        @Test
        @DisplayName("分配线索 - 更新失败")
        void testAssignLead_UpdateFailed() {
            try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
                // Arrange
                mockedSecurityUtils.when(SecurityUtils::getUserId).thenReturn(1L);
                mockedSecurityUtils.when(SecurityUtils::getUsername).thenReturn("testuser");
                
                when(crmLeadService.selectCrmLeadsById(1L)).thenReturn(testLead);
                when(crmLeadService.updateCrmLeads(any(CrmLeads.class))).thenReturn(0);

                // Act
                AjaxResult result = crmLeadController.assignLead(assignForm);

                // Assert
                assertNotNull(result);
                assertTrue(result.isError());
                assertEquals("分配失败", result.get("msg"));
                verify(crmLeadService).selectCrmLeadsById(1L);
                verify(crmLeadService).updateCrmLeads(any(CrmLeads.class));
            }
        }

        @Test
        @DisplayName("分配线索 - 异常")
        void testAssignLead_Exception() {
            try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
                // Arrange
                mockedSecurityUtils.when(SecurityUtils::getUserId).thenReturn(1L);
                mockedSecurityUtils.when(SecurityUtils::getUsername).thenReturn("testuser");
                
                when(crmLeadService.selectCrmLeadsById(1L))
                        .thenThrow(new RuntimeException("数据库连接失败"));

                // Act
                AjaxResult result = crmLeadController.assignLead(assignForm);

                // Assert
                assertNotNull(result);
                assertTrue(result.isError());
                assertEquals("分配失败：数据库连接失败", result.get("msg"));
                verify(crmLeadService).selectCrmLeadsById(1L);
            }
        }
    }

    @Nested
    @DisplayName("其他功能测试")
    class OtherTests {

        @Test
        @DisplayName("导出线索")
        void testExport() {
            // Arrange
            List<CrmLeads> leadList = Arrays.asList(testLead);
            when(crmLeadService.getLeadList(any(CrmLeads.class))).thenReturn(leadList);

            // Act
            AjaxResult result = crmLeadController.export(new CrmLeads());

            // Assert
            assertNotNull(result);
            // 导出功能会返回文件名或成功信息
            verify(crmLeadService).getLeadList(any(CrmLeads.class));
        }
    }

    @Nested
    @DisplayName("AssignForm 内部类测试")
    class AssignFormTests {

        @Test
        @DisplayName("AssignForm - getter/setter 测试")
        void testAssignForm_GetterSetter() {
            // Arrange
            AssignForm form = new AssignForm();
            
            // Act
            form.setLeadId(1L);
            form.setNewOwnerId(2L);
            
            // Assert
            assertEquals(1L, form.getLeadId());
            assertEquals(2L, form.getNewOwnerId());
        }

        @Test
        @DisplayName("AssignForm - null值测试")
        void testAssignForm_NullValues() {
            // Arrange
            AssignForm form = new AssignForm();
            
            // Act & Assert
            assertNull(form.getLeadId());
            assertNull(form.getNewOwnerId());
        }
    }
}