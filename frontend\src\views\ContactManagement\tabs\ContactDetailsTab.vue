<template>
    <div class="modern-contact-details">
        <!-- 联系人头部信息 -->
        <div class="contact-header">
            <div class="contact-info">
                <div class="avatar">
                    {{ getInitials(entityData.name) }}
                </div>
                <div class="contact-basic">
                    <h3 class="contact-name">{{ entityData.name || '未命名联系人' }}</h3>
                    <p class="contact-company">
                        {{ entityData.customerName || '暂无客户信息' }} 
                        <span v-if="entityData.position" class="position">· {{ entityData.position }}</span>
                    </p>
                </div>
            </div>
            <div class="contact-actions">
                <el-button size="small" type="primary" :icon="Edit" @click="toggleEditMode">
                    {{ isEditing ? '保存' : '编辑' }}
                </el-button>
                <el-button size="small" :icon="Star" :type="entityData.isFollowing ? 'warning' : 'default'">
                    {{ entityData.isFollowing ? '已关注' : '关注' }}
                </el-button>
            </div>
        </div>

        <!-- 信息卡片网格 -->
        <div class="info-cards-grid">
            <!-- 个人信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <el-icon class="card-icon personal"><User /></el-icon>
                    <h4 class="card-title">个人信息</h4>
                </div>
                <div class="card-content">
                    <div class="info-item">
                        <span class="info-label">姓名</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.name" 
                                size="small" 
                                placeholder="请输入姓名"
                            />
                            <span v-else>{{ entityData.name || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">职位</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.position" 
                                size="small" 
                                placeholder="请输入职位"
                            />
                            <span v-else>{{ entityData.position || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">性别</span>
                        <div class="info-value">
                            <el-select 
                                v-if="isEditing" 
                                v-model="formData.gender" 
                                size="small" 
                                placeholder="请选择性别"
                            >
                                <el-option label="男" value="男" />
                                <el-option label="女" value="女" />
                            </el-select>
                            <span v-else>{{ entityData.gender || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">生日</span>
                        <div class="info-value">
                            <el-date-picker 
                                v-if="isEditing" 
                                v-model="formData.birthday" 
                                size="small" 
                                type="date"
                                placeholder="选择生日"
                            />
                            <span v-else>{{ formatDate(entityData.birthday) || '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系方式卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <el-icon class="card-icon contact"><Phone /></el-icon>
                    <h4 class="card-title">联系方式</h4>
                </div>
                <div class="card-content">
                    <div class="info-item">
                        <span class="info-label">手机</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.mobile" 
                                size="small" 
                                placeholder="请输入手机号"
                            />
                            <span v-else class="phone-number">{{ entityData.mobile || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">电话</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.phone" 
                                size="small" 
                                placeholder="请输入办公电话"
                            />
                            <span v-else>{{ entityData.phone || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">固话</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.telephone" 
                                size="small" 
                                placeholder="请输入固定电话"
                            />
                            <span v-else>{{ entityData.telephone || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮箱</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.email" 
                                size="small" 
                                placeholder="请输入邮箱"
                            />
                            <span v-else class="email-address">{{ entityData.email || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">地址</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.address" 
                                size="small" 
                                placeholder="请输入地址"
                            />
                            <span v-else>{{ entityData.address || '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 客户关系卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <el-icon class="card-icon business"><OfficeBuilding /></el-icon>
                    <h4 class="card-title">客户关系</h4>
                </div>
                <div class="card-content">
                    <div class="info-item">
                        <span class="info-label">所属客户</span>
                        <div class="info-value">
                            <el-select 
                                v-if="isEditing" 
                                v-model="formData.customerId" 
                                size="small" 
                                placeholder="请选择客户"
                                filterable
                                remote
                                :remote-method="searchCustomersRemote"
                                :loading="loadingCustomers"
                                clearable
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="customer in customerOptions"
                                    :key="customer.value"
                                    :label="customer.label"
                                    :value="customer.value"
                                />
                            </el-select>
                            <span v-else class="customer-name">{{ entityData.customerName || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">部门</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.department" 
                                size="small" 
                                placeholder="请输入部门"
                            />
                            <span v-else>{{ entityData.department || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">决策角色</span>
                        <div class="info-value">
                            <el-select 
                                v-if="isEditing" 
                                v-model="formData.decisionRole" 
                                size="small" 
                                placeholder="请选择决策角色"
                            >
                                <el-option label="决策者" value="决策者" />
                                <el-option label="影响者" value="影响者" />
                                <el-option label="使用者" value="使用者" />
                                <el-option label="其他" value="其他" />
                            </el-select>
                            <span v-else>
                                <el-tag v-if="entityData.decisionRole" :type="getRoleTagType(entityData.decisionRole)" size="small">
                                    {{ entityData.decisionRole }}
                                </el-tag>
                                <span v-else>-</span>
                            </span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">联系人级别</span>
                        <div class="info-value">
                            <el-select 
                                v-if="isEditing" 
                                v-model="formData.contactLevel" 
                                size="small" 
                                placeholder="请选择级别"
                            >
                                <el-option label="A" value="A" />
                                <el-option label="B" value="B" />
                                <el-option label="C" value="C" />
                                <el-option label="D" value="D" />
                            </el-select>
                            <span v-else>
                                <el-tag v-if="entityData.contactLevel" :type="getLevelTagType(entityData.contactLevel)" size="small">
                                    {{ entityData.contactLevel }}级
                                </el-tag>
                                <span v-else>-</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <el-icon class="card-icon other"><InfoFilled /></el-icon>
                    <h4 class="card-title">其他信息</h4>
                </div>
                <div class="card-content">
                    <div class="info-item">
                        <span class="info-label">负责人</span>
                        <div class="info-value">
                            <span>{{ getResponsiblePersonName(entityData.responsiblePersonId) || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">下次联系</span>
                        <div class="info-value">
                            <el-date-picker 
                                v-if="isEditing" 
                                v-model="formData.nextContactTime" 
                                size="small" 
                                type="datetime"
                                placeholder="选择下次联系时间"
                            />
                            <span v-else>{{ formatDateTime(entityData.nextContactTime) || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">创建时间</span>
                        <div class="info-value">
                            <span>{{ formatDateTime(entityData.createTime) || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">状态</span>
                        <div class="info-value">
                            <el-select 
                                v-if="isEditing" 
                                v-model="formData.status" 
                                size="small" 
                                placeholder="请选择状态"
                            >
                                <el-option label="有效" value="0" />
                                <el-option label="无效" value="1" />
                            </el-select>
                            <span v-else>
                                <el-tag :type="entityData.status === '0' ? 'success' : 'danger'" size="small">
                                    {{ entityData.status === '0' ? '有效' : '无效' }}
                                </el-tag>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 备注区域 -->
        <div class="remarks-section" v-if="isEditing || entityData.remarks">
            <div class="info-card full-width">
                <div class="card-header">
                    <el-icon class="card-icon remarks"><Document /></el-icon>
                    <h4 class="card-title">备注信息</h4>
                </div>
                <div class="card-content">
                    <el-input 
                        v-if="isEditing" 
                        v-model="formData.remarks" 
                        type="textarea" 
                        :rows="3"
                        placeholder="请输入备注信息"
                    />
                    <p v-else class="remarks-text">{{ entityData.remarks || '暂无备注' }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Document, Edit, InfoFilled, OfficeBuilding, Phone, Star, User } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { reactive, ref, watch, onMounted } from 'vue';
import { searchCustomers } from '@/api/crm/customers';
import { getPrimaryCustomerByContactId } from '@/api/crm/contacts';

// 定义 props
const props = defineProps({
    entityData: {
        type: Object,
        required: true
    }
});

// 定义 emits
const emit = defineEmits(['update:entity-data']);

// 响应式数据
const isEditing = ref(false);
const formData = reactive({});
const customerOptions = ref([]);
const loadingCustomers = ref(false);

// 初始化表单数据
const initFormData = () => {
    console.log('=== initFormData 函数被调用 ===');
    console.log('props.entityData:', props.entityData);
    console.log('props.entityData 完整结构:', JSON.stringify(props.entityData, null, 2));
    
    // 详细检查关键字段
    console.log('🔍 关键字段检查:');
    console.log('  gender原值:', props.entityData.gender, '类型:', typeof props.entityData.gender);
    console.log('  mobile原值:', props.entityData.mobile, '类型:', typeof props.entityData.mobile);
    console.log('  customerName原值:', props.entityData.customerName, '类型:', typeof props.entityData.customerName);
    console.log('  birthday原值:', props.entityData.birthday, '类型:', typeof props.entityData.birthday);
    
    Object.assign(formData, {
        id: props.entityData.id || '', // 确保包含ID
        name: props.entityData.name || '',
        position: props.entityData.position || '',
        customerName: props.entityData.customerName || '',
        customerId: props.entityData.customerId || '',
        // 🔧 修复手机号字段映射问题
        mobile: props.entityData.mobile || props.entityData.phone || '', // 优先使用mobile，如果没有则使用phone
        phone: props.entityData.phone || '', // 办公电话
        telephone: props.entityData.telephone || '', // 固定电话
        email: props.entityData.email || '',
        address: props.entityData.address || '',
        department: props.entityData.department || '',
        decisionRole: props.entityData.decisionRole || '',
        contactLevel: props.entityData.contactLevel || '',
        gender: props.entityData.gender || '',
        birthday: props.entityData.birthday || '',
        nextContactTime: props.entityData.nextContactTime || '',
        status: props.entityData.status || '0',
        remarks: props.entityData.remarks || ''
    });
    
    console.log('📋 填充后的formData:', formData);
    console.log('  填充后gender:', formData.gender);
    console.log('  填充后mobile:', formData.mobile);
};

// 加载联系人的客户信息
const loadContactCustomer = async () => {
    if (!props.entityData?.id) return;
    
    try {
        const response = await getPrimaryCustomerByContactId(props.entityData.id);
        if (response.code === 200 && response.data) {
            const customer = response.data;
            // 将客户信息设置到选项中
            customerOptions.value = [{
                label: customer.customerName || customer.name,
                value: customer.id
            }];
            // 更新表单数据中的customerId
            formData.customerId = customer.id;
        }
    } catch (error) {
        console.error('加载联系人客户信息失败:', error);
    }
};

// 监听 entityData 变化
watch(() => props.entityData, () => {
    initFormData();
    loadContactCustomer(); // 同时加载客户信息
}, { immediate: true });

// 组件挂载时加载数据
onMounted(() => {
    loadContactCustomer();
});

// 获取姓名首字母
const getInitials = (name) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
};

// 切换编辑模式
const toggleEditMode = () => {
    console.log('=== toggleEditMode 被触发 ===');
    console.log('当前编辑状态:', isEditing.value);
    
    if (isEditing.value) {
        console.log('准备保存数据...');
        // 保存数据
        saveData();
    } else {
        console.log('进入编辑模式...');
        // 进入编辑模式
        isEditing.value = true;
        initFormData();
    }
};

// 保存数据
const saveData = async () => {
    try {
        // 验证必填字段
        if (!formData.name?.trim()) {
            ElMessage.error('请输入联系人姓名');
            return;
        }

        console.log('=== 保存数据开始 ===');
        console.log('原始数据 entityData:', props.entityData);
        console.log('表单数据 formData:', formData);

        // 构造符合后端要求的更新数据，字段直接对应
        const updateData = {
            // 基础字段（直接对应后端）
            id: props.entityData.id || formData.id,
            name: formData.name || '',
            position: formData.position || '',
            mobile: formData.mobile || '', // 手机号码
            phone: formData.phone || '', // 办公电话
            telephone: formData.telephone || '', // 固定电话
            email: formData.email || '',
            address: formData.address || '',
            gender: formData.gender || '',
            birthday: formData.birthday || null,
            department: formData.department || '',
            decisionRole: formData.decisionRole || '',
            contactLevel: formData.contactLevel || '',
            nextContactTime: formData.nextContactTime || null,
            status: formData.status || '0',
            remarks: formData.remarks || '',
            responsiblePersonId: props.entityData.responsiblePersonId || '',
            
            // 保持原有的系统字段
            createTime: props.entityData.createTime,
            createBy: props.entityData.createBy,
            updateTime: new Date().toISOString(),
            delFlag: props.entityData.delFlag || '0',
            
            // 客户关系字段（如果存在）
            customerId: props.entityData.customerId,
            customerName: formData.customerName || props.entityData.customerName
        };

        console.log('映射后的更新数据:', updateData);

        // 发送更新事件
        emit('update:entity-data', updateData);
        console.log('事件已发送: update:entity-data');
        
        isEditing.value = false;
        ElMessage.success('保存成功');
        
        console.log('=== 保存数据完成 ===');
    } catch (error) {
        console.error('保存失败:', error);
        ElMessage.error('保存失败');
    }
};

// 远程搜索客户
const searchCustomersRemote = async (query) => {
    if (!query) {
        customerOptions.value = [];
        return;
    }
    
    loadingCustomers.value = true;
    try {
        const response = await searchCustomers(query);
        if (response.code === 200) {
            customerOptions.value = (response.rows || response.data || []).map(customer => ({
                label: customer.customerName || customer.name,
                value: customer.id
            }));
        } else {
            customerOptions.value = [];
        }
    } catch (error) {
        console.error('搜索客户失败:', error);
        customerOptions.value = [];
    } finally {
        loadingCustomers.value = false;
    }
};

// 获取负责人姓名
const getResponsiblePersonName = (personId) => {
    // 这里可以根据实际需求从用户列表中获取姓名
    return personId ? `用户${personId}` : '';
};

// 格式化日期
const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('zh-CN');
};

// 格式化日期时间
const formatDateTime = (datetime) => {
    if (!datetime) return '';
    return new Date(datetime).toLocaleString('zh-CN');
};

// 获取角色标签类型
const getRoleTagType = (role) => {
    const typeMap = {
        '决策者': 'danger',
        '影响者': 'warning',
        '使用者': 'success',
        '其他': 'info'
    };
    return typeMap[role] || 'info';
};

// 获取级别标签类型
const getLevelTagType = (level) => {
    const typeMap = {
        'A': 'danger',
        'B': 'warning',
        'C': 'success',
        'D': 'info'
    };
    return typeMap[level] || 'info';
};
</script>

<style scoped>
.modern-contact-details {
    padding: 20px;
    background: #f8fafc;
    min-height: 100%;
}

/* 联系人头部 */
.contact-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid #e2e8f0;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 22px;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.contact-basic h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
}

.contact-basic p {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
}

.position {
    color: #374151;
    font-weight: 500;
}

.contact-actions {
    display: flex;
    gap: 8px;
}

/* 信息卡片网格 */
.info-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.info-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.info-card.full-width {
    grid-column: 1 / -1;
}

/* 卡片头部 */
.card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.card-icon {
    font-size: 18px;
}

.card-icon.personal {
    color: #8b5cf6;
}

.card-icon.contact {
    color: #10b981;
}

.card-icon.business {
    color: #3b82f6;
}

.card-icon.other {
    color: #f59e0b;
}

.card-icon.remarks {
    color: #ef4444;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

/* 卡片内容 */
.card-content {
    padding: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    min-height: 32px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    min-width: 80px;
    flex-shrink: 0;
}

.info-value {
    flex: 1;
    text-align: right;
    color: #1f2937;
    font-size: 14px;
}

.info-value span {
    word-break: break-all;
}

.phone-number,
.email-address {
    color: #3b82f6;
    cursor: pointer;
}

.phone-number:hover,
.email-address:hover {
    text-decoration: underline;
}

.customer-name {
    color: #059669;
    font-weight: 500;
}

/* 备注区域 */
.remarks-section {
    margin-top: 20px;
}

.remarks-text {
    color: #374151;
    line-height: 1.6;
    margin: 0;
    padding: 12px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* 表单元素样式 */
:deep(.el-input),
:deep(.el-select),
:deep(.el-date-picker) {
    width: 100%;
    max-width: 200px;
}

:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper),
:deep(.el-date-picker .el-input__wrapper) {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover),
:deep(.el-select .el-input__wrapper:hover),
:deep(.el-date-picker .el-input__wrapper:hover) {
    border-color: #3b82f6;
    background: #ffffff;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus),
:deep(.el-date-picker .el-input__wrapper.is-focus) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background: #ffffff;
}

/* 标签样式 */
:deep(.el-tag) {
    border-radius: 12px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .info-cards-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .modern-contact-details {
        padding: 16px;
    }
    
    .contact-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .contact-info {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-actions {
        width: 100%;
        justify-content: center;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .info-label {
        min-width: auto;
    }
    
    .info-value {
        text-align: left;
        width: 100%;
    }
    
    :deep(.el-input),
    :deep(.el-select),
    :deep(.el-date-picker) {
        max-width: 100%;
    }
}

/* 加载动画 */
.card-content {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 编辑模式下的特殊样式 */
.info-value :deep(.el-textarea__wrapper) {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    width: 100%;
    max-width: none;
}

.info-value :deep(.el-textarea__wrapper:hover) {
    border-color: #3b82f6;
    background: #ffffff;
}

.info-value :deep(.el-textarea__wrapper.is-focus) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background: #ffffff;
}
</style>