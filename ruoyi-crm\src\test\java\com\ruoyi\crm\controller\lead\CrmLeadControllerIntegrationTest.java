package com.ruoyi.crm.controller;

import static com.ruoyi.crm.controller.TestAssertionHelper.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.dto.LeadConvertDTO;
import com.ruoyi.common.domain.entity.CrmLeads;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.controller.CrmLeadController.AssignForm;
import com.ruoyi.crm.service.ICrmLeadService;

/**
 * CrmLeadController 集成测试类
 * 使用真实的数据库和完整的Spring上下文进行测试
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("线索控制器集成测试")
class CrmLeadControllerIntegrationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(CrmLeadControllerIntegrationTest.class);
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmLeadService crmLeadService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        // 只设置MockMvc，不创建测试数据
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .alwaysDo(print())
                .build();
    }

    // 保留工具方法，供需要的测试使用
    private CrmLeads createTestLead(String leadName, String customerName) {
        CrmLeads testLead = new CrmLeads();
        testLead.setLeadName(leadName);
        testLead.setCustomerName(customerName);
        testLead.setMobile("13900139000");
        testLead.setEmail("<EMAIL>");
        testLead.setStatus("1");
        testLead.setResponsiblePersonId("1");
        testLead.setRemark("集成测试用例");

        // 插入测试数据
        crmLeadService.insertCrmLeads(testLead);
        assertNotNull(testLead.getId(), "测试数据创建失败");
        return testLead;
    }

    private void cleanupTestLead(Long leadId) {
        if (leadId != null) {
            try {
                crmLeadService.deleteCrmLeadsById(leadId);
            } catch (Exception e) {
                // 忽略清理错误
            }
        }
    }

    @Nested
    @DisplayName("线索CRUD集成测试")
    class CrudIntegrationTests {

        @Test
        @DisplayName("完整的CRUD流程测试")
        void testFullCrudFlow() throws Exception {
            // 这个测试自己创建数据，不需要预置数据
            // 1. 创建线索
            CrmLeads newLead = new CrmLeads();
            newLead.setLeadName("CRUD测试线索");
            newLead.setCustomerName("CRUD测试客户");
            newLead.setMobile("13800138001");
            newLead.setEmail("<EMAIL>");
            newLead.setStatus("1");
            newLead.setResponsiblePersonId("1");
            // newLead.setSource("1");  // 如果CrmLeads没有这些字段则注释掉
            // newLead.setIndustry("教育");

            MvcResult createResult = mockMvc.perform(post("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newLead)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            String createResponseContent = createResult.getResponse().getContentAsString();
            
            // 方案1：直接从 JSON path 获取 ID（最简单）
            JsonNode rootNode = objectMapper.readTree(createResponseContent);
            Long createdLeadId = rootNode.path("data").path("id").asLong();
            
            // 方案2：如果需要完整的 CrmLeads 对象
            JsonNode dataNode = rootNode.path("data");
            CrmLeads createdLead = objectMapper.treeToValue(dataNode, CrmLeads.class);
            // 验证两种方式得到的 ID 一致
            assertEquals(createdLeadId, createdLead.getId());
            
            assertNotNull(createdLeadId, "创建线索后应返回ID");
            logger.warn("···createdLeadId: {}", createdLeadId);
            logger.warn("···createdLead ！！！: {}", createdLead);
            try {
                // 2. 查询单个线索
                mockMvc.perform(get("/front/crm/leads/{id}", createdLeadId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200))
                        .andExpect(jsonPath("$.data.leadName").value("CRUD测试线索"))
                        .andExpect(jsonPath("$.data.customerName").value("CRUD测试客户"));

                // 3. 修改线索
                newLead.setId(createdLeadId);
                newLead.setLeadName("修改后的线索名称");
                newLead.setRemarks("已修改");

                mockMvc.perform(put("/front/crm/leads")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newLead)))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 4. 验证修改结果
                mockMvc.perform(get("/front/crm/leads/{id}", createdLeadId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.data.leadName").value("修改后的线索名称"))
                        .andExpect(jsonPath("$.data.remarks").value("已修改"));

            } finally {
                // 5. 删除线索
                mockMvc.perform(delete("/front/crm/leads/{ids}", createdLeadId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));

                // 6. 验证删除结果 - 查询应该返回null或空
                MvcResult deleteVerifyResult = mockMvc.perform(get("/front/crm/leads/{id}", createdLeadId))
                        .andExpect(status().isOk())
                        .andReturn();
                
                String deleteVerifyContent = deleteVerifyResult.getResponse().getContentAsString();
                AjaxResult deleteVerifyResponse = objectMapper.readValue(deleteVerifyContent, AjaxResult.class);
                // 删除后查询应该返回null或者错误
                assertTrue(deleteVerifyResponse.get("data") == null || !deleteVerifyResponse.isSuccess());
            }
        }

        // 删除线索 然后查看线索信息。 根据delflag 判断是否删除成功。
        @Test
        @DisplayName("删除线索 - 详细验证")
        void testDeleteLead_DetailedValidation() throws Exception {
            // 这个测试需要预置数据
            CrmLeads testLead = createTestLead("删除测试线索", "删除测试客户");
            Long testLeadId = testLead.getId();
            
            try {
                // 1. 删除前先确认线索存在
                CrmLeads beforeDelete = crmLeadService.selectCrmLeadsById(testLeadId);
                assertNotNull(beforeDelete, "删除前线索应该存在");
                assertEquals("0", beforeDelete.getDelFlag(), "删除前del_flag应该为0");
                
                // 2. 删除线索
                mockMvc.perform(delete("/front/crm/leads/{ids}", testLeadId))
                        .andExpect(status().isOk())
                        .andExpect(jsonPath("$.code").value(200));
                
                // 3. 验证删除结果 - 正常查询应该返回null（因为查询条件包含del_flag='0'）
                CrmLeads afterDelete = crmLeadService.selectCrmLeadsById(testLeadId);
                assertNull(afterDelete, "删除后通过正常查询应该返回null，因为del_flag已不为0");
                
                // 4. 通过直接SQL验证删除标志已更新（这里我们验证删除操作确实执行了）
                // 由于selectCrmLeadsById有del_flag='0'的过滤条件，删除后查询为null是正确的行为
                // 这表明删除操作（逻辑删除）成功执行
                System.out.println("✅ 删除验证通过：线索已被逻辑删除（del_flag设置为'2'）");
            } finally {
                // 清理测试数据（虽然已经被逻辑删除了，但为了保险起见）
                cleanupTestLead(testLeadId);
            }
        }

        @Test
        @DisplayName("查询线索列表 - 带分页和筛选")
        void testGetLeadListWithFilters() throws Exception {
            // 这个测试需要预置数据来验证筛选功能
            CrmLeads testLead = createTestLead("列表测试线索", "列表测试客户");
            Long testLeadId = testLead.getId();
            
            try {
                MvcResult result = mockMvc.perform(get("/front/crm/leads/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .param("leadName", "列表测试")
                        .param("status", "1"))
                        .andExpect(status().isOk())
                        .andReturn();

                String responseContent = result.getResponse().getContentAsString();
                TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);
                // 打印响应内容 json格式
                String jsonResponse = objectMapper.writeValueAsString(response);
                // 使用 UTF-8 编码输出，避免乱码
                try {
                    System.out.write(("🔍 响应内容: " + jsonResponse + "\n").getBytes("UTF-8"));
                    System.out.flush();
                } catch (Exception e) {
                    System.out.println("Response content: " + jsonResponse);
                }
                assertNotNull(response);
                assertEquals(200, response.getCode());
                assertNotNull(response.getRows());
                assertTrue(response.getTotal() >= 1, "应该至少找到一条测试数据");
            } finally {
                cleanupTestLead(testLeadId);
            }
        }
    }

    @Nested
    @DisplayName("线索分配集成测试")
    class AssignIntegrationTests {
        
        private CrmLeads testLead;
        private Long testLeadId;
        
        @BeforeEach
        void setUpAssignTests() {
            // 只有分配测试需要预置数据
            testLead = createTestLead("分配测试线索", "分配测试客户");
            testLeadId = testLead.getId();
        }
        
        @AfterEach
        void tearDownAssignTests() {
            cleanupTestLead(testLeadId);
        }

        @Test
        @DisplayName("线索分配流程测试")
        void testAssignFlow() throws Exception {
            // 1. 确保测试线索存在
            CrmLeads originalLead = crmLeadService.selectCrmLeadsById(testLeadId);
            assertNotNull(originalLead);
            String originalOwnerId = originalLead.getResponsiblePersonId();

            // 2. 执行分配
            AssignForm assignForm = new AssignForm();
            assignForm.setLeadId(testLeadId);
            assignForm.setNewOwnerId(2L);

            mockMvc.perform(post("/front/crm/leads/assign")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(assignForm)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            // 3. 验证分配结果
            CrmLeads updatedLead = crmLeadService.selectCrmLeadsById(testLeadId);
            assertNotNull(updatedLead);
            assertEquals("2", updatedLead.getResponsiblePersonId());
            assertNotEquals(originalOwnerId, updatedLead.getResponsiblePersonId());
        }

        @Test
        @DisplayName("分配不存在的线索")
        void testAssignNonExistentLead() throws Exception {
            // 这个测试不需要预置数据，测试不存在的线索
            AssignForm assignForm = new AssignForm();
            assignForm.setLeadId(99999L); // 不存在的ID
            assignForm.setNewOwnerId(2L);

            mockMvc.perform(post("/front/crm/leads/assign")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(assignForm)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("线索不存在"));
        }
    }

    @Nested
    @DisplayName("线索转化集成测试")
    class ConvertIntegrationTests {
        
        private CrmLeads testLead;
        private Long testLeadId;
        
        @BeforeEach
        void setUpConvertTests() {
            // 转化测试需要预置数据
            testLead = createTestLead("转化测试线索", "转化测试客户");
            testLeadId = testLead.getId();
        }
        
        @AfterEach
        void tearDownConvertTests() {
            cleanupTestLead(testLeadId);
        }

        @Test
        @DisplayName("线索转化新客户流程 - 完整验证")
        void testConvertLead_Success() throws Exception {
            // 1. 获取转化前的线索状态
            CrmLeads originalLead = crmLeadService.selectCrmLeadsById(testLeadId);
            assertNotNull(originalLead, "测试线索不存在");
            String originalStatus = originalLead.getStatus();
            
            System.out.println("🔍 转化前线索状态: " + originalStatus);
            System.out.println("🔍 转化前线索信息: " + objectMapper.writeValueAsString(originalLead));

            // 2. 准备转化数据
            LeadConvertDTO convertDTO = new LeadConvertDTO();
            convertDTO.setLeadId(testLeadId);
            convertDTO.setConvertType("new");
            convertDTO.setCustomerName("转化生成的新客户");
            convertDTO.setIndustry("IT");

            LeadConvertDTO.ContactInfo contact = new LeadConvertDTO.ContactInfo();
            contact.setName("转化联系人");
            contact.setPosition("经理");
            contact.setPhone("13900139001");
            contact.setEmail("<EMAIL>");
            convertDTO.setContact(contact);

            System.out.println("🔍 转化请求数据: " + objectMapper.writeValueAsString(convertDTO));

            // 3. 执行转化并获取详细响应
            MvcResult result = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(convertDTO)))
                    .andExpect(status().isOk())
                    .andReturn();

            // 4. 解析并验证响应
            String responseContent = result.getResponse().getContentAsString();
            System.out.println("🔍 转化响应内容: " + responseContent);
            
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            // 验证响应结构
            assertNotNull(response, "响应不能为空");
            System.out.println("🔍 响应码: " + response.get("code"));
            System.out.println("🔍 响应消息: " + response.get("msg"));
            System.out.println("🔍 响应数据: " + response.get("data"));
            
            // 如果失败，打印详细错误信息
            if (!response.isSuccess()) {
                fail("线索转化失败: 错误码=" + response.get("code") + 
                     ", 错误信息=" + response.get("msg") + 
                     ", 响应数据=" + response.get("data"));
            }
            
            // 验证响应码和消息
            assertEquals(200, ((Integer) response.get("code")).intValue(), 
                "转化应该成功，但返回错误码: " + response.get("code") + ", 错误信息: " + response.get("msg"));

            // 5. 验证数据库中线索状态已改变
            CrmLeads convertedLead = crmLeadService.selectCrmLeadsById(testLeadId);
            assertNotNull(convertedLead, "转化后线索不应该被删除");
            
            System.out.println("🔍 转化后线索状态: " + convertedLead.getStatus());
            System.out.println("🔍 转化后线索信息: " + objectMapper.writeValueAsString(convertedLead));
            
            // 验证状态确实改变了（根据实际业务逻辑调整状态值）
            assertNotEquals(originalStatus, convertedLead.getStatus(), 
                "线索状态应该发生改变，转化前: " + originalStatus + ", 转化后: " + convertedLead.getStatus());
            
            // 如果转化成功，通常状态会变为"已转化"状态
            assertTrue(
                "2".equals(convertedLead.getStatus()) || "已转化".equals(convertedLead.getStatus()) || "converted".equals(convertedLead.getStatus()),
                "转化后线索状态应该为已转化状态，当前状态: " + convertedLead.getStatus()
            );

            // 6. 验证响应中包含转化结果信息
            Object data = response.get("data");
            if (data != null) {
                System.out.println("🔍 转化结果数据类型: " + data.getClass().getName());
                System.out.println("🔍 转化结果数据内容: " + data);
                
                // 如果返回的是Map或自定义对象，可以进一步验证
                if (data instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> resultMap = (java.util.Map<String, Object>) data;
                    
                    // 验证是否包含客户ID、联系人ID等关键信息
                    assertTrue(resultMap.containsKey("customerId") || resultMap.containsKey("leadId"), 
                        "转化结果应该包含客户ID或线索ID等关键信息，实际数据: " + resultMap);
                }
            }
        }

        @Test
        @DisplayName("线索转化 - 参数验证测试")
        void testConvertLead_ParameterValidation() throws Exception {
            // 这些测试不需要预置数据，测试参数验证
            // 测试缺少必要参数的情况
            System.out.println("\n🔍 测试1: 空的转化请求");
            LeadConvertDTO emptyDTO = new LeadConvertDTO();
            
            MvcResult emptyResult = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(emptyDTO)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String emptyResponse = emptyResult.getResponse().getContentAsString();
            System.out.println("🔍 空请求响应: " + emptyResponse);
            
            AjaxResult emptyResult_parsed = objectMapper.readValue(emptyResponse, AjaxResult.class);
            assertFalse(emptyResult_parsed.isSuccess(), 
                "空请求应该失败，但返回: " + emptyResponse);

            // 测试缺少线索ID的情况
            System.out.println("\n🔍 测试2: 缺少线索ID");
            LeadConvertDTO noLeadIdDTO = new LeadConvertDTO();
            noLeadIdDTO.setConvertType("new");
            noLeadIdDTO.setCustomerName("测试客户");
            
            MvcResult noLeadIdResult = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(noLeadIdDTO)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String noLeadIdResponse = noLeadIdResult.getResponse().getContentAsString();
            System.out.println("🔍 缺少线索ID响应: " + noLeadIdResponse);
            
            AjaxResult noLeadIdResult_parsed = objectMapper.readValue(noLeadIdResponse, AjaxResult.class);
            assertFalse(noLeadIdResult_parsed.isSuccess(), 
                "缺少线索ID应该失败，但返回: " + noLeadIdResponse);

            // 测试缺少转化类型的情况
            System.out.println("\n🔍 测试3: 缺少转化类型");
            LeadConvertDTO noTypeDTO = new LeadConvertDTO();
            noTypeDTO.setLeadId(testLeadId);
            noTypeDTO.setCustomerName("测试客户");
            
            MvcResult noTypeResult = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(noTypeDTO)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String noTypeResponse = noTypeResult.getResponse().getContentAsString();
            System.out.println("🔍 缺少转化类型响应: " + noTypeResponse);
            
            AjaxResult noTypeResult_parsed = objectMapper.readValue(noTypeResponse, AjaxResult.class);
            assertFalse(noTypeResult_parsed.isSuccess(), 
                "缺少转化类型应该失败，但返回: " + noTypeResponse);
        }

        @Test
        @DisplayName("转化不存在的线索")
        void testConvertNonExistentLead() throws Exception {
            // 这个测试不需要预置数据，测试不存在的线索
            Long nonExistentLeadId = 99999L;
            
            System.out.println("🔍 测试转化不存在的线索ID: " + nonExistentLeadId);
            
            LeadConvertDTO convertDTO = new LeadConvertDTO();
            convertDTO.setLeadId(nonExistentLeadId);
            convertDTO.setConvertType("new");
            convertDTO.setCustomerName("测试客户");

            MvcResult result = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(convertDTO)))
                    .andExpect(status().isOk())
                    .andReturn();
            
            String responseContent = result.getResponse().getContentAsString();
            System.out.println("🔍 不存在线索转化响应: " + responseContent);
            
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            // 验证应该返回错误
            assertFalse(response.isSuccess(), 
                "转化不存在的线索应该失败，但返回成功: " + responseContent);
            
            // 验证错误信息
            Integer code = (Integer) response.get("code");
            String msg = (String) response.get("msg");
            
            assertTrue(code == 500 || code == 404 || !response.isSuccess(), 
                "错误码应该表示失败，实际错误码: " + code);
            
            assertTrue(msg != null && (
                msg.contains("线索不存在") || 
                msg.contains("not found") || 
                msg.contains("不存在")
            ), "错误信息应该明确说明线索不存在，实际错误信息: " + msg);
        }

        @Test
        @DisplayName("线索转化 - 重复转化测试")
        void testConvertAlreadyConvertedLead() throws Exception {
            // 这个测试需要自己的测试数据来进行重复转化测试
            CrmLeads repeatTestLead = createTestLead("重复转化测试线索", "重复转化测试客户");
            Long repeatTestLeadId = repeatTestLead.getId();
            
            try {
                // 先成功转化一次
                LeadConvertDTO convertDTO = new LeadConvertDTO();
                convertDTO.setLeadId(repeatTestLeadId);
                convertDTO.setConvertType("new");
                convertDTO.setCustomerName("首次转化客户");
                convertDTO.setIndustry("IT");

                LeadConvertDTO.ContactInfo contact = new LeadConvertDTO.ContactInfo();
                contact.setName("首次转化联系人");
                contact.setPosition("经理");
                contact.setPhone("13900139001");
                contact.setEmail("<EMAIL>");
                convertDTO.setContact(contact);

                System.out.println("🔍 首次转化请求: " + objectMapper.writeValueAsString(convertDTO));

                // 第一次转化
                MvcResult firstResult = mockMvc.perform(post("/front/crm/leads/convert")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(convertDTO)))
                        .andExpect(status().isOk())
                        .andReturn();

                String firstResponse = firstResult.getResponse().getContentAsString();
                System.out.println("🔍 首次转化响应: " + firstResponse);

                // 再次尝试转化同一线索
                convertDTO.setCustomerName("重复转化客户");
                convertDTO.getContact().setEmail("<EMAIL>");
                
                System.out.println("🔍 重复转化请求: " + objectMapper.writeValueAsString(convertDTO));

                MvcResult secondResult = mockMvc.perform(post("/front/crm/leads/convert")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(convertDTO)))
                        .andExpect(status().isOk())
                        .andReturn();

                String secondResponse = secondResult.getResponse().getContentAsString();
                System.out.println("🔍 重复转化响应: " + secondResponse);
                
                AjaxResult secondResult_parsed = objectMapper.readValue(secondResponse, AjaxResult.class);
                
                // 重复转化应该失败或给出明确提示
                if (secondResult_parsed.isSuccess()) {
                    System.out.println("⚠️  注意: 系统允许重复转化，可能需要检查业务逻辑");
                } else {
                    String msg = (String) secondResult_parsed.get("msg");
                    assertTrue(msg != null && (
                        msg.contains("已转化") || 
                        msg.contains("重复") || 
                        msg.contains("已经")
                    ), "重复转化的错误信息应该明确，实际错误信息: " + msg);
                }
            } finally {
                cleanupTestLead(repeatTestLeadId);
            }
        }
    }

    @Nested
    @DisplayName("数据权限集成测试")
    class DataPermissionTests {
        
        private CrmLeads testLead;
        private Long testLeadId;
        
        @BeforeEach
        void setUpDataPermissionTests() {
            // 数据权限测试需要预置数据
            testLead = createTestLead("权限测试线索", "权限测试客户");
            testLeadId = testLead.getId();
        }
        
        @AfterEach
        void tearDownDataPermissionTests() {
            cleanupTestLead(testLeadId);
        }

        @Test
        @DisplayName("我负责的线索列表")
        void testMyLeadList() throws Exception {
            // 确保当前用户负责的线索
            testLead.setResponsiblePersonId(getCurrentUserId().toString());
            crmLeadService.updateCrmLeads(testLead);

            MvcResult result = mockMvc.perform(get("/front/crm/leads/my")
                    .param("pageNum", "1")
                    .param("pageSize", "10"))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);

            assertNotNull(response);
            assertEquals(200, response.getCode());
            // 应该能找到当前用户负责的线索
            assertTrue(response.getTotal() >= 1);
        }

        @Test
        @DisplayName("下属的线索列表")
        void testSubordinateLeadList() throws Exception {
            mockMvc.perform(get("/front/crm/leads/subordinate")
                    .param("pageNum", "1")
                    .param("pageSize", "10"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
        }

        @Test
        @DisplayName("我关注的线索列表")
        void testFollowedLeadList() throws Exception {
            mockMvc.perform(get("/front/crm/leads/followed")
                    .param("pageNum", "1")
                    .param("pageSize", "10"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
        }
    }

    @Nested
    @DisplayName("导出功能集成测试")
    class ExportIntegrationTests {

        @Test
        @DisplayName("导出线索数据")
        void testExportLeads() throws Exception {
            // 导出测试不需要特定的预置数据，使用查询条件即可
            CrmLeads queryLead = new CrmLeads();
            queryLead.setStatus("1");

            MvcResult result = mockMvc.perform(post("/front/crm/leads/export")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(queryLead)))
                    .andExpect(status().isOk())
                    .andReturn();

            // 验证返回结果包含导出文件信息
            String responseContent = result.getResponse().getContentAsString();
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);
            
            assertNotNull(response);
            assertEquals(200, (Integer) response.get("code"));
        }
    }

    @Nested
    @DisplayName("异常情况集成测试")
    class ExceptionIntegrationTests {

        @Test
        @DisplayName("无效参数测试 - 详细验证")
        void testInvalidParameters() throws Exception {
            // 这些测试不需要预置数据，测试异常情况
            System.out.println("\n🔍 测试无效参数处理");
            
            // 测试1: 无效的线索ID格式
            System.out.println("🔍 测试1: 无效的线索ID格式");
            MvcResult invalidIdResult = mockMvc.perform(get("/front/crm/leads/{id}", "invalid"))
                    .andReturn();
            
            System.out.println("🔍 无效ID响应状态: " + invalidIdResult.getResponse().getStatus());
            System.out.println("🔍 无效ID响应内容: " + invalidIdResult.getResponse().getContentAsString());
            
            // 验证应该返回400错误或者统一的错误响应
            assertTrue(
                invalidIdResult.getResponse().getStatus() == 400 || 
                invalidIdResult.getResponse().getStatus() == 500,
                "无效ID格式应该返回错误状态码"
            );

            // 测试2: 空的请求体创建线索
            System.out.println("\n🔍 测试2: 空请求体创建线索");
            MvcResult emptyBodyResult = mockMvc.perform(post("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content("{}"))
                    .andExpect(status().isOk())
                    .andReturn();
                    
            String emptyBodyResponse = emptyBodyResult.getResponse().getContentAsString();
            System.out.println("🔍 空请求体响应: " + emptyBodyResponse);
            
            AjaxResult emptyBodyResult_parsed = objectMapper.readValue(emptyBodyResponse, AjaxResult.class);
            
            // 空请求体应该失败或至少给出验证错误
            if (emptyBodyResult_parsed.isSuccess()) {
                System.out.println("⚠️  警告: 空请求体创建线索成功，可能需要增加参数验证");
                
                // 如果创建成功，清理测试数据
                Object data = emptyBodyResult_parsed.get("data");
                if (data instanceof CrmLeads) {
                    CrmLeads createdLead = (CrmLeads) data;
                    if (createdLead.getId() != null) {
                        cleanupTestLead(createdLead.getId());
                    }
                }
            } else {
                String msg = (String) emptyBodyResult_parsed.get("msg");
                System.out.println("🔍 空请求体错误信息: " + msg);
                assertTrue(msg != null && msg.length() > 0, "错误信息应该不为空");
            }

            // 测试3: 缺少必要字段的请求
            System.out.println("\n🔍 测试3: 缺少必要字段");
            CrmLeads incompleteLeads = new CrmLeads();
            // 只设置一个字段，其他必要字段为空
            incompleteLeads.setRemark("只有备注");
            
            MvcResult incompleteResult = mockMvc.perform(post("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(incompleteLeads)))
                    .andExpect(status().isOk())
                    .andReturn();
                    
            String incompleteResponse = incompleteResult.getResponse().getContentAsString();
            System.out.println("🔍 不完整数据响应: " + incompleteResponse);
            
            AjaxResult incompleteResult_parsed = objectMapper.readValue(incompleteResponse, AjaxResult.class);
            
            if (incompleteResult_parsed.isSuccess()) {
                System.out.println("⚠️  警告: 不完整数据创建线索成功，检查是否需要更严格的验证");
                
                // 如果创建成功，检查创建的数据并清理
                Object data = incompleteResult_parsed.get("data");
                if (data instanceof CrmLeads) {
                    CrmLeads createdLead = (CrmLeads) data;
                    System.out.println("🔍 创建的线索数据: " + objectMapper.writeValueAsString(createdLead));
                    
                    // 清理测试数据
                    if (createdLead.getId() != null) {
                        cleanupTestLead(createdLead.getId());
                    }
                }
            } else {
                System.out.println("✅ 不完整数据被正确拒绝");
            }
        }

        @Test
        @DisplayName("删除不存在的线索 - 详细验证")
        void testDeleteNonExistentLead() throws Exception {
            // 这个测试不需要预置数据，测试删除不存在的线索
            Long nonExistentId = 99999L;
            System.out.println("🔍 测试删除不存在的线索ID: " + nonExistentId);
            
            MvcResult deleteResult = mockMvc.perform(delete("/front/crm/leads/{ids}", nonExistentId))
                    .andExpect(status().isOk())
                    .andReturn();
                    
            String deleteResponse = deleteResult.getResponse().getContentAsString();
            System.out.println("🔍 删除不存在线索响应: " + deleteResponse);
            
            AjaxResult deleteResult_parsed = objectMapper.readValue(deleteResponse, AjaxResult.class);
            
            // 验证响应结构
            assertNotNull(deleteResult_parsed, "删除响应不能为空");
            
            Integer code = (Integer) deleteResult_parsed.get("code");
            String msg = (String) deleteResult_parsed.get("msg");
            
            System.out.println("🔍 删除响应码: " + code);
            System.out.println("🔍 删除响应消息: " + msg);
            
            // 删除不存在的记录通常有两种处理方式:
            // 1. 返回成功(幂等操作)
            // 2. 返回错误(明确指出记录不存在)
            assertTrue(code == 200 || code == 404 || code == 500, 
                "删除不存在记录的响应码应该是200(成功)、404(不存在)或500(错误)，实际: " + code);
                
            if (code == 200) {
                System.out.println("✅ 删除操作采用幂等设计，删除不存在的记录返回成功");
            } else {
                System.out.println("✅ 删除操作明确区分记录是否存在");
                assertTrue(msg != null && msg.length() > 0, "错误情况下应该有明确的错误信息");
            }
        }

        @Test
        @DisplayName("并发操作冲突测试")
        void testConcurrentOperationConflict() throws Exception {
            // 这个测试需要自己创建测试数据
            System.out.println("🔍 测试并发操作冲突");
            
            // 模拟两个线程同时删除同一个线索
            CrmLeads concurrentTestLead = createTestLead("并发测试线索", "并发测试客户");
            Long concurrentTestLeadId = concurrentTestLead.getId();
            
            try {
                // 第一次删除
                MvcResult firstDelete = mockMvc.perform(delete("/front/crm/leads/{ids}", concurrentTestLeadId))
                        .andExpect(status().isOk())
                        .andReturn();
                        
                String firstDeleteResponse = firstDelete.getResponse().getContentAsString();
                System.out.println("🔍 第一次删除响应: " + firstDeleteResponse);
                
                // 第二次删除同一个线索
                MvcResult secondDelete = mockMvc.perform(delete("/front/crm/leads/{ids}", concurrentTestLeadId))
                        .andExpect(status().isOk())
                        .andReturn();
                        
                String secondDeleteResponse = secondDelete.getResponse().getContentAsString();
                System.out.println("🔍 第二次删除响应: " + secondDeleteResponse);
                
                // 两次删除都应该有合理的响应
                AjaxResult firstResult = objectMapper.readValue(firstDeleteResponse, AjaxResult.class);
                AjaxResult secondResult = objectMapper.readValue(secondDeleteResponse, AjaxResult.class);
                
                assertTrue(firstResult.isSuccess(), "第一次删除应该成功");
                // 第一次删除应该返回200
                assertEquals(200, (Integer) firstResult.get("code"));
                
                // 第二次删除可能成功(幂等)也可能失败(记录不存在)，但应该有合理的响应
                assertTrue(secondResult.isSuccess() || (Integer) secondResult.get("code") == 404, 
                    "第二次删除应该返回200(成功)或404(不存在)，实际: " + secondResult.get("code"));

                
                // 第二次删除可能成功(幂等)也可能失败(记录不存在)，但应该有合理的响应
                System.out.println("✅ 并发删除测试完成，系统处理了重复删除操作");
                
            } finally {
                // 确保清理
                cleanupTestLead(concurrentTestLeadId);
            }
        }
    }

    @Nested
    @DisplayName("并发测试")
    class ConcurrencyTests {
        
        private CrmLeads testLead;
        private Long testLeadId;
        
        @BeforeEach
        void setUpConcurrencyTests() {
            // 并发测试需要预置数据
            testLead = createTestLead("并发修改测试线索", "并发修改测试客户");
            testLeadId = testLead.getId();
        }
        
        @AfterEach
        void tearDownConcurrencyTests() {
            cleanupTestLead(testLeadId);
        }

        @Test
        @DisplayName("并发修改线索测试")
        void testConcurrentUpdate() throws Exception {
            // 模拟两个用户同时修改同一线索
            CrmLeads lead1 = new CrmLeads();
            lead1.setId(testLeadId);
            lead1.setLeadName("用户1修改");
            lead1.setRemark("用户1的修改");

            CrmLeads lead2 = new CrmLeads();
            lead2.setId(testLeadId);
            lead2.setLeadName("用户2修改");
            lead2.setRemark("用户2的修改");

            // 第一个修改
            mockMvc.perform(put("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(lead1)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            // 第二个修改
            mockMvc.perform(put("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(lead2)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));

            // 验证最终结果
            CrmLeads finalLead = crmLeadService.selectCrmLeadsById(testLeadId);
            assertNotNull(finalLead);
            // 最后一次修改应该生效
            assertEquals("用户2修改", finalLead.getLeadName());
        }
    }

    @Nested
    @DisplayName("使用TestAssertionHelper的改进测试示例")
    class ImprovedTestExamples {
        
        private CrmLeads testLead;
        private Long testLeadId;
        
        @BeforeEach
        void setUpImprovedTests() {
            // 改进测试示例需要预置数据
            testLead = createTestLead("改进测试线索", "改进测试客户");
            testLeadId = testLead.getId();
        }
        
        @AfterEach
        void tearDownImprovedTests() {
            cleanupTestLead(testLeadId);
        }

        @Test
        @DisplayName("线索转化 - 使用辅助类进行详细验证")
        void testConvertLeadWithHelper() throws Exception {
            // 使用辅助类打印调试信息
            printDebugInfo("线索转化测试开始",
                "测试线索ID", testLeadId,
                "测试描述", "验证线索转化功能的完整流程"
            );

            // 准备转化数据
            LeadConvertDTO convertDTO = new LeadConvertDTO();
            convertDTO.setLeadId(testLeadId);
            convertDTO.setConvertType("new");
            convertDTO.setCustomerName("辅助类测试客户");
            convertDTO.setIndustry("IT");

            LeadConvertDTO.ContactInfo contact = new LeadConvertDTO.ContactInfo();
            contact.setName("辅助类测试联系人");
            contact.setPosition("经理");
            contact.setPhone("13900139002");
            contact.setEmail("<EMAIL>");
            convertDTO.setContact(contact);

            // 获取转化前状态
            CrmLeads beforeConvert = crmLeadService.selectCrmLeadsById(testLeadId);
            printDebugInfo("转化前状态", "线索状态", beforeConvert.getStatus());

            // 执行转化
            MvcResult result = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(convertDTO)))
                    .andExpect(status().isOk())
                    .andReturn();

            // 使用辅助类验证响应
            AjaxResult response = assertAndParseResult(result, "线索转化");
            
            if (response.isSuccess()) {
                assertSuccessResponse(response, "线索转化");
                
                // 验证响应数据结构
                assertResponseDataStructure(
                    response.get("data"), 
                    "转化结果", 
                    "customerId", "leadId"  // 根据实际API调整
                );
                
                // 验证状态变化
                CrmLeads afterConvert = crmLeadService.selectCrmLeadsById(testLeadId);
                assertStateChange(
                    beforeConvert.getStatus(), 
                    afterConvert.getStatus(), 
                    "线索状态转化", 
                    true  // 应该发生变化
                );
                
            } else {
                assertFailureResponse(response, "线索转化", "");
                
                // 如果转化失败，分析可能的原因
                printDebugInfo("转化失败分析",
                    "错误码", response.get("code"),
                    "错误信息", response.get("msg"),
                    "请求数据", convertDTO,
                    "线索当前状态", beforeConvert.getStatus()
                );
                
                // 根据错误信息给出建议
                String errorMsg = (String) response.get("msg");
                if (errorMsg != null) {
                    if (errorMsg.contains("已转化")) {
                        System.out.println("💡 建议: 线索可能已经被转化过了，检查线索状态");
                    } else if (errorMsg.contains("参数")) {
                        System.out.println("💡 建议: 检查转化参数是否完整和正确");
                    } else if (errorMsg.contains("权限")) {
                        System.out.println("💡 建议: 检查当前用户是否有转化权限");
                    }
                }
            }
        }

        @Test
        @DisplayName("参数验证 - 使用辅助类详细分析")
        void testParameterValidationWithHelper() throws Exception {
            printDebugInfo("参数验证测试", "测试目标", "验证各种无效参数的处理");

            // 测试场景1: 空的convertDTO
            LeadConvertDTO emptyDTO = new LeadConvertDTO();
            
            MvcResult emptyResult = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(emptyDTO)))
                    .andExpect(status().isOk())
                    .andReturn();

            AjaxResult emptyResponse = assertAndParseResult(emptyResult, "空DTO测试");
            assertFailureResponse(emptyResponse, "空DTO测试", "");

            // 测试场景2: 只有leadId，缺少其他必要参数
            LeadConvertDTO partialDTO = new LeadConvertDTO();
            partialDTO.setLeadId(testLeadId);
            
            MvcResult partialResult = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(partialDTO)))
                    .andExpect(status().isOk())
                    .andReturn();

            AjaxResult partialResponse = assertAndParseResult(partialResult, "部分参数测试");
            
            // 根据业务逻辑，这可能成功也可能失败
            if (partialResponse.isSuccess()) {
                System.out.println("⚠️  警告: 部分参数就能转化成功，可能需要加强参数验证");
            } else {
                assertFailureResponse(partialResponse, "部分参数测试", "");
            }

            // 测试场景3: 无效的convertType
            LeadConvertDTO invalidTypeDTO = new LeadConvertDTO();
            invalidTypeDTO.setLeadId(testLeadId);
            invalidTypeDTO.setConvertType("invalid_type");
            invalidTypeDTO.setCustomerName("测试客户");
            
            MvcResult invalidTypeResult = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidTypeDTO)))
                    .andExpect(status().isOk())
                    .andReturn();

            AjaxResult invalidTypeResponse = assertAndParseResult(invalidTypeResult, "无效类型测试");
            
            // 无效的转化类型应该被拒绝
            if (invalidTypeResponse.isSuccess()) {
                System.out.println("⚠️  警告: 无效的转化类型被接受，可能需要加强类型验证");
            } else {
                assertFailureResponse(invalidTypeResponse, "无效类型测试", "类型");
            }
        }
    }
} 