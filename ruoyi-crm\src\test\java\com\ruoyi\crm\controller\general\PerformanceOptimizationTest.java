package com.ruoyi.crm.controller;


import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.service.ICrmContactFollowService;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.common.service.ICrmUserHierarchyService;
import com.ruoyi.crm.BaseTestCase;

/**
 * CRM系统性能优化测试
 * 
 * <AUTHOR>
 * @date 2024-12-28
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@DisplayName("CRM系统性能优化测试")
class PerformanceOptimizationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceOptimizationTest.class);

    @Autowired
    private ICrmContactsService crmContactsService;

    @Autowired
    private ICrmContactFollowService crmContactFollowService;

    @Autowired
    private ICrmUserHierarchyService crmUserHierarchyService;

    @Test
    @DisplayName("联系人列表查询性能测试")
    void testContactListQueryPerformance() {
        long startTime = System.currentTimeMillis();
        
        CrmContacts query = new CrmContacts();
        List<CrmContacts> contacts = crmContactsService.selectCrmContactsList(query);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        logger.info("联系人列表查询性能测试完成，查询 {} 条记录，耗时 {} ms", contacts.size(), duration);
        
        // 性能基准：查询应在1秒内完成
        assertTrue(duration < 1000, "联系人列表查询耗时应少于1秒，实际耗时: " + duration + "ms");
    }

    @Test
    @DisplayName("用户层级关系查询性能测试")
    void testUserHierarchyQueryPerformance() {
        long startTime = System.currentTimeMillis();
        
        Long testUserId = 1L;
        List<Long> subordinateIds = crmUserHierarchyService.getSubordinateIds(testUserId);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        logger.info("用户层级关系查询性能测试完成，查询到 {} 个下属，耗时 {} ms", subordinateIds.size(), duration);
        
        // 性能基准：层级查询应在500ms内完成
        assertTrue(duration < 500, "用户层级关系查询耗时应少于500ms，实际耗时: " + duration + "ms");
    }

    @Test
    @DisplayName("关注功能性能测试")
    void testFollowPerformance() {
        long startTime = System.currentTimeMillis();
        
        Long testUserId = 1L;
        int followedCount = crmContactFollowService.getFollowedContactCount(testUserId);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        logger.info("关注功能性能测试完成，用户关注了 {} 个联系人，查询耗时 {} ms", followedCount, duration);
        
        // 性能基准：关注查询应在300ms内完成
        assertTrue(duration < 300, "关注功能查询耗时应少于300ms，实际耗时: " + duration + "ms");
    }

    @Test
    @DisplayName("并发访问性能测试")
    void testConcurrentAccessPerformance() throws InterruptedException {
        int threadCount = 10;
        int operationsPerThread = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 模拟并发查询联系人列表
                        CrmContacts query = new CrmContacts();
                        crmContactsService.selectCrmContactsList(query);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        executor.shutdown();
        
        assertTrue(completed, "并发测试应在30秒内完成");
        
        int totalOperations = threadCount * operationsPerThread;
        double averageTime = (double) duration / totalOperations;
        
        logger.info("并发访问性能测试完成，{} 个线程执行 {} 次操作，总耗时 {} ms，平均每次操作 {:.2f} ms", 
                   threadCount, totalOperations, duration, averageTime);
        
        // 性能基准：平均每次操作应在200ms内完成
        assertTrue(averageTime < 200, "平均每次操作耗时应少于200ms，实际平均耗时: " + averageTime + "ms");
    }
}