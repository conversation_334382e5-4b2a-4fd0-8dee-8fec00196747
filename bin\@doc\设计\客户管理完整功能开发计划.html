<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户管理完整功能开发计划</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin: -20px -20px 40px -20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #555;
            margin: 20px 0 15px 0;
            font-size: 1.4em;
        }
        
        .section h4 {
            color: #666;
            margin: 15px 0 10px 0;
            font-size: 1.2em;
        }
        
        .overview-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .phase-box {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .phase-box h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #667eea;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            left: -37px;
            top: 25px;
            width: 12px;
            height: 12px;
            background: #667eea;
            border-radius: 50%;
            border: 3px solid white;
        }
        
        .timeline-item h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .priority-high {
            color: #dc3545;
            font-weight: bold;
        }
        
        .priority-medium {
            color: #ffc107;
            font-weight: bold;
        }
        
        .priority-low {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-completed {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }
        
        .status-not-started {
            color: #dc3545;
            font-weight: bold;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .tech-item h5 {
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .highlight {
            background: linear-gradient(135deg, #667eea20, #764ba220);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #667eea;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }
        
        .step-number {
            background: #17a2b8;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 客户管理完整功能开发计划</h1>
            <p>基于联系人管理和付款管理模块的架构设计</p>
        </div>

        <!-- 项目概述 -->
        <div class="section">
            <h2>📋 项目概述</h2>
            <p>当前客户管理模块仅具备基础的客户列表展示功能，缺少完整的抽屉详情功能和子模块支持。本计划将参考联系人管理和付款管理模块的成功实现，为客户管理开发完整的功能体系。</p>
            
            <div class="highlight">
                <h3>核心目标</h3>
                <ul class="feature-list">
                    <li>完善客户详情抽屉功能，实现多Tab页面展示</li>
                    <li>开发客户相关的子模块功能</li>
                    <li>建立完整的客户数据管理体系</li>
                    <li>提升用户体验和操作效率</li>
                </ul>
            </div>
        </div>

        <!-- 现状分析 -->
        <div class="section">
            <h2>🔍 现状分析</h2>
            
            <div class="overview-cards">
                <div class="card">
                    <h4>✅ 已完成功能</h4>
                    <ul class="feature-list">
                        <li>客户列表展示</li>
                        <li>基础的筛选功能</li>
                        <li>客户新增/编辑对话框</li>
                        <li>批量操作（关注、分配）</li>
                        <li>简单的抽屉框架</li>
                    </ul>
                </div>
                
                <div class="card">
                    <h4>❌ 缺失功能</h4>
                    <ul class="feature-list">
                        <li>完整的客户详情Tab页面</li>
                        <li>客户跟进记录管理</li>
                        <li>客户附件管理</li>
                        <li>客户操作日志详情</li>
                        <li>客户团队成员管理</li>
                        <li>客户关联业务数据</li>
                    </ul>
                </div>
                
                <div class="card">
                    <h4>🎯 参考模块</h4>
                    <ul class="feature-list">
                        <li>联系人管理 - 抽屉Tab结构</li>
                        <li>付款管理 - 子模块架构</li>
                        <li>现有客户后端API</li>
                        <li>通用组件库</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 技术架构设计 -->
        <div class="section">
            <h2>🏗️ 技术架构设计</h2>
            
            <h3>前端组件结构</h3>
            <div class="code-block">
CustomerManagement/
├── index.vue                    # 主页面
├── config/
│   ├── index.ts                # 表格和操作配置
│   └── filterConfig.ts         # 筛选配置
├── types/
│   └── index.ts                # 类型定义
├── api/
│   ├── index.ts                # API接口
│   └── customer.ts             # 客户相关API
└── tabs/                       # 抽屉Tab组件
    ├── CustomerHeaderTab.vue    # 客户头部信息
    ├── CustomerDetailsTab.vue   # 客户详细资料
    ├── CustomerActivityTab.vue  # 客户活动记录
    ├── CustomerAttachmentTab.vue # 客户附件管理
    ├── CustomerTeamTab.vue      # 客户团队成员
    ├── CustomerLogTab.vue       # 客户操作日志
    ├── CustomerContactsTab.vue  # 关联联系人
    ├── CustomerOpportunityTab.vue # 关联商机
    └── CustomerContractsTab.vue # 关联合同
            </div>
            
            <h3>后端API设计</h3>
            <div class="code-block">
# 基础API（已存在）
GET  /front/crm/customer/list           # 客户列表
GET  /front/crm/customer/{id}           # 客户详情
POST /front/crm/customer                # 新增客户
PUT  /front/crm/customer                # 更新客户

# 需要新增的API
GET  /front/crm/customer/{id}/activities    # 客户活动记录
GET  /front/crm/customer/{id}/attachments   # 客户附件
GET  /front/crm/customer/{id}/team          # 客户团队成员
GET  /front/crm/customer/{id}/contacts      # 关联联系人
GET  /front/crm/customer/{id}/opportunities # 关联商机
GET  /front/crm/customer/{id}/contracts     # 关联合同
            </div>
        </div>

        <!-- 开发阶段规划 -->
        <div class="section">
            <h2>📅 开发阶段规划</h2>
            
            <div class="timeline">
                <div class="timeline-item">
                    <h4>第一阶段：抽屉Tab组件开发 <span class="priority-high">(优先级：高)</span></h4>
                    <p><strong>预计时间：</strong> 3-4天</p>
                    <p><strong>主要任务：</strong></p>
                    <ul class="feature-list">
                        <li>创建tabs目录和基础组件文件</li>
                        <li>开发CustomerHeaderTab.vue - 客户头部信息展示</li>
                        <li>开发CustomerDetailsTab.vue - 客户详细资料</li>
                        <li>开发CustomerActivityTab.vue - 客户活动记录</li>
                        <li>开发CustomerAttachmentTab.vue - 客户附件管理</li>
                        <li>配置抽屉Tab导航</li>
                    </ul>
                    <p><strong>参考实现：</strong> ContactManagement/tabs/ 目录下的组件结构</p>
                </div>
                
                <div class="timeline-item">
                    <h4>第二阶段：客户关联数据Tab开发 <span class="priority-medium">(优先级：中)</span></h4>
                    <p><strong>预计时间：</strong> 2-3天</p>
                    <p><strong>主要任务：</strong></p>
                    <ul class="feature-list">
                        <li>开发CustomerTeamTab.vue - 客户团队成员管理</li>
                        <li>开发CustomerContactsTab.vue - 关联联系人展示</li>
                        <li>开发CustomerOpportunityTab.vue - 关联商机展示</li>
                        <li>开发CustomerContractsTab.vue - 关联合同展示</li>
                        <li>实现数据联动和跳转功能</li>
                    </ul>
                    <p><strong>参考实现：</strong> PaymentManagement/tabs/ 目录下的关联数据展示</p>
                </div>
                
                <div class="timeline-item">
                    <h4>第三阶段：后端API接口开发 <span class="priority-high">(优先级：高)</span></h4>
                    <p><strong>预计时间：</strong> 2-3天</p>
                    <p><strong>主要任务：</strong></p>
                    <ul class="feature-list">
                        <li>开发客户活动记录相关API</li>
                        <li>开发客户附件管理API</li>
                        <li>开发客户团队成员API</li>
                        <li>开发客户关联数据查询API</li>
                        <li>完善客户操作日志API</li>
                    </ul>
                    <p><strong>技术要点：</strong> 复用现有的通用服务和Mapper</p>
                </div>
                
                <div class="timeline-item">
                    <h4>第四阶段：前后端联调和优化 <span class="priority-medium">(优先级：中)</span></h4>
                    <p><strong>预计时间：</strong> 2天</p>
                    <p><strong>主要任务：</strong></p>
                    <ul class="feature-list">
                        <li>前后端API接口联调</li>
                        <li>数据加载性能优化</li>
                        <li>用户体验优化</li>
                        <li>错误处理和边界情况处理</li>
                        <li>响应式布局适配</li>
                    </ul>
                </div>
                
                <div class="timeline-item">
                    <h4>第五阶段：测试和文档完善 <span class="priority-low">(优先级：低)</span></h4>
                    <p><strong>预计时间：</strong> 1-2天</p>
                    <p><strong>主要任务：</strong></p>
                    <ul class="feature-list">
                        <li>功能测试和bug修复</li>
                        <li>性能测试和优化</li>
                        <li>用户操作流程测试</li>
                        <li>代码注释和文档更新</li>
                        <li>部署和上线准备</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 详细功能设计 -->
        <div class="section">
            <h2>🎨 详细功能设计</h2>
            
            <div class="phase-box">
                <h3>CustomerHeaderTab.vue - 客户头部信息</h3>
                <h4>功能需求：</h4>
                <ul class="feature-list">
                    <li>显示客户基本信息（名称、行业、级别、状态）</li>
                    <li>显示联系方式（电话、手机、邮箱）</li>
                    <li>支持快速编辑功能</li>
                    <li>显示负责人信息</li>
                    <li>操作按钮区域（编辑、转移、删除等）</li>
                </ul>
                <h4>技术实现：</h4>
                <ul class="feature-list">
                    <li>参考ContactHeaderTab.vue的布局和交互逻辑</li>
                    <li>适配客户特有的字段结构</li>
                    <li>实现内联编辑功能</li>
                    <li>集成客户关注/取消关注功能</li>
                </ul>
            </div>
            
            <div class="phase-box">
                <h3>CustomerDetailsTab.vue - 客户详细资料</h3>
                <h4>功能需求：</h4>
                <ul class="feature-list">
                    <li>完整的客户信息展示</li>
                    <li>客户地址和网站信息</li>
                    <li>客户来源和级别详情</li>
                    <li>成交状态和下次联系时间</li>
                    <li>支持字段编辑和保存</li>
                </ul>
                <h4>技术实现：</h4>
                <ul class="feature-list">
                    <li>使用el-descriptions组件展示信息</li>
                    <li>实现编辑模式切换</li>
                    <li>表单验证和数据保存</li>
                </ul>
            </div>
            
            <div class="phase-box">
                <h3>CustomerActivityTab.vue - 客户活动记录</h3>
                <h4>功能需求：</h4>
                <ul class="feature-list">
                    <li>显示客户跟进记录时间线</li>
                    <li>支持添加新的跟进记录</li>
                    <li>活动类型分类（电话、邮件、会议、拜访等）</li>
                    <li>跟进结果和下次跟进时间</li>
                    <li>支持记录编辑和删除</li>
                </ul>
                <h4>技术实现：</h4>
                <ul class="feature-list">
                    <li>参考ContactActivityTab.vue的时间线组件</li>
                    <li>适配客户活动数据结构</li>
                    <li>实现活动记录的增删改查</li>
                    <li>集成富文本编辑器</li>
                </ul>
            </div>
            
            <div class="phase-box">
                <h3>CustomerAttachmentTab.vue - 客户附件管理</h3>
                <h4>功能需求：</h4>
                <ul class="feature-list">
                    <li>客户相关文件上传和管理</li>
                    <li>文件预览和下载功能</li>
                    <li>文件分类和标签管理</li>
                    <li>文件权限控制</li>
                </ul>
                <h4>技术实现：</h4>
                <ul class="feature-list">
                    <li>复用通用附件组件AttachmentTab</li>
                    <li>配置客户实体类型</li>
                    <li>实现文件上传和管理功能</li>
                </ul>
            </div>
        </div>

        <!-- API接口设计 -->
        <div class="section">
            <h2>🔌 API接口设计</h2>
            
            <table>
                <thead>
                    <tr>
                        <th>接口名称</th>
                        <th>请求方法</th>
                        <th>接口路径</th>
                        <th>功能描述</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>获取客户详情</td>
                        <td>GET</td>
                        <td>/front/crm/customer/{id}</td>
                        <td>获取客户基本信息</td>
                        <td><span class="status-completed">已完成</span></td>
                    </tr>
                    <tr>
                        <td>获取客户活动记录</td>
                        <td>GET</td>
                        <td>/front/crm/customer/{id}/activities</td>
                        <td>获取客户跟进记录列表</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>新增客户活动记录</td>
                        <td>POST</td>
                        <td>/front/crm/customer/{id}/activities</td>
                        <td>添加客户跟进记录</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取客户附件</td>
                        <td>GET</td>
                        <td>/front/crm/customer/{id}/attachments</td>
                        <td>获取客户相关附件列表</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取客户团队成员</td>
                        <td>GET</td>
                        <td>/front/crm/customer/{id}/team</td>
                        <td>获取客户团队成员列表</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取关联联系人</td>
                        <td>GET</td>
                        <td>/front/crm/customer/{id}/contacts</td>
                        <td>获取客户关联的联系人</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取关联商机</td>
                        <td>GET</td>
                        <td>/front/crm/customer/{id}/opportunities</td>
                        <td>获取客户关联的商机</td>
                        <td><span class="status-pending">待开发</span></td>
                    </tr>
                    <tr>
                        <td>获取客户操作日志</td>
                        <td>GET</td>
                        <td>/front/crm/customer/{id}/logs</td>
                        <td>获取客户操作历史记录</td>
                        <td><span class="status-completed">已完成</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 技术要点 -->
        <div class="section">
            <h2>⚙️ 技术要点</h2>
            
            <div class="overview-cards">
                <div class="card">
                    <h4>前端技术要点</h4>
                    <ul class="feature-list">
                        <li>复用现有的通用组件库</li>
                        <li>参考联系人管理的Tab结构</li>
                        <li>使用TypeScript确保类型安全</li>
                        <li>实现响应式布局设计</li>
                        <li>优化数据加载性能</li>
                    </ul>
                </div>
                
                <div class="card">
                    <h4>后端技术要点</h4>
                    <ul class="feature-list">
                        <li>复用现有的Service和Mapper</li>
                        <li>遵循现有的API设计规范</li>
                        <li>实现统一的异常处理</li>
                        <li>添加操作日志记录</li>
                        <li>优化数据库查询性能</li>
                    </ul>
                </div>
                
                <div class="card">
                    <h4>数据库设计要点</h4>
                    <ul class="feature-list">
                        <li>复用现有的客户相关表结构</li>
                        <li>扩展客户活动记录表</li>
                        <li>完善客户团队关系表</li>
                        <li>优化索引设计</li>
                        <li>确保数据一致性</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 风险评估 -->
        <div class="section">
            <h2>⚠️ 风险评估与应对</h2>
            
            <div class="phase-box">
                <h3>技术风险</h3>
                <ul class="feature-list">
                    <li><strong>组件复用兼容性：</strong> 确保通用组件能够适配客户管理的特殊需求</li>
                    <li><strong>数据加载性能：</strong> 多Tab页面同时加载可能影响性能，需要实现懒加载</li>
                    <li><strong>API接口设计：</strong> 新增API需要与现有接口保持一致性</li>
                </ul>
                
                <h3>业务风险</h3>
                <ul class="feature-list">
                    <li><strong>用户体验一致性：</strong> 确保与其他模块的操作体验保持一致</li>
                    <li><strong>数据权限控制：</strong> 客户数据的访问权限需要严格控制</li>
                    <li><strong>历史数据兼容：</strong> 新功能需要兼容现有的客户数据</li>
                </ul>
                
                <h3>应对策略</h3>
                <ul class="feature-list">
                    <li>分阶段开发，逐步验证功能可行性</li>
                    <li>充分测试组件复用的兼容性</li>
                    <li>实现数据懒加载和缓存机制</li>
                    <li>建立完善的错误处理机制</li>
                </ul>
            </div>
        </div>

        <!-- 预期效果 -->
        <div class="section">
            <h2>🎯 预期效果</h2>
            
            <div class="highlight">
                <h3>功能效果</h3>
                <ul class="feature-list">
                    <li>✅ 完整的客户详情展示和管理功能</li>
                    <li>✅ 高效的客户跟进记录管理</li>
                    <li>✅ 便捷的客户附件和文档管理</li>
                    <li>✅ 清晰的客户操作历史追踪</li>
                    <li>✅ 完善的客户关联数据展示</li>
                </ul>
                
                <h3>用户体验</h3>
                <ul class="feature-list">
                    <li>✅ 统一的操作界面和交互体验</li>
                    <li>✅ 快速的数据加载和响应</li>
                    <li>✅ 直观的信息展示和编辑</li>
                    <li>✅ 便捷的数据查找和筛选</li>
                </ul>
                
                <h3>技术效果</h3>
                <ul class="feature-list">
                    <li>✅ 高度复用的组件架构</li>
                    <li>✅ 可维护的代码结构</li>
                    <li>✅ 良好的扩展性设计</li>
                    <li>✅ 稳定的系统性能</li>
                </ul>
            </div>
        </div>

        <!-- 总结 -->
        <div class="section">
            <h2>📝 总结</h2>
            <p>本开发计划基于现有的联系人管理和付款管理模块的成功经验，为客户管理模块设计了完整的功能架构。通过分阶段的开发方式，确保每个功能模块都能稳定可靠地实现。</p>
            
            <div class="overview-cards">
                <div class="card">
                    <h4>开发周期</h4>
                    <p><strong>总计：10-14天</strong></p>
                    <ul class="feature-list">
                        <li>前端开发：6-8天</li>
                        <li>后端开发：2-3天</li>
                        <li>联调测试：2-3天</li>
                    </ul>
                </div>
                
                <div class="card">
                    <h4>技术栈</h4>
                    <ul class="feature-list">
                        <li>Vue 3 + TypeScript</li>
                        <li>Element Plus UI</li>
                        <li>Spring Boot + MyBatis</li>
                        <li>MySQL数据库</li>
                    </ul>
                </div>
                
                <div class="card">
                    <h4>交付成果</h4>
                    <ul class="feature-list">
                        <li>完整的客户管理功能</li>
                        <li>9个Tab页面组件</li>
                        <li>8个新增API接口</li>
                        <li>完善的技术文档</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>