-- ==============================================
-- 深化团队管理实施方案-V2 数据库脚本
-- 创建通用团队业务关联表
-- 作者: AI Assistant
-- 创建时间: 2025-07-10
-- ==============================================

-- 检查并创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS crm41 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE crm41;

-- ==============================================
-- 1. 创建通用团队业务关联表 (crm_team_relations)
-- ==============================================

-- 删除表（如果存在）
DROP TABLE IF EXISTS `crm_team_relations`;

-- 创建通用团队业务关联表
CREATE TABLE `crm_team_relations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID (关联crm_teams.id)',
  `biz_id` bigint(20) NOT NULL COMMENT '业务主键ID (例如: crm_contacts.id)',
  `biz_type` varchar(50) NOT NULL COMMENT '业务类型 (例如: CONTACT, LEAD, CUSTOMER, OPPORTUNITY)',
  `relation_status` char(1) DEFAULT '0' COMMENT '关联状态：0-正常，1-停用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_biz` (`team_id`, `biz_id`, `biz_type`) COMMENT '团队-业务唯一约束',
  KEY `idx_biz` (`biz_id`, `biz_type`) COMMENT '业务查询索引',
  KEY `idx_team_id` (`team_id`) COMMENT '团队查询索引',
  KEY `idx_biz_type` (`biz_type`) COMMENT '业务类型索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用团队业务关联表';

-- ==============================================
-- 2. 升级现有团队表 (crm_teams)
-- ==============================================

-- 检查并添加缺失的字段到现有的 crm_teams 表
-- 注意：这些 ALTER 语句会检查字段是否存在，如果存在则跳过

-- 添加团队编码字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_teams'
     AND table_schema = DATABASE()
     AND column_name = 'team_code') > 0,
    'SELECT "team_code column already exists"',
    'ALTER TABLE crm_teams ADD COLUMN team_code varchar(50) DEFAULT NULL COMMENT "团队编码" AFTER team_name'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加团队负责人姓名字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_teams'
     AND table_schema = DATABASE()
     AND column_name = 'leader_name') > 0,
    'SELECT "leader_name column already exists"',
    'ALTER TABLE crm_teams ADD COLUMN leader_name varchar(64) DEFAULT NULL COMMENT "团队负责人姓名" AFTER leader_id'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加团队类型字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_teams'
     AND table_schema = DATABASE()
     AND column_name = 'team_type') > 0,
    'SELECT "team_type column already exists"',
    'ALTER TABLE crm_teams ADD COLUMN team_type varchar(20) DEFAULT "BUSINESS" COMMENT "团队类型：BUSINESS-业务团队，TECHNICAL-技术团队，SUPPORT-支持团队" AFTER description'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加状态字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_teams'
     AND table_schema = DATABASE()
     AND column_name = 'status') > 0,
    'SELECT "status column already exists"',
    'ALTER TABLE crm_teams ADD COLUMN status char(1) DEFAULT "0" COMMENT "状态：0-正常，1-停用" AFTER team_type'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加备注字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_teams'
     AND table_schema = DATABASE()
     AND column_name = 'remark') > 0,
    'SELECT "remark column already exists"',
    'ALTER TABLE crm_teams ADD COLUMN remark varchar(500) DEFAULT NULL COMMENT "备注" AFTER del_flag'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'crm_teams'
     AND table_schema = DATABASE()
     AND index_name = 'uk_team_code') > 0,
    'SELECT "uk_team_code index already exists"',
    'ALTER TABLE crm_teams ADD UNIQUE KEY uk_team_code (team_code)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==============================================
-- 3. 升级现有团队成员表 (crm_team_members)
-- ==============================================

-- 检查并添加缺失的字段到现有的 crm_team_members 表

-- 添加用户名字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'user_name') > 0,
    'SELECT "user_name column already exists"',
    'ALTER TABLE crm_team_members ADD COLUMN user_name varchar(64) DEFAULT NULL COMMENT "用户名" AFTER user_id'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加用户昵称字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'nick_name') > 0,
    'SELECT "nick_name column already exists"',
    'ALTER TABLE crm_team_members ADD COLUMN nick_name varchar(64) DEFAULT NULL COMMENT "用户昵称" AFTER user_name'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修改角色字段名（从 role_in_team 改为 role_type）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'role_type') > 0,
    'SELECT "role_type column already exists"',
    IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
        WHERE table_name = 'crm_team_members'
        AND table_schema = DATABASE()
        AND column_name = 'role_in_team') > 0,
       'ALTER TABLE crm_team_members CHANGE COLUMN role_in_team role_type varchar(20) NOT NULL DEFAULT "member" COMMENT "角色类型：owner-负责人，admin-管理员，member-成员"',
       'ALTER TABLE crm_team_members ADD COLUMN role_type varchar(20) NOT NULL DEFAULT "member" COMMENT "角色类型：owner-负责人，admin-管理员，member-成员" AFTER nick_name')
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加状态字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'status') > 0,
    'SELECT "status column already exists"',
    'ALTER TABLE crm_team_members ADD COLUMN status char(1) DEFAULT "0" COMMENT "状态：0-正常，1-停用" AFTER join_time'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加创建者字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'create_by') > 0,
    'SELECT "create_by column already exists"',
    'ALTER TABLE crm_team_members ADD COLUMN create_by varchar(64) DEFAULT "" COMMENT "创建者" AFTER status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加创建时间字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'create_time') > 0,
    'SELECT "create_time column already exists"',
    'ALTER TABLE crm_team_members ADD COLUMN create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间" AFTER create_by'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加更新者字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'update_by') > 0,
    'SELECT "update_by column already exists"',
    'ALTER TABLE crm_team_members ADD COLUMN update_by varchar(64) DEFAULT "" COMMENT "更新者" AFTER create_time'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加更新时间字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'update_time') > 0,
    'SELECT "update_time column already exists"',
    'ALTER TABLE crm_team_members ADD COLUMN update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间" AFTER update_by'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加备注字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'crm_team_members'
     AND table_schema = DATABASE()
     AND column_name = 'remark') > 0,
    'SELECT "remark column already exists"',
    'ALTER TABLE crm_team_members ADD COLUMN remark varchar(500) DEFAULT NULL COMMENT "备注" AFTER update_time'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==============================================
-- 4. 插入初始数据
-- ==============================================

-- 插入默认团队（如果不存在）
INSERT IGNORE INTO `crm_teams` (`team_name`, `team_code`, `description`, `team_type`, `create_by`) 
VALUES 
('默认业务团队', 'DEFAULT_BUSINESS', '系统默认业务团队，用于未分配团队的业务对象', 'BUSINESS', 'system'),
('销售团队', 'SALES_TEAM', '负责销售相关业务', 'BUSINESS', 'system'),
('客服团队', 'SERVICE_TEAM', '负责客户服务相关业务', 'SUPPORT', 'system');

-- ==============================================
-- 5. 业务类型枚举说明
-- ==============================================

/*
支持的业务类型 (biz_type):
- CONTACT: 联系人 (crm_business_contacts)
- LEAD: 线索 (crm_leads) 
- CUSTOMER: 客户 (crm_business_customers)
- OPPORTUNITY: 商机 (crm_business_opportunities)
- CONTRACT: 合同 (crm_contracts)
- VISIT_PLAN: 拜访计划 (crm_visit_plans)

扩展说明：
1. 通过 biz_type 字段区分不同的业务模块
2. biz_id 存储对应业务表的主键ID
3. 一个业务对象只能关联一个团队（通过唯一约束保证）
4. 支持未来扩展新的业务类型，无需修改表结构
*/

-- ==============================================
-- 6. 验证脚本
-- ==============================================

-- 验证表是否创建成功
SELECT 
    table_name AS '表名',
    table_comment AS '表注释',
    table_rows AS '记录数',
    create_time AS '创建时间'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
  AND table_name IN ('crm_team_relations', 'crm_teams', 'crm_team_members')
ORDER BY table_name;

-- 验证索引是否创建成功
SELECT 
    table_name AS '表名',
    index_name AS '索引名',
    column_name AS '字段名',
    index_type AS '索引类型'
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name IN ('crm_team_relations', 'crm_teams', 'crm_team_members')
  AND index_name != 'PRIMARY'
ORDER BY table_name, index_name, seq_in_index;

-- 验证初始数据
SELECT 
    id,
    team_name,
    team_code,
    team_type,
    status,
    create_time
FROM crm_teams 
ORDER BY id;

-- ==============================================
-- 脚本执行完成
-- ==============================================
