<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmClaimLimitsMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmClaimLimits" id="CrmClaimLimitsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="roleId"    column="role_id"    />
        <result property="maxClaimDaily"    column="max_claim_daily"    />
        <result property="maxClaimTotal"    column="max_claim_total"    />
        <result property="enabled"    column="enabled"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <select id="selectUserClaimLimits" resultMap="CrmClaimLimitsResult">
        select * from crm_claim_limits
        where enabled = 1
          and (
            user_id = #{userId}
            <if test="roleIds != null and roleIds.size() > 0">
                or role_id in
                <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
            or (user_id is null and role_id is null)
          )
        order by 
            case 
                when user_id is not null then 1
                when role_id is not null then 2
                else 3
            end
        limit 1
    </select>

    <insert id="insertCrmClaimLimits" parameterType="com.ruoyi.common.domain.entity.CrmClaimLimits" useGeneratedKeys="true" keyProperty="id">
        insert into crm_claim_limits
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="roleId != null">role_id,</if>
            <if test="maxClaimDaily != null">max_claim_daily,</if>
            <if test="maxClaimTotal != null">max_claim_total,</if>
            <if test="enabled != null">enabled,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="maxClaimDaily != null">#{maxClaimDaily},</if>
            <if test="maxClaimTotal != null">#{maxClaimTotal},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

</mapper>