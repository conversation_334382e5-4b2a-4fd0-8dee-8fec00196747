package com.ruoyi.crm.controller;

import static com.ruoyi.crm.controller.TestAssertionHelper.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.BusinessLogContext;
import com.ruoyi.common.domain.BusinessOperationLog;
import com.ruoyi.common.domain.dto.LeadConvertDTO;
import com.ruoyi.common.domain.entity.CrmLeads;
import com.ruoyi.common.enums.BusinessClass;
import com.ruoyi.common.enums.OperationType;
import com.ruoyi.common.service.BusinessLogService;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.controller.CrmLeadController.AssignForm;
import com.ruoyi.crm.service.ICrmLeadService;

/**
 * 业务操作日志集成测试类
 * 测试新的@BusinessLog注解系统的完整功能
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */

@AutoConfigureWebMvc
@DisplayName("业务操作日志集成测试")
class BusinessLogIntegrationTest extends BaseTestCase {

    private static final Logger logger = LoggerFactory.getLogger(BusinessLogIntegrationTest.class);

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private BusinessLogService businessLogService;

    @Autowired
    private ICrmLeadService crmLeadService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    private CrmLeads testLead;
    private Long testLeadId;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .alwaysDo(print())
                .build();

        logger.info("业务操作日志集成测试开始");
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        if (testLeadId != null) {
            try {
                crmLeadService.deleteCrmLeadsByIds(new Long[]{testLeadId});
                logger.info("清理测试线索数据: {}", testLeadId);
            } catch (Exception e) {
                logger.warn("清理测试数据失败", e);
            }
        }
        logger.info("业务操作日志集成测试结束");
    }

    @Nested
    @DisplayName("线索CRUD操作日志测试")
    class LeadCrudLogTests {

        @Test
        @DisplayName("创建线索应该记录操作日志")
        void testCreateLeadLog() throws Exception {
            // 准备测试数据
            CrmLeads newLead = createTestLead("日志测试线索", "日志测试公司");

            // 执行创建操作
            MvcResult result = mockMvc.perform(post("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(newLead)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            // 解析响应获取创建的线索ID
            JsonNode rootNode = objectMapper.readTree(result.getResponse().getContentAsString());
            testLeadId = rootNode.path("data").path("id").asLong();

            // 验证操作日志是否正确记录
            List<BusinessOperationLog> logs = businessLogService.queryLogs(
                    BusinessClass.LEAD.getCode(), testLeadId);

            assertFalse(logs.isEmpty(), "应该记录创建操作日志");
            BusinessOperationLog log = logs.get(0);
            assertEquals(BusinessClass.LEAD.getCode(), log.getBusinessType());
            assertEquals(testLeadId, log.getBusinessId());
            assertEquals(OperationType.CREATE.getCode(), log.getOperationType());
            assertEquals("创建新线索", log.getOperationDesc());
            assertEquals(1L, log.getOperatorId()); // 验证操作员ID
            assertNotNull(log.getOperatorName()); // 验证操作员名称不为空
            assertEquals(Boolean.TRUE, log.getSuccess());
            assertNotNull(log.getOperationTime());
            assertNotNull(log.getRequestParams());
            assertNotNull(log.getResponseResult());

            logger.info("创建线索操作日志验证通过: {}", log);
        }

        @Test
        @DisplayName("更新线索应该记录操作日志和字段变更")
        void testUpdateLeadLog() throws Exception {
            // 先创建一个线索
            testLead = createTestLead("原始线索名称", "原始公司名称");
            testLeadId = insertTestLead(testLead);

            // 修改线索信息
            testLead.setId(testLeadId);
            testLead.setLeadName("更新后的线索名称");
            testLead.setCustomerName("更新后的公司名称");
            testLead.setPhone("***********");

            // 执行更新操作
            MvcResult result = mockMvc.perform(put("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(testLead)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            // 验证更新操作日志
            List<BusinessOperationLog> logs = businessLogService.queryLogs(
                    BusinessClass.LEAD.getCode(), testLeadId);

            assertFalse(logs.isEmpty(), "应该记录更新操作日志");
            BusinessOperationLog log = logs.get(0);
            assertEquals(BusinessClass.LEAD.getCode(), log.getBusinessType());
            assertEquals(testLeadId, log.getBusinessId());
            assertEquals(OperationType.UPDATE.getCode(), log.getOperationType());
            assertEquals("更新线索信息", log.getOperationDesc());
            assertEquals(1L, log.getOperatorId());
            assertNotNull(log.getOperatorName());
            assertEquals(Boolean.TRUE, log.getSuccess());
            assertNotNull(log.getFieldChanges(), "应该记录字段变更信息");

            logger.info("更新线索操作日志验证通过: {}", log);
        }

        @Test
        @DisplayName("删除线索应该记录操作日志")
        void testDeleteLeadLog() throws Exception {
            // 先创建一个线索
            testLead = createTestLead("待删除线索", "待删除公司");
            testLeadId = insertTestLead(testLead);

            // 执行删除操作
            MvcResult result = mockMvc.perform(delete("/front/crm/leads/" + testLeadId))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            // 验证删除操作日志
            List<BusinessOperationLog> logs = businessLogService.queryLogs(
                    BusinessClass.LEAD.getCode(), testLeadId);

            assertFalse(logs.isEmpty(), "应该记录删除操作日志");
            BusinessOperationLog log = logs.get(0);
            assertEquals(BusinessClass.LEAD.getCode(), log.getBusinessType());
            assertEquals(testLeadId, log.getBusinessId());
            assertEquals(OperationType.DELETE.getCode(), log.getOperationType());
            assertEquals("删除线索", log.getOperationDesc());
            assertEquals(1L, log.getOperatorId());
            assertNotNull(log.getOperatorName());
            assertEquals(Boolean.TRUE, log.getSuccess());

            logger.info("删除线索操作日志验证通过: {}", log);
            
            // 清理标记，避免重复删除
            testLeadId = null;
        }
    }

    @Nested
    @DisplayName("线索特殊操作日志测试")
    class LeadSpecialOperationLogTests {

        @BeforeEach
        void setUpSpecialTests() {
            // 创建用于特殊操作测试的线索
            testLead = createTestLead("特殊操作测试线索", "特殊操作测试公司");
            testLeadId = insertTestLead(testLead);
        }

        @Test
        @DisplayName("线索转换应该记录操作日志")
        void testConvertLeadLog() throws Exception {
            // 准备转换数据
            LeadConvertDTO convertDTO = new LeadConvertDTO();
            convertDTO.setLeadId(testLeadId);
            convertDTO.setCustomerName("转换后的客户名称");
            convertDTO.setIndustry("测试转换操作");
            convertDTO.setConvertType("new"); // 新增：明确指定转换类型为新建

            // 新增：创建并设置有效的联系人信息
            LeadConvertDTO.ContactInfo contactInfo = new LeadConvertDTO.ContactInfo();
            contactInfo.setName("转换后的联系人");
            contactInfo.setPhone("13987654321");
            contactInfo.setPosition("测试职位");
            contactInfo.setEmail("<EMAIL>");
            convertDTO.setContact(contactInfo);


            // 执行转换操作
            MvcResult result = mockMvc.perform(post("/front/crm/leads/convert")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(convertDTO)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            // 验证转换操作日志
            List<BusinessOperationLog> logs = businessLogService.queryLogs(
                    BusinessClass.LEAD.getCode(), testLeadId);

            assertFalse(logs.isEmpty(), "应该记录转换操作日志");
            BusinessOperationLog log = logs.get(0);
            assertEquals(BusinessClass.LEAD.getCode(), log.getBusinessType());
            assertEquals(testLeadId, log.getBusinessId());
            assertEquals(OperationType.CONVERT.getCode(), log.getOperationType());
            assertEquals("将线索转换为客户", log.getOperationDesc());
            assertEquals(1L, log.getOperatorId());
            assertNotNull(log.getOperatorName());
            assertEquals(Boolean.TRUE, log.getSuccess());

            logger.info("线索转换操作日志验证通过: {}", log);
        }

        @Test
        @DisplayName("线索分配应该记录操作日志")
        void testAssignLeadLog() throws Exception {
            // 准备分配数据
            AssignForm assignForm = new AssignForm();
            assignForm.setLeadId(testLeadId);
            assignForm.setNewOwnerId(2L);
            // assignForm.setReason("测试分配操作");

            // 执行分配操作
            MvcResult result = mockMvc.perform(post("/front/crm/leads/assign")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(assignForm)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andReturn();

            // 验证分配操作日志
            List<BusinessOperationLog> logs = businessLogService.queryLogs(
                    BusinessClass.LEAD.getCode(), testLeadId);

            assertFalse(logs.isEmpty(), "应该记录分配操作日志");
            BusinessOperationLog log = logs.get(0);
            assertEquals(BusinessClass.LEAD.getCode(), log.getBusinessType());
            assertEquals(testLeadId, log.getBusinessId());
            assertEquals(OperationType.ASSIGN.getCode(), log.getOperationType());
            assertEquals("分配线索", log.getOperationDesc());
            assertEquals(1L, log.getOperatorId());
            assertNotNull(log.getOperatorName());
            assertEquals(Boolean.TRUE, log.getSuccess());

            logger.info("线索分配操作日志验证通过: {}", log);
        }
    }

    @Nested
    @DisplayName("异常情况日志测试")
    class ExceptionLogTests {

        @Test
        @DisplayName("创建无效线索应该记录错误日志")
        void testCreateInvalidLeadLog() throws Exception {
            // 准备无效的线索数据（缺少必要字段）
            CrmLeads invalidLead = new CrmLeads();
            // 故意不设置必要字段，触发验证错误

            // 执行创建操作（期望失败）
            MvcResult result = mockMvc.perform(post("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidLead)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andReturn();

            // 验证错误日志（如果有记录的话）
            // 注意：根据具体实现，失败的操作可能不会记录日志
            logger.info("无效线索创建测试完成");
        }

        @Test
        @DisplayName("更新不存在的线索应该记录错误日志")
        void testUpdateNonExistentLeadLog() throws Exception {
            // 准备更新不存在的线索数据
            CrmLeads nonExistentLead = createTestLead("不存在的线索", "不存在的公司");
            nonExistentLead.setId(99999L); // 使用不存在的ID

            // 执行更新操作（期望失败）
            MvcResult result = mockMvc.perform(put("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(nonExistentLead)))
                    .andExpect(status().isOk())
                    .andReturn();

            // 验证错误日志（如果有记录的话）
            logger.info("不存在线索更新测试完成");
        }
    }

    @Nested
    @DisplayName("日志查询功能测试")
    class LogQueryTests {

        @BeforeEach
        void setUpQueryTests() {
            // 创建测试线索并执行多个操作
            testLead = createTestLead("查询测试线索", "查询测试公司");
            testLeadId = insertTestLead(testLead);
        }

        @Test
        @DisplayName("执行多个操作后应该能查询到所有日志")
        void testQueryMultipleOperationLogs() throws Exception {
            // 执行多个操作
            // 1. 更新操作
            testLead.setLeadName("更新后的查询测试线索");
            mockMvc.perform(put("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(testLead)))
                    .andExpect(status().isOk());

            // 2. 分配操作
            AssignForm assignForm = new AssignForm();
            assignForm.setLeadId(testLeadId);
            assignForm.setNewOwnerId(2L);
            mockMvc.perform(post("/front/crm/leads/assign")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(assignForm)))
                    .andExpect(status().isOk());

            // 查询所有操作日志
            List<BusinessOperationLog> logs = businessLogService.queryLogs(
                    BusinessClass.LEAD.getCode(), testLeadId);

            // 验证日志数量和内容
            assertEquals(2, logs.size(), "应该记录2条操作日志");
            
            // 验证日志按时间倒序排列
            BusinessOperationLog latestLog = logs.get(0);
            assertEquals(OperationType.ASSIGN.getCode(), latestLog.getOperationType());
            
            BusinessOperationLog earlierLog = logs.get(1);
            assertEquals(OperationType.UPDATE.getCode(), earlierLog.getOperationType());

            logger.info("查询多个操作日志验证通过，共{}条日志", logs.size());
        }

        @Test
        @DisplayName("分页查询日志应该正常工作")
        void testPaginationQueryLogs() throws Exception {
            // 执行多个操作创建足够的日志
            for (int i = 0; i < 5; i++) {
                testLead.setLeadName("分页测试线索" + i);
                mockMvc.perform(put("/front/crm/leads")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testLead)))
                        .andExpect(status().isOk());
            }

            // 分页查询
            List<BusinessOperationLog> page1 = businessLogService.queryLogsPage(
                    BusinessClass.LEAD.getCode(), testLeadId, 1, 3);
            List<BusinessOperationLog> page2 = businessLogService.queryLogsPage(
                    BusinessClass.LEAD.getCode(), testLeadId, 2, 3);

            // 验证分页结果
            assertEquals(3, page1.size(), "第一页应该有3条记录");
            assertEquals(2, page2.size(), "第二页应该有2条记录");

            logger.info("分页查询日志验证通过，第一页{}条，第二页{}条", page1.size(), page2.size());
        }

        @Test
        @DisplayName("根据操作人查询日志应该正常工作")
        void testQueryLogsByOperator() throws Exception {
            // 执行一些操作
            testLead.setLeadName("操作人查询测试线索");
            mockMvc.perform(put("/front/crm/leads")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(testLead)))
                    .andExpect(status().isOk());

            // 根据操作人查询日志（admin用户ID为1）
            List<BusinessOperationLog> operatorLogs = businessLogService.queryLogsByOperator(1L, 1, 10);

            // 验证查询结果
            assertFalse(operatorLogs.isEmpty(), "应该查询到操作人的日志");
            operatorLogs.forEach(log -> {
                assertEquals(1L, log.getOperatorId());
                assertNotNull(log.getOperatorName()); // 只验证名称不为空
            });

            logger.info("根据操作人查询日志验证通过，共{}条日志", operatorLogs.size());
        }

        @Test
        @DisplayName("并发写入时，日志查询结果顺序应该稳定")
        void testQueryLogs_WithConcurrentWrites() throws Exception {
            final int concurrentTasks = 20;
            ExecutorService executor = Executors.newFixedThreadPool(10);
            CountDownLatch latch = new CountDownLatch(concurrentTasks);

            // 直接调用 Service 层进行并发测试，绕过 MockMvc 在子线程中的事务问题
            for (int i = 0; i < concurrentTasks; i++) {
                final int taskIndex = i;
                executor.submit(() -> {
                    try {
                        BusinessLogContext context = new BusinessLogContext();
                        context.setBusinessClass(BusinessClass.LEAD);
                        context.setBusinessId(testLeadId);
                        context.setBusinessName(testLead.getLeadName());
                        context.setOperatorId(1L);
                        context.setOperatorName("testuser");
                        context.setOperationTime(new java.util.Date());
                        context.setSuccess(true);

                        if (taskIndex % 2 == 0) {
                            context.setOperationType(OperationType.UPDATE);
                            context.setDescription("并发更新 " + taskIndex);
                        } else {
                            context.setOperationType(OperationType.ASSIGN);
                            context.setDescription("并发分配 " + taskIndex);
                        }
                        businessLogService.recordLog(context);
                    } catch (Exception e) {
                        logger.error("并发任务执行失败", e);
                    } finally {
                        latch.countDown();
                    }
                });
            }

            // 等待所有任务完成
            assertTrue(latch.await(30, TimeUnit.SECONDS), "并发任务执行超时");
            executor.shutdown();

            // 查询所有操作日志
            List<BusinessOperationLog> logs = businessLogService.queryLogs(
                    BusinessClass.LEAD.getCode(), testLeadId);

            // 验证日志数量
            assertEquals(concurrentTasks, logs.size(), "应该记录所有并发操作的日志");

            // 验证日志严格按ID倒序排列
            Long previousLogId = Long.MAX_VALUE;
            for (BusinessOperationLog currentLog : logs) {
                assertTrue(currentLog.getId() < previousLogId, "日志ID应该严格递减，确保排序稳定性");
                previousLogId = currentLog.getId();
            }

            logger.info("并发写入日志测试验证通过，共{}条日志，排序稳定", logs.size());
        }
    }

    @Nested
    @DisplayName("日志禁用功能测试")
    class LogDisableTests {

        @Test
        @DisplayName("禁用的操作不应该记录日志")
        void testDisabledOperationLog() throws Exception {

                // 1. 准备测试数据
            CrmLeads exportLeadnewCrmLeads = createTestLead("导出测试线索", "导出测试公司");
            
            // 2. 【新增】将测试数据插入数据库，确保查询时能找到它
            insertTestLead(exportLeadnewCrmLeads);
            // 准备测试数据
            CrmLeads exportLead = createTestLead("导出测试线索", "导出测试公司");

            // 执行导出操作（该操作禁用了日志记录）
            MvcResult result = mockMvc.perform(post("/front/crm/leads/export")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(exportLead)))
                    .andExpect(status().isOk())
                    .andReturn();

            // 验证没有记录日志
            // 注意：由于导出操作不涉及特定的业务ID，这里主要验证系统不会因为禁用的注解而报错
            logger.info("禁用操作日志测试完成");
        }
    }

    /**
     * 创建测试线索
     */
    private CrmLeads createTestLead(String leadName, String companyName) {
        CrmLeads lead = new CrmLeads();
        lead.setLeadName(leadName);
        lead.setCustomerName(companyName);
        lead.setPhone("13812345678");
        lead.setEmail("<EMAIL>");
        lead.setCustomerIndustry("测试行业");
        lead.setLeadSource("测试来源");
        lead.setStatus("1");
        lead.setResponsiblePersonId("1");
        return lead;
    }

    /**
     * 插入测试线索并返回ID
     */
    private Long insertTestLead(CrmLeads lead) {
        try {
            int rows = crmLeadService.insertCrmLeads(lead);
            if (rows > 0) {
                return lead.getId();
            }
        } catch (Exception e) {
            logger.error("插入测试线索失败", e);
        }
        return null;
    }
}
