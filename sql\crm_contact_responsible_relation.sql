-- =============================================
-- 联系人-业务员关系表创建脚本
-- 实现联系人与业务员的多对多关系管理
-- 创建时间: 2025-07-29
-- =============================================

-- 创建联系人-业务员多对多关系表
CREATE TABLE `crm_contact_responsible_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contact_id` bigint(20) NOT NULL COMMENT '联系人ID',
  `responsible_person_id` bigint(20) NOT NULL COMMENT '负责人ID(业务员)',
  `business_type` varchar(50) DEFAULT 'GENERAL' COMMENT '业务类型(GENERAL/PACKAGING/PROTOTYPE/3D_PRINTING/MOLD/DESIGN)',
  `relation_status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '关系状态(ACTIVE/INACTIVE/TRANSFERRED)',
  `start_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始负责时间',
  `end_date` datetime DEFAULT NULL COMMENT '结束负责时间',
  `team_id` bigint(20) DEFAULT NULL COMMENT '所属团队ID',
  `assign_type` varchar(20) DEFAULT 'MANUAL' COMMENT '分配方式(MANUAL/TEAM_ASSIGN/POOL_CLAIM/MIGRATED)',
  `assign_by` bigint(20) DEFAULT NULL COMMENT '分配人ID',
  `assign_time` datetime DEFAULT NULL COMMENT '分配时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contact_responsible_business` (`contact_id`,`responsible_person_id`,`business_type`,`relation_status`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_responsible_person_id` (`responsible_person_id`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_relation_status` (`relation_status`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_assign_type` (`assign_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人-业务员关系表';

-- 添加复合索引优化查询性能
CREATE INDEX idx_contact_status_business ON crm_contact_responsible_relation(contact_id, relation_status, business_type);
CREATE INDEX idx_responsible_status_date ON crm_contact_responsible_relation(responsible_person_id, relation_status, start_date);
CREATE INDEX idx_team_status ON crm_contact_responsible_relation(team_id, relation_status);

-- 添加外键约束（可选，根据实际需要决定是否启用）
-- ALTER TABLE crm_contact_responsible_relation ADD CONSTRAINT fk_contact_relation_contact FOREIGN KEY (contact_id) REFERENCES crm_contacts(id);
-- ALTER TABLE crm_contact_responsible_relation ADD CONSTRAINT fk_contact_relation_user FOREIGN KEY (responsible_person_id) REFERENCES sys_user(user_id);
-- ALTER TABLE crm_contact_responsible_relation ADD CONSTRAINT fk_contact_relation_team FOREIGN KEY (team_id) REFERENCES crm_teams(id);

-- 验证表结构
SELECT 
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '是否可空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '字段注释'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'crm_contact_responsible_relation'
ORDER BY ORDINAL_POSITION;

-- 验证索引创建
SHOW INDEX FROM crm_contact_responsible_relation;