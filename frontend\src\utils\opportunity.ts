import type { OpportunityStage } from '@/types/opportunity'
import { formatNumber } from '@/utils/format'

// 商机阶段配置
export const stageConfig: Record<OpportunityStage, {
  label: string
  type: string
  defaultProbability: number
}> = {
  initial_contact: {
    label: '初步接触',
    type: 'info',
    defaultProbability: 20
  },
  needs_analysis: {
    label: '需求分析',
    type: 'warning',
    defaultProbability: 40
  },
  proposal: {
    label: '方案/报价',
    type: 'success',
    defaultProbability: 60
  },
  negotiation: {
    label: '谈判/复审',
    type: 'danger',
    defaultProbability: 80
  },
  closed_won: {
    label: '赢单',
    type: 'success',
    defaultProbability: 100
  },
  closed_lost: {
    label: '输单',
    type: 'info',
    defaultProbability: 0
  }
}

// 获取商机阶段标签
export const getStageName = (stage: OpportunityStage): string => {
  return stageConfig[stage]?.label || '未知阶段'
}

// 获取商机阶段标签类型
export const getStageType = (stage: OpportunityStage): string => {
  return stageConfig[stage]?.type || 'info'
}

// 获取商机阶段默认概率
export const getDefaultProbability = (stage: OpportunityStage): number => {
  return stageConfig[stage]?.defaultProbability || 0
}

// 格式化商机金额
export const formatAmount = (amount: number): string => {
  return formatNumber(amount, { style: 'currency', currency: 'CNY' })
}

// 格式化商机概率
export const formatProbability = (probability: number): string => {
  return `${probability}%`
}

// 获取概率状态
export const getProbabilityStatus = (probability: number): string => {
  if (probability >= 80) return 'success'
  if (probability >= 50) return 'warning'
  return 'exception'
}

// 获取概率范围选项
export const getProbabilityRangeOptions = () => [
  { label: '0-20%', value: '0-20' },
  { label: '21-40%', value: '21-40' },
  { label: '41-60%', value: '41-60' },
  { label: '61-80%', value: '61-80' },
  { label: '81-100%', value: '81-100' }
]

// 解析概率范围
export const parseProbabilityRange = (range: string): [number, number] => {
  const [min, max] = range.split('-').map(Number)
  return [min, max]
}

// 获取商机阶段选项
export const getStageOptions = () => {
  return Object.entries(stageConfig).map(([value, config]) => ({
    value,
    label: config.label
  }))
} 