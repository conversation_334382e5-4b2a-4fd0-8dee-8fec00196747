<template>
    <div class="customer-contracts-tab">
        <!-- 合同统计 -->
        <div class="contracts-stats-section">
            <el-row :gutter="16">
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ contracts.length }}</div>
                            <div class="stat-label">合同总数</div>
                        </div>
                        <el-icon class="stat-icon"><Document /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ activeContracts.length }}</div>
                            <div class="stat-label">执行中</div>
                        </div>
                        <el-icon class="stat-icon active"><Clock /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ completedContracts.length }}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <el-icon class="stat-icon completed"><CircleCheckFilled /></el-icon>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="stat-card">
                        <div class="stat-content">
                            <div class="stat-number">{{ formatAmount(totalAmount) }}</div>
                            <div class="stat-label">合同总额</div>
                        </div>
                        <el-icon class="stat-icon amount"><Money /></el-icon>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 操作区域 -->
        <div class="contracts-actions-section">
            <div class="section-header">
                <h3>关联合同</h3>
                <div class="header-actions">
                    <el-button type="primary" size="small" @click="showCreateContractDialog">
                        <el-icon><Plus /></el-icon>
                        新建合同
                    </el-button>
                    <el-button size="small" @click="showLinkContractDialog">
                        <el-icon><Link /></el-icon>
                        关联合同
                    </el-button>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="filter-controls">
                <el-select 
                    v-model="filterStatus" 
                    placeholder="合同状态" 
                    clearable 
                    size="small"
                    style="width: 150px;"
                    @change="handleFilterChange"
                >
                    <el-option label="全部" value="" />
                    <el-option label="草稿" value="draft" />
                    <el-option label="待审核" value="pending" />
                    <el-option label="已签署" value="signed" />
                    <el-option label="执行中" value="executing" />
                    <el-option label="已完成" value="completed" />
                    <el-option label="已终止" value="terminated" />
                </el-select>
                
                <el-input
                    v-model="searchKeyword"
                    placeholder="搜索合同名称或编号"
                    size="small"
                    style="width: 220px;"
                    clearable
                    @input="handleSearch"
                >
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>

                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    @change="handleDateChange"
                />

                <div class="view-controls">
                    <el-button-group size="small">
                        <el-button 
                            :type="viewMode === 'card' ? 'primary' : 'default'"
                            @click="viewMode = 'card'"
                        >
                            <el-icon><Grid /></el-icon>
                        </el-button>
                        <el-button 
                            :type="viewMode === 'table' ? 'primary' : 'default'"
                            @click="viewMode = 'table'"
                        >
                            <el-icon><List /></el-icon>
                        </el-button>
                    </el-button-group>
                </div>
            </div>
        </div>

        <!-- 合同列表 -->
        <div class="contracts-list-section" v-loading="loading">
            <div v-if="filteredContracts.length === 0" class="empty-state">
                <el-empty description="暂无关联合同" />
            </div>

            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="contracts-grid">
                <div 
                    v-for="contract in filteredContracts" 
                    :key="contract.id"
                    class="contract-card"
                    @click="handleContractClick(contract)"
                >
                    <div class="contract-header">
                        <div class="contract-title">
                            <h4 class="contract-name">{{ contract.contractName }}</h4>
                            <div class="contract-number">{{ contract.contractNumber }}</div>
                        </div>
                        <div class="contract-status">
                            <el-tag 
                                :type="getStatusTagType(contract.contractStatus)"
                                size="small"
                            >
                                {{ getStatusName(contract.contractStatus) }}
                            </el-tag>
                        </div>
                    </div>

                    <div class="contract-content">
                        <div class="contract-info">
                            <div class="info-row">
                                <div class="info-item">
                                    <span class="info-label">合同金额：</span>
                                    <span class="info-value amount">{{ formatAmount(contract.contractAmount) }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">合同类型：</span>
                                    <span class="info-value">{{ contract.contractType || '-' }}</span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="info-item">
                                    <span class="info-label">签署日期：</span>
                                    <span class="info-value">{{ contract.signDate || '-' }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">到期日期：</span>
                                    <span class="info-value" :class="{ 'expires-soon': isExpiringSoon(contract.endDate) }">
                                        {{ contract.endDate || '-' }}
                                    </span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="info-item">
                                    <span class="info-label">负责人：</span>
                                    <span class="info-value">{{ contract.ownerName || '-' }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">执行进度：</span>
                                    <div class="progress-wrapper">
                                        <el-progress 
                                            :percentage="contract.executionProgress || 0" 
                                            :stroke-width="6"
                                            :show-text="false"
                                            :color="getProgressColor(contract.executionProgress || 0)"
                                        />
                                        <span class="progress-text">{{ contract.executionProgress || 0 }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="contract-actions">
                            <el-dropdown @command="handleContractAction">
                                <el-button size="small" text>
                                    <el-icon><MoreFilled /></el-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item :command="{ action: 'view', contract }">
                                            <el-icon><View /></el-icon>
                                            查看详情
                                        </el-dropdown-item>
                                        <el-dropdown-item :command="{ action: 'edit', contract }">
                                            <el-icon><Edit /></el-icon>
                                            编辑合同
                                        </el-dropdown-item>
                                        <el-dropdown-item :command="{ action: 'download', contract }">
                                            <el-icon><Download /></el-icon>
                                            下载合同
                                        </el-dropdown-item>
                                        <el-dropdown-item :command="{ action: 'progress', contract }">
                                            <el-icon><TrendCharts /></el-icon>
                                            更新进度
                                        </el-dropdown-item>
                                        <el-dropdown-item 
                                            :command="{ action: 'unlink', contract }"
                                            divided
                                        >
                                            <el-icon><Link /></el-icon>
                                            取消关联
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>

                    <div v-if="contract.remarks" class="contract-remarks">
                        <p>{{ contract.remarks }}</p>
                    </div>
                </div>
            </div>

            <!-- 表格视图 -->
            <div v-if="viewMode === 'table'" class="contracts-table">
                <el-table :data="filteredContracts" stripe @row-click="handleContractClick">
                    <el-table-column prop="contractNumber" label="合同编号" min-width="140" />
                    
                    <el-table-column prop="contractName" label="合同名称" min-width="200" show-overflow-tooltip />
                    
                    <el-table-column prop="contractAmount" label="合同金额" min-width="120">
                        <template #default="{ row }">
                            <span class="amount-text">{{ formatAmount(row.contractAmount) }}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="contractStatus" label="状态" min-width="100">
                        <template #default="{ row }">
                            <el-tag 
                                :type="getStatusTagType(row.contractStatus)"
                                size="small"
                            >
                                {{ getStatusName(row.contractStatus) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="signDate" label="签署日期" min-width="120" />
                    
                    <el-table-column prop="endDate" label="到期日期" min-width="120">
                        <template #default="{ row }">
                            <span :class="{ 'expires-soon': isExpiringSoon(row.endDate) }">
                                {{ row.endDate || '-' }}
                            </span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="executionProgress" label="执行进度" min-width="120">
                        <template #default="{ row }">
                            <div class="progress-cell">
                                <el-progress 
                                    :percentage="row.executionProgress || 0" 
                                    :stroke-width="4"
                                    :show-text="false"
                                    :color="getProgressColor(row.executionProgress || 0)"
                                />
                                <span class="progress-text">{{ row.executionProgress || 0 }}%</span>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="ownerName" label="负责人" min-width="100" />
                    
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="{ row }">
                            <el-button size="small" text @click.stop="handleContractAction({ action: 'view', contract: row })">
                                查看
                            </el-button>
                            <el-button size="small" text @click.stop="handleContractAction({ action: 'download', contract: row })">
                                下载
                            </el-button>
                            <el-button size="small" text @click.stop="handleContractAction({ action: 'unlink', contract: row })">
                                取消关联
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <!-- 新建合同对话框 -->
        <el-dialog 
            v-model="createContractVisible" 
            title="新建合同" 
            width="900px"
            :before-close="closeCreateContractDialog"
        >
            <el-form :model="createContractForm" :rules="createContractRules" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="合同名称" prop="contractName">
                            <el-input 
                                v-model="createContractForm.contractName" 
                                placeholder="请输入合同名称"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="合同编号" prop="contractNumber">
                            <el-input 
                                v-model="createContractForm.contractNumber" 
                                placeholder="请输入合同编号"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="合同金额" prop="contractAmount">
                            <el-input-number 
                                v-model="createContractForm.contractAmount" 
                                :min="0"
                                :precision="2"
                                style="width: 100%"
                                placeholder="请输入合同金额"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="合同类型" prop="contractType">
                            <el-select 
                                v-model="createContractForm.contractType" 
                                placeholder="请选择合同类型"
                                style="width: 100%"
                            >
                                <el-option label="销售合同" value="sales" />
                                <el-option label="服务合同" value="service" />
                                <el-option label="采购合同" value="purchase" />
                                <el-option label="租赁合同" value="lease" />
                                <el-option label="其他" value="other" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="签署日期" prop="signDate">
                            <el-date-picker 
                                v-model="createContractForm.signDate" 
                                type="date"
                                placeholder="选择签署日期"
                                style="width: 100%"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="到期日期" prop="endDate">
                            <el-date-picker 
                                v-model="createContractForm.endDate" 
                                type="date"
                                placeholder="选择到期日期"
                                style="width: 100%"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="合同描述">
                    <el-input 
                        v-model="createContractForm.remarks" 
                        type="textarea"
                        :rows="4"
                        placeholder="请描述合同的主要内容和条款"
                        maxlength="500"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeCreateContractDialog">取消</el-button>
                    <el-button 
                        type="primary" 
                        @click="handleCreateContract" 
                        :loading="createContractLoading"
                    >
                        创建合同
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { 
    Plus,
    Link,

    Search,
    Grid,
    List,
    View,
    Edit,
    Download,
    TrendCharts,
    MoreFilled,
    Document,
    Clock,
    CircleCheckFilled,
    Money
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { CustomerData } from '../types';

interface ContractData {
    id: string;
    contractName: string;
    contractNumber: string;
    contractAmount: number;
    contractType: string;
    contractStatus: string;
    signDate: string;
    endDate: string;
    executionProgress: number;
    ownerName: string;
    remarks: string;
    createTime: string;
}

interface Props {
    entityData: CustomerData | null;
}

const props = defineProps<Props>();

// 状态管理
const loading = ref(false);
const contracts = ref<ContractData[]>([]);
const filterStatus = ref('');
const searchKeyword = ref('');
const dateRange = ref<[string, string] | null>(null);
const viewMode = ref<'card' | 'table'>('card');

// 新建合同相关
const createContractVisible = ref(false);
const createContractLoading = ref(false);
const createContractForm = reactive({
    contractName: '',
    contractNumber: '',
    contractAmount: 0,
    contractType: '',
    signDate: '',
    endDate: '',
    remarks: ''
});

const createContractRules = {
    contractName: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    contractNumber: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
    contractAmount: [{ required: true, message: '请输入合同金额', trigger: 'blur' }],
    contractType: [{ required: true, message: '请选择合同类型', trigger: 'change' }]
};

// 计算属性
const activeContracts = computed(() => 
    contracts.value.filter(c => ['signed', 'executing'].includes(c.contractStatus))
);

const completedContracts = computed(() => 
    contracts.value.filter(c => c.contractStatus === 'completed')
);

const totalAmount = computed(() => 
    contracts.value.reduce((sum, c) => sum + c.contractAmount, 0)
);

const filteredContracts = computed(() => {
    let result = contracts.value;
    
    // 按状态过滤
    if (filterStatus.value) {
        result = result.filter(c => c.contractStatus === filterStatus.value);
    }
    
    // 按关键词搜索
    if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(c => 
            c.contractName.toLowerCase().includes(keyword) ||
            c.contractNumber.toLowerCase().includes(keyword)
        );
    }
    
    // 按日期范围过滤
    if (dateRange.value && dateRange.value.length === 2) {
        const [startDate, endDate] = dateRange.value;
        result = result.filter(c => 
            c.signDate >= startDate && c.signDate <= endDate
        );
    }
    
    return result.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
});

// 格式化金额
const formatAmount = (amount: number) => {
    if (amount >= 10000) {
        return (amount / 10000).toFixed(1) + '万';
    }
    return amount.toLocaleString();
};

// 获取状态名称
const getStatusName = (status: string) => {
    const statusMap = {
        draft: '草稿',
        pending: '待审核',
        signed: '已签署',
        executing: '执行中',
        completed: '已完成',
        terminated: '已终止'
    };
    return statusMap[status as keyof typeof statusMap] || status;
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
    const typeMap = {
        draft: 'info',
        pending: 'warning',
        signed: 'primary',
        executing: 'success',
        completed: 'success',
        terminated: 'danger'
    };
    return typeMap[status as keyof typeof typeMap] || 'info';
};

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
    if (percentage < 30) return '#f56c6c';
    if (percentage < 70) return '#e6a23c';
    return '#67c23a';
};

// 判断是否即将到期
const isExpiringSoon = (endDate: string) => {
    if (!endDate) return false;
    const today = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30 && diffDays > 0;
};

// 处理筛选变化
const handleFilterChange = () => {
    // 筛选逻辑在computed中处理
};

// 处理搜索
const handleSearch = () => {
    // 搜索逻辑在computed中处理
};

// 处理日期变化
const handleDateChange = () => {
    // 日期筛选逻辑在computed中处理
};

// 处理合同点击
const handleContractClick = (contract: ContractData) => {
    ElMessage.info('查看合同详情功能开发中');
};

// 处理合同操作
const handleContractAction = async (command: { action: string; contract: ContractData }) => {
    const { action, contract } = command;
    
    switch (action) {
        case 'view':
            ElMessage.info('查看合同详情功能开发中');
            break;
        case 'edit':
            ElMessage.info('编辑合同功能开发中');
            break;
        case 'download':
            ElMessage.info('下载合同功能开发中');
            break;
        case 'progress':
            ElMessage.info('更新合同进度功能开发中');
            break;
        case 'unlink':
            await handleUnlinkContract(contract);
            break;
    }
};

// 取消关联合同
const handleUnlinkContract = async (contract: ContractData) => {
    try {
        await ElMessageBox.confirm(
            `确定要取消与合同 "${contract.contractName}" 的关联吗？`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        // TODO: 调用API取消关联
        // await unlinkContract(props.entityData?.id, contract.id);
        
        ElMessage.success('取消关联成功');
        await loadContracts();
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('取消关联失败');
        }
    }
};

// 显示关联合同对话框
const showLinkContractDialog = () => {
    ElMessage.info('关联合同功能开发中');
};

// 显示新建合同对话框
const showCreateContractDialog = () => {
    createContractVisible.value = true;
};

// 关闭新建合同对话框
const closeCreateContractDialog = () => {
    createContractVisible.value = false;
    Object.assign(createContractForm, {
        contractName: '',
        contractNumber: '',
        contractAmount: 0,
        contractType: '',
        signDate: '',
        endDate: '',
        remarks: ''
    });
};

// 创建合同
const handleCreateContract = async () => {
    createContractLoading.value = true;
    try {
        // TODO: 调用API创建合同
        // await createContract({
        //     ...createContractForm,
        //     customerId: props.entityData?.id
        // });
        
        ElMessage.success('创建合同成功');
        closeCreateContractDialog();
        await loadContracts();
    } catch (error) {
        ElMessage.error('创建合同失败');
    } finally {
        createContractLoading.value = false;
    }
};

// 加载合同列表
const loadContracts = async () => {
    loading.value = true;
    try {
        // TODO: 调用API加载合同列表
        // const response = await getCustomerContracts(props.entityData?.id);
        // contracts.value = response.data;
        
        // 模拟数据
        contracts.value = [];
    } catch (error) {
        ElMessage.error('加载合同列表失败');
    } finally {
        loading.value = false;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    if (props.entityData) {
        loadContracts();
    }
});
</script>

<style scoped lang="scss">
.customer-contracts-tab {
    .contracts-stats-section {
        margin-bottom: 24px;

        .stat-card {
            text-align: center;
            position: relative;
            overflow: hidden;

            .stat-content {
                .stat-number {
                    font-size: 28px;
                    font-weight: 600;
                    color: #303133;
                    margin-bottom: 8px;
                }

                .stat-label {
                    font-size: 14px;
                    color: #606266;
                }
            }

            .stat-icon {
                position: absolute;
                right: 16px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 32px;
                color: #409eff;
                opacity: 0.3;

                &.active {
                    color: #e6a23c;
                }

                &.completed {
                    color: #67c23a;
                }

                &.amount {
                    color: #f56c6c;
                }
            }
        }
    }

    .contracts-actions-section {
        margin-bottom: 24px;

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            h3 {
                margin: 0;
                color: #303133;
                font-size: 18px;
                font-weight: 600;
            }

            .header-actions {
                display: flex;
                gap: 12px;
            }
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;

            .view-controls {
                margin-left: auto;
            }
        }
    }

    .contracts-list-section {
        .empty-state {
            text-align: center;
            padding: 60px 0;
        }

        .contracts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;

            .contract-card {
                border: 1px solid #e4e7ed;
                border-radius: 12px;
                padding: 20px;
                background: white;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    transform: translateY(-2px);
                }

                .contract-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 16px;

                    .contract-title {
                        flex: 1;

                        .contract-name {
                            margin: 0 0 4px 0;
                            font-size: 16px;
                            font-weight: 600;
                            color: #303133;
                        }

                        .contract-number {
                            font-size: 12px;
                            color: #909399;
                        }
                    }
                }

                .contract-content {
                    position: relative;

                    .contract-info {
                        .info-row {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 12px;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            .info-item {
                                flex: 1;
                                display: flex;
                                flex-direction: column;
                                font-size: 14px;

                                .info-label {
                                    color: #606266;
                                    font-weight: 500;
                                    margin-bottom: 4px;
                                }

                                .info-value {
                                    color: #303133;

                                    &.amount {
                                        font-weight: 600;
                                        color: #f56c6c;
                                    }

                                    &.expires-soon {
                                        color: #e6a23c;
                                        font-weight: 500;
                                    }
                                }

                                .progress-wrapper {
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;

                                    .el-progress {
                                        flex: 1;
                                    }

                                    .progress-text {
                                        font-weight: 500;
                                        font-size: 12px;
                                    }
                                }
                            }
                        }
                    }

                    .contract-actions {
                        position: absolute;
                        top: 0;
                        right: 0;
                    }
                }

                .contract-remarks {
                    margin-top: 16px;
                    padding: 12px;
                    background: #f8f9fa;
                    border-radius: 6px;

                    p {
                        margin: 0;
                        font-size: 14px;
                        line-height: 1.6;
                        color: #606266;
                    }
                }
            }
        }

        .contracts-table {
            .amount-text {
                font-weight: 600;
                color: #f56c6c;
            }

            .expires-soon {
                color: #e6a23c;
                font-weight: 500;
            }

            .progress-cell {
                display: flex;
                align-items: center;
                gap: 8px;

                .el-progress {
                    flex: 1;
                }

                .progress-text {
                    font-weight: 500;
                    font-size: 12px;
                }
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .customer-contracts-tab {
        .contracts-stats-section {
            .el-row {
                .el-col {
                    margin-bottom: 16px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }

        .contracts-actions-section {
            .section-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;

                .header-actions {
                    justify-content: center;
                }
            }

            .filter-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        .contracts-list-section {
            .contracts-grid {
                grid-template-columns: 1fr;
                gap: 16px;

                .contract-card {
                    .contract-header {
                        flex-direction: column;
                        gap: 12px;
                        align-items: stretch;
                    }

                    .contract-content {
                        .contract-actions {
                            position: static;
                            margin-top: 12px;
                            text-align: right;
                        }
                    }
                }
            }
        }
    }
}
</style>