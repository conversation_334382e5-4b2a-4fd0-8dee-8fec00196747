<template>
    <div class="customer-header-tab">
        <!-- 顶部标题区 -->
        <div class="header-section">
            <div class="title-area">
                <div class="entity-type">{{ modelName }}</div>
                <h2 class="entity-name">
                    {{ entityData?.customerName || '未知客户' }}
                    <el-icon 
                        class="star-icon" 
                        :class="{ 'star-filled': entityData?.isFollowing }"
                        @click="handleToggleFollow"
                    >
                        <StarFilled v-if="entityData?.isFollowing" />
                        <Star v-else />
                    </el-icon>
                </h2>
            </div>
            
            <!-- 操作按钮区 -->
            <div class="action-buttons">
                <template v-for="(action, index) in actions" :key="index">
                    <button
                        :class="getButtonClass(action)"
                        @click="handleActionClick(action)"
                    >
                        <el-icon v-if="action.icon" class="button-icon">
                            <component :is="action.icon" />
                        </el-icon>
                        {{ action.label }}
                    </button>
                </template>
            </div>
        </div>

        <!-- 基本信息区 -->
        <div class="info-section">
            <div class="info-grid">
                <div class="info-row">
                    <div class="info-item" @dblclick="handleFieldEdit('customerName')">
                        <span class="info-label">客户名称</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'customerName'">
                                <el-input
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请输入客户名称"
                                    @blur="handleSaveField"
                                    @keyup.enter="handleSaveField"
                                    v-focus
                                />
                            </template>
                            <span v-else class="info-value">
                                {{ entityData?.customerName || '-' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item" @dblclick="handleFieldEdit('customerIndustry')">
                        <span class="info-label">所属行业</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'customerIndustry'">
                                <el-input
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请输入所属行业"
                                    @blur="handleSaveField"
                                    @keyup.enter="handleSaveField"
                                    v-focus
                                />
                            </template>
                            <span v-else class="info-value">
                                {{ entityData?.customerIndustry || '-' }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="info-row">
                    <div class="info-item" @dblclick="handleFieldEdit('mobile')">
                        <span class="info-label">手机号</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'mobile'">
                                <el-input
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请输入手机号码"
                                    @blur="handleSaveField"
                                    @keyup.enter="handleSaveField"
                                    v-focus
                                />
                            </template>
                            <span v-else class="info-value">
                                {{ entityData?.mobile || '-' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item" @dblclick="handleFieldEdit('email')">
                        <span class="info-label">邮箱</span>
                        <div class="info-value-wrapper">
                            <template v-if="editingField === 'email'">
                                <el-input
                                    v-model="editingValue"
                                    size="small"
                                    placeholder="请输入邮箱地址"
                                    @blur="handleSaveField"
                                    @keyup.enter="handleSaveField"
                                    v-focus
                                />
                            </template>
                            <span v-else class="info-value">
                                {{ entityData?.email || '-' }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">负责人</span>
                        <div class="info-value-wrapper">
                            <span class="info-value">
                                {{ getResponsiblePersonName(entityData?.responsiblePersonId) || '-' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">创建时间</span>
                        <div class="info-value-wrapper">
                            <span class="info-value">
                                {{ entityData?.createTime || '-' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { StarFilled, Star } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { CustomerData } from '../types';

interface Action {
    type: string;
    label: string;
    icon?: any;
    variant?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
}

interface Props {
    entityData: CustomerData | null;
    modelName: string;
    actions?: Action[];
    userOptions?: any[];
}

const props = withDefaults(defineProps<Props>(), {
    entityData: () => null,
    modelName: '',
    actions: () => [],
    userOptions: () => []
});

const emit = defineEmits<{
    'toggle-follow': [customer: CustomerData];
    'action-click': [action: Action];
    'field-update': [field: string, value: any];
}>();

// 编辑状态
const editingField = ref<string>('');
const editingValue = ref<string>('');

// 处理关注状态切换
const handleToggleFollow = () => {
    if (props.entityData) {
        emit('toggle-follow', props.entityData);
    }
};

// 处理操作按钮点击
const handleActionClick = (action: Action) => {
    emit('action-click', action);
};

// 处理字段编辑
const handleFieldEdit = (field: string) => {
    editingField.value = field;
    editingValue.value = (props.entityData as any)?.[field] || '';
};

// 保存字段编辑
const handleSaveField = async () => {
    if (editingField.value && props.entityData && editingValue.value !== (props.entityData as any)[editingField.value]) {
        try {
            emit('field-update', editingField.value, editingValue.value);
            ElMessage.success('更新成功');
        } catch (error) {
            ElMessage.error('更新失败');
        }
    }
    editingField.value = '';
    editingValue.value = '';
};

// 获取按钮样式类
const getButtonClass = (action: Action) => {
    const baseClass = 'action-button';
    const variantClass = action.variant ? `action-button--${action.variant}` : 'action-button--primary';
    return `${baseClass} ${variantClass}`;
};

// 获取客户级别标签类型
const getCustomerLevelTagType = (level: string) => {
    const levelMap: Record<string, string> = {
        'A级': 'danger',
        'B级': 'warning', 
        'C级': 'success',
        'D级': 'info'
    };
    return levelMap[level] || 'info';
};

// 获取负责人姓名
const getResponsiblePersonName = (userId: string) => {
    const user = props.userOptions?.find(u => u.userId === userId);
    return user?.nickName || user?.userName || '';
};

// v-focus 指令
const vFocus = {
    mounted: (el: HTMLElement) => {
        const input = el.querySelector('input');
        if (input) {
            input.focus();
            input.select();
        }
    }
};
</script>

<style scoped lang="scss">
.customer-header-tab {
    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 8px 12px;
        background: #fff;
        border-bottom: 1px solid #e5e7eb;
        position: relative;

        .title-area {
            flex: 1;
            
            .entity-type {
                font-size: 11px;
                color: rgba(107, 114, 128, 0.8);
                margin-bottom: 2px;
                font-weight: 500;
                text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .entity-name {
                margin: 0;
                font-size: 15px;
                font-weight: 700;
                color: #1f2937;
                display: flex;
                align-items: center;
                gap: 4px;
                line-height: 1.1;
                text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8);

                .star-icon {
                    cursor: pointer;
                    font-size: 14px;
                    color: rgba(209, 213, 219, 0.8);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.6));

                    &:hover {
                        color: #fbbf24;
                        transform: scale(1.1) rotate(5deg);
                        filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.3));
                    }

                    &.star-filled {
                        color: #fbbf24;
                        filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.3));
                    }
                }
            }
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;

            .action-button {
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 8px 16px;
                border-radius: 6px;
                border: 1px solid;
                background: transparent;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;

                .button-icon {
                    font-size: 16px;
                }

                &--primary {
                    border-color: #409eff;
                    color: #409eff;

                    &:hover {
                        background: #409eff;
                        color: white;
                    }
                }

                &--success {
                    border-color: #67c23a;
                    color: #67c23a;

                    &:hover {
                        background: #67c23a;
                        color: white;
                    }
                }

                &--warning {
                    border-color: #e6a23c;
                    color: #e6a23c;

                    &:hover {
                        background: #e6a23c;
                        color: white;
                    }
                }

                &--danger {
                    border-color: #f56c6c;
                    color: #f56c6c;

                    &:hover {
                        background: #f56c6c;
                        color: white;
                    }
                }
            }
        }
    }

    .info-section {
        padding: 8px 12px;
        
        .info-grid {
            display: flex;
            flex-direction: column;
            gap: 3px;
            
            .info-row {
                display: flex;
                gap: 16px;

                .info-item {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    padding: 2px 4px;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s;
                    min-height: 20px;

                    &:hover {
                        background: rgba(255, 255, 255, 0.4);
                        backdrop-filter: blur(8px);
                        -webkit-backdrop-filter: blur(8px);
                    }

                    .info-label {
                        font-size: 11px;
                        color: rgba(107, 114, 128, 0.9);
                        font-weight: 500;
                        min-width: 45px;
                        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
                    }

                    .info-value-wrapper {
                        flex: 1;
                        display: flex;
                        align-items: center;

                        .info-value {
                            font-size: 12px;
                            color: #1f2937;
                            font-weight: 600;
                            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6);
                        }

                        .el-input {
                             width: 150px;
                         }

                         .el-tag {
                             margin: 0;
                             font-size: 11px;
                         }
                     }
                 }
             }
         }
     }

     :deep(.el-input__wrapper) {
         background: linear-gradient(135deg, 
             rgba(255, 255, 255, 0.9) 0%, 
             rgba(255, 255, 255, 0.7) 100%);
         backdrop-filter: blur(8px);
         -webkit-backdrop-filter: blur(8px);
         border: 1px solid rgba(255, 255, 255, 0.5);
         border-radius: 4px;
         box-shadow: 
             0 1px 3px rgba(0, 0, 0, 0.06),
             inset 0 1px 0 rgba(255, 255, 255, 0.8);
         transition: all 0.2s;
         padding: 1px 8px;
     }

     :deep(.el-input__wrapper:hover) {
         border-color: rgba(59, 130, 246, 0.5);
         box-shadow: 
             0 1px 4px rgba(0, 0, 0, 0.1),
             inset 0 1px 0 rgba(255, 255, 255, 0.9);
     }

     :deep(.el-input__wrapper.is-focus) {
         border-color: rgba(59, 130, 246, 0.7);
         box-shadow: 
             0 0 0 2px rgba(59, 130, 246, 0.2),
             0 1px 4px rgba(0, 0, 0, 0.1),
             inset 0 1px 0 rgba(255, 255, 255, 0.9);
     }

     :deep(.el-input__inner) {
         font-size: 12px;
         line-height: 20px;
     }
}

@media (max-width: 768px) {
    .customer-header-tab {
        .header-section {
            flex-direction: column;
            gap: 16px;

            .action-buttons {
                width: 100%;
                justify-content: flex-start;
            }
        }

        .info-section {
            .info-grid {
                .info-row {
                    grid-template-columns: 1fr;
                    gap: 16px;
                }
            }
        }
    }
}
</style>