-- 联系人操作日志表创建脚本
-- 基于客户操作日志表结构，适配联系人业务

-- 创建联系人操作日志表
DROP TABLE IF EXISTS `crm_contact_operation_log`;
CREATE TABLE `crm_contact_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contact_id` bigint(20) NOT NULL COMMENT '联系人ID',
  `business_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务类型',
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型(CREATE,UPDATE,DELETE,FOLLOW,UNFOLLOW等)',
  `operation_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作内容',
  `operation_details` json NULL COMMENT '操作细则，存储变更前后的值',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人名称',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附加数据',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_contact_id`(`contact_id`) USING BTREE,
  INDEX `idx_business_operation`(`business_type`, `operation_type`) USING BTREE,
  INDEX `idx_operation_time`(`operation_time`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '联系人操作日志表' ROW_FORMAT = Dynamic;

-- 插入一些示例数据用于测试
INSERT INTO `crm_contact_operation_log` (`contact_id`, `business_type`, `operation_type`, `operation_content`, `operation_details`, `operator_id`, `operator_name`, `operation_time`) VALUES
(1, '联系人', 'CREATE', '创建联系人：张三', '{"after":{"name":"张三","phone":"***********"}}', 1, 'admin', NOW()),
(1, '联系人', 'UPDATE', '更新联系人信息', '{"before":{"phone":"***********"},"after":{"phone":"***********"}}', 1, 'admin', NOW()),
(1, '联系人', 'FOLLOW', '关注联系人', '{"after":{"isFollowing":true}}', 1, 'admin', NOW());
