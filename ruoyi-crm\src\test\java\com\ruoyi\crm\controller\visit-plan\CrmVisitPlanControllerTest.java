package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmVisitPlan;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.service.ICrmVisitPlanService;

/**
 * 拜访计划Controller单元测试
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@ExtendWith(MockitoExtension.class)
class CrmVisitPlanControllerTest {
    
    @Mock
    private ICrmVisitPlanService crmVisitPlanService;
    
    @Mock
    private HttpServletResponse response;
    
    @InjectMocks
    private CrmVisitPlanController visitPlanController;
    
    private CrmVisitPlan testPlan;
    
    @BeforeEach
    void setUp() {
        // 设置BaseController的mock数据
        ReflectionTestUtils.invokeMethod(visitPlanController, "setUserId", 1L);
        ReflectionTestUtils.invokeMethod(visitPlanController, "setUsername", "testUser");
        ReflectionTestUtils.invokeMethod(visitPlanController, "setDeptId", 100L);
        
        testPlan = new CrmVisitPlan();
        testPlan.setId(1L);
        testPlan.setVisitPlanName("测试拜访计划");
        testPlan.setVisitTime(new Date());
        testPlan.setCustomerId(100L);
        testPlan.setCustomerName("测试客户");
        testPlan.setVisitPurpose("产品演示");
        testPlan.setStatus("planned");
    }
    
    @Test
    void testList() {
        // 准备数据
        List<CrmVisitPlan> planList = Arrays.asList(testPlan);
        when(crmVisitPlanService.selectCrmVisitPlanList(any(CrmVisitPlan.class))).thenReturn(planList);
        
        // 执行测试
        TableDataInfo result = visitPlanController.list(new CrmVisitPlan());
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(crmVisitPlanService, times(1)).selectCrmVisitPlanList(any(CrmVisitPlan.class));
    }
    
    @Test
    void testListByObject() {
        // 准备数据
        List<CrmVisitPlan> planList = Arrays.asList(testPlan);
        when(crmVisitPlanService.selectByRelatedObject("customer", 100L)).thenReturn(planList);
        
        // 执行测试
        AjaxResult result = visitPlanController.listByObject("customer", 100L);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.get("code"));
        List<CrmVisitPlan> data = (List<CrmVisitPlan>) result.get("data");
        assertEquals(1, data.size());
        verify(crmVisitPlanService, times(1)).selectByRelatedObject("customer", 100L);
    }
    
    @Test
    void testGetInfo() {
        // 准备数据
        when(crmVisitPlanService.selectCrmVisitPlanById(1L)).thenReturn(testPlan);
        
        // 执行测试
        AjaxResult result = visitPlanController.getInfo(1L);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.get("code"));
        CrmVisitPlan data = (CrmVisitPlan) result.get("data");
        assertEquals("测试拜访计划", data.getVisitPlanName());
        verify(crmVisitPlanService, times(1)).selectCrmVisitPlanById(1L);
    }
    
    @Test
    void testAdd() {
        // 准备数据
        CrmVisitPlan newPlan = new CrmVisitPlan();
        newPlan.setVisitPlanName("新拜访计划");
        newPlan.setCustomerId(100L);
        
        when(crmVisitPlanService.insertCrmVisitPlan(any(CrmVisitPlan.class))).thenReturn(1);
        
        // 执行测试
        AjaxResult result = visitPlanController.add(newPlan);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.get("code"));
        assertEquals("testUser", newPlan.getCreateBy());
        assertNotNull(newPlan.getCreateTime());
        assertEquals(1L, newPlan.getOwnerId());
        assertEquals("testUser", newPlan.getOwnerName());
        assertEquals(100L, newPlan.getDeptId());
        verify(crmVisitPlanService, times(1)).insertCrmVisitPlan(any(CrmVisitPlan.class));
    }
    
    @Test
    void testEdit() {
        // 准备数据
        CrmVisitPlan updatePlan = new CrmVisitPlan();
        updatePlan.setId(1L);
        updatePlan.setVisitPlanName("更新的拜访计划");
        
        when(crmVisitPlanService.updateCrmVisitPlan(any(CrmVisitPlan.class))).thenReturn(1);
        
        // 执行测试
        AjaxResult result = visitPlanController.edit(updatePlan);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.get("code"));
        assertEquals("testUser", updatePlan.getUpdateBy());
        assertNotNull(updatePlan.getUpdateTime());
        verify(crmVisitPlanService, times(1)).updateCrmVisitPlan(any(CrmVisitPlan.class));
    }
    
    @Test
    void testRemove() {
        // 准备数据
        Long[] ids = {1L, 2L};
        when(crmVisitPlanService.deleteCrmVisitPlanByIds(ids)).thenReturn(2);
        
        // 执行测试
        AjaxResult result = visitPlanController.remove(ids);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.get("code"));
        verify(crmVisitPlanService, times(1)).deleteCrmVisitPlanByIds(ids);
    }
    
    @Test
    void testPostpone_Success() {
        // 准备数据
        Map<String, Object> params = new HashMap<>();
        params.put("reason", "客户临时有事");
        params.put("remark", "需要重新安排");
        params.put("newVisitTime", "2025-02-01 14:00:00");
        
        when(crmVisitPlanService.postponeVisitPlan(eq(1L), anyString(), anyString(), any(Date.class))).thenReturn(1);
        
        // 执行测试
        AjaxResult result = visitPlanController.postpone(1L, params);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.get("code"));
        verify(crmVisitPlanService, times(1)).postponeVisitPlan(eq(1L), anyString(), anyString(), any(Date.class));
    }
    
    @Test
    void testPostpone_EmptyReason() {
        // 准备数据
        Map<String, Object> params = new HashMap<>();
        params.put("reason", "");
        params.put("newVisitTime", "2025-02-01 14:00:00");
        
        // 执行测试
        AjaxResult result = visitPlanController.postpone(1L, params);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.get("code"));
        assertEquals("延期原因不能为空", result.get("msg"));
    }
    
    @Test
    void testCancel_Success() {
        // 准备数据
        Map<String, Object> params = new HashMap<>();
        params.put("reason", "客户取消会面");
        params.put("remark", "下次再约");
        
        when(crmVisitPlanService.cancelVisitPlan(eq(1L), anyString(), anyString())).thenReturn(1);
        
        // 执行测试
        AjaxResult result = visitPlanController.cancel(1L, params);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.get("code"));
        verify(crmVisitPlanService, times(1)).cancelVisitPlan(eq(1L), anyString(), anyString());
    }
    
    @Test
    void testCancel_EmptyReason() {
        // 准备数据
        Map<String, Object> params = new HashMap<>();
        params.put("reason", null);
        
        // 执行测试
        AjaxResult result = visitPlanController.cancel(1L, params);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.get("code"));
        assertEquals("取消原因不能为空", result.get("msg"));
    }
    
    @Test
    void testComplete_Success() {
        // 准备数据
        Map<String, Object> params = new HashMap<>();
        params.put("followupContent", "拜访顺利完成，客户对产品很感兴趣");
        
        when(crmVisitPlanService.completeVisitPlan(eq(1L), anyString())).thenReturn(1);
        
        // 执行测试
        AjaxResult result = visitPlanController.complete(1L, params);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(200, result.get("code"));
        verify(crmVisitPlanService, times(1)).completeVisitPlan(eq(1L), anyString());
    }
    
    @Test
    void testComplete_EmptyContent() {
        // 准备数据
        Map<String, Object> params = new HashMap<>();
        params.put("followupContent", "");
        
        // 执行测试
        AjaxResult result = visitPlanController.complete(1L, params);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.get("code"));
        assertEquals("跟进记录内容不能为空", result.get("msg"));
    }
    
    @Test
    void testGetStatistics_Admin() {
        // 准备数据
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("total", 10);
        statistics.put("completed", 5);
        statistics.put("completionRate", "50.00");
        
        when(crmVisitPlanService.getStatistics(null, null)).thenReturn(statistics);
        
        // 使用MockedStatic模拟静态方法
        try (MockedStatic<SecurityUtils> securityUtils = mockStatic(SecurityUtils.class)) {
            securityUtils.when(() -> SecurityUtils.isAdmin(1L)).thenReturn(true);
            
            // 执行测试
            AjaxResult result = visitPlanController.getStatistics(null);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(200, result.get("code"));
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            assertEquals(10, data.get("total"));
            verify(crmVisitPlanService, times(1)).getStatistics(null, null);
        }
    }
    
    @Test
    void testGetStatistics_NonAdmin() {
        // 准备数据
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("total", 5);
        statistics.put("completed", 3);
        statistics.put("completionRate", "60.00");
        
        when(crmVisitPlanService.getStatistics(1L, "month")).thenReturn(statistics);
        
        // 使用MockedStatic模拟静态方法
        try (MockedStatic<SecurityUtils> securityUtils = mockStatic(SecurityUtils.class)) {
            securityUtils.when(() -> SecurityUtils.isAdmin(1L)).thenReturn(false);
            
            // 执行测试
            AjaxResult result = visitPlanController.getStatistics("month");
            
            // 验证结果
            assertNotNull(result);
            assertEquals(200, result.get("code"));
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            assertEquals(5, data.get("total"));
            verify(crmVisitPlanService, times(1)).getStatistics(1L, "month");
        }
    }
    
    @Test
    void testExport() {
        // 准备数据
        List<CrmVisitPlan> planList = Arrays.asList(testPlan);
        when(crmVisitPlanService.selectCrmVisitPlanList(any(CrmVisitPlan.class))).thenReturn(planList);
        
        // 执行测试
        assertDoesNotThrow(() -> {
            visitPlanController.export(response, new CrmVisitPlan());
        });
        
        // 验证调用
        verify(crmVisitPlanService, times(1)).selectCrmVisitPlanList(any(CrmVisitPlan.class));
    }
}
