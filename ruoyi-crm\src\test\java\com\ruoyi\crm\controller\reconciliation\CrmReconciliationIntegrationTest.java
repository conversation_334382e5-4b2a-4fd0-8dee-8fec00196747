package com.ruoyi.crm.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmReconciliation;
import com.ruoyi.crm.BaseTestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.*;

/**
 * 对账单真实集成测试类
 * 不使用 Mock，直接连接真实数据库进行端到端测试
 * 继承 BaseTestCase 获得完整的用户认证支持
 */
@AutoConfigureMockMvc
public class CrmReconciliationIntegrationTest extends BaseTestCase {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试新增和查询对账单的完整流程
     */
    @Test
    public void testAddAndGetReconciliation() throws Exception {
        // 1. 准备要新增的对账单数据
        CrmReconciliation newReconciliation = new CrmReconciliation();
        newReconciliation.setCustomerId(1L);
        newReconciliation.setReconciliationNo("REC-2025-001");
        newReconciliation.setTotalAmount(new BigDecimal("1500.00"));
        newReconciliation.setStatus("draft");
        newReconciliation.setResponsibleUserId(1L);
        newReconciliation.setResponsibleUserName("admin");

        // 2. 执行新增操作
        mockMvc.perform(post("/front/crm/reconciliation")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newReconciliation)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 3. 执行查询列表操作来验证数据
        mockMvc.perform(get("/front/crm/reconciliation/list"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.rows", hasSize(greaterThan(0))));
    }

    /**
     * 测试修改对账单 - 简化版本
     */
    @Test
    public void testUpdateReconciliation() throws Exception {
        try {
            // 1. 先查询现有记录或创建一个简单记录
            CrmReconciliation reconciliation = new CrmReconciliation();
            reconciliation.setReconciliationId(1L); // 假设存在ID为1的记录
            reconciliation.setReconciliationNo("REC-TEST-001");
            reconciliation.setRelationType("customer"); // 设置关联类型
            reconciliation.setCustomerId(1L);
            reconciliation.setCustomerName("测试客户");
            reconciliation.setTotalAmount(new BigDecimal("1000.00"));
            reconciliation.setStatus("draft");
            reconciliation.setResponsibleUserId(1L);
            reconciliation.setResponsibleUserName("admin");

            // 2. 直接尝试更新操作，看具体错误
            String response = mockMvc.perform(put("/front/crm/reconciliation")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(reconciliation)))
                    .andReturn().getResponse().getContentAsString();
            
            System.out.println("更新操作响应: " + response);
            
            // 检查是否成功
            if (response.contains("\"code\":200")) {
                System.out.println("测试成功！");
            } else {
                System.out.println("测试失败，响应内容: " + response);
                // 也要断言失败以便看到错误
                mockMvc.perform(put("/front/crm/reconciliation")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(reconciliation)))
                        .andExpect(jsonPath("$.code").value(200));
            }

        } catch (Exception e) {
            System.err.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 测试删除对账单
     */
    @Test
    public void testDeleteReconciliation() throws Exception {
        // 1. 先创建一个对账单
        CrmReconciliation newReconciliation = new CrmReconciliation();
        newReconciliation.setCustomerId(1L);
        newReconciliation.setReconciliationNo("REC-2025-003");
        newReconciliation.setTotalAmount(new BigDecimal("1000.00"));
        newReconciliation.setStatus("draft");
        newReconciliation.setResponsibleUserId(1L);
        newReconciliation.setResponsibleUserName("admin");

        mockMvc.perform(post("/front/crm/reconciliation")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newReconciliation)))
                .andExpect(status().isOk());

        // 2. 执行删除操作
        mockMvc.perform(delete("/front/crm/reconciliation/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
