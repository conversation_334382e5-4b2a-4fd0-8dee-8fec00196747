-- ================================================
-- 拜访计划功能菜单权限配置脚本
-- 创建时间：2024-06-30
-- 说明：包含菜单配置、权限配置
-- ================================================

-- 1. 插入拜访计划主菜单
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访计划', 2000, 4, 'visitPlan', 'crm/visitPlan/index', '', 1, 0, 'C', '0', '0', 'crm:visitPlan:list', 'calendar', 'admin', NOW(), '', NULL, '拜访计划菜单');

-- 获取刚插入的菜单ID（需要根据实际情况调整）
SET @menu_id = LAST_INSERT_ID();

-- 2. 插入拜访计划子菜单权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访计划查询', @menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:query', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划新增', @menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:add', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划修改', @menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:edit', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划删除', @menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:remove', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划导出', @menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:export', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划延期', @menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:postpone', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划取消', @menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:cancel', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划完成', @menu_id, 8, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:complete', '#', 'admin', NOW(), '', NULL, ''),
('拜访计划统计', @menu_id, 9, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlan:statistics', '#', 'admin', NOW(), '', NULL, '');

-- 3. 插入拜访计划提醒管理菜单（可选）
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访提醒管理', 2000, 5, 'visitPlanReminder', 'crm/visitPlanReminder/index', '', 1, 0, 'C', '0', '0', 'crm:visitPlanReminder:list', 'bell', 'admin', NOW(), '', NULL, '拜访提醒管理菜单');

-- 获取提醒管理菜单ID
SET @reminder_menu_id = LAST_INSERT_ID();

-- 4. 插入拜访提醒管理子菜单权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访提醒查询', @reminder_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanReminder:query', '#', 'admin', NOW(), '', NULL, ''),
('拜访提醒新增', @reminder_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanReminder:add', '#', 'admin', NOW(), '', NULL, ''),
('拜访提醒修改', @reminder_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanReminder:edit', '#', 'admin', NOW(), '', NULL, ''),
('拜访提醒删除', @reminder_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanReminder:remove', '#', 'admin', NOW(), '', NULL, '');

-- 5. 插入拜访日志管理菜单（可选）
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访日志管理', 2000, 6, 'visitPlanLog', 'crm/visitPlanLog/index', '', 1, 0, 'C', '0', '0', 'crm:visitPlanLog:list', 'documentation', 'admin', NOW(), '', NULL, '拜访日志管理菜单');

-- 获取日志管理菜单ID
SET @log_menu_id = LAST_INSERT_ID();

-- 6. 插入拜访日志管理子菜单权限
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访日志查询', @log_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanLog:query', '#', 'admin', NOW(), '', NULL, ''),
('拜访日志删除', @log_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanLog:remove', '#', 'admin', NOW(), '', NULL, ''),
('拜访日志导出', @log_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'crm:visitPlanLog:export', '#', 'admin', NOW(), '', NULL, '');

-- 7. 为超级管理员角色分配权限（假设角色ID为1）
-- 插入拜访计划相关权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 1, `menu_id` FROM `sys_menu` WHERE `perms` LIKE 'crm:visitPlan%' OR `perms` LIKE 'crm:visitPlanReminder%' OR `perms` LIKE 'crm:visitPlanLog%';

-- 8. 为CRM管理员角色分配权限（需要根据实际角色ID调整）
-- INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) 
-- SELECT 2, `menu_id` FROM `sys_menu` WHERE `perms` LIKE 'crm:visitPlan%';

-- 9. 创建拜访计划操作类型字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('拜访计划操作类型', 'visit_plan_operation_type', '0', 'admin', NOW(), '拜访计划操作类型列表');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES 
(1, '创建', 'create', 'visit_plan_operation_type', '', 'primary', 'Y', '0', 'admin', NOW(), '创建拜访计划'),
(2, '更新', 'update', 'visit_plan_operation_type', '', 'info', 'N', '0', 'admin', NOW(), '更新拜访计划'),
(3, '延期', 'postpone', 'visit_plan_operation_type', '', 'warning', 'N', '0', 'admin', NOW(), '延期拜访计划'),
(4, '取消', 'cancel', 'visit_plan_operation_type', '', 'danger', 'N', '0', 'admin', NOW(), '取消拜访计划'),
(5, '完成', 'complete', 'visit_plan_operation_type', '', 'success', 'N', '0', 'admin', NOW(), '完成拜访计划');

COMMIT;

-- 验证菜单插入结果
SELECT menu_id, menu_name, parent_id, path, component, perms, icon, remark 
FROM sys_menu 
WHERE perms LIKE 'crm:visitPlan%' OR perms LIKE 'crm:visitPlanReminder%' OR perms LIKE 'crm:visitPlanLog%'
ORDER BY parent_id, order_num;
