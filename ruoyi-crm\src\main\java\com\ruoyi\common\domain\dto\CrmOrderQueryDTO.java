package com.ruoyi.common.domain.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 订单查询数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
public class CrmOrderQueryDTO {

    /** 订单ID */
    private Long id;

    /** 订单编号 */
    private String orderNo;

    /** 订单标题 */
    private String orderTitle;

    /** 询价单号 */
    private String quoteNo;

    /** 客户ID */
    private Long customerId;

    /** 客户名称 */
    private String customerName;

    /** 联系人姓名 */
    private String contactPerson;

    /** 联系电话 */
    private String contactPhone;

    /** 关联商机ID */
    private Long opportunityId;

    /** 关联合同ID */
    private Long contractId;

    /** 订单来源 */
    private String orderSource;

    /** 订单类型 */
    private String orderType;

    /** 优先级 */
    private String priorityLevel;

    /** 紧急标志 */
    private Integer urgentFlag;

    /** 客户类型 */
    private String customerType;

    /** 订单状态 */
    private String status;

    /** 分配状态 */
    private String assignmentStatus;

    /** 负责人ID */
    private Long ownerId;

    /** 负责人姓名 */
    private String ownerName;

    /** 币种 */
    private String currency;

    // ========== 查询条件 ==========
    
    /** 订单编号模糊查询 */
    private String orderNoLike;

    /** 客户名称模糊查询 */
    private String customerNameLike;

    /** 联系人姓名模糊查询 */
    private String contactPersonLike;

    /** 联系电话模糊查询 */
    private String contactPhoneLike;

    /** 状态列表 */
    private List<String> statusList;

    /** 分配状态列表 */
    private List<String> assignmentStatusList;

    /** 订单来源列表 */
    private List<String> orderSourceList;

    /** 订单类型列表 */
    private List<String> orderTypeList;

    /** 优先级列表 */
    private List<String> priorityLevelList;

    /** 客户类型列表 */
    private List<String> customerTypeList;

    /** 负责人ID列表 */
    private List<Long> ownerIds;

    /** 客户ID列表 */
    private List<Long> customerIds;

    /** 商机ID列表 */
    private List<Long> opportunityIds;

    // ========== 金额范围 ==========
    
    /** 最小金额 */
    private BigDecimal minAmount;

    /** 最大金额 */
    private BigDecimal maxAmount;

    /** 最小已付金额 */
    private BigDecimal minPaidAmount;

    /** 最大已付金额 */
    private BigDecimal maxPaidAmount;

    // ========== 日期范围 ==========
    
    /** 创建开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createStartDate;

    /** 创建结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createEndDate;

    /** 下单开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderStartDate;

    /** 下单结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderEndDate;

    /** 分配开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assignStartDate;

    /** 分配结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date assignEndDate;

    /** 预期交付开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedDeliveryStartDate;

    /** 预期交付结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedDeliveryEndDate;

    /** 实际交付开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualDeliveryStartDate;

    /** 实际交付结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualDeliveryEndDate;

    // ========== 排序条件 ==========
    
    /** 排序字段 */
    private String orderBy;

    /** 排序方向：ASC/DESC */
    private String orderDirection;

    // ========== 分页条件 ==========
    
    /** 页码 */
    private Integer pageNum;

    /** 每页大小 */
    private Integer pageSize;

    // ========== 统计条件 ==========
    
    /** 是否包含统计信息 */
    private Boolean includeStatistics;

    /** 是否包含订单项信息 */
    private Boolean includeOrderItems;

    /** 是否包含关联信息 */
    private Boolean includeRelatedInfo;

    // ========== 权限过滤 ==========
    
    /** 当前用户ID（用于权限过滤） */
    private Long currentUserId;

    /** 当前用户部门ID（用于权限过滤） */
    private Long currentDeptId;

    /** 数据权限范围：ALL-全部,DEPT-本部门,SELF-本人 */
    private String dataScope;

    /** 下属用户ID列表（用于权限过滤） */
    private List<Long> subordinateUserIds;

    // ========== 快捷查询 ==========
    
    /** 我的订单 */
    private Boolean myOrders;

    /** 我部门的订单 */
    private Boolean deptOrders;

    /** 未分配的订单 */
    private Boolean unassignedOrders;

    /** 紧急订单 */
    private Boolean urgentOrders;

    /** 逾期订单 */
    private Boolean overdueOrders;

    /** 今日创建的订单 */
    private Boolean todayOrders;

    /** 本周创建的订单 */
    private Boolean thisWeekOrders;

    /** 本月创建的订单 */
    private Boolean thisMonthOrders;
}
