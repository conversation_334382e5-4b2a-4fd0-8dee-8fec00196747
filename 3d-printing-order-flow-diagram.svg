<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
      .step-box { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; rx: 8; }
      .table-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; rx: 6; }
      .decision-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 2; }
      .step-text { font-family: Arial, sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
      .table-text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; text-anchor: middle; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #4caf50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title" text-anchor="middle">3D打印订单状态流转图与数据库操作</text>
  
  <!-- 前端提交 -->
  <rect x="50" y="60" width="120" height="60" class="step-box"/>
  <text x="110" y="85" class="step-text">前端提交</text>
  <text x="110" y="100" class="step-text">3D打印订单</text>
  
  <!-- 后端接收数据包 -->
  <rect x="220" y="60" width="120" height="60" class="step-box"/>
  <text x="280" y="85" class="step-text">后端接收</text>
  <text x="280" y="100" class="step-text">数据包</text>
  
  <!-- 步骤1: 处理客户 -->
  <rect x="50" y="160" width="120" height="60" class="step-box"/>
  <text x="110" y="180" class="step-text">步骤1:</text>
  <text x="110" y="195" class="step-text">处理客户信息</text>
  
  <!-- 客户表操作 -->
  <rect x="220" y="140" width="100" height="100" class="table-box"/>
  <text x="270" y="160" class="table-text">crm_customer</text>
  <text x="270" y="175" class="table-text">操作:</text>
  <text x="270" y="190" class="table-text">1.查询客户</text>
  <text x="270" y="205" class="table-text">2.创建客户</text>
  <text x="270" y="220" class="table-text">(如不存在)</text>
  
  <!-- 步骤2: 处理联系人 -->
  <rect x="50" y="280" width="120" height="60" class="step-box"/>
  <text x="110" y="300" class="step-text">步骤2:</text>
  <text x="110" y="315" class="step-text">处理联系人信息</text>
  
  <!-- 联系人表操作 -->
  <rect x="220" y="260" width="100" height="100" class="table-box"/>
  <text x="270" y="280" class="table-text">crm_contact</text>
  <text x="270" y="295" class="table-text">操作:</text>
  <text x="270" y="310" class="table-text">1.查询联系人</text>
  <text x="270" y="325" class="table-text">2.创建联系人</text>
  <text x="270" y="340" class="table-text">(如不存在)</text>
  
  <!-- 步骤3: 创建商机 -->
  <rect x="50" y="400" width="120" height="60" class="step-box"/>
  <text x="110" y="420" class="step-text">步骤3:</text>
  <text x="110" y="435" class="step-text">创建商机</text>
  
  <!-- 商机表操作 -->
  <rect x="220" y="380" width="100" height="100" class="table-box"/>
  <text x="270" y="400" class="table-text">crm_opportunity</text>
  <text x="270" y="415" class="table-text">操作:</text>
  <text x="270" y="430" class="table-text">1.插入商机记录</text>
  <text x="270" y="445" class="table-text">阶段:proposal</text>
  <text x="270" y="460" class="table-text">来源:3D打印</text>
  
  <!-- 步骤4: 创建订单 -->
  <rect x="50" y="520" width="120" height="60" class="step-box"/>
  <text x="110" y="540" class="step-text">步骤4:</text>
  <text x="110" y="555" class="step-text">创建订单</text>
  
  <!-- 订单表操作 -->
  <rect x="220" y="500" width="100" height="100" class="table-box"/>
  <text x="270" y="520" class="table-text">crm_order</text>
  <text x="270" y="535" class="table-text">操作:</text>
  <text x="270" y="550" class="table-text">1.插入订单记录</text>
  <text x="270" y="565" class="table-text">2.关联客户ID</text>
  <text x="270" y="580" class="table-text">3.关联商机ID</text>
  
  <!-- 返回结果 -->
  <rect x="50" y="640" width="120" height="60" class="step-box"/>
  <text x="110" y="660" class="step-text">返回结果</text>
  <text x="110" y="675" class="step-text">给前端</text>
  
  <!-- 数据包详情 -->
  <rect x="400" y="60" width="350" height="540" class="step-box"/>
  <text x="575" y="85" class="step-text" style="font-size: 14px; font-weight: bold;">接收的数据包结构</text>
  
  <text x="420" y="110" class="table-text" style="text-anchor: start; font-weight: bold;">ThreeDPrintingOrderCreateDTO:</text>
  <text x="420" y="130" class="table-text" style="text-anchor: start;">├─ quoteNo: 询价单号</text>
  <text x="420" y="145" class="table-text" style="text-anchor: start;">├─ totalAmount: 订单总金额</text>
  <text x="420" y="160" class="table-text" style="text-anchor: start;">├─ customerInfo: 客户信息</text>
  <text x="420" y="175" class="table-text" style="text-anchor: start;">│   ├─ companyName: 公司名称</text>
  <text x="420" y="190" class="table-text" style="text-anchor: start;">│   ├─ contactName: 联系人姓名</text>
  <text x="420" y="205" class="table-text" style="text-anchor: start;">│   ├─ contactPhone: 联系电话</text>
  <text x="420" y="220" class="table-text" style="text-anchor: start;">│   ├─ contactEmail: 联系邮箱</text>
  <text x="420" y="235" class="table-text" style="text-anchor: start;">│   └─ address: 联系地址</text>
  <text x="420" y="255" class="table-text" style="text-anchor: start;">├─ items: 订单项目列表</text>
  <text x="420" y="270" class="table-text" style="text-anchor: start;">│   ├─ modelInfo: 模型信息</text>
  <text x="420" y="285" class="table-text" style="text-anchor: start;">│   │   ├─ name: 模型名称</text>
  <text x="420" y="300" class="table-text" style="text-anchor: start;">│   │   ├─ volume: 体积</text>
  <text x="420" y="315" class="table-text" style="text-anchor: start;">│   │   └─ surfaceArea: 表面积</text>
  <text x="420" y="330" class="table-text" style="text-anchor: start;">│   ├─ material: 材料类型</text>
  <text x="420" y="345" class="table-text" style="text-anchor: start;">│   ├─ quantity: 数量</text>
  <text x="420" y="360" class="table-text" style="text-anchor: start;">│   ├─ unitPrice: 单价</text>
  <text x="420" y="375" class="table-text" style="text-anchor: start;">│   └─ totalPrice: 总价</text>
  <text x="420" y="395" class="table-text" style="text-anchor: start;">├─ sprayOptions: 喷漆选项</text>
  <text x="420" y="410" class="table-text" style="text-anchor: start;">└─ insertOptions: 镶嵌选项</text>
  
  <text x="575" y="440" class="step-text" style="font-size: 14px; font-weight: bold;">返回的结果包结构</text>
  <text x="420" y="460" class="table-text" style="text-anchor: start; font-weight: bold;">ThreeDPrintingOrderResultVO:</text>
  <text x="420" y="480" class="table-text" style="text-anchor: start;">├─ orderId: 订单ID</text>
  <text x="420" y="495" class="table-text" style="text-anchor: start;">├─ orderNo: 订单号</text>
  <text x="420" y="510" class="table-text" style="text-anchor: start;">├─ customerId: 客户ID</text>
  <text x="420" y="525" class="table-text" style="text-anchor: start;">├─ isNewCustomer: 是否新客户</text>
  <text x="420" y="540" class="table-text" style="text-anchor: start;">├─ contactId: 联系人ID</text>
  <text x="420" y="555" class="table-text" style="text-anchor: start;">├─ isNewContact: 是否新联系人</text>
  <text x="420" y="570" class="table-text" style="text-anchor: start;">└─ opportunityId: 商机ID</text>
  
  <!-- 状态流转图 -->
  <rect x="800" y="60" width="350" height="540" class="step-box"/>
  <text x="975" y="85" class="step-text" style="font-size: 14px; font-weight: bold;">状态流转与表操作详情</text>
  
  <!-- 客户状态 -->
  <rect x="820" y="110" width="80" height="40" class="decision-box"/>
  <text x="860" y="130" class="table-text">客户存在?</text>
  
  <rect x="920" y="100" width="100" height="30" class="table-box"/>
  <text x="970" y="118" class="table-text">查询 crm_customer</text>
  
  <rect x="920" y="140" width="100" height="30" class="table-box"/>
  <text x="970" y="158" class="table-text">插入 crm_customer</text>
  
  <!-- 联系人状态 -->
  <rect x="820" y="190" width="80" height="40" class="decision-box"/>
  <text x="860" y="210" class="table-text">联系人存在?</text>
  
  <rect x="920" y="180" width="100" height="30" class="table-box"/>
  <text x="970" y="198" class="table-text">查询 crm_contact</text>
  
  <rect x="920" y="220" width="100" height="30" class="table-box"/>
  <text x="970" y="238" class="table-text">插入 crm_contact</text>
  
  <!-- 商机创建 -->
  <rect x="820" y="270" width="80" height="40" class="step-box"/>
  <text x="860" y="290" class="table-text">创建商机</text>
  
  <rect x="920" y="270" width="100" height="40" class="table-box"/>
  <text x="970" y="285" class="table-text">插入 crm_opportunity</text>
  <text x="970" y="300" class="table-text">阶段: proposal</text>
  
  <!-- 订单创建 -->
  <rect x="820" y="330" width="80" height="40" class="step-box"/>
  <text x="860" y="350" class="table-text">创建订单</text>
  
  <rect x="920" y="330" width="100" height="40" class="table-box"/>
  <text x="970" y="345" class="table-text">插入 crm_order</text>
  <text x="970" y="360" class="table-text">关联各ID</text>
  
  <!-- 日志记录 -->
  <rect x="820" y="390" width="200" height="80" class="step-box"/>
  <text x="920" y="410" class="table-text" style="font-weight: bold;">日志记录内容:</text>
  <text x="920" y="425" class="table-text">1. 完整数据包信息</text>
  <text x="920" y="440" class="table-text">2. 每步骤处理结果</text>
  <text x="920" y="455" class="table-text">3. 最终返回结果</text>
  
  <!-- 箭头连接 -->
  <line x1="170" y1="90" x2="220" y2="90" class="arrow"/>
  <line x1="110" y1="120" x2="110" y2="160" class="arrow"/>
  <line x1="170" y1="190" x2="220" y2="190" class="arrow"/>
  <line x1="110" y1="220" x2="110" y2="280" class="arrow"/>
  <line x1="170" y1="310" x2="220" y2="310" class="arrow"/>
  <line x1="110" y1="340" x2="110" y2="400" class="arrow"/>
  <line x1="170" y1="430" x2="220" y2="430" class="arrow"/>
  <line x1="110" y1="460" x2="110" y2="520" class="arrow"/>
  <line x1="170" y1="550" x2="220" y2="550" class="arrow"/>
  <line x1="110" y1="580" x2="110" y2="640" class="arrow"/>
  
  <!-- 数据流箭头 -->
  <line x1="340" y1="90" x2="400" y2="90" class="data-flow"/>
  <line x1="340" y1="670" x2="400" y2="500" class="data-flow"/>
  
  <!-- 状态流转箭头 -->
  <line x1="900" y1="130" x2="920" y2="115" class="arrow"/>
  <line x1="900" y1="130" x2="920" y2="155" class="arrow"/>
  <line x1="900" y1="210" x2="920" y2="195" class="arrow"/>
  <line x1="900" y1="210" x2="920" y2="235" class="arrow"/>
  <line x1="900" y1="290" x2="920" y2="290" class="arrow"/>
  <line x1="900" y1="350" x2="920" y2="350" class="arrow"/>
  
</svg>