package com.ruoyi.crm.controller;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.domain.entity.CrmVisitPlan;
import com.ruoyi.common.mapper.CrmCustomerMapper;
import com.ruoyi.common.mapper.CrmVisitPlanMapper;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmVisitPlanService;

/**
 * 拜访计划模块全面集成测试
 * 
 * 测试覆盖：
 * 1. CRUD基础功能测试
 * 2. 状态管理测试（延期、取消、完成、启动）
 * 3. 统计信息和查询测试
 * 4. 权限和业务规则验证测试
 * 5. 并发操作测试
 * 6. 性能压力测试
 * 7. 边界条件和异常场景测试
 */

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CrmVisitPlanControllerIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmVisitPlanService visitPlanService;

    @Autowired
    private CrmCustomerMapper customerMapper;

    @Autowired
    private CrmVisitPlanMapper visitPlanMapper;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    // 测试数据
    private CrmCustomer testCustomer;
    private CrmVisitPlan testPlan;
    private List<CrmVisitPlan> batchTestPlans;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
        
        // 初始化测试数据
        initTestData();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据（事务回滚会自动处理）
    }

    // ==================== CRUD基础功能测试 ====================

    @Test
    @Order(1)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:list"})
    @DisplayName("查询拜访计划列表 - 正常情况")
    void testGetVisitPlanList_Normal() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/list")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @Order(2)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:query"})
    @DisplayName("根据ID查询拜访计划详情")
    void testGetVisitPlanById() throws Exception {
        // 先创建一个拜访计划
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        mockMvc.perform(get("/crm/visitPlan/{id}", testPlan.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(testPlan.getId()));
    }

    @Test
    @Order(3)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:add"})
    @DisplayName("创建拜访计划 - 正常情况")
    void testCreateVisitPlan_Normal() throws Exception {
        CrmVisitPlan newPlan = createTestVisitPlan("新建拜访计划", testCustomer.getId());
        String requestBody = objectMapper.writeValueAsString(newPlan);

        mockMvc.perform(post("/crm/visitPlan")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));
    }

    @Test
    @Order(4)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:edit"})
    @DisplayName("更新拜访计划 - 正常情况")
    void testUpdateVisitPlan_Normal() throws Exception {
        // 先创建一个拜访计划
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        // 更新拜访计划
        testPlan.setVisitPlanName("更新后的拜访计划");
        testPlan.setVisitPurpose("更新后的拜访目的");
        String requestBody = objectMapper.writeValueAsString(testPlan);

        mockMvc.perform(put("/crm/visitPlan")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(5)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:remove"})
    @DisplayName("删除拜访计划 - 正常情况")
    void testDeleteVisitPlan_Normal() throws Exception {
        // 先创建一个拜访计划
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        mockMvc.perform(delete("/crm/visitPlan/{ids}", testPlan.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 状态管理测试 ====================

    @Test
    @Order(10)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:postpone"})
    @DisplayName("延期拜访计划 - 正常情况")
    void testPostponeVisitPlan_Normal() throws Exception {
        // 先创建一个拜访计划
        testPlan.setStatus("PENDING");
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        Map<String, Object> params = new HashMap<>();
        params.put("reason", "客户临时有事");
        params.put("remark", "延期一天");
        params.put("newVisitTime", "2024-12-31 10:00:00");

        String requestBody = objectMapper.writeValueAsString(params);

        mockMvc.perform(post("/crm/visitPlan/postpone/{id}", testPlan.getId())
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(11)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:cancel"})
    @DisplayName("取消拜访计划 - 正常情况")
    void testCancelVisitPlan_Normal() throws Exception {
        // 先创建一个拜访计划
        testPlan.setStatus("PENDING");
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        Map<String, Object> params = new HashMap<>();
        params.put("reason", "客户取消会议");
        params.put("remark", "客户通知取消");

        String requestBody = objectMapper.writeValueAsString(params);

        mockMvc.perform(post("/crm/visitPlan/cancel/{id}", testPlan.getId())
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(12)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:complete"})
    @DisplayName("完成拜访计划 - 正常情况")
    void testCompleteVisitPlan_Normal() throws Exception {
        // 先创建一个拜访计划并启动
        testPlan.setStatus("IN_PROGRESS");
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        Map<String, Object> params = new HashMap<>();
        params.put("followupContent", "拜访顺利完成，客户对产品表示满意");

        String requestBody = objectMapper.writeValueAsString(params);

        mockMvc.perform(post("/crm/visitPlan/complete/{id}", testPlan.getId())
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(13)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:edit"})
    @DisplayName("启动拜访计划 - 正常情况")
    void testStartVisitPlan_Normal() throws Exception {
        // 先创建一个待启动的拜访计划
        testPlan.setStatus("PENDING");
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        mockMvc.perform(post("/crm/visitPlan/start/{id}", testPlan.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 统计和查询功能测试 ====================

    @Test
    @Order(20)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:statistics"})
    @DisplayName("获取拜访计划统计信息")
    void testGetStatistics() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/statistics")
                .param("dateRange", "2024-01-01,2024-12-31")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isMap());
    }

    @Test
    @Order(21)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:statistics"}, roles = {"admin"})
    @DisplayName("获取全部统计信息（管理员权限）")
    void testGetAllStatistics() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/statistics/all")
                .param("dateRange", "2024-01-01,2024-12-31")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isMap());
    }

    @Test
    @Order(22)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:query"})
    @DisplayName("获取即将到期的拜访计划")
    void testGetUpcomingPlans() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/upcoming")
                .param("minutes", "60")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @Order(23)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:query"})
    @DisplayName("根据关联对象查询拜访计划")
    void testListByObject() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/listByObject")
                .param("objectType", "customer")
                .param("objectId", testCustomer.getId().toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }

    // ==================== 权限验证测试 ====================

    @Test
    @Order(30)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:query"})
    @DisplayName("权限测试 - 检查编辑权限")
    void testCanEdit() throws Exception {
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        mockMvc.perform(get("/crm/visitPlan/canEdit/{id}", testPlan.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isBoolean());
    }

    @Test
    @Order(31)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:query"})
    @DisplayName("权限测试 - 检查删除权限")
    void testCanDelete() throws Exception {
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        mockMvc.perform(get("/crm/visitPlan/canDelete/{id}", testPlan.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isBoolean());
    }

    @Test
    @Order(32)
    @DisplayName("权限测试 - 未登录用户访问")
    void testUnauthorized_NoLogin() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @Order(33)
    @WithMockUser(username = "user", authorities = {})
    @DisplayName("权限测试 - 无权限用户")
    void testForbidden_NoPermission() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    // ==================== 数据验证和边界条件测试 ====================

    @Test
    @Order(40)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:add"})
    @DisplayName("数据验证 - 必填字段缺失")
    void testCreateVisitPlan_MissingRequiredFields() throws Exception {
        CrmVisitPlan invalidPlan = new CrmVisitPlan();
        // 不设置必填字段
        String requestBody = objectMapper.writeValueAsString(invalidPlan);

        mockMvc.perform(post("/crm/visitPlan")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @Order(41)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:postpone"})
    @DisplayName("延期验证 - 缺少延期原因")
    void testPostponeVisitPlan_MissingReason() throws Exception {
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        Map<String, Object> params = new HashMap<>();
        params.put("newVisitTime", "2024-12-31 10:00:00");
        // 缺少 reason

        String requestBody = objectMapper.writeValueAsString(params);

        mockMvc.perform(post("/crm/visitPlan/postpone/{id}", testPlan.getId())
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value(containsString("延期原因不能为空")));
    }

    @Test
    @Order(42)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:cancel"})
    @DisplayName("取消验证 - 缺少取消原因")
    void testCancelVisitPlan_MissingReason() throws Exception {
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        Map<String, Object> params = new HashMap<>();
        // 缺少 reason

        String requestBody = objectMapper.writeValueAsString(params);

        mockMvc.perform(post("/crm/visitPlan/cancel/{id}", testPlan.getId())
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value(containsString("取消原因不能为空")));
    }

    @Test
    @Order(43)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:complete"})
    @DisplayName("完成验证 - 缺少跟进内容")
    void testCompleteVisitPlan_MissingContent() throws Exception {
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        Map<String, Object> params = new HashMap<>();
        // 缺少 followupContent

        String requestBody = objectMapper.writeValueAsString(params);

        mockMvc.perform(post("/crm/visitPlan/complete/{id}", testPlan.getId())
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value(containsString("跟进记录内容不能为空")));
    }

    @Test
    @Order(44)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:list"})
    @DisplayName("分页边界测试 - 超大页码")
    void testPagination_LargePage() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/list")
                .param("pageNum", "999999")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isEmpty());
    }

    @Test
    @Order(45)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:add"})
    @DisplayName("边界测试 - 超长字段内容")
    void testCreateVisitPlan_LongContent() throws Exception {
        CrmVisitPlan plan = createTestVisitPlan("测试拜访计划", testCustomer.getId());
        plan.setVisitPurpose("a".repeat(1000)); // 超长拜访目的
        plan.setRemark("b".repeat(2000)); // 超长备注

        String requestBody = objectMapper.writeValueAsString(plan);

        mockMvc.perform(post("/crm/visitPlan")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                // 根据实际业务逻辑验证是否应该成功或失败
    }

    // ==================== 异常场景测试 ====================

    @Test
    @Order(50)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:query"})
    @DisplayName("异常测试 - 查询不存在的拜访计划")
    void testGetVisitPlan_NotExists() throws Exception {
        mockMvc.perform(get("/crm/visitPlan/{id}", 99999L)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").doesNotExist());
    }

    @Test
    @Order(51)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:edit"})
    @DisplayName("异常测试 - 更新不存在的拜访计划")
    void testUpdateVisitPlan_NotExists() throws Exception {
        CrmVisitPlan nonExistentPlan = createTestVisitPlan("不存在的计划", testCustomer.getId());
        nonExistentPlan.setId(99999L);

        String requestBody = objectMapper.writeValueAsString(nonExistentPlan);

        mockMvc.perform(put("/crm/visitPlan")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @Order(52)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:postpone"})
    @DisplayName("异常测试 - 延期无效的时间格式")
    void testPostponeVisitPlan_InvalidDateFormat() throws Exception {
        visitPlanMapper.insertCrmVisitPlan(testPlan);

        Map<String, Object> params = new HashMap<>();
        params.put("reason", "测试延期");
        params.put("newVisitTime", "invalid-date-format");

        String requestBody = objectMapper.writeValueAsString(params);

        mockMvc.perform(post("/crm/visitPlan/postpone/{id}", testPlan.getId())
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value(containsString("延期失败")));
    }

    @Test
    @Order(53)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:add"})
    @DisplayName("异常测试 - 无效JSON格式")
    void testCreateVisitPlan_InvalidJson() throws Exception {
        String invalidJson = "{invalid json format}";

        mockMvc.perform(post("/crm/visitPlan")
                .content(invalidJson)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    // ==================== 并发操作测试 ====================

    @Test
    @Order(60)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:add"})
    @DisplayName("并发测试 - 同时创建多个拜访计划")
    void testConcurrentCreatePlans() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < 20; i++) {
            final int index = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    CrmVisitPlan plan = createTestVisitPlan("并发测试计划 " + index, testCustomer.getId());
                    String requestBody = objectMapper.writeValueAsString(plan);

                    mockMvc.perform(post("/crm/visitPlan")
                            .content(requestBody)
                            .contentType(MediaType.APPLICATION_JSON))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.code").value(200));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有操作完成
        assertDoesNotThrow(() -> 
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join()
        );
        executor.shutdown();
    }

    @Test
    @Order(61)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:edit"})
    @DisplayName("并发测试 - 同时更新同一个拜访计划")
    void testConcurrentUpdateSamePlan() throws Exception {
        // 先创建一个拜访计划
        visitPlanMapper.insertCrmVisitPlan(testPlan);
        
        ExecutorService executor = Executors.newFixedThreadPool(5);
        List<CompletableFuture<String>> futures = new ArrayList<>();

        for (int i = 0; i < 10; i++) {
            final int index = i;
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    CrmVisitPlan updatePlan = new CrmVisitPlan();
                    updatePlan.setId(testPlan.getId());
                    updatePlan.setCustomerId(testPlan.getCustomerId());
                    updatePlan.setVisitPlanName("并发更新测试 " + index);
                    updatePlan.setVisitPurpose("并发更新目的 " + index);
                    updatePlan.setVisitTime(new Date());
                    
                    String requestBody = objectMapper.writeValueAsString(updatePlan);

                    return mockMvc.perform(put("/crm/visitPlan")
                            .content(requestBody)
                            .contentType(MediaType.APPLICATION_JSON))
                            .andReturn()
                            .getResponse()
                            .getContentAsString();
                } catch (Exception e) {
                    return "error: " + e.getMessage();
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有操作完成并验证结果
        List<String> results = new ArrayList<>();
        futures.forEach(future -> {
            try {
                results.add(future.get());
            } catch (Exception e) {
                results.add("exception: " + e.getMessage());
            }
        });

        // 验证至少有部分操作成功
        long successCount = results.stream()
                .filter(result -> result.contains("\"code\":200"))
                .count();
        
        assertTrue(successCount > 0, "并发更新应该至少有部分成功");
        executor.shutdown();
    }

    // ==================== 性能压力测试 ====================

    @Test
    @Order(70)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:list"})
    @DisplayName("性能测试 - 大量数据查询")
    void testPerformance_LargeDataQuery() throws Exception {
        // 创建大量测试数据
        createManyTestPlans(1000);

        long startTime = System.currentTimeMillis();

        mockMvc.perform(get("/crm/visitPlan/list")
                .param("pageNum", "1")
                .param("pageSize", "100")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 3000, "大数据量查询应该在3秒内完成，实际用时: " + duration + "ms");
    }

    @Test
    @Order(71)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:statistics"})
    @DisplayName("性能测试 - 统计信息查询")
    void testPerformance_StatisticsQuery() throws Exception {
        long startTime = System.currentTimeMillis();

        mockMvc.perform(get("/crm/visitPlan/statistics")
                .param("dateRange", "2024-01-01,2024-12-31")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 2000, "统计查询应该在2秒内完成，实际用时: " + duration + "ms");
    }

    @Test
    @Order(72)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:list"})
    @DisplayName("压力测试 - 高并发查询")
    void testStress_HighConcurrentQueries() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(20);
        List<CompletableFuture<Long>> futures = new ArrayList<>();

        for (int i = 0; i < 100; i++) {
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                try {
                    long start = System.currentTimeMillis();
                    mockMvc.perform(get("/crm/visitPlan/list")
                            .param("pageNum", "1")
                            .param("pageSize", "10")
                            .contentType(MediaType.APPLICATION_JSON))
                            .andExpect(status().isOk());
                    return System.currentTimeMillis() - start;
                } catch (Exception e) {
                    return -1L;
                }
            }, executor);
            futures.add(future);
        }

        List<Long> durations = new ArrayList<>();
        futures.forEach(future -> {
            try {
                durations.add(future.get());
            } catch (Exception e) {
                durations.add(-1L);
            }
        });

        // 计算成功率和平均响应时间
        long successCount = durations.stream().filter(d -> d > 0).count();
        double successRate = (double) successCount / durations.size() * 100;
        double avgDuration = durations.stream().filter(d -> d > 0).mapToLong(Long::longValue).average().orElse(0);

        assertTrue(successRate >= 95, "高并发查询成功率应该 >= 95%，实际: " + successRate + "%");
        assertTrue(avgDuration < 1000, "高并发查询平均响应时间应该 < 1秒，实际: " + avgDuration + "ms");
        
        executor.shutdown();
    }

    // ==================== 管理员功能测试 ====================

    @Test
    @Order(80)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:edit"}, roles = {"admin"})
    @DisplayName("管理员功能 - 手动处理提醒")
    void testProcessReminders() throws Exception {
        mockMvc.perform(post("/crm/visitPlan/processReminders")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value(containsString("成功处理")));
    }

    @Test
    @Order(81)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:edit"}, roles = {"admin"})
    @DisplayName("管理员功能 - 自动更新状态")
    void testAutoUpdateStatus() throws Exception {
        mockMvc.perform(post("/crm/visitPlan/autoUpdateStatus")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value(containsString("成功更新")));
    }

    // ==================== 数据一致性测试 ====================

    @Test
    @Order(90)
    @WithMockUser(username = "admin", authorities = {"crm:visitPlan:add", "crm:visitPlan:query"})
    @DisplayName("数据一致性测试 - 创建后立即查询")
    void testDataConsistency_CreateAndQuery() throws Exception {
        CrmVisitPlan newPlan = createTestVisitPlan("一致性测试计划", testCustomer.getId());
        String requestBody = objectMapper.writeValueAsString(newPlan);

        // 创建拜访计划
        String createResponse = mockMvc.perform(post("/crm/visitPlan")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn()
                .getResponse()
                .getContentAsString();

        // 立即查询验证数据一致性
        mockMvc.perform(get("/crm/visitPlan/list")
                .param("visitPlanName", "一致性测试计划")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows[?(@.visitPlanName == '一致性测试计划')]").exists());
    }

    // ==================== 辅助方法 ====================

    private void initTestData() {
        // 创建测试客户
        testCustomer = createTestCustomer("拜访测试客户", "13900139002", "<EMAIL>");
        customerMapper.insertCrmCustomer(testCustomer);

        // 创建测试拜访计划
        testPlan = createTestVisitPlan("测试拜访计划", testCustomer.getId());
        
        // 创建批量测试数据
        batchTestPlans = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            CrmVisitPlan plan = createTestVisitPlan("批量测试计划 " + i, testCustomer.getId());
            batchTestPlans.add(plan);
        }
    }

    private CrmCustomer createTestCustomer(String name, String mobile, String email) {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerName(name);
        customer.setMobile(mobile);
        customer.setEmail(email);
        customer.setResponsiblePersonId("1");
        customer.setCustomerIndustry("测试行业");
        customer.setCustomerLevel("A级");
        customer.setCustomerSource("测试来源");
        customer.setStatus("1");
        customer.setDelFlag("0");
        customer.setCreateTime(new Date());
        customer.setUpdateTime(new Date());
        return customer;
    }

    private CrmVisitPlan createTestVisitPlan(String planName, Long customerId) {
        CrmVisitPlan plan = new CrmVisitPlan();
        plan.setVisitPlanName(planName);
        plan.setCustomerId(customerId);
        plan.setCustomerName(testCustomer.getCustomerName());
        plan.setVisitTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000)); // 明天
        plan.setVisitPurpose("测试拜访目的");
        plan.setRemindTime(30); // 提前30分钟提醒
        plan.setRemark("测试备注");
        plan.setOwnerId(1L);
        plan.setOwnerName("测试用户");
        plan.setDeptId(100L);
        plan.setDeptName("测试部门");
        plan.setStatus("PENDING");
        plan.setDelFlag("0");
        plan.setCreateTime(new Date());
        plan.setUpdateTime(new Date());
        return plan;
    }

    private void createManyTestPlans(int count) {
        for (int i = 0; i < count; i++) {
            CrmVisitPlan plan = createTestVisitPlan("大量数据测试计划 " + i, testCustomer.getId());
            plan.setStatus(i % 3 == 0 ? "PENDING" : (i % 3 == 1 ? "IN_PROGRESS" : "COMPLETED"));
            plan.setVisitTime(new Date(System.currentTimeMillis() + i * 60 * 60 * 1000)); // 不同时间
            visitPlanMapper.insertCrmVisitPlan(plan);
        }
    }
}