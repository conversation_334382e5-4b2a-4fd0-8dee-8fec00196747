package com.ruoyi.common.mapper;

import com.ruoyi.common.domain.entity.CrmPoolOperationLog;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 客户公海操作日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface CrmPoolOperationLogMapper {
    
    /**
     * 新增操作日志
     * 
     * @param log 操作日志
     * @return 结果
     */
    int insert(CrmPoolOperationLog log);
    
    /**
     * 查询操作日志列表
     * 
     * @param log 查询条件
     * @return 操作日志列表
     */
    List<CrmPoolOperationLog> selectList(CrmPoolOperationLog log);
    
    /**
     * 根据ID查询操作日志
     * 
     * @param id 主键ID
     * @return 操作日志
     */
    CrmPoolOperationLog selectById(@Param("id") Long id);
    
}