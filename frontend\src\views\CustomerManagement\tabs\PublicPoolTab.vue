<template>
    <div class="public-pool-tab">
        <div class="tab-header">
            <h3>公海管理</h3>
            <div class="header-actions">
                <el-button 
                    type="primary" 
                    size="small"
                    @click="handleBatchClaimFromPool"
                    :disabled="selectedPoolCustomers.length === 0"
                >
                    <el-icon><Download /></el-icon>
                    批量认领
                </el-button>
                <el-button 
                    type="warning" 
                    size="small"
                    @click="handleBatchReturnToPool"
                    :disabled="selectedOwnCustomers.length === 0"
                >
                    <el-icon><Upload /></el-icon>
                    批量放回公海
                </el-button>
            </div>
        </div>

        <el-tabs v-model="activeSubTab" class="pool-tabs">
            <el-tab-pane label="公海客户" name="pool">
                <common-filter
                    v-model:searchValue="poolSearchInput"
                    v-model:filterValue="poolFilterType"
                    :config="poolFilterConfig"
                    @search="handlePoolSearch"
                    @filter="handlePoolFilterChange"
                />

                <el-table 
                    :data="poolCustomers" 
                    v-loading="poolLoading"
                    border 
                    sortable 
                    tooltip-effect="dark"
                    :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                    style="width: 100%; margin-top: 16px; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
                    @selection-change="handlePoolSelectionChange">
                    
                    <el-table-column type="selection" width="55" />
                    
                    <el-table-column prop="customerName" label="客户名称" min-width="150">
                        <template #default="scope">
                            <el-button link type="primary" @click="viewCustomerDetail(scope.row)">
                                {{ scope.row.customerName }}
                            </el-button>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="mobile" label="手机号" min-width="130" />
                    <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
                    <el-table-column prop="customerIndustry" label="所属行业" min-width="120" />
                    <el-table-column prop="customerLevel" label="客户级别" min-width="120" />
                    <el-table-column prop="daysInPool" label="在公海天数" min-width="100" />
                    <el-table-column prop="lastOwner" label="上一负责人" min-width="120" />
                    <el-table-column prop="returnReason" label="放回原因" min-width="150" show-overflow-tooltip />
                    
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="scope">
                            <el-button 
                                type="primary" 
                                link 
                                size="small" 
                                @click="handleClaimCustomer(scope.row)"
                            >
                                <el-icon><Download /></el-icon>
                                认领
                            </el-button>
                            <el-button 
                                type="info" 
                                link 
                                size="small" 
                                @click="viewCustomerDetail(scope.row)"
                            >
                                <el-icon><View /></el-icon>
                                查看
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <pagination 
                    v-show="totalPoolCustomers > 0" 
                    :total="totalPoolCustomers" 
                    :page.sync="poolQueryParams.pageNum"
                    :limit.sync="poolQueryParams.pageSize"
                    @pagination="handlePoolPagination" 
                />
            </el-tab-pane>

            <el-tab-pane label="我的客户" name="own">
                <common-filter
                    v-model:searchValue="ownSearchInput"
                    v-model:filterValue="ownFilterType"
                    :config="ownFilterConfig"
                    @search="handleOwnSearch"
                    @filter="handleOwnFilterChange"
                />

                <el-table 
                    :data="ownCustomers" 
                    v-loading="ownLoading"
                    border 
                    sortable 
                    tooltip-effect="dark"
                    :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                    style="width: 100%; margin-top: 16px; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
                    @selection-change="handleOwnSelectionChange">
                    
                    <el-table-column type="selection" width="55" />
                    
                    <el-table-column prop="customerName" label="客户名称" min-width="150">
                        <template #default="scope">
                            <el-button link type="primary" @click="viewCustomerDetail(scope.row)">
                                {{ scope.row.customerName }}
                            </el-button>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="mobile" label="手机号" min-width="130" />
                    <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
                    <el-table-column prop="customerIndustry" label="所属行业" min-width="120" />
                    <el-table-column prop="customerLevel" label="客户级别" min-width="120" />
                    <el-table-column prop="claimTime" label="认领时间" min-width="150" />
                    <el-table-column prop="lastFollowupTime" label="最后跟进时间" min-width="150" />
                    
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="scope">
                            <el-button 
                                type="warning" 
                                link 
                                size="small" 
                                @click="handleReturnToPool(scope.row)"
                            >
                                <el-icon><Upload /></el-icon>
                                放回公海
                            </el-button>
                            <el-button 
                                type="info" 
                                link 
                                size="small" 
                                @click="viewCustomerDetail(scope.row)"
                            >
                                <el-icon><View /></el-icon>
                                查看
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <pagination 
                    v-show="totalOwnCustomers > 0" 
                    :total="totalOwnCustomers" 
                    :page.sync="ownQueryParams.pageNum"
                    :limit.sync="ownQueryParams.pageSize"
                    @pagination="handleOwnPagination" 
                />
            </el-tab-pane>
        </el-tabs>

        <!-- 放回公海原因对话框 -->
        <el-dialog v-model="returnReasonDialogVisible" title="放回公海" width="500px">
            <el-form :model="returnForm" :rules="returnRules" ref="returnFormRef" label-width="100px">
                <el-form-item label="放回原因" prop="reason">
                    <el-select v-model="returnForm.reason" placeholder="请选择放回原因" style="width: 100%">
                        <el-option label="客户无效" value="invalid" />
                        <el-option label="无法联系" value="no_contact" />
                        <el-option label="暂无需求" value="no_demand" />
                        <el-option label="合作终止" value="cooperation_end" />
                        <el-option label="其他" value="other" />
                    </el-select>
                </el-form-item>
                
                <el-form-item label="详细说明" prop="description">
                    <el-input 
                        v-model="returnForm.description" 
                        type="textarea" 
                        :rows="4" 
                        placeholder="请输入详细说明"
                    />
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="returnReasonDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirmReturn" :loading="returnLoading">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
    Download, 
    Upload, 
    View 
} from '@element-plus/icons-vue';

import CommonFilter from '@/components/CommonFilter/index.vue';
import Pagination from '@/components/Pagination/index.vue';

import type { CustomerData } from '@/api/crm/customer/types';
import request from '@/utils/request';

// 状态管理
const poolLoading = ref(false);
const ownLoading = ref(false);
const returnLoading = ref(false);

// 数据
const poolCustomers = ref<CustomerData[]>([]);
const ownCustomers = ref<CustomerData[]>([]);
const selectedPoolCustomers = ref<CustomerData[]>([]);
const selectedOwnCustomers = ref<CustomerData[]>([]);

// 统计
const totalPoolCustomers = ref(0);
const totalOwnCustomers = ref(0);

// 当前选中的子标签页
const activeSubTab = ref('pool');

// 搜索和筛选
const poolSearchInput = ref('');
const poolFilterType = ref('all');
const ownSearchInput = ref('');
const ownFilterType = ref('all');

// 查询参数
const poolQueryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    customerName: '',
    mobile: '',
    customerIndustry: '',
    customerLevel: ''
});

const ownQueryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    customerName: '',
    mobile: '',
    customerIndustry: '',
    customerLevel: ''
});

// 放回公海表单
const returnReasonDialogVisible = ref(false);
const returnForm = ref({
    customerId: 0,
    reason: '',
    description: ''
});

const returnRules = {
    reason: [{ required: true, message: '请选择放回原因', trigger: 'change' }]
};

// 筛选配置
const poolFilterConfig = {
    search: {
        placeholder: '搜索公海客户名称、手机号',
        width: '240px',
        icon: 'Search',
        debounceTime: 300
    },
    filter: {
        label: '客户级别：',
        options: [
            { label: '全部', value: 'all' },
            { label: 'A级客户', value: 'A级' },
            { label: 'B级客户', value: 'B级' },
            { label: 'C级客户', value: 'C级' },
            { label: 'D级客户', value: 'D级' }
        ],
        buttonStyle: true,
        size: 'default'
    }
};

const ownFilterConfig = {
    search: {
        placeholder: '搜索我的客户名称、手机号',
        width: '240px',
        icon: 'Search',
        debounceTime: 300
    },
    filter: {
        label: '客户级别：',
        options: [
            { label: '全部', value: 'all' },
            { label: 'A级客户', value: 'A级' },
            { label: 'B级客户', value: 'B级' },
            { label: 'C级客户', value: 'C级' },
            { label: 'D级客户', value: 'D级' }
        ],
        buttonStyle: true,
        size: 'default'
    }
};

// 生命周期
onMounted(() => {
    loadPoolCustomers();
    loadOwnCustomers();
});

// 方法
const loadPoolCustomers = async () => {
    poolLoading.value = true;
    try {
        const response = await getPoolCustomers(poolQueryParams);
        if (response.code === 200) {
            poolCustomers.value = response.rows || [];
            totalPoolCustomers.value = response.total || 0;
            
            // 转换字段名
            poolCustomers.value = poolCustomers.value.map(item => ({
                ...item,
                lastOwner: item.previousOwnerName,
                returnReason: getReturnReasonText(item.returnReason)
            }));
        }
    } catch (error) {
        ElMessage.error('加载公海客户失败');
    } finally {
        poolLoading.value = false;
    }
};

const loadOwnCustomers = async () => {
    ownLoading.value = true;
    try {
        const response = await getOwnCustomers(ownQueryParams);
        if (response.code === 200) {
            ownCustomers.value = response.rows || [];
            totalOwnCustomers.value = response.total || 0;
        }
    } catch (error) {
        ElMessage.error('加载我的客户失败');
    } finally {
        ownLoading.value = false;
    }
};

const handlePoolSelectionChange = (selection: CustomerData[]) => {
    selectedPoolCustomers.value = selection;
};

const handleOwnSelectionChange = (selection: CustomerData[]) => {
    selectedOwnCustomers.value = selection;
};

const handleClaimCustomer = async (customer: CustomerData) => {
    try {
        await ElMessageBox.confirm(`确认认领客户"${customer.customerName}"吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        });
        
        const response = await claimCustomerApi(customer.id!);
        if (response.code === 200) {
            ElMessage.success('认领成功');
            loadPoolCustomers();
            loadOwnCustomers();
        } else {
            ElMessage.error(response.msg || '认领失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('认领失败');
        }
    }
};

const handleBatchClaimFromPool = async () => {
    if (selectedPoolCustomers.value.length === 0) {
        ElMessage.warning('请选择要认领的客户');
        return;
    }
    
    try {
        await ElMessageBox.confirm(`确认认领选中的 ${selectedPoolCustomers.value.length} 个客户吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        });
        
        const customerIds = selectedPoolCustomers.value.map(c => c.id!);
        const response = await batchClaimCustomersApi(customerIds);
        if (response.code === 200) {
            ElMessage.success(response.msg || '批量认领成功');
            loadPoolCustomers();
            loadOwnCustomers();
        } else {
            ElMessage.error(response.msg || '批量认领失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('批量认领失败');
        }
    }
};

const handleReturnToPool = (customer: CustomerData) => {
    returnForm.value = {
        customerId: customer.id!,
        reason: '',
        description: ''
    };
    returnReasonDialogVisible.value = true;
};

const handleBatchReturnToPool = () => {
    if (selectedOwnCustomers.value.length === 0) {
        ElMessage.warning('请选择要放回公海的客户');
        return;
    }
    
    returnForm.value = {
        customerId: 0, // 批量操作
        reason: '',
        description: ''
    };
    returnReasonDialogVisible.value = true;
};

const handleConfirmReturn = async () => {
    returnLoading.value = true;
    try {
        if (returnForm.value.customerId === 0) {
            // 批量操作
            const customerIds = selectedOwnCustomers.value.map(c => c.id!);
            const response = await batchReturnToPoolApi(
                customerIds,
                returnForm.value.reason,
                returnForm.value.description
            );
            if (response.code === 200) {
                ElMessage.success(response.msg || '批量放回公海成功');
                returnReasonDialogVisible.value = false;
                loadPoolCustomers();
                loadOwnCustomers();
            } else {
                ElMessage.error(response.msg || '放回公海失败');
            }
        } else {
            // 单个操作
            const response = await returnToPoolApi(
                returnForm.value.customerId,
                returnForm.value.reason,
                returnForm.value.description
            );
            if (response.code === 200) {
                ElMessage.success('放回公海成功');
                returnReasonDialogVisible.value = false;
                loadPoolCustomers();
                loadOwnCustomers();
            } else {
                ElMessage.error(response.msg || '放回公海失败');
            }
        }
    } catch (error) {
        ElMessage.error('放回公海失败');
    } finally {
        returnLoading.value = false;
    }
};

const viewCustomerDetail = (customer: CustomerData) => {
    // TODO: 打开客户详情
    ElMessage.info('查看客户详情功能开发中');
};

const handlePoolSearch = (value: string) => {
    poolQueryParams.customerName = value;
    poolQueryParams.pageNum = 1;
    loadPoolCustomers();
};

const handlePoolFilterChange = (value: string) => {
    poolQueryParams.customerLevel = value === 'all' ? '' : value;
    poolQueryParams.pageNum = 1;
    loadPoolCustomers();
};

const handleOwnSearch = (value: string) => {
    ownQueryParams.customerName = value;
    ownQueryParams.pageNum = 1;
    loadOwnCustomers();
};

const handleOwnFilterChange = (value: string) => {
    ownQueryParams.customerLevel = value === 'all' ? '' : value;
    ownQueryParams.pageNum = 1;
    loadOwnCustomers();
};

const handlePoolPagination = (val: { page: number; limit: number }) => {
    poolQueryParams.pageNum = val.page;
    poolQueryParams.pageSize = val.limit;
    loadPoolCustomers();
};

const handleOwnPagination = (val: { page: number; limit: number }) => {
    ownQueryParams.pageNum = val.page;
    ownQueryParams.pageSize = val.limit;
    loadOwnCustomers();
};

// API 函数
const getPoolCustomers = (params: any) => {
    return request({
        url: '/crm/customer/pool',
        method: 'get',
        params
    });
};

const getOwnCustomers = (params: any) => {
    return request({
        url: '/crm/customer/own',
        method: 'get',
        params
    });
};

const claimCustomerApi = (customerId: number) => {
    return request({
        url: `/crm/customer/${customerId}/claim`,
        method: 'post'
    });
};

const batchClaimCustomersApi = (customerIds: number[]) => {
    return request({
        url: '/crm/customer/batch/claim',
        method: 'post',
        data: customerIds
    });
};

const returnToPoolApi = (customerId: number, reason: string, remark: string) => {
    return request({
        url: `/crm/customer/${customerId}/return`,
        method: 'post',
        params: { reason, remark }
    });
};

const batchReturnToPoolApi = (customerIds: number[], reason: string, remark: string) => {
    return request({
        url: '/crm/customer/batch/return',
        method: 'post',
        data: { customerIds, reason, remark }
    });
};

// 辅助函数
const getReturnReasonText = (reason: string) => {
    const reasonMap: Record<string, string> = {
        'NO_FOLLOW': '未跟进',
        'NO_DEAL': '未成交',
        'MANUAL': '手动放入',
        'invalid': '客户无效',
        'no_contact': '无法联系',
        'no_demand': '暂无需求',
        'cooperation_end': '合作终止',
        'other': '其他'
    };
    return reasonMap[reason] || reason;
};
</script>

<style scoped>
.public-pool-tab {
    padding: 20px;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tab-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #303133;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.pool-tabs {
    margin-top: 20px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>