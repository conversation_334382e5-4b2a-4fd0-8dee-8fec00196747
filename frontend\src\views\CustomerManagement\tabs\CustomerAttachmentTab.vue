<template>
    <div class="customer-attachment-tab">
        <!-- 文件上传区域 -->
        <div class="upload-section">
            <el-card class="upload-card">
                <template #header>
                    <div class="upload-header">
                        <el-icon class="header-icon"><UploadFilled /></el-icon>
                        <span>文件上传</span>
                    </div>
                </template>
                
                <el-upload
                    class="upload-area"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :data="uploadData"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="beforeUpload"
                    :show-file-list="false"
                    drag
                    multiple
                >
                    <div class="upload-content">
                        <el-icon class="upload-icon"><UploadFilled /></el-icon>
                        <div class="upload-text">
                            <p>将文件拖到此处，或<em>点击上传</em></p>
                            <p class="upload-hint">支持多文件上传，单个文件大小不超过10MB</p>
                        </div>
                    </div>
                </el-upload>
            </el-card>
        </div>

        <!-- 文件分类和筛选 -->
        <div class="filter-section">
            <div class="filter-controls">
                <el-select 
                    v-model="filterType" 
                    placeholder="文件类型" 
                    clearable 
                    size="small"
                    style="width: 150px;"
                    @change="handleFilterChange"
                >
                    <el-option label="全部" value="" />
                    <el-option label="图片" value="image" />
                    <el-option label="文档" value="document" />
                    <el-option label="表格" value="spreadsheet" />
                    <el-option label="其他" value="other" />
                </el-select>
                
                <el-input
                    v-model="searchKeyword"
                    placeholder="搜索文件名"
                    size="small"
                    style="width: 200px;"
                    clearable
                    @input="handleSearch"
                >
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>

                <div class="view-controls">
                    <el-button-group size="small">
                        <el-button 
                            :type="viewMode === 'grid' ? 'primary' : 'default'"
                            @click="viewMode = 'grid'"
                        >
                            <el-icon><Grid /></el-icon>
                        </el-button>
                        <el-button 
                            :type="viewMode === 'list' ? 'primary' : 'default'"
                            @click="viewMode = 'list'"
                        >
                            <el-icon><List /></el-icon>
                        </el-button>
                    </el-button-group>
                </div>
            </div>

            <div class="file-stats">
                <el-tag size="small" type="info">
                    共 {{ filteredAttachments.length }} 个文件
                </el-tag>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="attachments-section" v-loading="loading">
            <div v-if="filteredAttachments.length === 0" class="empty-state">
                <el-empty description="暂无附件" />
            </div>

            <!-- 网格视图 -->
            <div v-if="viewMode === 'grid'" class="attachments-grid">
                <div 
                    v-for="attachment in filteredAttachments" 
                    :key="attachment.id"
                    class="attachment-card"
                >
                    <div class="file-preview" @click="handlePreview(attachment)">
                        <div class="file-icon">
                            <el-icon>
                                <component :is="getFileIcon(attachment.fileExtension)" />
                            </el-icon>
                        </div>
                        <img 
                            v-if="isImageFile(attachment.fileExtension)" 
                            :src="attachment.fileUrl" 
                            :alt="attachment.fileName"
                            class="file-thumbnail"
                        />
                    </div>
                    
                    <div class="file-info">
                        <div class="file-name" :title="attachment.fileName">
                            {{ attachment.fileName }}
                        </div>
                        <div class="file-meta">
                            <span class="file-size">{{ formatFileSize(attachment.fileSize) }}</span>
                            <span class="file-time">{{ attachment.createTime }}</span>
                        </div>
                    </div>

                    <div class="file-actions">
                        <el-button size="small" text @click="handleDownload(attachment)">
                            <el-icon><Download /></el-icon>
                        </el-button>
                        <el-button size="small" text type="danger" @click="handleDelete(attachment)">
                            <el-icon><Delete /></el-icon>
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 列表视图 -->
            <div v-if="viewMode === 'list'" class="attachments-list">
                <el-table :data="filteredAttachments" stripe>
                    <el-table-column width="60">
                        <template #default="{ row }">
                            <div class="file-icon-cell">
                                <el-icon>
                                    <component :is="getFileIcon(row.fileExtension)" />
                                </el-icon>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="fileName" label="文件名" min-width="200">
                        <template #default="{ row }">
                            <div class="file-name-cell" @click="handlePreview(row)">
                                {{ row.fileName }}
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="fileSize" label="大小" width="100">
                        <template #default="{ row }">
                            {{ formatFileSize(row.fileSize) }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="createTime" label="上传时间" width="160" />
                    
                    <el-table-column prop="createBy" label="上传者" width="120" />
                    
                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
                            <el-button size="small" text @click="handleDownload(row)">
                                下载
                            </el-button>
                            <el-button size="small" text type="danger" @click="handleDelete(row)">
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <!-- 文件预览对话框 -->
        <el-dialog 
            v-model="previewVisible" 
            :title="previewFile?.fileName"
            width="80%"
            :before-close="closePreview"
        >
            <div class="preview-content">
                <div v-if="isImageFile(previewFile?.fileExtension)" class="image-preview">
                    <img :src="previewFile?.fileUrl" :alt="previewFile?.fileName" />
                </div>
                <div v-else class="file-preview-placeholder">
                    <el-icon class="preview-icon">
                        <component :is="getFileIcon(previewFile?.fileExtension)" />
                    </el-icon>
                    <p>{{ previewFile?.fileName }}</p>
                    <p class="preview-hint">此文件类型不支持在线预览</p>
                </div>
            </div>
            
            <template #footer>
                <div class="preview-actions">
                    <el-button @click="closePreview">关闭</el-button>
                    <el-button type="primary" @click="handleDownload(previewFile!)">
                        <el-icon><Download /></el-icon>
                        下载
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { 
    UploadFilled, 
    Search, 
    Grid, 
    List, 
    Download, 
    Delete,
    Document,
    Picture,
    VideoPlay,
    Folder
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import type { CustomerData } from '../types';

interface AttachmentFile {
    id: string;
    fileName: string;
    fileUrl: string;
    fileSize: number;
    fileExtension: string;
    entityId: string;
    entityType: string;
    createTime: string;
    createBy: string;
}

interface Props {
    entityData: CustomerData | null;
}

const props = defineProps<Props>();

const userStore = useUserStore();

// 状态管理
const loading = ref(false);
const attachments = ref<AttachmentFile[]>([]);
const filterType = ref('');
const searchKeyword = ref('');
const viewMode = ref<'grid' | 'list'>('grid');

// 文件预览
const previewVisible = ref(false);
const previewFile = ref<AttachmentFile | null>(null);

// 上传配置
const uploadUrl = `${import.meta.env.VITE_APP_BASE_API}/common/upload`;
const uploadHeaders = computed(() => ({
    Authorization: `Bearer ${userStore.token}`
}));
const uploadData = computed(() => ({
    entityId: props.entityData?.id,
    entityType: 'customer'
}));

// 过滤后的附件列表
const filteredAttachments = computed(() => {
    let result = attachments.value;
    
    // 按文件类型过滤
    if (filterType.value) {
        result = result.filter(file => {
            const type = getFileType(file.fileExtension);
            return type === filterType.value;
        });
    }
    
    // 按关键词搜索
    if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(file => 
            file.fileName.toLowerCase().includes(keyword)
        );
    }
    
    return result;
});

// 获取文件类型
const getFileType = (extension: string) => {
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
    const documentExts = ['doc', 'docx', 'pdf', 'txt', 'rtf'];
    const spreadsheetExts = ['xls', 'xlsx', 'csv'];
    
    const ext = extension.toLowerCase();
    if (imageExts.includes(ext)) return 'image';
    if (documentExts.includes(ext)) return 'document';
    if (spreadsheetExts.includes(ext)) return 'spreadsheet';
    return 'other';
};

// 获取文件图标
const getFileIcon = (extension: string) => {
    const type = getFileType(extension);
    const iconMap = {
        image: Picture,
        document: Document,
        spreadsheet: Document,
        other: Folder
    };
    return iconMap[type as keyof typeof iconMap] || Folder;
};

// 判断是否为图片文件
const isImageFile = (extension?: string) => {
    if (!extension) return false;
    return getFileType(extension) === 'image';
};

// 格式化文件大小
const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
    if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + ' MB';
    return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
};

// 上传前检查
const beforeUpload = (file: File) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        ElMessage.error('文件大小不能超过10MB');
        return false;
    }
    return true;
};

// 上传成功
const handleUploadSuccess = (response: any) => {
    if (response.code === 200) {
        ElMessage.success('上传成功');
        loadAttachments();
    } else {
        ElMessage.error(response.msg || '上传失败');
    }
};

// 上传失败
const handleUploadError = () => {
    ElMessage.error('上传失败');
};

// 处理筛选变化
const handleFilterChange = () => {
    // 筛选逻辑在computed中处理
};

// 处理搜索
const handleSearch = () => {
    // 搜索逻辑在computed中处理
};

// 文件预览
const handlePreview = (file: AttachmentFile) => {
    previewFile.value = file;
    previewVisible.value = true;
};

// 关闭预览
const closePreview = () => {
    previewVisible.value = false;
    previewFile.value = null;
};

// 文件下载
const handleDownload = (file: AttachmentFile) => {
    const link = document.createElement('a');
    link.href = file.fileUrl;
    link.download = file.fileName;
    link.click();
};

// 删除文件
const handleDelete = async (file: AttachmentFile) => {
    try {
        await ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        // TODO: 调用API删除文件
        // await deleteAttachment(file.id);
        
        ElMessage.success('删除成功');
        await loadAttachments();
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败');
        }
    }
};

// 加载附件列表
const loadAttachments = async () => {
    loading.value = true;
    try {
        // TODO: 调用API加载附件列表
        // const response = await getCustomerAttachments(props.entityData?.id);
        // attachments.value = response.data;
        
        // 模拟数据
        attachments.value = [];
    } catch (error) {
        ElMessage.error('加载附件失败');
    } finally {
        loading.value = false;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    if (props.entityData) {
        loadAttachments();
    }
});
</script>

<style scoped lang="scss">
.customer-attachment-tab {
    .upload-section {
        margin-bottom: 24px;

        .upload-card {
            .upload-header {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 500;

                .header-icon {
                    color: #409eff;
                }
            }

            .upload-area {
                .upload-content {
                    padding: 40px 20px;
                    text-align: center;

                    .upload-icon {
                        font-size: 48px;
                        color: #c0c4cc;
                        margin-bottom: 16px;
                    }

                    .upload-text {
                        p {
                            margin: 0;
                            line-height: 1.5;

                            &:first-child {
                                font-size: 16px;
                                color: #606266;
                                margin-bottom: 8px;

                                em {
                                    color: #409eff;
                                    font-style: normal;
                                }
                            }

                            &.upload-hint {
                                font-size: 12px;
                                color: #909399;
                            }
                        }
                    }
                }
            }
        }
    }

    .filter-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 12px;

            .view-controls {
                margin-left: 12px;
            }
        }

        .file-stats {
            display: flex;
            gap: 8px;
        }
    }

    .attachments-section {
        .empty-state {
            text-align: center;
            padding: 60px 0;
        }

        .attachments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;

            .attachment-card {
                border: 1px solid #e4e7ed;
                border-radius: 8px;
                overflow: hidden;
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    transform: translateY(-2px);
                }

                .file-preview {
                    position: relative;
                    height: 120px;
                    background: #f5f7fa;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    .file-icon {
                        font-size: 48px;
                        color: #909399;
                    }

                    .file-thumbnail {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .file-info {
                    padding: 12px;

                    .file-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: #303133;
                        margin-bottom: 8px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .file-meta {
                        display: flex;
                        justify-content: space-between;
                        font-size: 12px;
                        color: #909399;

                        .file-size {
                            font-weight: 500;
                        }
                    }
                }

                .file-actions {
                    display: flex;
                    justify-content: center;
                    gap: 8px;
                    padding: 8px 12px 12px;
                    border-top: 1px solid #f0f2f5;
                }
            }
        }

        .attachments-list {
            .file-icon-cell {
                display: flex;
                justify-content: center;
                font-size: 20px;
                color: #909399;
            }

            .file-name-cell {
                cursor: pointer;
                color: #409eff;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }

    .preview-content {
        text-align: center;

        .image-preview {
            img {
                max-width: 100%;
                max-height: 500px;
                border-radius: 8px;
            }
        }

        .file-preview-placeholder {
            padding: 60px 20px;

            .preview-icon {
                font-size: 64px;
                color: #c0c4cc;
                margin-bottom: 16px;
            }

            p {
                margin: 8px 0;
                
                &:first-of-type {
                    font-size: 16px;
                    font-weight: 500;
                    color: #303133;
                }

                &.preview-hint {
                    font-size: 14px;
                    color: #909399;
                }
            }
        }
    }

    .preview-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .customer-attachment-tab {
        .filter-section {
            flex-direction: column;
            gap: 16px;
            align-items: stretch;

            .filter-controls {
                flex-wrap: wrap;
                justify-content: center;
            }

            .file-stats {
                justify-content: center;
            }
        }

        .attachments-section {
            .attachments-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 16px;
            }
        }
    }
}
</style>