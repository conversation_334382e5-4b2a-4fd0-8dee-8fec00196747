<template>
    <div ref="drawerRef">
        <el-drawer
            v-model="drawerVisible"
            :with-header="false"
            size="80%"
            :destroy-on-close="false"
            :modal="true"
            :close-on-click-modal="true"
            :close-on-press-escape="true"
            :append-to-body="true"        
        >
            <div class="drawer-container" @click.stop>
             <!-- 左侧导航 -->
             <div class="drawer-nav">
                <div class="nav-list">
                    <div 
                        v-for="item in drawerConfig?.menuItems || []"
                        :key="item.key"
                        :class="['nav-item', { active: activeTab === item.key }]"
                        @click="handleTabChange(item.key)"
                    >
                        <el-icon><component :is="item.icon" /></el-icon>
                        <template v-if="item.badge">
                            <el-badge :value="getBadgeValue(item)" class="menu-badge">
                                <span class="nav-label">{{ item.label }}</span>
                            </el-badge>
                        </template>
                        <template v-else>
                            <span class="nav-label">{{ item.label }}</span>
                        </template>
                    </div>
                </div>
            </div>
           

            <div class="drawer-body">
                <!-- 顶部信息区域 -->
                <div class="drawer-header" v-if="headerComponent">
                    <component 
                        :is="headerComponent"
                        :entity-data="entityData"
                        :modelName="modelName"
                        :actions="computedActions"
                        v-bind="headerProps || {}"
                        @update:entity="updateEntity"
                        @action="handleAction"
                    />
                </div>

                <!-- 右侧内容区 -->
                <div class="drawer-content">
                    <keep-alive>
                        <component 
                            :is="currentComponent"
                            :entity-data="entityData"
                            :model-name="modelName"
                            @update:entity="updateEntity"
                            @update:entity-data="handleUpdateEntityData"
                        ></component>
                    </keep-alive>
                </div>
            </div>
        </div>
        </el-drawer>
    </div>
</template>

<script setup lang="ts">
import { EntityData, EntityType } from '@/types/entity';
import { computed, ref, watch, type Component } from 'vue';
import type { DrawerConfig, MenuItem } from './types';

export interface Action {
    label: string;
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default';
    icon?: string;
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
    handler?: (data: any) => void;
}

interface Props {
    modelValue: boolean;
    entityType: EntityType;
    entityData: EntityData;
    modelName: string;
    drawerConfig: DrawerConfig;
    headerComponent?: Component;
    headerProps?: Record<string, any>;
    actions?: Action[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void;
    (e: 'update:entityData', value: Record<string, any>): void;
    (e: 'save', value: Record<string, any>): void;
}>();

// 抽屉显示状态
const drawerVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});


// 监听抽屉状态变化，重置相关状态
watch(() => props.modelValue, (newValue) => {
    if (newValue) {
        // 抽屉打开时，确保有默认激活的标签页
        if (props.drawerConfig?.menuItems?.length > 0 && !activeTab.value) {
            activeTab.value = props.drawerConfig.menuItems[0].key;
        }
    }
});

// 全局点击监听器相关代码已移除，依赖Element Plus原生关闭机制

// 当前激活的标签页
const activeTab = ref('');

// 监听配置变化，设置默认标签页
watch(() => props.drawerConfig, (newConfig) => {
    if (newConfig?.menuItems?.length > 0 && !activeTab.value) {
        activeTab.value = newConfig.menuItems[0].key;
    }
}, { immediate: true });

// 当前显示的组件
const currentComponent = computed(() => {
    console.log('计算currentComponent, activeTab:', activeTab.value);
    
    if (!props.drawerConfig?.menuItems) {
        console.log('没有菜单项配置');
        return null;
    }
    
    const menuItem = props.drawerConfig.menuItems.find(item => item.key === activeTab.value);
    console.log('找到的菜单项:', menuItem);
    
    if (menuItem?.component) {
        console.log('准备加载组件:', menuItem.component);
        console.log('获取到的组件实例:', menuItem.component ? '成功' : '失败');
        return  menuItem.component;
    }
    
    console.log('未找到组件配置');
    return null;
});

// 处理标签页切换
const handleTabChange = (tab: string) => {
    console.log('handleTabChange', tab);    // 调试日志
    activeTab.value = tab;
};

// 获取徽标数值
const getBadgeValue = (item: MenuItem) => {
    if (item.key === 'attachments') {
        return (props.entityData as any).attachmentsCount || 0;
    }
    return 0;
};

// 更新实体数据
const updateEntity = (newData: Record<string, any>) => {
    emit('update:entityData', { ...props.entityData, ...newData });
};

// 处理子组件的 update:entity-data 事件
const handleUpdateEntityData = (newData: Record<string, any>) => {
    console.log('CommonDrawer 接收到 update:entity-data 事件:', newData);
    emit('update:entityData', newData);
};

// 处理操作按钮点击
const handleAction = (action: Action) => {
    if (action.handler) {
        action.handler(props.entityData);
    }
};

// 编辑状态控制
const isEdited = ref(false);

// 添加保存按钮到操作列表
const computedActions = computed(() => {
    const baseActions = props.actions || [];
    
    // 检查是否已经有保存按钮，避免重复添加
    const hasSaveAction = baseActions.some(action => action.label === '保存');
    
    if (hasSaveAction) {
        return baseActions;
    }
    
    const saveAction: Action = {
        label: '保存',
        type: 'primary',
        icon: 'Check',
        disabled: !isEdited.value,
        handler: () => {
            emit('save', props.entityData);
            isEdited.value = false;
        }
    };

    return [...baseActions, saveAction];
});
</script>
<style>
.ep-drawer {
    background: radial-gradient(circle at center, aliceblue, #f0f2f5);
    border-radius: 7px;
}
.drawer-container {
    display: flex;
    height: 100%;
}
.menu-badge {
    text-align: right;
}

.drawer-nav {
    width: 100px;
    border-right: 1px solid var(--el-border-color-light);
    height: 100%;
}

.nav-list {
    padding: 6px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 12px;
    height: 32px;
    margin: 4px 0;
    cursor: pointer;
    font-size: 12px;
    font-weight: normal;
    color: #606266;
    transition: all 0.3s;
    text-align: left;
    
    &:hover {
        background-color: #c8ccd1;
    }
    
    &.active {
        background-color: #fff;
        color: var(--el-color-primary);
        font-weight: 500;
        position: relative;
        
        &::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: var(--el-color-primary);
        }
    }

    .el-icon {
        margin-right: 8px;
        font-size: 14px;
        flex-shrink: 0;
    }
}

.nav-label {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
    text-align: right;
}

.menu-badge {
    flex: 1;
    :deep(.el-badge__content) {
        background-color: var(--el-color-primary);
        transform: translateY(-4px);
    }
}

.drawer-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.drawer-header {
    padding: 24px;
    background-color: #fff;
    margin: 32px;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.drawer-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
}
</style>