package com.ruoyi.crm.domain.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 3D打印订单创建请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public class ThreeDPrintingOrderCreateDTO {
    
    /** 询价单号 */
    private String quoteNo;
    
    /** 客户信息 */
    private CustomerInfoDTO customerInfo;
    
    /** 订单项目列表 */
    private List<OrderItemDTO> items;
    
    /** 订单总金额 */
    private BigDecimal totalAmount;
    
    /** 喷漆选项 */
    private List<String> sprayOptions;
    
    /** 镶嵌选项 */
    private List<String> insertOptions;
    
    /**
     * 客户信息DTO
     */
    public static class CustomerInfoDTO {
        /** 公司名称 */
        private String companyName;
        
        /** 联系人姓名 */
        private String contactName;
        
        /** 手机号码 */
        private String contactPhone;
        
        /** 电子邮箱 */
        private String contactEmail;
        
        /** 收货地址 */
        private String deliveryAddress;
        
        /** 备注信息 */
        private String remark;
        
        // Getters and Setters
        public String getCompanyName() {
            return companyName;
        }
        
        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }
        
        public String getContactName() {
            return contactName;
        }
        
        public void setContactName(String contactName) {
            this.contactName = contactName;
        }
        
        public String getContactPhone() {
            return contactPhone;
        }
        
        public void setContactPhone(String contactPhone) {
            this.contactPhone = contactPhone;
        }
        
        public String getContactEmail() {
            return contactEmail;
        }
        
        public void setContactEmail(String contactEmail) {
            this.contactEmail = contactEmail;
        }
        
        public String getDeliveryAddress() {
            return deliveryAddress;
        }
        
        public void setDeliveryAddress(String deliveryAddress) {
            this.deliveryAddress = deliveryAddress;
        }
        
        public String getRemark() {
            return remark;
        }
        
        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
    
    /**
     * 订单项目DTO
     */
    public static class OrderItemDTO {
        /** 模型名称 */
        private String modelName;
        
        /** 模型信息 */
        private ModelInfoDTO modelInfo;
        
        /** 材料名称 */
        private String material;
        
        /** 材料ID */
        private String materialId;
        
        /** 数量 */
        private Integer quantity;
        
        /** 单价 */
        private BigDecimal unitPrice;
        
        /** 总价 */
        private BigDecimal totalPrice;
        
        /** 后处理选项 */
        private List<String> processOptions;
        
        // Getters and Setters
        public String getModelName() {
            return modelName;
        }
        
        public void setModelName(String modelName) {
            this.modelName = modelName;
        }
        
        public ModelInfoDTO getModelInfo() {
            return modelInfo;
        }
        
        public void setModelInfo(ModelInfoDTO modelInfo) {
            this.modelInfo = modelInfo;
        }
        
        public String getMaterial() {
            return material;
        }
        
        public void setMaterial(String material) {
            this.material = material;
        }
        
        public String getMaterialId() {
            return materialId;
        }
        
        public void setMaterialId(String materialId) {
            this.materialId = materialId;
        }
        
        public Integer getQuantity() {
            return quantity;
        }
        
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        
        public BigDecimal getUnitPrice() {
            return unitPrice;
        }
        
        public void setUnitPrice(BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
        }
        
        public BigDecimal getTotalPrice() {
            return totalPrice;
        }
        
        public void setTotalPrice(BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
        }
        
        public List<String> getProcessOptions() {
            return processOptions;
        }
        
        public void setProcessOptions(List<String> processOptions) {
            this.processOptions = processOptions;
        }
    }
    
    /**
     * 模型信息DTO
     */
    public static class ModelInfoDTO {
        /** 尺寸 */
        private String dimensions;
        
        /** 体积 */
        private String volume;
        
        /** 表面积 */
        private String surfaceArea;
        
        // Getters and Setters
        public String getDimensions() {
            return dimensions;
        }
        
        public void setDimensions(String dimensions) {
            this.dimensions = dimensions;
        }
        
        public String getVolume() {
            return volume;
        }
        
        public void setVolume(String volume) {
            this.volume = volume;
        }
        
        public String getSurfaceArea() {
            return surfaceArea;
        }
        
        public void setSurfaceArea(String surfaceArea) {
            this.surfaceArea = surfaceArea;
        }
    }
    
    // Main DTO Getters and Setters
    public String getQuoteNo() {
        return quoteNo;
    }
    
    public void setQuoteNo(String quoteNo) {
        this.quoteNo = quoteNo;
    }
    
    public CustomerInfoDTO getCustomerInfo() {
        return customerInfo;
    }
    
    public void setCustomerInfo(CustomerInfoDTO customerInfo) {
        this.customerInfo = customerInfo;
    }
    
    public List<OrderItemDTO> getItems() {
        return items;
    }
    
    public void setItems(List<OrderItemDTO> items) {
        this.items = items;
    }
    
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public List<String> getSprayOptions() {
        return sprayOptions;
    }
    
    public void setSprayOptions(List<String> sprayOptions) {
        this.sprayOptions = sprayOptions;
    }
    
    public List<String> getInsertOptions() {
        return insertOptions;
    }
    
    public void setInsertOptions(List<String> insertOptions) {
        this.insertOptions = insertOptions;
    }
}