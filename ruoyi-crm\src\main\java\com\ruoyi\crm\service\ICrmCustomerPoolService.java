package com.ruoyi.crm.service;

import com.ruoyi.common.domain.entity.CrmCustomerPool;
import com.ruoyi.common.domain.entity.CrmPoolOperationLog;

import java.util.List;

/**
 * 客户公海Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface ICrmCustomerPoolService {
    
    /**
     * 查询公海客户列表
     * 
     * @param crmCustomerPool 查询条件
     * @return 公海客户列表
     */
    List<CrmCustomerPool> selectPoolCustomerList(CrmCustomerPool crmCustomerPool);
    
    /**
     * 查询我的客户列表
     * 
     * @param userId 用户ID
     * @return 客户列表
     */
    List<CrmCustomerPool> selectMyCustomerList(Long userId);
    
    /**
     * 将客户放入公海
     * 
     * @param customerIds 客户ID列表
     * @param reason 放入原因
     * @param remark 备注
     * @return 结果
     */
    int returnToPool(List<Long> customerIds, String reason, String remark);
    
    /**
     * 认领客户
     * 
     * @param customerIds 客户ID列表
     * @return 结果
     */
    int claimCustomers(List<Long> customerIds);
    
    /**
     * 检查用户是否可以认领客户
     * 
     * @param userId 用户ID
     * @param claimCount 认领数量
     * @return 检查结果
     */
    boolean checkClaimLimit(Long userId, int claimCount);
    
    /**
     * 记录公海操作日志
     * 
     * @param log 操作日志
     */
    void recordOperationLog(CrmPoolOperationLog log);
    
    /**
     * 自动回收长期未跟进的客户到公海
     * 
     * @return 回收数量
     */
    int autoReturnToPool();
    
    /**
     * 更新客户在公海的天数
     */
    void updateDaysInPool();
}