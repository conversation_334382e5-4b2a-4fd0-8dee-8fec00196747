package com.ruoyi.common.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 客户公海记录对象 crm_customer_pool
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmCustomerPool extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long id;

    /** 客户ID */
    private Long customerId;

    /** 客户名称（冗余字段，方便查询） */
    private String customerName;

    /** 公海类型：PUBLIC(公共公海), DEPARTMENT(部门公海) */
    private String poolType;

    /** 部门ID（部门公海时使用） */
    private Long departmentId;

    /** 前负责人ID */
    private Long previousOwnerId;

    /** 前负责人姓名 */
    private String previousOwnerName;

    /** 放入原因：NO_FOLLOW(未跟进), NO_DEAL(未成交), MANUAL(手动放入), OTHER(其他) */
    private String returnReason;

    /** 放入备注 */
    private String returnRemark;

    /** 放入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnTime;

    /** 放入操作人 */
    private String returnBy;

    /** 在公海天数 */
    private Integer daysInPool;

    /** 状态：IN_POOL(在公海), CLAIMED(已认领) */
    private String status;

    /** 认领时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date claimTime;

    /** 认领人 */
    private String claimBy;

    // 扩展字段（非数据库字段）
    /** 客户手机号 */
    private transient String mobile;

    /** 客户邮箱 */
    private transient String email;

    /** 客户行业 */
    private transient String customerIndustry;

    /** 客户级别 */
    private transient String customerLevel;

    /** 最后跟进时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private transient Date lastFollowupTime;
}