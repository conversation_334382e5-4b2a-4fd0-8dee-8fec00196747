package com.ruoyi.crm.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.domain.entity.CrmCustomerContactRelation;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.service.ICrmCustomerContactRelationService;
import com.ruoyi.common.service.ICrmCustomerService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.crm.common.ContactOperationLogHelper;

/**
 * 客户联系人关联关系Controller
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/crm/customer-contact-relation")
public class CrmCustomerContactRelationController extends BaseController {
    
    @Autowired
    private ICrmCustomerContactRelationService crmCustomerContactRelationService;
    
    @Autowired
    private ICrmCustomerService crmCustomerService;
    
    @Autowired
    private ContactOperationLogHelper contactOperationLogHelper;

    /**
     * 查询客户联系人关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmCustomerContactRelation crmCustomerContactRelation) {
        startPage();
        List<CrmCustomerContactRelation> list = crmCustomerContactRelationService.selectCrmCustomerContactRelationList(crmCustomerContactRelation);
        return getDataTable(list);
    }

    /**
     * 导出客户联系人关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:export')")
    @Log(title = "客户联系人关联关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmCustomerContactRelation crmCustomerContactRelation) {
        List<CrmCustomerContactRelation> list = crmCustomerContactRelationService.selectCrmCustomerContactRelationList(crmCustomerContactRelation);
        ExcelUtil<CrmCustomerContactRelation> util = new ExcelUtil<CrmCustomerContactRelation>(CrmCustomerContactRelation.class);
        util.exportExcel(response, list, "客户联系人关联关系数据");
    }

    /**
     * 获取客户联系人关联关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(crmCustomerContactRelationService.selectCrmCustomerContactRelationById(id));
    }

    /**
     * 根据客户ID查询关联的联系人列表
     */
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping("/customer/{customerId}")
    public AjaxResult getContactsByCustomerId(@PathVariable("customerId") Long customerId) {
        List<CrmContacts> contacts = crmCustomerContactRelationService.selectContactsByCustomerId(customerId);
        return success(contacts);
    }

    /**
     * 根据联系人ID查询关联的客户列表
     */
    @PreAuthorize("@ss.hasPermi('crm:contact:query')")
    @GetMapping("/contact/{contactId}")
    public AjaxResult getCustomersByContactId(@PathVariable("contactId") Long contactId) {
        List<CrmCustomer> customers = crmCustomerContactRelationService.selectCustomersByContactId(contactId);
        return success(customers);
    }

    /**
     * 根据客户ID和联系人ID查询关联关系
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:query')")
    @GetMapping("/relation")
    public AjaxResult getRelation(@RequestParam("customerId") Long customerId, 
                                  @RequestParam("contactId") Long contactId) {
        CrmCustomerContactRelation relation = crmCustomerContactRelationService.selectRelationByCustomerAndContact(customerId, contactId);
        return success(relation);
    }

    /**
     * 新增客户联系人关联关系
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:add')")
    @Log(title = "客户联系人关联关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmCustomerContactRelation crmCustomerContactRelation) {
        // 设置创建信息
        crmCustomerContactRelation.setCreateBy(getUsername());
        crmCustomerContactRelation.setCreateTime(DateUtils.getNowDate());
        crmCustomerContactRelation.setDelFlag("0");
        
        // 设置默认值
        if (crmCustomerContactRelation.getStatus() == null || crmCustomerContactRelation.getStatus().isEmpty()) {
            crmCustomerContactRelation.setStatus(CrmCustomerContactRelation.STATUS_ACTIVE);
        }
        if (crmCustomerContactRelation.getStartDate() == null) {
            crmCustomerContactRelation.setStartDate(DateUtils.getNowDate());
        }
        if (crmCustomerContactRelation.getIsPrimary() == null) {
            crmCustomerContactRelation.setIsPrimary(CrmCustomerContactRelation.IS_PRIMARY_NO);
        }
        if (crmCustomerContactRelation.getRelationType() == null || crmCustomerContactRelation.getRelationType().isEmpty()) {
            crmCustomerContactRelation.setRelationType(CrmCustomerContactRelation.RELATION_TYPE_SECONDARY);
        }

        return toAjax(crmCustomerContactRelationService.insertCrmCustomerContactRelation(crmCustomerContactRelation));
    }

    /**
     * 修改客户联系人关联关系
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:edit')")
    @Log(title = "客户联系人关联关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmCustomerContactRelation crmCustomerContactRelation) {
        crmCustomerContactRelation.setUpdateBy(getUsername());
        crmCustomerContactRelation.setUpdateTime(DateUtils.getNowDate());
        return toAjax(crmCustomerContactRelationService.updateCrmCustomerContactRelation(crmCustomerContactRelation));
    }

    /**
     * 删除客户联系人关联关系
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:remove')")
    @Log(title = "客户联系人关联关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(crmCustomerContactRelationService.deleteCrmCustomerContactRelationByIds(ids));
    }

    /**
     * 建立客户与联系人的关联关系
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:add')")
    @Log(title = "建立客户联系人关联", businessType = BusinessType.INSERT)
    @PostMapping("/link")
    public AjaxResult linkCustomerContact(@RequestBody Map<String, Object> params) {
        Long customerId = Long.valueOf(params.get("customerId").toString());
        Long contactId = Long.valueOf(params.get("contactId").toString());
        String relationType = (String) params.getOrDefault("relationType", CrmCustomerContactRelation.RELATION_TYPE_SECONDARY);
        String isPrimary = (String) params.getOrDefault("isPrimary", "0");

        // 检查是否已存在关联关系
        CrmCustomerContactRelation existingRelation = crmCustomerContactRelationService.selectRelationByCustomerAndContact(customerId, contactId);
        if (existingRelation != null) {
            return error("该客户与联系人已存在关联关系");
        }

        int result = crmCustomerContactRelationService.createCustomerContactRelation(customerId, contactId, relationType, isPrimary);
        
        // 记录联系人操作日志
        if (result > 0) {
            try {
                // 获取客户名称
                CrmCustomer customer = crmCustomerService.selectCrmCustomerById(customerId);
                String customerName = customer != null ? customer.getCustomerName() : ("客户ID: " + customerId);
                contactOperationLogHelper.recordCustomerRelationLog(contactId, "link_customer", customerName, relationType);
            } catch (Exception e) {
                // 如果获取客户信息失败，使用备用方法记录日志
                contactOperationLogHelper.recordCustomerRelationLogById(contactId, "link_customer", customerId, relationType);
            }
        }
        
        return toAjax(result);
    }

    /**
     * 取消客户与联系人的关联关系
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:remove')")
    @Log(title = "取消客户联系人关联", businessType = BusinessType.DELETE)
    @PostMapping("/unlink")
    public AjaxResult unlinkCustomerContact(@RequestBody Map<String, Object> params) {
        Long customerId = Long.valueOf(params.get("customerId").toString());
        Long contactId = Long.valueOf(params.get("contactId").toString());

        // 查找关联关系
        CrmCustomerContactRelation relation = crmCustomerContactRelationService.selectRelationByCustomerAndContact(customerId, contactId);
        if (relation == null) {
            return error("未找到对应的关联关系");
        }

        // 删除关联关系
        int result = crmCustomerContactRelationService.deleteCrmCustomerContactRelationById(relation.getId());
        
        // 记录联系人操作日志
        if (result > 0) {
            try {
                // 获取客户名称
                CrmCustomer customer = crmCustomerService.selectCrmCustomerById(customerId);
                String customerName = customer != null ? customer.getCustomerName() : ("客户ID: " + customerId);
                contactOperationLogHelper.recordCustomerRelationLog(contactId, "unlink_customer", customerName, relation.getRelationType());
            } catch (Exception e) {
                // 如果获取客户信息失败，使用备用方法记录日志
                contactOperationLogHelper.recordCustomerRelationLogById(contactId, "unlink_customer", customerId, relation.getRelationType());
            }
        }
        
        return toAjax(result);
    }

    /**
     * 批量建立客户联系人关联关系
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:add')")
    @Log(title = "批量建立客户联系人关联", businessType = BusinessType.INSERT)
    @PostMapping("/batch-link")
    public AjaxResult batchLinkCustomerContacts(@RequestBody Map<String, Object> params) {
        Long customerId = Long.valueOf(params.get("customerId").toString());
        @SuppressWarnings("unchecked")
        List<Object> contactIdObjects = (List<Object>) params.get("contactIds");
        String relationType = (String) params.getOrDefault("relationType", CrmCustomerContactRelation.RELATION_TYPE_SECONDARY);

        if (contactIdObjects == null || contactIdObjects.isEmpty()) {
            return error("联系人ID列表不能为空");
        }

        // 将 Integer/Long 对象转换为 Long 列表
        List<Long> contactIds = contactIdObjects.stream()
                .map(obj -> Long.valueOf(obj.toString()))
                .collect(java.util.stream.Collectors.toList());

        int successCount = 0;
        int failCount = 0;
        StringBuilder failMessages = new StringBuilder();

        for (Long contactId : contactIds) {
            try {
                // 检查是否已存在关联关系
                CrmCustomerContactRelation existingRelation = crmCustomerContactRelationService.selectRelationByCustomerAndContact(customerId, contactId);
                if (existingRelation != null) {
                    failCount++;
                    failMessages.append("联系人ID:").append(contactId).append("已存在关联关系; ");
                    continue;
                }

                int result = crmCustomerContactRelationService.createCustomerContactRelation(customerId, contactId, relationType, "0");
                if (result > 0) {
                    successCount++;
                } else {
                    failCount++;
                    failMessages.append("联系人ID:").append(contactId).append("关联失败; ");
                }
            } catch (Exception e) {
                failCount++;
                failMessages.append("联系人ID:").append(contactId).append("关联异常; ");
                logger.error("批量关联联系人异常", e);
            }
        }

        String message = String.format("批量关联完成：成功%d个，失败%d个", successCount, failCount);
        if (failCount > 0) {
            message += "。失败原因：" + failMessages.toString();
        }

        return success(message);
    }

    /**
     * 设置主要联系人
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:edit')")
    @Log(title = "设置主要联系人", businessType = BusinessType.UPDATE)
    @PutMapping("/set-primary")
    public AjaxResult setPrimaryContact(@RequestBody Map<String, Object> params) {
        Long customerId = Long.valueOf(params.get("customerId").toString());
        Long contactId = Long.valueOf(params.get("contactId").toString());

        // 检查关联关系是否存在
        CrmCustomerContactRelation relation = crmCustomerContactRelationService.selectRelationByCustomerAndContact(customerId, contactId);
        if (relation == null) {
            return error("未找到对应的关联关系");
        }

        int result = crmCustomerContactRelationService.setPrimaryContact(customerId, contactId);
        
        // 记录联系人操作日志
        if (result > 0) {
            try {
                // 获取客户名称
                CrmCustomer customer = crmCustomerService.selectCrmCustomerById(customerId);
                String customerName = customer != null ? customer.getCustomerName() : ("客户ID: " + customerId);
                contactOperationLogHelper.recordCustomerRelationLog(contactId, "set_primary_customer", customerName, "主要联系人");
            } catch (Exception e) {
                // 如果获取客户信息失败，使用备用方法记录日志
                contactOperationLogHelper.recordCustomerRelationLogById(contactId, "set_primary_customer", customerId, "主要联系人");
            }
        }
        
        return result > 0 ? success("设置主要联系人成功") : error("设置主要联系人失败");
    }

    /**
     * 搜索可关联的联系人（排除已关联的）
     */
    @PreAuthorize("@ss.hasPermi('crm:contact:query')")
    @GetMapping("/available-contacts/{customerId}")
    public AjaxResult getAvailableContacts(@PathVariable("customerId") Long customerId,
                                           @RequestParam(value = "keyword", required = false) String keyword) {
        try {
            // 获取已关联的联系人ID列表
            List<CrmContacts> linkedContacts = crmCustomerContactRelationService.selectContactsByCustomerId(customerId);
            List<Long> linkedContactIds = linkedContacts.stream()
                    .map(CrmContacts::getId)
                    .collect(java.util.stream.Collectors.toList());

            // TODO: 调用联系人服务搜索联系人，排除已关联的
            // 这里需要在联系人服务中添加相应的搜索方法
            // List<CrmContacts> availableContacts = contactsService.searchAvailableContacts(keyword, linkedContactIds);
            
            // 临时返回空列表，待联系人搜索接口完善后替换
            return success(java.util.Collections.emptyList());
        } catch (Exception e) {
            logger.error("搜索可关联联系人失败", e);
            return error("搜索可关联联系人失败");
        }
    }

    /**
     * 获取关联关系统计信息
     */
    @PreAuthorize("@ss.hasPermi('crm:customerContactRelation:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            // 这里可以添加统计逻辑，比如总关联数、主要联系人数等
            // 暂时返回基础信息
            return success("统计功能待实现");
        } catch (Exception e) {
            logger.error("获取关联关系统计信息失败", e);
            return error("获取统计信息失败");
        }
    }
}