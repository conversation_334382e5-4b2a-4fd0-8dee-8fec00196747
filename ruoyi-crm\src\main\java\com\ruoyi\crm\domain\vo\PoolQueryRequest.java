package com.ruoyi.crm.domain.vo;

/**
 * 公海查询请求VO
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class PoolQueryRequest {
    
    /** 查询类型：PERSONAL/TEAM/BUSINESS_TYPE */
    private String queryType;
    
    /** 团队ID（团队公海查询时使用） */
    private Long teamId;
    
    /** 业务类型（业务类型公海查询时使用） */
    private String businessType;
    
    /** 用户ID（个人公海查询时使用） */
    private Long userId;
    
    /** 页码 */
    private Integer pageNum;
    
    /** 页面大小 */
    private Integer pageSize;

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "PoolQueryRequest{" +
                "queryType='" + queryType + '\'' +
                ", teamId=" + teamId +
                ", businessType='" + businessType + '\'' +
                ", userId=" + userId +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
    
    /**
     * 查询类型常量
     */
    public static class QueryType {
        /** 个人公海 */
        public static final String PERSONAL = "PERSONAL";
        /** 团队公海 */
        public static final String TEAM = "TEAM";
        /** 业务类型公海 */
        public static final String BUSINESS_TYPE = "BUSINESS_TYPE";
    }
}