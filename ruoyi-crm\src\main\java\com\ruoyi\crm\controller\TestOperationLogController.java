package com.ruoyi.crm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.crm.common.ContactOperationLogHelper;

/**
 * 测试操作日志控制器
 */
@RestController
@RequestMapping("/test/operationLog")
public class TestOperationLogController {

    @Autowired
    private ContactOperationLogHelper contactOperationLogHelper;

    /**
     * 测试联系人操作日志记录
     */
    @GetMapping("/testContactLog")
    public AjaxResult testContactLog(@RequestParam Long contactId) {
        try {
            System.out.println("开始测试联系人操作日志记录...");
            
            // 测试记录拜访计划日志
            contactOperationLogHelper.recordVisitPlanLog(
                contactId,
                "add_visit_plan",
                "测试拜访计划",
                "这是一个测试的拜访计划详情"
            );
            
            System.out.println("测试完成");
            return AjaxResult.success("操作日志记录测试成功");
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("操作日志记录测试失败: " + e.getMessage());
        }
    }
}