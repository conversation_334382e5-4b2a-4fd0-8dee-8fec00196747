-- =====================================================
-- CRM订单管理模块数据库升级脚本
-- 版本: v1.0
-- 创建日期: 2025-02-02
-- 说明: 扩展现有订单表，新增分配管理和业务转化功能
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 扩展现有订单主表 (crm_order)
-- =====================================================

-- 检查并添加新字段到现有订单表（使用兼容语法）
-- 商机关联字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'opportunity_id') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `opportunity_id` bigint(20) DEFAULT NULL COMMENT ''关联商机ID'' AFTER `customer_id`',
    'SELECT ''Column opportunity_id already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 订单来源字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'order_source') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `order_source` varchar(50) DEFAULT ''MANUAL'' COMMENT ''订单来源：MANUAL-手动创建,OPPORTUNITY-商机转化,3D_PRINTING-3D打印'' AFTER `quote_no`',
    'SELECT ''Column order_source already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 分配状态字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'assignment_status') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `assignment_status` varchar(20) DEFAULT ''UNASSIGNED'' COMMENT ''分配状态：UNASSIGNED-未分配,ASSIGNED-已分配'' AFTER `status`',
    'SELECT ''Column assignment_status already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 负责人字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'owner_id') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `owner_id` bigint(20) DEFAULT NULL COMMENT ''负责人ID'' AFTER `assignment_status`',
    'SELECT ''Column owner_id already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'owner_name') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `owner_name` varchar(100) DEFAULT NULL COMMENT ''负责人姓名（冗余）'' AFTER `owner_id`',
    'SELECT ''Column owner_name already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 分配相关字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'assigned_by') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `assigned_by` bigint(20) DEFAULT NULL COMMENT ''分配人ID'' AFTER `owner_name`',
    'SELECT ''Column assigned_by already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'assigned_time') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `assigned_time` datetime DEFAULT NULL COMMENT ''分配时间'' AFTER `assigned_by`',
    'SELECT ''Column assigned_time already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 订单标题字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'order_title') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `order_title` varchar(200) DEFAULT NULL COMMENT ''订单标题'' AFTER `order_no`',
    'SELECT ''Column order_title already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 联系人关联字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'contact_id') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `contact_id` bigint(20) DEFAULT NULL COMMENT ''联系人ID'' AFTER `customer_name`',
    'SELECT ''Column contact_id already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 优先级字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'priority_level') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `priority_level` varchar(20) DEFAULT ''NORMAL'' COMMENT ''优先级：HIGH-高,NORMAL-普通,LOW-低'' AFTER `order_source`',
    'SELECT ''Column priority_level already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 紧急标志
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'urgent_flag') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `urgent_flag` tinyint(1) DEFAULT ''0'' COMMENT ''紧急标志：0-普通,1-紧急'' AFTER `priority_level`',
    'SELECT ''Column urgent_flag already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 客户类型字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'customer_type') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `customer_type` varchar(20) DEFAULT NULL COMMENT ''客户类型：NEW-新客户,EXISTING-老客户'' AFTER `urgent_flag`',
    'SELECT ''Column customer_type already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 订单类型字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'order_type') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `order_type` varchar(50) DEFAULT ''STANDARD'' COMMENT ''订单类型：STANDARD-标准,3D_PRINTING-3D打印,CUSTOM-定制'' AFTER `customer_type`',
    'SELECT ''Column order_type already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 币种字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'currency') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `currency` varchar(10) DEFAULT ''CNY'' COMMENT ''币种'' AFTER `total_amount`',
    'SELECT ''Column currency already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 已付金额字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'paid_amount') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `paid_amount` decimal(15,2) DEFAULT ''0.00'' COMMENT ''已付金额'' AFTER `currency`',
    'SELECT ''Column paid_amount already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 订单日期字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'order_date') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `order_date` date DEFAULT NULL COMMENT ''下单日期'' AFTER `paid_amount`',
    'SELECT ''Column order_date already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 预期交付日期字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'expected_delivery_date') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `expected_delivery_date` date DEFAULT NULL COMMENT ''预期交付日期'' AFTER `order_date`',
    'SELECT ''Column expected_delivery_date already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 实际交付日期字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'actual_delivery_date') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `actual_delivery_date` date DEFAULT NULL COMMENT ''实际交付日期'' AFTER `expected_delivery_date`',
    'SELECT ''Column actual_delivery_date already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 合同关联字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'contract_id') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `contract_id` bigint(20) DEFAULT NULL COMMENT ''关联合同ID'' AFTER `opportunity_id`',
    'SELECT ''Column contract_id already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除标志字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'del_flag') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `del_flag` char(1) DEFAULT ''0'' COMMENT ''删除标志：0-存在,1-删除'' AFTER `remarks`',
    'SELECT ''Column del_flag already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 标准化创建和更新字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'create_by') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `create_by` varchar(64) DEFAULT '''' COMMENT ''创建者'' AFTER `del_flag`',
    'SELECT ''Column create_by already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND COLUMN_NAME = 'update_by') = 0,
    'ALTER TABLE `crm_order` ADD COLUMN `update_by` varchar(64) DEFAULT '''' COMMENT ''更新者'' AFTER `create_time`',
    'SELECT ''Column update_by already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 2. 扩展订单明细表 (crm_order_item)
-- =====================================================

-- 检查并添加新字段到订单明细表（使用兼容语法）
-- 产品ID字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'product_id') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `product_id` bigint(20) DEFAULT NULL COMMENT ''产品ID'' AFTER `order_id`',
    'SELECT ''Column product_id already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 产品编码字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'product_code') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `product_code` varchar(100) DEFAULT NULL COMMENT ''产品编码'' AFTER `model_name`',
    'SELECT ''Column product_code already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 产品名称字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'product_name') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `product_name` varchar(200) DEFAULT NULL COMMENT ''产品名称'' AFTER `product_code`',
    'SELECT ''Column product_name already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 规格参数字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'specification') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `specification` text COMMENT ''规格参数'' AFTER `product_name`',
    'SELECT ''Column specification already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 单位字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'unit') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `unit` varchar(20) DEFAULT NULL COMMENT ''单位'' AFTER `quantity`',
    'SELECT ''Column unit already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 折扣率字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'discount_rate') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `discount_rate` decimal(5,2) DEFAULT ''0.00'' COMMENT ''折扣率'' AFTER `unit_price`',
    'SELECT ''Column discount_rate already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 小计金额字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'subtotal') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `subtotal` decimal(15,2) DEFAULT NULL COMMENT ''小计金额'' AFTER `discount_rate`',
    'SELECT ''Column subtotal already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3D打印选项字段（使用TEXT代替JSON以兼容旧版本MySQL）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'print_options') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `print_options` text COMMENT ''打印选项'' AFTER `process_options`',
    'SELECT ''Column print_options already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 排序字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'sort_order') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `sort_order` int(11) DEFAULT ''0'' COMMENT ''排序'' AFTER `print_options`',
    'SELECT ''Column sort_order already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 标准化创建和更新字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'create_by') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `create_by` varchar(64) DEFAULT '''' COMMENT ''创建者'' AFTER `sort_order`',
    'SELECT ''Column create_by already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND COLUMN_NAME = 'update_by') = 0,
    'ALTER TABLE `crm_order_item` ADD COLUMN `update_by` varchar(64) DEFAULT '''' COMMENT ''更新者'' AFTER `create_time`',
    'SELECT ''Column update_by already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. 创建订单分配历史表
-- =====================================================

DROP TABLE IF EXISTS `crm_order_assignment_log`;
CREATE TABLE `crm_order_assignment_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  
  -- 分配信息
  `action_type` varchar(50) NOT NULL COMMENT '操作类型：ASSIGN-分配,TRANSFER-转移,RECLAIM-回收,GRAB-抢单',
  `from_user_id` bigint(20) DEFAULT NULL COMMENT '原负责人ID',
  `from_user_name` varchar(100) DEFAULT NULL COMMENT '原负责人姓名',
  `to_user_id` bigint(20) NOT NULL COMMENT '新负责人ID',
  `to_user_name` varchar(100) NOT NULL COMMENT '新负责人姓名',
  
  -- 操作信息
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `reason` varchar(500) DEFAULT NULL COMMENT '操作原因',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  
  -- 其他信息
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_to_user_id` (`to_user_id`),
  KEY `idx_from_user_id` (`from_user_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_action_type` (`action_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单分配历史表';

-- =====================================================
-- 4. 创建业务转化日志表
-- =====================================================

DROP TABLE IF EXISTS `crm_business_conversion_log`;
CREATE TABLE `crm_business_conversion_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 转化信息
  `conversion_type` varchar(50) NOT NULL COMMENT '转化类型：LEAD_TO_CUSTOMER-线索转客户,LEAD_TO_OPPORTUNITY-线索转商机,OPPORTUNITY_TO_ORDER-商机转订单,ORDER_TO_CONTRACT-订单转合同',
  `source_type` varchar(50) NOT NULL COMMENT '源实体类型：LEAD-线索,CUSTOMER-客户,OPPORTUNITY-商机,ORDER-订单',
  `source_id` bigint(20) NOT NULL COMMENT '源实体ID',
  `source_name` varchar(200) DEFAULT NULL COMMENT '源实体名称',
  `target_type` varchar(50) NOT NULL COMMENT '目标实体类型：CUSTOMER-客户,OPPORTUNITY-商机,ORDER-订单,CONTRACT-合同',
  `target_id` bigint(20) NOT NULL COMMENT '目标实体ID',
  `target_name` varchar(200) DEFAULT NULL COMMENT '目标实体名称',
  
  -- 转化详情
  `conversion_data` text COMMENT '转化详情数据',
  `conversion_amount` decimal(15,2) DEFAULT NULL COMMENT '转化金额',
  `success_flag` tinyint(1) DEFAULT '1' COMMENT '转化成功标志：0-失败,1-成功',
  
  -- 操作信息
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `conversion_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '转化时间',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`id`),
  KEY `idx_conversion_type` (`conversion_type`),
  KEY `idx_source` (`source_type`, `source_id`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_conversion_time` (`conversion_time`),
  KEY `idx_success_flag` (`success_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务转化日志表';

-- =====================================================
-- 5. 创建新客户通知表
-- =====================================================

DROP TABLE IF EXISTS `crm_new_customer_notifications`;
CREATE TABLE `crm_new_customer_notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 客户信息
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(100) NOT NULL COMMENT '客户名称',
  `customer_phone` varchar(20) DEFAULT NULL COMMENT '客户电话',
  `customer_source` varchar(50) DEFAULT NULL COMMENT '客户来源',
  
  -- 订单信息
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联订单ID',
  `order_no` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `order_amount` decimal(15,2) DEFAULT NULL COMMENT '订单金额',
  
  -- 通知信息
  `notification_type` varchar(50) DEFAULT 'NEW_CUSTOMER' COMMENT '通知类型：NEW_CUSTOMER-新客户,UNASSIGNED_ORDER-未分配订单',
  `notification_status` varchar(20) DEFAULT 'PENDING' COMMENT '通知状态：PENDING-待处理,PROCESSING-处理中,COMPLETED-已完成,CANCELLED-已取消',
  `priority_level` varchar(20) DEFAULT 'NORMAL' COMMENT '优先级：HIGH-高,NORMAL-普通,LOW-低',
  
  -- 处理信息
  `assigned_to` bigint(20) DEFAULT NULL COMMENT '分配给（管理员ID）',
  `assigned_by` bigint(20) DEFAULT NULL COMMENT '分配人ID',
  `assigned_time` datetime DEFAULT NULL COMMENT '分配时间',
  `processed_by` bigint(20) DEFAULT NULL COMMENT '处理人ID',
  `processed_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_result` varchar(500) DEFAULT NULL COMMENT '处理结果',
  
  -- 通知渠道
  `notification_channels` text COMMENT '通知渠道：["SYSTEM","WECHAT","EMAIL"]',
  `wechat_sent` tinyint(1) DEFAULT '0' COMMENT '企业微信是否已发送：0-未发送,1-已发送',
  `email_sent` tinyint(1) DEFAULT '0' COMMENT '邮件是否已发送：0-未发送,1-已发送',
  
  -- 审计字段
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志：0-存在,1-删除',
  
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_notification_status` (`notification_status`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_priority_level` (`priority_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新客户通知表';

-- =====================================================
-- 6. 优化现有表的索引
-- =====================================================

-- 为订单主表添加新索引（使用兼容语法）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_opportunity_id') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_opportunity_id` (`opportunity_id`)',
    'SELECT ''Index idx_opportunity_id already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_assignment_status') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_assignment_status` (`assignment_status`)',
    'SELECT ''Index idx_assignment_status already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_owner_id') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_owner_id` (`owner_id`)',
    'SELECT ''Index idx_owner_id already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_order_source') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_order_source` (`order_source`)',
    'SELECT ''Index idx_order_source already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_priority_level') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_priority_level` (`priority_level`)',
    'SELECT ''Index idx_priority_level already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_customer_type') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_customer_type` (`customer_type`)',
    'SELECT ''Index idx_customer_type already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_order_date') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_order_date` (`order_date`)',
    'SELECT ''Index idx_order_date already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_contract_id') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_contract_id` (`contract_id`)',
    'SELECT ''Index idx_contract_id already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_contact_id') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_contact_id` (`contact_id`)',
    'SELECT ''Index idx_contact_id already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_del_flag') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_del_flag` (`del_flag`)',
    'SELECT ''Index idx_del_flag already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 为订单明细表添加新索引
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND INDEX_NAME = 'idx_product_id') = 0,
    'ALTER TABLE `crm_order_item` ADD INDEX `idx_product_id` (`product_id`)',
    'SELECT ''Index idx_product_id already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND INDEX_NAME = 'idx_product_code') = 0,
    'ALTER TABLE `crm_order_item` ADD INDEX `idx_product_code` (`product_code`)',
    'SELECT ''Index idx_product_code already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order_item' AND INDEX_NAME = 'idx_sort_order') = 0,
    'ALTER TABLE `crm_order_item` ADD INDEX `idx_sort_order` (`sort_order`)',
    'SELECT ''Index idx_sort_order already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 复合索引优化
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_status_assignment') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_status_assignment` (`status`, `assignment_status`)',
    'SELECT ''Index idx_status_assignment already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_customer_owner') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_customer_owner` (`customer_id`, `owner_id`)',
    'SELECT ''Index idx_customer_owner already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crm_order' AND INDEX_NAME = 'idx_create_time_status') = 0,
    'ALTER TABLE `crm_order` ADD INDEX `idx_create_time_status` (`create_time`, `status`)',
    'SELECT ''Index idx_create_time_status already exists'' as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- 7. 数据迁移和初始化
-- =====================================================

-- 更新现有订单的默认值
UPDATE `crm_order` SET 
  `order_source` = '3D_PRINTING',
  `assignment_status` = 'ASSIGNED',
  `order_type` = '3D_PRINTING',
  `customer_type` = 'EXISTING',
  `order_date` = DATE(`create_time`)
WHERE `order_source` IS NULL OR `order_source` = '';

-- 更新现有订单明细的默认值
UPDATE `crm_order_item` SET 
  `product_name` = `model_name`,
  `subtotal` = `total_price`,
  `unit` = '个'
WHERE `product_name` IS NULL OR `product_name` = '';

-- =====================================================
-- 8. 创建视图（可选）
-- =====================================================

-- 订单详情视图
CREATE OR REPLACE VIEW `v_order_details` AS
SELECT 
  o.id,
  o.order_no,
  o.order_title,
  o.customer_id,
  o.customer_name,
  o.contact_id,
  o.opportunity_id,
  o.contract_id,
  o.total_amount,
  o.paid_amount,
  o.currency,
  o.status,
  o.assignment_status,
  o.owner_id,
  o.owner_name,
  o.order_source,
  o.order_type,
  o.priority_level,
  o.urgent_flag,
  o.customer_type,
  o.order_date,
  o.expected_delivery_date,
  o.actual_delivery_date,
  o.create_time,
  o.update_time,
  -- 统计信息
  COUNT(oi.id) as item_count,
  SUM(oi.quantity) as total_quantity
FROM `crm_order` o
LEFT JOIN `crm_order_item` oi ON o.id = oi.order_id
WHERE o.del_flag = '0'
GROUP BY o.id;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 脚本执行完成
-- =====================================================
SELECT 'CRM订单管理模块数据库升级完成！' as message;
