<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM系统核心开发文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
        }
        h1 {
            text-align: center;
            border-bottom: 4px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            border: none;
        }
        h2 {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            margin-top: 40px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        h3 {
            color: #3498db;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            background-color: #f8fbff;
            padding: 12px 15px;
            border-radius: 0 8px 8px 0;
            margin-top: 30px;
        }
        .section {
            background-color: white;
            padding: 30px;
            margin-bottom: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: 1px solid #e8eef5;
        }
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        .task-item {
            background-color: #ffffff;
            border: 1px solid #e8eef5;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }
        .task-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .task-item h4 {
            margin-top: 0;
            color: #2980b9;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }
        .priority-high {
            border-left: 5px solid #e74c3c;
            background-color: #fdf2f2;
        }
        .priority-medium {
            border-left: 5px solid #f39c12;
            background-color: #fef9e7;
        }
        .priority-low {
            border-left: 5px solid #27ae60;
            background-color: #f0f9f0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 6px rgba(0,0,0,0.05);
        }
        th, td {
            border: 1px solid #e8eef5;
            padding: 15px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            font-weight: 600;
            font-size: 14px;
        }
        tr:nth-child(even) {
            background-color: #f8fbff;
        }
        tr:hover {
            background-color: #e8f4fd;
        }
        .timeline {
            position: relative;
            padding-left: 50px;
            margin: 30px 0;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #3498db, #2ecc71);
            border-radius: 2px;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 35px;
            background-color: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.1);
            border: 1px solid #e8eef5;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -42px;
            top: 30px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #3498db;
            border: 4px solid white;
            box-shadow: 0 0 0 4px #3498db;
        }
        .code-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px 0;
            overflow-x: auto;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin: 2px;
        }
        .status-todo { background-color: #6c757d; color: white; }
        .status-progress { background-color: #ffc107; color: #212529; }
        .status-done { background-color: #28a745; color: white; }
        .effort-estimate {
            background: linear-gradient(135deg, #17a2b8, #20c997);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            font-weight: 600;
            margin: 5px;
            font-size: 12px;
        }
        .api-endpoint {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .method-get { border-left-color: #28a745; }
        .method-post { border-left-color: #007bff; }
        .method-put { border-left-color: #ffc107; }
        .method-delete { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <h1>🚀 CRM系统核心开发文档</h1>
    
    <div class="highlight-box">
        <h3 style="margin-top: 0; color: white;">📋 项目概述</h3>
        <p style="margin-bottom: 0; font-size: 18px;">
            基于现有CRM系统，完善业务转化链路，新增订单管理模块，优化3D订单分配机制
            <br><strong>预计开发周期：7周 | 总工时：276小时</strong>
        </p>
    </div>

    <div class="section">
        <h2>一、核心开发任务</h2>
        
        <div class="task-grid">
            <div class="task-item priority-high">
                <h4>🔄 业务转化优化</h4>
                <ul>
                    <li>线索转化增加商机创建选项</li>
                    <li>商机转订单功能开发</li>
                    <li>订单转合同功能开发</li>
                    <li>完善转化日志记录</li>
                </ul>
                <div class="effort-estimate">40小时</div>
            </div>
            
            <div class="task-item priority-high">
                <h4>📋 订单管理模块</h4>
                <ul>
                    <li>订单列表管理界面</li>
                    <li>订单详情抽屉组件</li>
                    <li>未分配订单池</li>
                    <li>我的订单工作台</li>
                    <li>订单分配中心</li>
                </ul>
                <div class="effort-estimate">120小时</div>
            </div>
            
            <div class="task-item priority-high">
                <h4>🎯 智能分配系统</h4>
                <ul>
                    <li>基于电话号码的客户匹配</li>
                    <li>新客户通知机制</li>
                    <li>老客户自动分配</li>
                    <li>企业微信通知集成</li>
                </ul>
                <div class="effort-estimate">48小时</div>
            </div>
            
            <div class="task-item priority-medium">
                <h4>🗄️ 数据库设计</h4>
                <ul>
                    <li>订单主表和明细表设计</li>
                    <li>分配历史表设计</li>
                    <li>转化日志表设计</li>
                    <li>索引优化</li>
                </ul>
                <div class="effort-estimate">32小时</div>
            </div>
            
            <div class="task-item priority-medium">
                <h4>🔧 API接口开发</h4>
                <ul>
                    <li>订单CRUD接口</li>
                    <li>分配管理接口</li>
                    <li>转化功能接口</li>
                    <li>统计分析接口</li>
                </ul>
                <div class="effort-estimate">36小时</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>二、开发时序计划</h2>
        
        <div class="timeline">
            <div class="timeline-item">
                <h3>第1周：数据库设计与基础架构</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-progress">进行中</span>
                    <span class="effort-estimate">40小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>数据库表设计</strong> - 订单主表、明细表、分配日志表</li>
                    <li><strong>基础Entity和DTO</strong> - Java实体类和数据传输对象</li>
                    <li><strong>基础Mapper接口</strong> - MyBatis数据访问层</li>
                    <li><strong>数据迁移脚本</strong> - 现有3D订单数据结构调整</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第2周：后端核心服务开发</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate">48小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>订单基础服务</strong> - CRUD操作、状态管理</li>
                    <li><strong>订单分配服务</strong> - 分配逻辑、抢单机制</li>
                    <li><strong>客户匹配服务</strong> - 电话号码匹配、新老客户识别</li>
                    <li><strong>通知服务扩展</strong> - 企业微信通知</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第3周：API接口层开发</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate">36小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>订单Controller开发</strong> - REST API接口实现</li>
                    <li><strong>参数验证</strong> - 请求参数校验和异常处理</li>
                    <li><strong>分页排序</strong> - 列表查询的分页和排序功能</li>
                    <li><strong>批量操作接口</strong> - 批量分配、状态更新等</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第4-5周：前端界面开发</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate">80小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>订单列表页面</strong> - 主列表、筛选器、批量操作</li>
                    <li><strong>未分配订单池</strong> - 公海池列表、抢单功能</li>
                    <li><strong>我的订单工作台</strong> - 个人订单看板</li>
                    <li><strong>订单详情抽屉</strong> - 详情展示、快速编辑</li>
                    <li><strong>分配中心界面</strong> - 管理员分配工具</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第6周：业务流程整合</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate">40小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>线索转化优化</strong> - 增加商机创建选项</li>
                    <li><strong>商机转订单功能</strong> - 商机界面增加转订单按钮</li>
                    <li><strong>订单转合同功能</strong> - 订单界面增加转合同功能</li>
                    <li><strong>3D订单创建逻辑改造</strong> - 集成新的分配机制</li>
                </ul>
            </div>

            <div class="timeline-item">
                <h3>第7周：测试与上线</h3>
                <div style="margin: 15px 0;">
                    <span class="status-badge status-todo">待开始</span>
                    <span class="effort-estimate">32小时</span>
                </div>
                
                <h4>📋 主要任务</h4>
                <ul>
                    <li><strong>系统集成测试</strong> - 端到端业务流程测试</li>
                    <li><strong>性能压力测试</strong> - 高并发场景测试</li>
                    <li><strong>数据迁移执行</strong> - 生产环境数据迁移</li>
                    <li><strong>灰度发布</strong> - 小范围用户试用</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>三、核心业务流程图</h2>

        <h3>3.1 完整业务转化流程</h3>
        <div id="business-flow-diagram"></div>

        <h3>3.2 3D订单智能分配流程</h3>
        <div id="order-assignment-diagram"></div>

        <h3>3.3 订单管理核心流程</h3>
        <div id="order-management-diagram"></div>
    </div>

    <div class="section">
        <h2>四、关键时序图</h2>

        <h3>4.1 线索转化时序图</h3>
        <div id="lead-conversion-sequence"></div>

        <h3>4.2 3D订单创建与分配时序图</h3>
        <div id="order-creation-sequence"></div>

        <h3>4.3 商机转订单时序图</h3>
        <div id="opportunity-order-sequence"></div>
    </div>

    <div class="section">
        <h2>五、核心API接口设计</h2>

        <h3>5.1 订单管理接口</h3>
        <div class="api-endpoint method-get">
            <strong>GET</strong> /api/orders<br>
            <small>获取订单列表，支持分页和筛选</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders<br>
            <small>创建新订单</small>
        </div>
        <div class="api-endpoint method-put">
            <strong>PUT</strong> /api/orders/{id}/status<br>
            <small>更新订单状态</small>
        </div>

        <h3>5.2 订单分配接口</h3>
        <div class="api-endpoint method-get">
            <strong>GET</strong> /api/orders/unassigned<br>
            <small>获取未分配订单列表</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/{id}/assign<br>
            <small>分配订单给指定用户</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/{id}/grab<br>
            <small>用户抢单操作</small>
        </div>

        <h3>5.3 业务转化接口</h3>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/leads/{id}/convert<br>
            <small>线索转化（支持创建商机）</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/opportunities/{id}/convert-to-order<br>
            <small>商机转订单</small>
        </div>
        <div class="api-endpoint method-post">
            <strong>POST</strong> /api/orders/convert-to-contract<br>
            <small>订单转合同（支持批量）</small>
        </div>
    </div>

    <div class="section">
        <h2>六、数据库核心表结构</h2>

        <h3>6.1 订单主表 (crm_order)</h3>
        <div class="code-block">
CREATE TABLE `crm_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `opportunity_id` bigint(20) DEFAULT NULL COMMENT '关联商机ID',
  `total_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `status` varchar(50) DEFAULT 'PENDING' COMMENT '订单状态',
  `assignment_status` varchar(20) DEFAULT 'UNASSIGNED' COMMENT '分配状态',
  `owner_id` bigint(20) DEFAULT NULL COMMENT '负责人ID',
  `order_source` varchar(50) DEFAULT 'MANUAL' COMMENT '订单来源',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_opportunity_id` (`opportunity_id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';
        </div>

        <h3>6.2 订单分配历史表 (crm_order_assignment_log)</h3>
        <div class="code-block">
CREATE TABLE `crm_order_assignment_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `action_type` varchar(50) NOT NULL COMMENT '操作类型：ASSIGN,TRANSFER,GRAB',
  `from_user_id` bigint(20) DEFAULT NULL COMMENT '原负责人ID',
  `to_user_id` bigint(20) NOT NULL COMMENT '新负责人ID',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `reason` varchar(500) DEFAULT NULL COMMENT '操作原因',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单分配历史表';
        </div>
    </div>

    <div class="section">
        <h2>七、技术实现要点</h2>

        <h3>7.1 客户智能匹配算法</h3>
        <div class="code-block">
@Service
public class CustomerMatchingService {

    /**
     * 根据电话号码匹配客户
     */
    public CustomerMatchResult matchCustomerByPhone(String phone) {
        // 1. 精确匹配手机号
        Customer customer = customerMapper.findByPhone(phone);
        if (customer != null) {
            return CustomerMatchResult.builder()
                .matched(true)
                .customer(customer)
                .matchType("EXACT_PHONE")
                .build();
        }

        // 2. 模糊匹配（去除国家代码、分隔符等）
        String normalizedPhone = normalizePhone(phone);
        customer = customerMapper.findByNormalizedPhone(normalizedPhone);
        if (customer != null) {
            return CustomerMatchResult.builder()
                .matched(true)
                .customer(customer)
                .matchType("FUZZY_PHONE")
                .build();
        }

        // 3. 未匹配到，标记为新客户
        return CustomerMatchResult.builder()
            .matched(false)
            .matchType("NEW_CUSTOMER")
            .build();
    }
}
        </div>

        <h3>7.2 订单状态管理</h3>
        <table>
            <thead>
                <tr>
                    <th>状态</th>
                    <th>英文标识</th>
                    <th>颜色</th>
                    <th>可执行操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>待分配</td>
                    <td>UNASSIGNED</td>
                    <td style="color: #6c757d;">⚫ 灰色</td>
                    <td>分配负责人、抢单</td>
                </tr>
                <tr>
                    <td>已分配</td>
                    <td>ASSIGNED</td>
                    <td style="color: #17a2b8;">🔵 蓝色</td>
                    <td>确认接单、拒单、转移</td>
                </tr>
                <tr>
                    <td>进行中</td>
                    <td>IN_PROGRESS</td>
                    <td style="color: #ffc107;">🟡 黄色</td>
                    <td>更新进度、完成</td>
                </tr>
                <tr>
                    <td>已完成</td>
                    <td>COMPLETED</td>
                    <td style="color: #28a745;">✅ 绿色</td>
                    <td>转合同、归档</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>八、开发成功标准</h2>

        <h3>8.1 功能指标</h3>
        <ul>
            <li><strong>线索转化成功率</strong> = 100%</li>
            <li><strong>订单自动分配准确率</strong> ≥ 95%</li>
            <li><strong>通知送达率</strong> ≥ 98%</li>
            <li><strong>系统可用性</strong> ≥ 99.5%</li>
        </ul>

        <h3>8.2 性能指标</h3>
        <ul>
            <li><strong>订单列表加载时间</strong> ≤ 2秒</li>
            <li><strong>客户匹配响应时间</strong> ≤ 1秒</li>
            <li><strong>订单创建响应时间</strong> ≤ 3秒</li>
        </ul>

        <h3>8.3 业务指标</h3>
        <ul>
            <li><strong>订单响应时间缩短</strong> 50%</li>
            <li><strong>新客户处理效率提升</strong> 60%</li>
            <li><strong>用户满意度</strong> ≥ 4.5/5</li>
        </ul>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                showSequenceNumbers: true
            }
        });

        // 业务转化流程图
        const businessFlowDiagram = `
        flowchart TD
            A[线索Lead] --> B{转化选择}
            B -->|仅客户| C[创建客户+联系人]
            B -->|客户+商机| D[创建客户+联系人+商机]
            C --> E[手动创建商机]
            D --> F[商机跟进]
            E --> F
            F --> G{商机状态}
            G -->|成交| H[商机转订单]
            G -->|失败| I[商机关闭]
            H --> J[订单管理]
            J --> K{订单完成}
            K -->|是| L[订单转合同]
            K -->|否| M[继续跟进]
            L --> N[合同签署]
            N --> O[回款管理]

            P[3D询价] --> Q[客户匹配]
            Q -->|老客户| R[自动分配给负责人]
            Q -->|新客户| S[通知管理员分配]
            R --> J
            S --> T[管理员分配] --> J

            style A fill:#e1f5fe
            style D fill:#c8e6c9
            style H fill:#fff3e0
            style L fill:#f3e5f5
            style P fill:#fce4ec
        `;

        // 3D订单分配流程图
        const orderAssignmentDiagram = `
        flowchart TD
            A[3D订单创建] --> B[提取客户电话]
            B --> C{电话号码匹配}
            C -->|精确匹配| D[找到现有客户]
            C -->|模糊匹配| E[找到疑似客户]
            C -->|无匹配| F[标记为新客户]

            D --> G[获取客户负责人]
            G --> H{负责人状态}
            H -->|在职| I[自动分配给负责人]
            H -->|离职| J[进入未分配池]

            E --> K[人工确认客户]
            K --> L{确认结果}
            L -->|是同一客户| G
            L -->|不是| F

            F --> M[创建新客户记录]
            M --> N[发送新客户通知]
            N --> O[管理员分配]

            I --> P[发送分配通知]
            J --> Q[进入公海池]
            O --> P
            Q --> R[等待抢单或分配]

            style A fill:#e3f2fd
            style I fill:#c8e6c9
            style N fill:#fff3e0
            style Q fill:#ffebee
        `;

        // 订单管理流程图
        const orderManagementDiagram = `
        flowchart TD
            A[订单列表] --> B{操作选择}
            B -->|查看详情| C[订单详情抽屉]
            B -->|批量操作| D[批量处理]
            B -->|筛选| E[高级筛选]

            C --> F{详情操作}
            F -->|编辑| G[订单编辑]
            F -->|状态更新| H[状态变更]
            F -->|转合同| I[转合同流程]
            F -->|分配| J[重新分配]

            D --> K{批量类型}
            K -->|批量分配| L[选择负责人]
            K -->|批量转合同| M[合同信息填写]
            K -->|批量导出| N[Excel导出]

            E --> O[筛选结果]
            O --> P[保存筛选条件]

            G --> Q[保存更新]
            H --> R[状态流转]
            I --> S[生成合同]
            J --> T[分配确认]

            style A fill:#e8f5e8
            style C fill:#e3f2fd
            style I fill:#fff3e0
            style M fill:#f3e5f5
        `;

        // 线索转化时序图
        const leadConversionSequence = `
        sequenceDiagram
            participant U as 用户
            participant F as 前端界面
            participant B as 后端服务
            participant D as 数据库
            participant N as 通知服务

            U->>F: 点击线索转化
            F->>F: 显示转化对话框
            U->>F: 填写客户信息
            U->>F: 选择创建商机
            U->>F: 填写商机信息
            U->>F: 确认转化

            F->>B: POST /api/leads/{id}/convert
            B->>B: 验证转化数据
            B->>D: 创建客户记录
            B->>D: 创建联系人记录

            alt 创建商机
                B->>D: 创建商机记录
                B->>D: 关联客户和商机
            end

            B->>D: 更新线索状态
            B->>D: 记录转化日志
            B->>N: 发送转化通知
            B->>F: 返回转化结果
            F->>U: 显示转化成功
        `;

        // 3D订单创建时序图
        const orderCreationSequence = `
        sequenceDiagram
            participant T as 3D系统
            participant C as CRM服务
            participant M as 客户匹配服务
            participant D as 数据库
            participant N as 通知服务
            participant W as 企业微信

            T->>C: 创建订单请求
            C->>M: 客户电话匹配
            M->>D: 查询客户信息

            alt 匹配到现有客户
                M->>C: 返回客户信息
                C->>D: 获取客户负责人
                C->>D: 创建订单（已分配）
                C->>N: 发送分配通知
                N->>W: 企业微信通知
            else 新客户
                M->>C: 返回新客户标识
                C->>D: 创建新客户记录
                C->>D: 创建订单（未分配）
                C->>N: 发送新客户通知
                N->>W: 通知管理员
            end

            C->>T: 返回订单创建结果
        `;

        // 商机转订单时序图
        const opportunityOrderSequence = `
        sequenceDiagram
            participant U as 用户
            participant F as 前端界面
            participant B as 后端服务
            participant D as 数据库
            participant V as 验证服务

            U->>F: 点击商机转订单
            F->>B: GET /api/opportunities/{id}
            B->>D: 查询商机信息
            B->>F: 返回商机详情
            F->>F: 显示转订单对话框

            U->>F: 填写订单信息
            U->>F: 确认转化

            F->>B: POST /api/opportunities/{id}/convert-to-order
            B->>V: 验证商机状态
            B->>V: 验证订单金额

            alt 验证通过
                B->>D: 创建订单记录
                B->>D: 更新商机统计
                B->>D: 记录转化日志
                B->>F: 返回成功结果
                F->>U: 显示转化成功
            else 验证失败
                B->>F: 返回错误信息
                F->>U: 显示错误提示
            end
        `;

        // 渲染图表
        document.getElementById('business-flow-diagram').innerHTML = '<div class="mermaid">' + businessFlowDiagram + '</div>';
        document.getElementById('order-assignment-diagram').innerHTML = '<div class="mermaid">' + orderAssignmentDiagram + '</div>';
        document.getElementById('order-management-diagram').innerHTML = '<div class="mermaid">' + orderManagementDiagram + '</div>';
        document.getElementById('lead-conversion-sequence').innerHTML = '<div class="mermaid">' + leadConversionSequence + '</div>';
        document.getElementById('order-creation-sequence').innerHTML = '<div class="mermaid">' + orderCreationSequence + '</div>';
        document.getElementById('opportunity-order-sequence').innerHTML = '<div class="mermaid">' + opportunityOrderSequence + '</div>';
    </script>
</body>
</html>
