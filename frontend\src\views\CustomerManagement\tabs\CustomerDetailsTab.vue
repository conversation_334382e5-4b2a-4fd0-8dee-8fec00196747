<template>
    <div class="modern-customer-details">
        <!-- 客户头部信息 -->
        <div class="customer-header">
            <div class="customer-info">
                <div class="avatar">
                    {{ getInitials(entityData?.customerName) }}
                </div>
                <div class="customer-basic">
                    <h3 class="customer-name">{{ entityData?.customerName || '未命名客户' }}</h3>
                    <p class="customer-industry">
                        {{ entityData?.customerIndustry || '暂无行业信息' }} 
                        <span v-if="entityData?.customerLevel" class="level">· {{ entityData?.customerLevel }}</span>
                    </p>
                </div>
            </div>
            <div class="customer-actions">
                <el-button size="small" type="primary" :icon="Edit" @click="toggleEditMode">
                    {{ isEditing ? '保存' : '编辑' }}
                </el-button>
                <el-button size="small" :icon="Star" :type="entityData?.isFollowing ? 'warning' : 'default'">
                    {{ entityData?.isFollowing ? '已关注' : '关注' }}
                </el-button>
            </div>
        </div>

        <!-- 信息卡片网格 -->
        <div class="info-cards-grid">
            <!-- 基本信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <el-icon class="card-icon basic"><OfficeBuilding /></el-icon>
                    <h4 class="card-title">基本信息</h4>
                </div>
                <div class="card-content">
                    <div class="info-item">
                        <span class="info-label">客户名称</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.customerName" 
                                size="small" 
                                placeholder="请输入客户名称"
                            />
                            <span v-else>{{ entityData?.customerName || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">所属行业</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.customerIndustry" 
                                size="small" 
                                placeholder="请输入所属行业"
                            />
                            <span v-else>{{ entityData?.customerIndustry || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">客户级别</span>
                        <div class="info-value">
                            <el-select 
                                v-if="isEditing" 
                                v-model="formData.customerLevel" 
                                size="small" 
                                placeholder="请选择客户级别"
                            >
                                <el-option label="A级" value="A级" />
                                <el-option label="B级" value="B级" />
                                <el-option label="C级" value="C级" />
                                <el-option label="D级" value="D级" />
                            </el-select>
                            <el-tag 
                                v-else 
                                :type="getCustomerLevelTagType(entityData?.customerLevel)"
                                size="small"
                            >
                                {{ entityData?.customerLevel || '-' }}
                            </el-tag>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">客户来源</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.customerSource" 
                                size="small" 
                                placeholder="请输入客户来源"
                            />
                            <span v-else>{{ entityData?.customerSource || '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系方式卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <el-icon class="card-icon contact"><Phone /></el-icon>
                    <h4 class="card-title">联系方式</h4>
                </div>
                <div class="card-content">
                    <div class="info-item">
                        <span class="info-label">手机号</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.mobile" 
                                size="small" 
                                placeholder="请输入手机号"
                            />
                            <span v-else>{{ entityData?.mobile || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">电话</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.phone" 
                                size="small" 
                                placeholder="请输入电话"
                            />
                            <span v-else>{{ entityData?.phone || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮箱</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.email" 
                                size="small" 
                                placeholder="请输入邮箱"
                            />
                            <span v-else>{{ entityData?.email || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">网址</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.website" 
                                size="small" 
                                placeholder="请输入网址"
                            />
                            <span v-else>
                                <a v-if="entityData?.website" :href="entityData?.website" target="_blank" class="website-link">
                                    {{ entityData?.website }}
                                </a>
                                <span v-else>-</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地址信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <el-icon class="card-icon address"><Location /></el-icon>
                    <h4 class="card-title">地址信息</h4>
                </div>
                <div class="card-content">
                    <div class="info-item full-width">
                        <span class="info-label">客户地址</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.customerAddress" 
                                size="small" 
                                placeholder="请输入客户地址"
                                type="textarea"
                                :rows="2"
                            />
                            <span v-else>{{ entityData?.customerAddress || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">首要联系人</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.primaryContact" 
                                size="small" 
                                placeholder="请输入首要联系人"
                            />
                            <span v-else>{{ entityData?.primaryContact || '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <el-icon class="card-icon other"><InfoFilled /></el-icon>
                    <h4 class="card-title">其他信息</h4>
                </div>
                <div class="card-content">
                    <div class="info-item">
                        <span class="info-label">成交状态</span>
                        <div class="info-value">
                            <el-select 
                                v-if="isEditing" 
                                v-model="formData.dealStatus" 
                                size="small" 
                                placeholder="请选择成交状态"
                            >
                                <el-option label="未成交" value="未成交" />
                                <el-option label="已成交" value="已成交" />
                                <el-option label="跟进中" value="跟进中" />
                            </el-select>
                            <span v-else>{{ entityData?.dealStatus || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">下次联系时间</span>
                        <div class="info-value">
                            <el-date-picker 
                                v-if="isEditing" 
                                v-model="formData.nextContactTime" 
                                size="small" 
                                type="datetime" 
                                placeholder="选择下次联系时间"
                                format="YYYY-MM-DD HH:mm"
                                value-format="YYYY-MM-DD HH:mm:ss"
                            />
                            <span v-else>{{ entityData?.nextContactTime || '-' }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">状态</span>
                        <div class="info-value">
                            <el-select 
                                v-if="isEditing" 
                                v-model="formData.status" 
                                size="small" 
                                placeholder="请选择状态"
                            >
                                <el-option label="正常" value="1" />
                                <el-option label="禁用" value="0" />
                            </el-select>
                            <el-tag 
                                v-else 
                                :type="entityData?.status === '1' ? 'success' : 'info'"
                                size="small"
                            >
                                {{ entityData?.status === '1' ? '正常' : '禁用' }}
                            </el-tag>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-label">创建时间</span>
                        <div class="info-value">
                            <span>{{ entityData?.createTime || '-' }}</span>
                        </div>
                    </div>
                    <div v-if="entityData?.remarks" class="info-item full-width">
                        <span class="info-label">备注</span>
                        <div class="info-value">
                            <el-input 
                                v-if="isEditing" 
                                v-model="formData.remarks" 
                                size="small" 
                                placeholder="请输入备注"
                                type="textarea"
                                :rows="3"
                            />
                            <span v-else>{{ entityData?.remarks || '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { Edit, Star, OfficeBuilding, Phone, Location, InfoFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { CustomerData } from '../types';

interface Props {
    entityData: CustomerData | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    'update-data': [data: Partial<CustomerData>];
}>();

// 编辑状态
const isEditing = ref(false);

// 表单数据
const formData = reactive<Partial<CustomerData>>({});

// 获取姓名首字母
const getInitials = (name?: string) => {
    if (!name) return '客';
    const words = name.trim().split(' ');
    if (words.length >= 2) {
        return words[0][0] + words[1][0];
    }
    return name[0] || '客';
};

// 获取客户级别标签类型
const getCustomerLevelTagType = (level?: string) => {
    if (!level) return 'info';
    const levelMap: Record<string, string> = {
        'A级': 'danger',
        'B级': 'warning', 
        'C级': 'success',
        'D级': 'info'
    };
    return levelMap[level] || 'info';
};

// 切换编辑模式
const toggleEditMode = () => {
    if (isEditing.value) {
        // 保存数据
        saveData();
    } else {
        // 进入编辑模式，初始化表单数据
        if (props.entityData) {
            Object.assign(formData, props.entityData);
        }
        isEditing.value = true;
    }
};

// 保存数据
const saveData = async () => {
    try {
        emit('update-data', formData);
        isEditing.value = false;
        ElMessage.success('保存成功');
    } catch (error) {
        ElMessage.error('保存失败');
    }
};
</script>

<style scoped lang="scss">
.modern-customer-details {
    .customer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        margin-bottom: 24px;
        color: white;

        .customer-info {
            display: flex;
            align-items: center;
            gap: 16px;

            .avatar {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.2);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                font-weight: bold;
                color: white;
                backdrop-filter: blur(10px);
            }

            .customer-basic {
                .customer-name {
                    margin: 0;
                    font-size: 24px;
                    font-weight: 600;
                }

                .customer-industry {
                    margin: 4px 0 0;
                    opacity: 0.9;
                    font-size: 14px;

                    .level {
                        font-weight: 500;
                    }
                }
            }
        }

        .customer-actions {
            display: flex;
            gap: 12px;

            .el-button {
                border-color: rgba(255, 255, 255, 0.3);
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);

                &:hover {
                    background: rgba(255, 255, 255, 0.2);
                }
            }
        }
    }

    .info-cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 24px;

        .info-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e4e7ed;
            overflow: hidden;
            transition: all 0.3s ease;

            &:hover {
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }

            .card-header {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 20px 24px 16px;
                border-bottom: 1px solid #f0f2f5;

                .card-icon {
                    font-size: 20px;
                    padding: 8px;
                    border-radius: 8px;

                    &.basic {
                        background: linear-gradient(135deg, #667eea, #764ba2);
                        color: white;
                    }

                    &.contact {
                        background: linear-gradient(135deg, #f093fb, #f5576c);
                        color: white;
                    }

                    &.address {
                        background: linear-gradient(135deg, #4facfe, #00f2fe);
                        color: white;
                    }

                    &.other {
                        background: linear-gradient(135deg, #43e97b, #38f9d7);
                        color: white;
                    }
                }

                .card-title {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                }
            }

            .card-content {
                padding: 20px 24px 24px;

                .info-item {
                    display: flex;
                    align-items: flex-start;
                    margin-bottom: 16px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    &.full-width {
                        flex-direction: column;
                        align-items: stretch;

                        .info-label {
                            margin-bottom: 8px;
                        }

                        .info-value {
                            width: 100%;
                        }
                    }

                    .info-label {
                        flex-shrink: 0;
                        width: 100px;
                        color: #606266;
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 32px;
                    }

                    .info-value {
                        flex: 1;
                        color: #303133;
                        font-size: 14px;
                        min-width: 0;

                        .website-link {
                            color: #409eff;
                            text-decoration: none;

                            &:hover {
                                text-decoration: underline;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .modern-customer-details {
        .customer-header {
            flex-direction: column;
            gap: 16px;
            text-align: center;

            .customer-actions {
                width: 100%;
                justify-content: center;
            }
        }

        .info-cards-grid {
            grid-template-columns: 1fr;
            gap: 16px;

            .info-card {
                .card-content {
                    .info-item {
                        flex-direction: column;
                        align-items: stretch;

                        .info-label {
                            width: auto;
                            margin-bottom: 4px;
                            line-height: 20px;
                        }
                    }
                }
            }
        }
    }
}
</style>