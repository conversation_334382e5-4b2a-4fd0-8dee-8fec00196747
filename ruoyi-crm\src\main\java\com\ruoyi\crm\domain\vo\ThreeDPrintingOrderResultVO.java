package com.ruoyi.crm.domain.vo;

/**
 * 3D打印订单创建结果VO
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public class ThreeDPrintingOrderResultVO {
    
    /** 订单ID */
    private Long orderId;
    
    /** 订单号 */
    private String orderNo;
    
    /** 客户ID */
    private Long customerId;
    
    /** 客户是否为新创建 */
    private Boolean isNewCustomer;
    
    /** 联系人ID */
    private Long contactId;
    
    /** 联系人是否为新创建 */
    private Boolean isNewContact;
    
    /** 商机ID */
    private Long opportunityId;
    
    /** 创建时间 */
    private String createTime;
    
    /** 成功标识 */
    private Boolean success;
    
    /** 消息 */
    private String message;
    
    // 构造函数
    public ThreeDPrintingOrderResultVO() {}
    
    public static ThreeDPrintingOrderResultVO success(Long orderId, String orderNo, Long customerId, 
                                                     Boolean isNewCustomer, Long contactId, Boolean isNewContact, 
                                                     Long opportunityId) {
        ThreeDPrintingOrderResultVO result = new ThreeDPrintingOrderResultVO();
        result.setOrderId(orderId);
        result.setOrderNo(orderNo);
        result.setCustomerId(customerId);
        result.setIsNewCustomer(isNewCustomer);
        result.setContactId(contactId);
        result.setIsNewContact(isNewContact);
        result.setOpportunityId(opportunityId);
        result.setSuccess(true);
        result.setMessage("订单创建成功，已自动创建CRM记录");
        result.setCreateTime(java.time.LocalDateTime.now().toString());
        return result;
    }
    
    public static ThreeDPrintingOrderResultVO failure(String message) {
        ThreeDPrintingOrderResultVO result = new ThreeDPrintingOrderResultVO();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
    
    // Getters and Setters
    public Long getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    
    public String getOrderNo() {
        return orderNo;
    }
    
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
    
    public Long getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }
    
    public Boolean getIsNewCustomer() {
        return isNewCustomer;
    }
    
    public void setIsNewCustomer(Boolean isNewCustomer) {
        this.isNewCustomer = isNewCustomer;
    }
    
    public Long getContactId() {
        return contactId;
    }
    
    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }
    
    public Boolean getIsNewContact() {
        return isNewContact;
    }
    
    public void setIsNewContact(Boolean isNewContact) {
        this.isNewContact = isNewContact;
    }
    
    public Long getOpportunityId() {
        return opportunityId;
    }
    
    public void setOpportunityId(Long opportunityId) {
        this.opportunityId = opportunityId;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    public Boolean getSuccess() {
        return success;
    }
    
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
}