package com.ruoyi.common.domain.dto;

import java.util.List;

import lombok.Data;

/**
 * 订单分配数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
public class CrmOrderAssignmentDTO {

    /** 订单ID列表 */
    private List<Long> orderIds;

    /** 单个订单ID */
    private Long orderId;

    /** 目标负责人ID */
    private Long toUserId;

    /** 目标负责人姓名 */
    private String toUserName;

    /** 原负责人ID */
    private Long fromUserId;

    /** 原负责人姓名 */
    private String fromUserName;

    /** 分配类型：ASSIGN-分配,TRANSFER-转移,RECLAIM-回收,GRAB-抢单 */
    private String assignmentType;

    /** 分配原因 */
    private String reason;

    /** 是否强制分配（忽略负责人状态检查） */
    private Boolean forceAssign;

    /** 是否发送通知 */
    private Boolean sendNotification;

    /** 通知方式：SYSTEM-系统通知,WECHAT-企业微信,EMAIL-邮件 */
    private List<String> notificationMethods;

    /** 操作人ID */
    private Long operatorId;

    /** 操作人姓名 */
    private String operatorName;

    /** 客户端IP地址 */
    private String ipAddress;

    /** 用户代理 */
    private String userAgent;

    // ========== 批量分配配置 ==========
    
    /** 分配策略：AVERAGE-平均分配,WORKLOAD-按工作量分配,MANUAL-手动指定 */
    private String assignmentStrategy;

    /** 目标负责人列表（批量分配用） */
    private List<AssignmentTarget> assignmentTargets;

    /** 是否考虑负责人工作量 */
    private Boolean considerWorkload;

    /** 是否考虑负责人专业领域 */
    private Boolean considerExpertise;

    /** 最大分配数量限制 */
    private Integer maxAssignmentCount;

    // ========== 抢单配置 ==========
    
    /** 抢单时间限制（分钟） */
    private Integer grabTimeLimit;

    /** 抢单数量限制 */
    private Integer grabCountLimit;

    /** 抢单条件检查 */
    private Boolean checkGrabConditions;

    // ========== 转移配置 ==========
    
    /** 是否转移历史记录 */
    private Boolean transferHistory;

    /** 是否转移跟进记录 */
    private Boolean transferFollowups;

    /** 是否转移附件文件 */
    private Boolean transferAttachments;

    // ========== 验证结果 ==========
    
    /** 验证是否通过 */
    private Boolean validationPassed;

    /** 验证错误信息 */
    private List<String> validationErrors;

    /** 警告信息 */
    private List<String> warnings;

    /**
     * 分配目标内部类
     */
    @Data
    public static class AssignmentTarget {
        /** 负责人ID */
        private Long userId;
        
        /** 负责人姓名 */
        private String userName;
        
        /** 分配数量 */
        private Integer assignmentCount;
        
        /** 当前工作量 */
        private Integer currentWorkload;
        
        /** 专业领域 */
        private List<String> expertiseAreas;
        
        /** 是否在线 */
        private Boolean online;
        
        /** 最后活跃时间 */
        private String lastActiveTime;
    }

    // ========== 常量定义 ==========
    
    /** 分配类型：分配 */
    public static final String ASSIGNMENT_TYPE_ASSIGN = "ASSIGN";
    
    /** 分配类型：转移 */
    public static final String ASSIGNMENT_TYPE_TRANSFER = "TRANSFER";
    
    /** 分配类型：回收 */
    public static final String ASSIGNMENT_TYPE_RECLAIM = "RECLAIM";
    
    /** 分配类型：抢单 */
    public static final String ASSIGNMENT_TYPE_GRAB = "GRAB";

    /** 分配策略：平均分配 */
    public static final String STRATEGY_AVERAGE = "AVERAGE";
    
    /** 分配策略：按工作量分配 */
    public static final String STRATEGY_WORKLOAD = "WORKLOAD";
    
    /** 分配策略：手动指定 */
    public static final String STRATEGY_MANUAL = "MANUAL";

    /** 通知方式：系统通知 */
    public static final String NOTIFICATION_SYSTEM = "SYSTEM";
    
    /** 通知方式：企业微信 */
    public static final String NOTIFICATION_WECHAT = "WECHAT";
    
    /** 通知方式：邮件 */
    public static final String NOTIFICATION_EMAIL = "EMAIL";
}
