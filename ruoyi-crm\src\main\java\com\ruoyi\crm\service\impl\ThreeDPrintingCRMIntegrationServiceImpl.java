package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.domain.entity.CrmOpportunity;
import com.ruoyi.common.domain.entity.CrmOrder;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.common.service.ICrmCustomerService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.domain.dto.ThreeDPrintingOrderCreateDTO;
import com.ruoyi.crm.domain.vo.ThreeDPrintingOrderResultVO;
import com.ruoyi.crm.service.ICrmOpportunityService;
import com.ruoyi.crm.service.ICrmOrderService;
import com.ruoyi.crm.service.ICustomerNotificationService;
import com.ruoyi.crm.service.IThreeDPrintingCRMIntegrationService;

/**
 * 3D打印CRM集成服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ThreeDPrintingCRMIntegrationServiceImpl implements IThreeDPrintingCRMIntegrationService {
    
    private static final Logger log = LoggerFactory.getLogger(ThreeDPrintingCRMIntegrationServiceImpl.class);
    
    @Autowired
    private ICrmCustomerService crmCustomerService;
    
    @Autowired
    private ICrmContactsService crmContactsService;
    
    @Autowired
    private ICrmOpportunityService crmOpportunityService;
    
    @Autowired
    private ICrmOrderService crmOrderService;
    
    @Autowired
    private ICustomerNotificationService customerNotificationService;
    
    @Override
    public ThreeDPrintingOrderResultVO createOrderWithCRMIntegration(ThreeDPrintingOrderCreateDTO orderCreateDTO) {
        try {
            log.info("=== 开始3D打印订单CRM集成流程 ===");
            log.info("询价单号: {}", orderCreateDTO.getQuoteNo());
            
            // 打印完整的请求数据包
            log.info("=== 接收到的完整数据包 ===");
            log.info("客户信息: 公司名称={}, 联系人={}, 手机号={}, 邮箱={}, 地址={}", 
                    orderCreateDTO.getCustomerInfo().getCompanyName(),
                    orderCreateDTO.getCustomerInfo().getContactName(),
                    orderCreateDTO.getCustomerInfo().getContactPhone(),
                    orderCreateDTO.getCustomerInfo().getContactEmail(),
                    orderCreateDTO.getCustomerInfo().getDeliveryAddress());
            log.info("订单总金额: {}", orderCreateDTO.getTotalAmount());
            log.info("订单项目数量: {}", orderCreateDTO.getItems().size());
            
            // 打印每个订单项目的详细信息
            for (int i = 0; i < orderCreateDTO.getItems().size(); i++) {
                var item = orderCreateDTO.getItems().get(i);
                log.info("项目[{}]: 模型名称={}, 材料={}, 数量={}, 单价={}, 总价={}", 
                        i + 1, 
                        item.getModelName(),
                        item.getMaterial(),
                        item.getQuantity(),
                        item.getUnitPrice(),
                        item.getTotalPrice());
            }
            
            if (orderCreateDTO.getSprayOptions() != null && !orderCreateDTO.getSprayOptions().isEmpty()) {
                log.info("喷漆选项: {}", String.join(", ", orderCreateDTO.getSprayOptions()));
            }
            if (orderCreateDTO.getInsertOptions() != null && !orderCreateDTO.getInsertOptions().isEmpty()) {
                log.info("镶嵌选项: {}", String.join(", ", orderCreateDTO.getInsertOptions()));
            }
            
            log.info("=== 开始状态流转处理 ===");
            
            // 1. 检查或创建客户
            log.info("步骤1: 处理客户信息");
            Long customerId = processCustomer(orderCreateDTO.getCustomerInfo(), orderCreateDTO.getQuoteNo());
            boolean isNewCustomer = checkCustomerExistsByMobile(orderCreateDTO.getCustomerInfo().getContactPhone()) == null;
            log.info("客户处理结果: 客户ID={}, 是否新客户={}", customerId, isNewCustomer);
            
            // 2. 创建联系人
            log.info("步骤2: 处理联系人信息");
            Long contactId = processContact(customerId, orderCreateDTO.getCustomerInfo(), orderCreateDTO.getQuoteNo());
            boolean isNewContact = checkContactExists(customerId, orderCreateDTO.getCustomerInfo().getContactPhone()) == null;
            log.info("联系人处理结果: 联系人ID={}, 是否新联系人={}", contactId, isNewContact);
            
            // 3. 创建商机
            log.info("步骤3: 创建商机");
            Long opportunityId = createOpportunity(customerId, orderCreateDTO);
            log.info("商机创建结果: 商机ID={}", opportunityId);
            
            // 4. 创建订单
            log.info("步骤4: 创建订单");
            String orderNo = generateOrderNo();
            Long orderId = createOrder(orderCreateDTO, customerId, contactId, opportunityId, orderNo);
            log.info("订单创建结果: 订单ID={}, 订单号={}", orderId, orderNo);
            
            // 检查是否需要通知负责人
            CrmCustomer customer = crmCustomerService.selectCrmCustomerById(customerId);
            if (customer != null && StringUtils.hasText(customer.getResponsiblePersonId())) {
                Long managerId = Long.parseLong(customer.getResponsiblePersonId());
                customerNotificationService.notifyManagerNewOrder(
                    managerId, 
                    customerId, 
                    customer.getCustomerName(), 
                    orderNo, 
                    orderCreateDTO.getTotalAmount()
                );
            }
            
            log.info("=== 3D打印订单CRM集成流程完成 ===");
            log.info("最终结果: 订单号={}, 客户ID={}, 联系人ID={}, 商机ID={}, 订单ID={}", 
                    orderNo, customerId, contactId, opportunityId, orderId);
            
            // 构建返回结果
            ThreeDPrintingOrderResultVO result = ThreeDPrintingOrderResultVO.success(orderId, orderNo, customerId, isNewCustomer, 
                                                      contactId, isNewContact, opportunityId);
            log.info("返回结果数据包: {}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("=== 3D打印订单创建失败 ===");
            log.error("询价单号: {}", orderCreateDTO.getQuoteNo());
            log.error("错误详情: {}", e.getMessage(), e);
            throw new RuntimeException("订单创建失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Long checkCustomerExistsByMobile(String mobile) {
        if (!StringUtils.hasText(mobile)) {
            return null;
        }
        
        CrmCustomer existingCustomer = crmCustomerService.selectCrmCustomerByMobile(mobile.trim());
        return existingCustomer != null ? existingCustomer.getId() : null;
    }
    
    @Override
    public Long checkContactExists(Long customerId, String mobile) {
        if (customerId == null || !StringUtils.hasText(mobile)) {
            return null;
        }
        
        // 查询该客户下的所有联系人
        CrmContacts queryContacts = new CrmContacts();
        // 由于CrmContacts不再直接包含customerId字段，需要通过其他方式查询
        // 这里我们查询所有联系人，然后在内存中过滤
        List<CrmContacts> contactsList = crmContactsService.selectCrmContactsList(queryContacts);
        
        // 检查是否有相同手机号的联系人
        for (CrmContacts contact : contactsList) {
            if (mobile.trim().equals(contact.getMobile())) {
                return contact.getId();
            }
        }
        
        return null;
    }
    
    /**
     * 处理客户信息（检查存在或创建新客户）
     */
    private Long processCustomer(ThreeDPrintingOrderCreateDTO.CustomerInfoDTO customerInfo, String quoteNo) {
        log.debug("开始处理客户信息: 公司名称={}, 联系电话={}", customerInfo.getCompanyName(), customerInfo.getContactPhone());
        
        // 检查客户是否已存在
        Long existingCustomerId = checkCustomerExistsByMobile(customerInfo.getContactPhone());
        
        if (existingCustomerId != null) {
            log.info("客户已存在，使用现有客户ID: {}", existingCustomerId);
            log.debug("跳过客户创建，直接使用现有客户记录");
            return existingCustomerId;
        }
        
        log.info("客户不存在，开始创建新客户");
        
        // 创建新客户
        CrmCustomer newCustomer = new CrmCustomer();
        newCustomer.setCustomerName(customerInfo.getCompanyName());
        newCustomer.setMobile(customerInfo.getContactPhone());
        newCustomer.setEmail(customerInfo.getContactEmail());
        newCustomer.setCustomerAddress(customerInfo.getDeliveryAddress());
        newCustomer.setPrimaryContact(customerInfo.getContactName());
        newCustomer.setRemarks(customerInfo.getRemark());
        newCustomer.setCustomerSource("3D打印报价系统");
        newCustomer.setCustomerLevel("A级客户");
        newCustomer.setStatus("1"); // 正常状态
        
        // 新客户不分配负责人，放入公海池
        newCustomer.setResponsiblePersonId(null); // 不分配负责人
        try {
            Long currentUserId = SecurityUtils.getUserId();
            newCustomer.setCreateBy(currentUserId.toString());
        } catch (Exception e) {
            log.warn("获取当前用户ID失败，使用默认值", e);
            newCustomer.setCreateBy("1");
        }
        
        newCustomer.setCreateTime(new Date());
        
        log.debug("即将插入客户数据: 公司名称={}, 联系人={}, 电话={}, 邮箱={}, 地址={}, 来源={}", 
                newCustomer.getCustomerName(), newCustomer.getPrimaryContact(), newCustomer.getMobile(), 
                newCustomer.getEmail(), newCustomer.getCustomerAddress(), newCustomer.getCustomerSource());
        
        int result = crmCustomerService.insertCrmCustomer(newCustomer);
        if (result <= 0) {
            log.error("客户数据插入失败，返回结果: {}", result);
            throw new RuntimeException("创建客户失败");
        }
        
        log.info("成功创建新客户，ID: {}, 公司名称: {}", newCustomer.getId(), newCustomer.getCustomerName());
        
        // 发送新客户通知
        customerNotificationService.notifyNewCustomerFromThreeD(
            newCustomer.getId(), 
            newCustomer.getCustomerName(), 
            newCustomer.getMobile(), 
            quoteNo
        );
        
        return newCustomer.getId();
    }
    
    /**
     * 处理联系人信息（检查存在或创建新联系人）
     */
    private Long processContact(Long customerId, ThreeDPrintingOrderCreateDTO.CustomerInfoDTO customerInfo, String quoteNo) {
        log.debug("开始处理联系人信息: 客户ID={}, 联系人={}, 电话={}", customerId, customerInfo.getContactName(), customerInfo.getContactPhone());
        
        // 检查联系人是否已存在
        Long existingContactId = checkContactExists(customerId, customerInfo.getContactPhone());
        
        if (existingContactId != null) {
            log.info("联系人已存在，使用现有联系人ID: {}", existingContactId);
            log.debug("跳过联系人创建，直接使用现有联系人记录");
            return existingContactId;
        }
        
        log.info("联系人不存在，开始创建新联系人");
        
        // 创建新联系人
        CrmContacts newContact = new CrmContacts();
        newContact.setName(customerInfo.getContactName());
        newContact.setMobile(customerInfo.getContactPhone());
        newContact.setEmail(customerInfo.getContactEmail());
        newContact.setPosition("主要联系人");
        newContact.setRemarks("通过3D打印报价系统创建 - 询价单号: " + quoteNo);
        newContact.setStatus("1"); // 正常状态
        
        // 设置创建者信息
        try {
            Long currentUserId = SecurityUtils.getUserId();
            newContact.setCreateBy(currentUserId.toString());
        } catch (Exception e) {
            log.warn("获取当前用户ID失败，使用默认值", e);
            newContact.setCreateBy("1");
        }
        
        newContact.setCreateTime(new Date());
        
        log.debug("即将插入联系人数据: 姓名={}, 电话={}, 邮箱={}, 职位={}, 备注={}", 
                newContact.getName(), newContact.getMobile(), newContact.getEmail(), 
                newContact.getPosition(), newContact.getRemarks());
        
        int result = crmContactsService.insertCrmContacts(newContact);
        if (result <= 0) {
            log.error("联系人数据插入失败，返回结果: {}", result);
            throw new RuntimeException("创建联系人失败");
        }
        
        // 建立联系人与客户的关联关系
        // crmContactsService.associateContactWithCustomer(newContact.getId(), customerId); // 方法不存在
        log.debug("联系人与客户关联: 联系人ID={}, 客户ID={}", newContact.getId(), customerId);
        
        log.info("成功创建新联系人，ID: {}, 姓名: {}", newContact.getId(), newContact.getName());
        return newContact.getId();
    }

    /**
     * 创建商机
     */
    private Long createOpportunity(Long customerId, ThreeDPrintingOrderCreateDTO orderCreateDTO) {
        log.debug("开始创建商机: 客户ID={}, 询价单号={}, 总金额={}", customerId, orderCreateDTO.getQuoteNo(), orderCreateDTO.getTotalAmount());
        
        CrmOpportunity opportunity = new CrmOpportunity();
        opportunity.setOpportunityName("3D打印服务 - " + orderCreateDTO.getQuoteNo());
        opportunity.setCustomerId(customerId);
        opportunity.setCustomerName(orderCreateDTO.getCustomerInfo().getCompanyName()); // 设置客户名称
        opportunity.setOpportunityStage("proposal");
        opportunity.setOpportunityAmount(orderCreateDTO.getTotalAmount());
        opportunity.setWinRate(BigDecimal.valueOf(70));
        
        // 设置预期成交日期（7天后）
        Date expectedDate = new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L);
        opportunity.setExpectedCloseDate(expectedDate);
        
        StringBuilder remarks = new StringBuilder();
        remarks.append("来源：3D打印报价系统\n");
        remarks.append("询价单号：").append(orderCreateDTO.getQuoteNo()).append("\n");
        remarks.append("包含模型数量：").append(orderCreateDTO.getItems().size()).append("个\n");
        remarks.append("订单金额：¥").append(orderCreateDTO.getTotalAmount());
        
        opportunity.setRemarks(remarks.toString());
        opportunity.setOpportunitySource("3D打印报价系统");
        opportunity.setStatus("1"); // 正常状态
        
        // **实现客户匹配分配机制**
        // 检查客户是否有负责人
        CrmCustomer customer = crmCustomerService.selectCrmCustomerById(customerId);
        if (customer != null && StringUtils.hasText(customer.getResponsiblePersonId())) {
            // 客户有负责人，商机分配给客户负责人
            Long managerId = Long.parseLong(customer.getResponsiblePersonId());
            opportunity.setManagerId(managerId);
            log.info("检测到现有客户负责人，商机分配给负责人ID: {}", managerId);
        } else {
            // 客户无负责人（新客户或公海客户），不分配商机负责人
            opportunity.setManagerId(null);
            log.info("客户无负责人，商机暂不分配，待管理员手动分配");
        }
        
        try {
            Long currentUserId = SecurityUtils.getUserId();
            opportunity.setCreateBy(currentUserId.toString());
        } catch (Exception e) {
            log.warn("获取当前用户ID失败，使用默认值", e);
            opportunity.setCreateBy("1");
        }
        
        opportunity.setCreateTime(new Date());
        
        log.debug("即将插入商机数据: 商机名称={}, 客户ID={}, 客户名称={}, 阶段={}, 金额={}, 成功率={}%, 预期成交日期={}, 来源={}", 
                opportunity.getOpportunityName(), opportunity.getCustomerId(), opportunity.getCustomerName(), 
                opportunity.getOpportunityStage(), opportunity.getOpportunityAmount(), opportunity.getWinRate(), 
                opportunity.getExpectedCloseDate(), opportunity.getOpportunitySource());
        
        int result = crmOpportunityService.insertCrmOpportunity(opportunity);
        if (result <= 0) {
            log.error("商机数据插入失败，返回结果: {}", result);
            throw new RuntimeException("创建商机失败");
        }
        
        log.info("成功创建商机，ID: {}, 商机名称: {}, 金额: {}", opportunity.getId(), opportunity.getOpportunityName(), opportunity.getOpportunityAmount());
        return opportunity.getId();
    }
    
    /**
     * 创建订单
     */
    private Long createOrder(ThreeDPrintingOrderCreateDTO orderCreateDTO, Long customerId, 
                           Long contactId, Long opportunityId, String orderNo) {
        log.debug("开始创建订单: 订单号={}, 客户ID={}, 联系人ID={}, 商机ID={}, 总金额={}", 
                orderNo, customerId, contactId, opportunityId, orderCreateDTO.getTotalAmount());
        
        CrmOrder order = new CrmOrder();
        order.setOrderNo(orderNo);
        order.setQuoteNo(orderCreateDTO.getQuoteNo());
        order.setCustomerId(customerId);
        order.setTotalAmount(orderCreateDTO.getTotalAmount());
        order.setStatus("pending"); // 待处理状态
        
        // 构建订单详情
        StringBuilder details = new StringBuilder();
        details.append("3D打印订单详情：\n");
        for (ThreeDPrintingOrderCreateDTO.OrderItemDTO item : orderCreateDTO.getItems()) {
            details.append("- ").append(item.getModelName())
                   .append("，材料：").append(item.getMaterial())
                   .append("，数量：").append(item.getQuantity())
                   .append("，单价：¥").append(item.getUnitPrice())
                   .append("，小计：¥").append(item.getTotalPrice()).append("\n");
        }
        
        if (orderCreateDTO.getSprayOptions() != null && !orderCreateDTO.getSprayOptions().isEmpty()) {
            details.append("喷漆选项：").append(String.join("、", orderCreateDTO.getSprayOptions())).append("\n");
        }
        
        if (orderCreateDTO.getInsertOptions() != null && !orderCreateDTO.getInsertOptions().isEmpty()) {
            details.append("镶嵌选项：").append(String.join("、", orderCreateDTO.getInsertOptions())).append("\n");
        }
        
        order.setRemarks(details.toString());  // 使用备注字段存储订单详情
        order.setDeliveryAddress(orderCreateDTO.getCustomerInfo().getDeliveryAddress());
        order.setRemarks(orderCreateDTO.getCustomerInfo().getRemark());
        
        // 设置创建者信息
        try {
            Long currentUserId = SecurityUtils.getUserId();
            order.setCreateBy(currentUserId.toString());
        } catch (Exception e) {
            log.warn("获取当前用户ID失败，使用默认值", e);
            order.setCreateBy("1");
        }
        
        order.setCreateTime(new Date());
        
        log.debug("即将插入订单数据: 订单号={}, 询价单号={}, 客户ID={}, 总金额={}, 状态={}, 配送地址={}", 
                order.getOrderNo(), order.getQuoteNo(), order.getCustomerId(), 
                order.getTotalAmount(), order.getStatus(), order.getDeliveryAddress());
        
        int result = crmOrderService.insertCrmOrder(order);
        if (result <= 0) {
            log.error("订单数据插入失败，返回结果: {}", result);
            throw new RuntimeException("创建订单失败");
        }
        
        log.info("成功创建订单，ID: {}, 订单号: {}, 总金额: {}", order.getId(), orderNo, order.getTotalAmount());
        return order.getId();
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis();
    }
}