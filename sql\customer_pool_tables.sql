-- 客户公海管理相关表
-- 创建时间：2025-01-25

-- 1. 公海规则配置表
DROP TABLE IF EXISTS `crm_pool_rules`;
CREATE TABLE `crm_pool_rules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_type` varchar(50) NOT NULL COMMENT '规则类型：NO_FOLLOW(未跟进), NO_DEAL(未成交), MANUAL(手动放入)',
  `days_limit` int(11) DEFAULT NULL COMMENT '天数限制',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `description` varchar(500) DEFAULT NULL COMMENT '规则描述',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_rule_type` (`rule_type`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公海规则配置表';

-- 2. 客户公海记录表
DROP TABLE IF EXISTS `crm_customer_pool`;
CREATE TABLE `crm_customer_pool` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `pool_type` varchar(50) NOT NULL DEFAULT 'PUBLIC' COMMENT '公海类型：PUBLIC(公共公海), DEPARTMENT(部门公海)',
  `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID（部门公海时使用）',
  `previous_owner_id` bigint(20) DEFAULT NULL COMMENT '前负责人ID',
  `previous_owner_name` varchar(100) DEFAULT NULL COMMENT '前负责人姓名',
  `return_reason` varchar(50) NOT NULL COMMENT '放入原因：NO_FOLLOW(未跟进), NO_DEAL(未成交), MANUAL(手动放入), OTHER(其他)',
  `return_remark` varchar(500) DEFAULT NULL COMMENT '放入备注',
  `return_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '放入时间',
  `return_by` varchar(64) DEFAULT NULL COMMENT '放入操作人',
  `days_in_pool` int(11) DEFAULT '0' COMMENT '在公海天数',
  `status` varchar(20) DEFAULT 'IN_POOL' COMMENT '状态：IN_POOL(在公海), CLAIMED(已认领)',
  `claim_time` datetime DEFAULT NULL COMMENT '认领时间',
  `claim_by` varchar(64) DEFAULT NULL COMMENT '认领人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_pool_type` (`pool_type`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_previous_owner` (`previous_owner_id`),
  KEY `idx_status` (`status`),
  KEY `idx_return_time` (`return_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户公海记录表';

-- 3. 客户认领限制表
DROP TABLE IF EXISTS `crm_claim_limits`;
CREATE TABLE `crm_claim_limits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '限制ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（为空表示全局限制）',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID（为空表示全局限制）',
  `max_claim_daily` int(11) DEFAULT NULL COMMENT '每日最大认领数',
  `max_claim_total` int(11) DEFAULT NULL COMMENT '最大持有客户数',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户认领限制表';

-- 4. 客户公海操作日志表
DROP TABLE IF EXISTS `crm_pool_operation_log`;
CREATE TABLE `crm_pool_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(255) DEFAULT NULL COMMENT '客户名称',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型：RETURN(放入公海), CLAIM(认领), AUTO_RETURN(自动回收)',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `from_user_id` bigint(20) DEFAULT NULL COMMENT '原负责人ID',
  `from_user_name` varchar(100) DEFAULT NULL COMMENT '原负责人姓名',
  `to_user_id` bigint(20) DEFAULT NULL COMMENT '新负责人ID',
  `to_user_name` varchar(100) DEFAULT NULL COMMENT '新负责人姓名',
  `reason` varchar(500) DEFAULT NULL COMMENT '操作原因',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `ip_addr` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户公海操作日志表';

-- 5. 添加客户表的公海相关字段（如果不存在）
-- 注意：MySQL 5.7不支持ADD COLUMN IF NOT EXISTS语法，需要先检查字段是否存在
-- 请根据实际情况执行以下语句
-- ALTER TABLE `crm_business_customers` 
-- ADD COLUMN `pool_status` varchar(20) DEFAULT 'PRIVATE' COMMENT '公海状态：PRIVATE(私有), PUBLIC(公海)',
-- ADD COLUMN `last_followup_time` datetime DEFAULT NULL COMMENT '最后跟进时间',
-- ADD COLUMN `claim_time` datetime DEFAULT NULL COMMENT '认领时间',
-- ADD COLUMN `claim_limit_time` datetime DEFAULT NULL COMMENT '保护期限';

-- 添加索引
-- ALTER TABLE `crm_business_customers`
-- ADD INDEX `idx_pool_status` (`pool_status`),
-- ADD INDEX `idx_last_followup_time` (`last_followup_time`);

-- 6. 初始化默认公海规则
INSERT INTO `crm_pool_rules` (`rule_name`, `rule_type`, `days_limit`, `enabled`, `description`) VALUES
('7天未跟进自动进入公海', 'NO_FOLLOW', 7, 1, '客户7天未跟进将自动进入公海'),
('30天未成交自动进入公海', 'NO_DEAL', 30, 1, '客户30天未成交将自动进入公海'),
('手动放入公海', 'MANUAL', NULL, 1, '销售人员可以手动将客户放入公海');

-- 7. 初始化默认认领限制
INSERT INTO `crm_claim_limits` (`user_id`, `role_id`, `max_claim_daily`, `max_claim_total`, `enabled`) VALUES
(NULL, NULL, 10, 100, 1); -- 全局默认限制：每日最多认领10个，最多持有100个客户