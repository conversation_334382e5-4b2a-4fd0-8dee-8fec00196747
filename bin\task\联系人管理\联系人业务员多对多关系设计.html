<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人-业务员多对多关系设计方案</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- Prism.js for code highlighting -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #27ae60;
            border-bottom: 3px solid #27ae60;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .insight-box {
            background-color: #e8f5e8;
            border: 2px solid #27ae60;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .scenario-box {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .mermaid-diagram {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        .code-section {
            margin: 20px 0;
        }
        .code-title {
            background-color: #495057;
            color: white;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin: 0;
            font-weight: bold;
            font-size: 14px;
        }
        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0 0 5px 5px !important;
            font-size: 13px !important;
        }
        .business-flow {
            background-color: #fff3e0;
            padding: 20px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
        .solution-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .solution-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .advantage-list {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .advantage-item {
            margin: 8px 0;
            padding: 8px;
            background-color: white;
            border-left: 3px solid #28a745;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤝 联系人-业务员多对多关系设计方案</h1>
        
        <div class="insight-box">
            <h3>💡 核心业务理解</h3>
            <p><strong>你的业务场景完全正确！</strong>这是真实的B2B业务模式：</p>
            <ul>
                <li>✅ <strong>一个联系人</strong>可以有<strong>多种不同的业务需求</strong></li>
                <li>✅ <strong>不同的业务需求</strong>对应<strong>不同的专业业务员</strong></li>
                <li>✅ <strong>一个业务员</strong>可以服务<strong>多个联系人</strong></li>
                <li>✅ <strong>关系是动态的</strong>，随业务发展变化</li>
            </ul>
        </div>

        <h2>📊 真实业务场景</h2>

        <div class="scenario-box">
            <h4>🎯 典型案例：A公司的王五联系人</h4>
            <div class="business-flow">
                <p><strong>联系人：</strong>王五 - A公司研发部经理</p>
                <p><strong>业务场景1：</strong>需要玩具原型设计 → 联系业务员张三（原型专家）</p>
                <p><strong>业务场景2：</strong>需要包装设计服务 → 联系业务员李四（包装专家）</p>
                <p><strong>业务场景3：</strong>需要3D打印服务 → 联系业务员王五（3D打印专家）</p>
            </div>
            <p><strong>结果：</strong>王五这个联系人同时与3个业务员建立了业务关系！</p>
        </div>

        <div class="scenario-box">
            <h4>🎯 反向案例：张三业务员的客户网络</h4>
            <div class="business-flow">
                <p><strong>业务员：</strong>张三（原型设计专家）</p>
                <p><strong>联系人1：</strong>A公司王五 - 需要玩具原型</p>
                <p><strong>联系人2：</strong>B公司赵六 - 需要电子产品原型</p>
                <p><strong>联系人3：</strong>C公司李七 - 需要工业零件原型</p>
            </div>
            <p><strong>结果：</strong>张三业务员同时服务多个不同公司的联系人！</p>
        </div>

        <h2>🏗️ 最终架构设计：联系人-业务员关系表</h2>

        <div class="mermaid-diagram">
            <div class="mermaid">
erDiagram
    CrmCustomer {
        Long id PK
        String customerName
        String customerIndustry
        String status
        String delFlag
    }
    
    CrmContacts {
        Long id PK
        String name
        String department
        String position
        String mobile
        String email
        String status
        String delFlag
    }
    
    CrmCustomerContactRelation {
        Long id PK
        Long customerId FK
        Long contactId FK
        String relationType
        Integer isPrimary
        Date startDate
        String status
    }
    
    CrmContactResponsibleRelation {
        Long id PK
        Long contactId FK
        Long responsiblePersonId FK
        String businessType
        String relationStatus
        Date startDate
        Date endDate
        String remark
        Date createTime
        String createBy
    }
    
    CrmPoolOperationLog {
        Long id PK
        Long customerId FK
        Long contactId FK
        String operationType
        Long operatorId FK
        Long fromUserId FK
        Long toUserId FK
        String reason
        Date operationTime
    }
    
    SysUser {
        Long userId PK
        String userName
        String nickName
    }
    
    CrmCustomer ||--o{ CrmCustomerContactRelation : "has"
    CrmContacts ||--o{ CrmCustomerContactRelation : "belongs"
    CrmContacts ||--o{ CrmContactResponsibleRelation : "managed"
    SysUser ||--o{ CrmContactResponsibleRelation : "manages"
            </div>
        </div>

        <h2>📋 核心表结构设计</h2>

        <div class="code-section">
            <div class="code-title">联系人-业务员关系表 (crm_contact_responsible_relation)</div>
            <pre><code class="language-sql">CREATE TABLE crm_contact_responsible_relation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    contact_id BIGINT NOT NULL COMMENT '联系人ID',
    responsible_person_id BIGINT NOT NULL COMMENT '业务员ID',
    business_type VARCHAR(50) COMMENT '业务类型(原型设计/包装设计/3D打印/采购咨询等)',
    relation_status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '关系状态:ACTIVE有效/INACTIVE无效',
    start_date DATETIME NOT NULL COMMENT '开始服务时间',
    end_date DATETIME COMMENT '结束服务时间(NULL表示当前服务)',
    remark VARCHAR(500) COMMENT '备注说明',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    create_by VARCHAR(64) COMMENT '创建人',
    update_time DATETIME COMMENT '更新时间',
    update_by VARCHAR(64) COMMENT '更新人',
    
    -- 索引设计
    INDEX idx_contact_id (contact_id),
    INDEX idx_responsible_person_id (responsible_person_id),
    INDEX idx_business_type (business_type),
    INDEX idx_relation_status (relation_status),
    INDEX idx_start_date (start_date),
    
    -- 唯一约束：同一联系人+同一业务员+同一业务类型+有效状态，只能有一条记录
    UNIQUE KEY uk_contact_responsible_business_active (
        contact_id, responsible_person_id, business_type, relation_status
    ),
    
    -- 外键约束
    FOREIGN KEY (contact_id) REFERENCES crm_contacts(id),
    FOREIGN KEY (responsible_person_id) REFERENCES sys_user(user_id)
) COMMENT='联系人-业务员关系表';</code></pre>
        </div>

        <h2>💼 核心业务逻辑实现</h2>

        <div class="code-section">
            <div class="code-title">1. 联系人认领逻辑</div>
            <pre><code class="language-java">@Override
@Transactional
public int claimContact(Long contactId, String businessType, String remark) {
    Long responsiblePersonId = SecurityUtils.getUserId();
    
    // 1. 检查是否已经建立了该业务类型的关系
    CrmContactResponsibleRelation existing = contactResponsibleMapper
        .selectActiveRelation(contactId, responsiblePersonId, businessType);
    if (existing != null) {
        throw new ServiceException("您已经负责该联系人的" + businessType + "业务");
    }
    
    // 2. 检查认领限制
    if (!checkClaimLimit(responsiblePersonId, 1)) {
        throw new ServiceException("超出认领限制");
    }
    
    // 3. 创建新的负责关系
    CrmContactResponsibleRelation relation = new CrmContactResponsibleRelation();
    relation.setContactId(contactId);
    relation.setResponsiblePersonId(responsiblePersonId);
    relation.setBusinessType(businessType);
    relation.setRelationStatus("ACTIVE");
    relation.setStartDate(new Date());
    relation.setRemark(remark);
    
    int result = contactResponsibleMapper.insert(relation);
    
    // 4. 记录操作日志
    recordContactClaimLog(contactId, businessType, responsiblePersonId);
    
    return result;
}

@Override
@Transactional  
public int batchClaimContacts(List&lt;Long&gt; contactIds, String businessType) {
    Long responsiblePersonId = SecurityUtils.getUserId();
    
    // 检查认领限制
    if (!checkClaimLimit(responsiblePersonId, contactIds.size())) {
        throw new ServiceException("超出认领限制");
    }
    
    int successCount = 0;
    for (Long contactId : contactIds) {
        try {
            claimContact(contactId, businessType, "批量认领");
            successCount++;
        } catch (Exception e) {
            // 记录失败原因，继续处理其他联系人
            log.warn("认领联系人{}失败: {}", contactId, e.getMessage());
        }
    }
    
    return successCount;
}</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">2. 查询逻辑</div>
            <pre><code class="language-java">/**
 * 获取联系人的所有业务员
 */
@Override
public List&lt;CrmContactResponsibleRelation&gt; getContactResponsiblePersons(Long contactId) {
    return contactResponsibleMapper.selectActiveRelationsByContactId(contactId);
}

/**
 * 获取业务员负责的所有联系人
 */
@Override
public List&lt;CrmContacts&gt; getMyContacts(Long responsiblePersonId, String businessType) {
    return contactResponsibleMapper.selectContactsByResponsiblePerson(
        responsiblePersonId, businessType);
}

/**
 * 获取客户的所有负责人（通过联系人间接获取）
 */
@Override
public List&lt;ResponsiblePersonVO&gt; getCustomerResponsiblePersons(Long customerId) {
    return contactResponsibleMapper.selectResponsiblePersonsByCustomerId(customerId);
}

/**
 * 获取公海联系人（无任何业务员负责的联系人）
 */
@Override
public List&lt;CrmContacts&gt; getPoolContacts(String businessType) {
    return contactResponsibleMapper.selectContactsWithoutResponsible(businessType);
}</code></pre>
        </div>

        <div class="code-section">
            <div class="code-title">3. 公海逻辑</div>
            <pre><code class="language-java">/**
 * 将联系人的某个业务关系放入公海
 */
@Override
@Transactional
public int returnContactToPool(Long contactId, Long responsiblePersonId, 
                              String businessType, String reason, String remark) {
    
    // 1. 查找现有关系
    CrmContactResponsibleRelation relation = contactResponsibleMapper
        .selectActiveRelation(contactId, responsiblePersonId, businessType);
    if (relation == null) {
        throw new ServiceException("未找到对应的负责关系");
    }
    
    // 2. 验证权限（只能放入自己负责的）
    Long currentUserId = SecurityUtils.getUserId();
    if (!currentUserId.equals(responsiblePersonId)) {
        throw new ServiceException("只能放入自己负责的联系人业务");
    }
    
    // 3. 结束关系
    relation.setRelationStatus("INACTIVE");
    relation.setEndDate(new Date());
    relation.setRemark(relation.getRemark() + "; 放入公海原因: " + reason);
    
    int result = contactResponsibleMapper.updateById(relation);
    
    // 4. 记录操作日志
    recordContactReturnLog(contactId, businessType, responsiblePersonId, reason, remark);
    
    return result;
}</code></pre>
        </div>

        <h2>📈 数据迁移策略</h2>

        <div class="code-section">
            <div class="code-title">现有数据迁移</div>
            <pre><code class="language-sql">-- 1. 从联系人表的现有负责人字段迁移到关系表
INSERT INTO crm_contact_responsible_relation (
    contact_id, responsible_person_id, business_type, 
    relation_status, start_date, create_time
)
SELECT 
    id, responsible_person_id, '综合业务',
    'ACTIVE', COALESCE(create_time, NOW()), NOW()
FROM crm_contacts 
WHERE responsible_person_id IS NOT NULL
AND responsible_person_id != '';

-- 2. 从客户表迁移：为没有联系人的客户创建默认联系人+关系
-- 第一步：创建默认联系人
INSERT INTO crm_contacts (
    name, department, position, mobile, email, 
    status, del_flag, create_time
)
SELECT 
    CONCAT(customer_name, '-默认联系人'), '综合部', '联系人', 
    '', '', '1', '0', NOW()
FROM crm_customer 
WHERE responsible_person_id IS NOT NULL
AND id NOT IN (
    SELECT DISTINCT customer_id 
    FROM crm_customer_contact_relation 
    WHERE status = 'active'
);

-- 第二步：建立客户-联系人关系
INSERT INTO crm_customer_contact_relation (
    customer_id, contact_id, relation_type, is_primary, 
    start_date, status, create_time
)
SELECT 
    c.id, ct.id, '主要联系人', 1,
    NOW(), 'active', NOW()
FROM crm_customer c
INNER JOIN crm_contacts ct ON ct.name = CONCAT(c.customer_name, '-默认联系人')
WHERE c.responsible_person_id IS NOT NULL;

-- 第三步：建立联系人-业务员关系
INSERT INTO crm_contact_responsible_relation (
    contact_id, responsible_person_id, business_type,
    relation_status, start_date, create_time
)
SELECT 
    ct.id, c.responsible_person_id, '综合业务',
    'ACTIVE', NOW(), NOW()
FROM crm_customer c
INNER JOIN crm_contacts ct ON ct.name = CONCAT(c.customer_name, '-默认联系人')
WHERE c.responsible_person_id IS NOT NULL;</code></pre>
        </div>

        <h2>✨ 方案优势</h2>

        <div class="advantage-list">
            <div class="advantage-item">
                <strong>🎯 业务贴合度：</strong>完全符合真实B2B业务场景，一个联系人确实可能需要多种不同的专业服务
            </div>
            <div class="advantage-item">
                <strong>🔄 灵活性：</strong>业务关系可以动态建立和结束，支持业务发展变化
            </div>
            <div class="advantage-item">
                <strong>📊 统计完整性：</strong>可以按业务类型、按客户、按业务员多维度进行数据分析
            </div>
            <div class="advantage-item">
                <strong>🤝 协作性：</strong>不同专业的业务员可以同时服务同一个联系人，提升服务质量
            </div>
            <div class="advantage-item">
                <strong>📈 扩展性：</strong>可以轻松添加新的业务类型，支持业务扩展
            </div>
            <div class="advantage-item">
                <strong>🔍 追溯性：</strong>完整记录业务关系的历史变更，支持业务分析和审计
            </div>
        </div>

        <h2>⚡ 实施计划</h2>

        <div class="solution-grid">
            <div class="solution-card">
                <h4>📅 第1-2天：表结构和数据</h4>
                <ul>
                    <li>创建联系人-业务员关系表</li>
                    <li>设计合理的索引和约束</li>
                    <li>执行数据迁移脚本</li>
                    <li>验证数据完整性</li>
                </ul>
            </div>
            <div class="solution-card">
                <h4>📅 第3-4天：业务逻辑</h4>
                <ul>
                    <li>实现联系人认领逻辑</li>
                    <li>实现公海查询逻辑</li>
                    <li>实现多维度查询接口</li>
                    <li>完善异常处理</li>
                </ul>
            </div>
        </div>

        <div class="solution-grid">
            <div class="solution-card">
                <h4>📅 第5天：测试修复</h4>
                <ul>
                    <li>修复原有的集成测试</li>
                    <li>新增多对多关系测试</li>
                    <li>验证并发场景</li>
                    <li>性能测试和优化</li>
                </ul>
            </div>
            <div class="solution-card">
                <h4>📅 第6天：完善优化</h4>
                <ul>
                    <li>完善操作日志记录</li>
                    <li>优化查询性能</li>
                    <li>补充业务规则验证</li>
                    <li>文档更新</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #e8f5e8; border-radius: 5px;">
            <h3>🎉 最终确认</h3>
            <p><strong>✅ 联系人-业务员多对多关系表设计</strong></p>
            <p><strong>✅ 支持一个联系人对应多个不同专业的业务员</strong></p>
            <p><strong>✅ 支持一个业务员服务多个联系人</strong></p>
            <p><strong>✅ 完全基于现有架构，最小改动实现最大价值</strong></p>
            <p style="color: #27ae60; font-weight: bold;">这个方案完全符合您的业务需求！🚀</p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#333333',
                fill: '#ececff',
                fontSize: 12
            }
        });

        // Auto highlight code blocks
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>