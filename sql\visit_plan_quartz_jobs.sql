-- ================================================
-- 拜访计划功能定时任务配置脚本
-- 创建时间：2024-06-30
-- 说明：配置Quartz定时任务
-- ================================================

-- 1. 拜访计划提醒处理任务（每5分钟执行一次）
INSERT INTO `sys_job` (`job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访计划提醒处理', 'DEFAULT', 'visitPlanTask.processReminders', '0 */5 * * * ?', '3', '1', '0', 'admin', NOW(), '', NULL, '每5分钟执行一次，处理待发送的拜访计划提醒');

-- 2. 拜访计划状态自动更新任务（每小时执行一次）
INSERT INTO `sys_job` (`job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访计划状态自动更新', 'DEFAULT', 'visitPlanTask.autoUpdateStatus', '0 0 * * * ?', '3', '1', '0', 'admin', NOW(), '', NULL, '每小时执行一次，自动更新过期拜访计划的状态');

-- 3. 拜访计划提醒记录清理任务（每天凌晨2点执行）
INSERT INTO `sys_job` (`job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访计划提醒记录清理', 'DEFAULT', 'visitPlanTask.cleanExpiredReminders', '0 0 2 * * ?', '3', '1', '0', 'admin', NOW(), '', NULL, '每天凌晨2点执行，清理过期的提醒记录');

-- 4. 拜访计划统计报告生成任务（每天凌晨1点执行）
INSERT INTO `sys_job` (`job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
('拜访计划统计报告生成', 'DEFAULT', 'visitPlanTask.generateStatisticsReport', '0 0 1 * * ?', '3', '1', '0', 'admin', NOW(), '', NULL, '每天凌晨1点执行，生成拜访计划统计报告');

COMMIT;

-- 验证定时任务插入结果
SELECT job_id, job_name, job_group, invoke_target, cron_expression, status, remark 
FROM sys_job 
WHERE invoke_target LIKE 'visitPlanTask%'
ORDER BY job_id;

-- Cron表达式说明：
-- 0 */5 * * * ?    每5分钟执行一次
-- 0 0 * * * ?      每小时执行一次
-- 0 0 2 * * ?      每天凌晨2点执行
-- 0 0 1 * * ?      每天凌晨1点执行

-- 任务状态说明：
-- 0: 正常
-- 1: 暂停

-- 执行策略说明（misfire_policy）：
-- 1: 立即执行
-- 2: 执行一次
-- 3: 放弃执行

-- 并发执行说明（concurrent）：
-- 0: 允许并发
-- 1: 禁止并发
