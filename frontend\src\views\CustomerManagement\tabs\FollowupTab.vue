<template>
    <div class="followup-tab">
        <div class="tab-header">
            <h3>跟进记录</h3>
            <div class="header-actions">
                <el-button 
                    type="primary" 
                    size="small"
                    @click="openFollowupDialog"
                >
                    <el-icon><Plus /></el-icon>
                    新增跟进记录
                </el-button>
                <el-button 
                    type="success" 
                    size="small"
                    @click="handleExportFollowups"
                >
                    <el-icon><Download /></el-icon>
                    导出记录
                </el-button>
            </div>
        </div>

        <!-- 筛选组件 -->
        <common-filter
            v-model:searchValue="searchInput"
            v-model:filterValue="filterType"
            :config="filterConfig"
            @search="handleSearch"
            @filter="handleFilterChange"
        />

        <!-- 跟进记录列表 -->
        <el-table 
            :data="followupRecords" 
            v-loading="loading"
            border 
            sortable 
            tooltip-effect="dark"
            :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
            style="width: 100%; margin-top: 16px; border-radius: 10px; box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);"
            @selection-change="handleSelectionChange">
            
            <el-table-column type="selection" width="55" />
            
            <el-table-column prop="customerName" label="客户名称" min-width="150">
                <template #default="scope">
                    <el-button link type="primary" @click="viewCustomerDetail(scope.row)">
                        {{ scope.row.customerName }}
                    </el-button>
                </template>
            </el-table-column>
            
            <el-table-column prop="followupType" label="跟进类型" min-width="100">
                <template #default="scope">
                    <el-tag :type="getFollowupTypeColor(scope.row.followupType)" size="small">
                        {{ getFollowupTypeName(scope.row.followupType) }}
                    </el-tag>
                </template>
            </el-table-column>
            
            
            <el-table-column prop="followupContent" label="跟进内容" min-width="300" show-overflow-tooltip />
            
            
            
            <el-table-column prop="userName" label="跟进人" min-width="100">
                <template #default="scope">
                    {{ scope.row.userName || scope.row.userNickName || '-' }}
                </template>
            </el-table-column>
            
            <el-table-column prop="createTime" label="跟进时间" min-width="150" />
            
            
            <el-table-column label="操作" width="180" fixed="right">
                <template #default="scope">
                    <el-button 
                        type="primary" 
                        link 
                        size="small" 
                        @click="viewFollowupDetail(scope.row)"
                    >
                        <el-icon><View /></el-icon>
                        查看
                    </el-button>
                    <el-button 
                        type="warning" 
                        link 
                        size="small" 
                        @click="editFollowup(scope.row)"
                    >
                        <el-icon><Edit /></el-icon>
                        编辑
                    </el-button>
                    <el-button 
                        type="danger" 
                        link 
                        size="small" 
                        @click="deleteFollowup(scope.row)"
                    >
                        <el-icon><Delete /></el-icon>
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        
        <!-- 分页组件 -->
        <pagination 
            v-show="totalFollowups > 0" 
            :total="totalFollowups" 
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handlePagination" 
        />

        <!-- 新增/编辑跟进记录对话框 -->
        <el-dialog 
            v-model="dialogVisible" 
            :title="dialogTitle"
            width="700px"
            :before-close="handleDialogClose"
        >
            <el-form :model="followupForm" :rules="followupRules" ref="followupFormRef" label-width="120px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="关联客户" prop="customerId">
                            <el-select 
                                v-model="followupForm.customerId" 
                                placeholder="请选择客户" 
                                style="width: 100%"
                                filterable
                                remote
                                :remote-method="searchCustomers"
                                :loading="customerSearchLoading"
                            >
                                <el-option 
                                    v-for="customer in customerOptions" 
                                    :key="customer.id" 
                                    :label="customer.customerName" 
                                    :value="customer.id" 
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="跟进类型" prop="followupType">
                            <el-select v-model="followupForm.followupType" placeholder="请选择跟进类型" style="width: 100%">
                                <el-option label="电话沟通" value="call" />
                                <el-option label="面访" value="visit" />
                                <el-option label="邮件" value="email" />
                                <el-option label="微信" value="wechat" />
                                <el-option label="短信" value="sms" />
                                <el-option label="其他" value="other" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                
                <el-form-item label="跟进内容" prop="followupContent">
                    <el-input 
                        v-model="followupForm.followupContent" 
                        type="textarea" 
                        :rows="4" 
                        placeholder="请详细描述跟进内容"
                    />
                </el-form-item>
                
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="handleDialogClose">取消</el-button>
                    <el-button type="primary" @click="handleSaveFollowup" :loading="saving">
                        {{ isEdit ? '更新' : '创建' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 跟进记录详情对话框 -->
        <el-dialog v-model="detailDialogVisible" title="跟进记录详情" width="600px">
            <div v-if="selectedFollowup" class="followup-detail">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="客户名称">{{ selectedFollowup.customerName }}</el-descriptions-item>
                    <el-descriptions-item label="跟进类型">
                        <el-tag :type="getFollowupTypeColor(selectedFollowup.followupType)" size="small">
                            {{ getFollowupTypeName(selectedFollowup.followupType) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="跟进内容" :span="2">
                        <div class="content-text">{{ selectedFollowup.followupContent }}</div>
                    </el-descriptions-item>
                    <el-descriptions-item label="跟进人">{{ selectedFollowup.userName || selectedFollowup.userNickName || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="跟进时间">{{ selectedFollowup.createTime }}</el-descriptions-item>
                </el-descriptions>
            </div>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import { 
    Plus,
    Download,
    View,
    Edit,
    Delete
} from '@element-plus/icons-vue';

import CommonFilter from '@/components/CommonFilter/index.vue';
import Pagination from '@/components/Pagination/index.vue';

import type { CustomerData } from '@/api/crm/customer/types';
import request from '@/utils/request';

// Props
interface Props {
    customerId?: number;
    customerName?: string;
}

const props = defineProps<Props>();
const route = useRoute();

// 跟进记录类型定义
interface FollowupRecord {
    id?: number;
    customerId: number;
    customerName?: string;
    followupType: string;
    followupContent: string;
    createTime?: string;
    updateTime?: string;
    creatorId?: number;
    userName?: string;
    userNickName?: string;
}

// 状态管理
const loading = ref(false);
const saving = ref(false);
const customerSearchLoading = ref(false);

// 表单引用
const followupFormRef = ref();

// 数据
const followupRecords = ref<FollowupRecord[]>([]);
const selectedFollowups = ref<FollowupRecord[]>([]);
const customerOptions = ref<CustomerData[]>([]);

// 统计
const totalFollowups = ref(0);

// 弹窗状态
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);

// 选中的跟进记录
const selectedFollowup = ref<FollowupRecord | null>(null);

// 搜索和筛选
const searchInput = ref('');
const filterType = ref('all');

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    customerId: props.customerId || Number(route.params.customerId) || undefined,
    customerName: '',
    followupType: '',
    followupResult: '',
    followupUserName: '',
    startTime: '',
    endTime: ''
});

// 跟进记录表单
const followupForm = ref<FollowupRecord>({
    customerId: 0,
    customerName: '',
    followupType: '',
    followupContent: '',
    createTime: '',
    creatorId: 0
});

// 计算属性
const isEdit = computed(() => !!followupForm.value.id);
const dialogTitle = computed(() => isEdit.value ? '编辑跟进记录' : '新增跟进记录');

// 表单验证规则
const followupRules = {
    customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
    followupType: [{ required: true, message: '请选择跟进类型', trigger: 'change' }],
    followupContent: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }]
};

// 筛选配置
const filterConfig = {
    search: {
        placeholder: '搜索客户名称、跟进内容',
        width: '240px',
        icon: 'Search',
        debounceTime: 300
    },
    filter: {
        label: '筛选：',
        options: [
            { label: '全部', value: 'all' },
            { label: '电话沟通', value: 'call' },
            { label: '面访', value: 'visit' },
            { label: '邮件', value: 'email' },
            { label: '微信', value: 'wechat' },
            { label: '短信', value: 'sms' },
            { label: '其他', value: 'other' }
        ],
        buttonStyle: true,
        size: 'default'
    }
};

// 生命周期
onMounted(() => {
    loadFollowupRecords();
});

// 方法
const loadFollowupRecords = async () => {
    loading.value = true;
    try {
        const response = await getFollowupRecords(queryParams);
        if (response.code === 200) {
            followupRecords.value = response.rows || [];
            totalFollowups.value = response.total || 0;
        }
    } catch (error) {
        ElMessage.error('加载跟进记录失败');
    } finally {
        loading.value = false;
    }
};

const handleSelectionChange = (selection: FollowupRecord[]) => {
    selectedFollowups.value = selection;
};

const openFollowupDialog = () => {
    // 检查是否有传入的客户ID或从路由获取
    const currentCustomerId = props.customerId || Number(route.params.customerId) || 0;
    const currentCustomerName = props.customerName || '';
    
    console.log('打开跟进对话框，客户ID:', currentCustomerId);
    
    followupForm.value = {
        customerId: currentCustomerId,
        customerName: currentCustomerName,
        followupType: '',
        followupContent: '',
        createTime: '',
        creatorId: 0
    };
    
    // 如果有客户ID，将其添加到客户选项中
    if (currentCustomerId && currentCustomerName) {
        customerOptions.value = [{
            id: currentCustomerId,
            customerName: currentCustomerName
        } as CustomerData];
    }
    
    dialogVisible.value = true;
};

const editFollowup = (followup: FollowupRecord) => {
    followupForm.value = { ...followup };
    dialogVisible.value = true;
};

const handleDialogClose = () => {
    dialogVisible.value = false;
    followupFormRef.value?.resetFields();
};

const handleSaveFollowup = async () => {
    console.log('点击保存按钮');
    console.log('表单数据:', followupForm.value);
    
    try {
        // 验证表单
        await followupFormRef.value?.validate();
        console.log('表单验证通过');
        
        saving.value = true;
        let response;
        if (isEdit.value) {
            response = await updateFollowupRecord(followupForm.value);
        } else {
            console.log('准备创建新跟进记录');
            response = await createFollowupRecord(followupForm.value);
        }
        
        console.log('API响应:', response);
        
        if (response.code === 200) {
            ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
            dialogVisible.value = false;
            loadFollowupRecords();
        } else {
            ElMessage.error(response.msg || '保存失败');
        }
    } catch (error) {
        console.error('保存失败:', error);
        if (error !== 'cancel' && error !== false) {
            ElMessage.error('保存失败');
        }
    } finally {
        saving.value = false;
    }
};


const deleteFollowup = async (followup: FollowupRecord) => {
    try {
        await ElMessageBox.confirm(`确认删除该跟进记录吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });
        
        const response = await deleteFollowupRecord(followup.id!);
        if (response.code === 200) {
            ElMessage.success('删除成功');
            loadFollowupRecords();
        } else {
            ElMessage.error(response.msg || '删除失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('删除失败');
        }
    }
};

const viewFollowupDetail = (followup: FollowupRecord) => {
    selectedFollowup.value = followup;
    detailDialogVisible.value = true;
};

const viewCustomerDetail = (followup: FollowupRecord) => {
    // TODO: 打开客户详情
    ElMessage.info('查看客户详情功能开发中');
};

const searchCustomers = async (query: string) => {
    if (!query) return;
    customerSearchLoading.value = true;
    try {
        const response = await searchCustomersApi(query);
        if (response.code === 200) {
            customerOptions.value = response.rows || [];
        }
    } catch (error) {
        ElMessage.error('搜索客户失败');
    } finally {
        customerSearchLoading.value = false;
    }
};

const handleExportFollowups = () => {
    ElMessage.info('导出功能开发中');
};

const handleSearch = (value: string) => {
    queryParams.customerName = value;
    queryParams.pageNum = 1;
    loadFollowupRecords();
};

const handleFilterChange = (value: string) => {
    // 重置筛选参数
    Object.assign(queryParams, {
        followupType: '',
        pageNum: 1
    });
    
    if (['call', 'visit', 'email', 'wechat', 'sms', 'other'].includes(value)) {
        queryParams.followupType = value;
    }
    
    loadFollowupRecords();
};

const handlePagination = (val: { page: number; limit: number }) => {
    queryParams.pageNum = val.page;
    queryParams.pageSize = val.limit;
    loadFollowupRecords();
};

// 工具方法
const getFollowupTypeName = (type: string) => {
    const typeMap: Record<string, string> = {
        call: '电话沟通',
        visit: '面访',
        email: '邮件',
        wechat: '微信',
        sms: '短信',
        other: '其他'
    };
    return typeMap[type] || type;
};

const getFollowupTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
        call: 'primary',
        visit: 'success',
        email: 'info',
        wechat: 'warning',
        sms: 'danger',
        other: ''
    };
    return colorMap[type] || '';
};


// API 函数
const getFollowupRecords = (params: any) => {
    return request({
        url: '/front/crm/customer/followup/list',
        method: 'get',
        params
    });
};

const createFollowupRecord = (data: FollowupRecord) => {
    console.log('创建跟进记录，发送数据:', {
        customerId: data.customerId,
        followupType: data.followupType,
        followupContent: data.followupContent
    });
    
    return request({
        url: '/front/crm/customer/followup',
        method: 'post',
        data: {
            customerId: data.customerId,
            followupType: data.followupType,
            followupContent: data.followupContent
        }
    });
};

const updateFollowupRecord = (data: FollowupRecord) => {
    return request({
        url: '/front/crm/customer/followup',
        method: 'put',
        data: {
            id: data.id,
            customerId: data.customerId,
            followupType: data.followupType,
            followupContent: data.followupContent
        }
    });
};

const deleteFollowupRecord = (id: number) => {
    return request({
        url: `/front/crm/customer/followup/${id}`,
        method: 'delete'
    });
};

const searchCustomersApi = (query: string) => {
    return request({
        url: '/front/crm/customers/list',
        method: 'get',
        params: { 
            customerName: query,
            pageNum: 1,
            pageSize: 20
        }
    });
};
</script>

<style scoped>
.followup-tab {
    padding: 20px;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tab-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #303133;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.text-danger {
    color: #f56c6c;
}

.text-muted {
    color: #909399;
}

.overdue-icon {
    margin-left: 4px;
    color: #f56c6c;
}

.followup-detail .content-text {
    white-space: pre-wrap;
    word-break: break-word;
}
</style>