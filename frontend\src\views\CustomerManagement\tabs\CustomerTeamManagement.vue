<template>
  <div class="customer-team-management">
    <!-- 团队分配状态 -->
    <div class="team-assignment-section">
      <h3>团队分配状态</h3>
      <div v-if="teamInfo" class="team-info">
        <div class="team-card">
          <div class="team-header">
            <span class="team-name">{{ teamInfo.teamName }}</span>
            <span class="team-leader">负责人：{{ teamInfo.leaderName }}</span>
          </div>
          <div class="team-meta">
            <span class="assign-time">分配时间：{{ formatDate(teamInfo.assignTime) }}</span>
          </div>
          <div class="team-actions">
            <el-button type="primary" size="small" @click="openTeamAssignDialog">重新分配</el-button>
            <el-button type="danger" size="small" @click="unassignTeam">取消分配</el-button>
          </div>
        </div>
      </div>
      <div v-else class="no-team">
        <p>暂未分配团队</p>
        <el-button type="primary" @click="openTeamAssignDialog">分配团队</el-button>
      </div>
    </div>

    <!-- 团队成员列表 -->
    <div class="team-members-section" v-if="teamInfo">
      <div class="section-header">
        <h3>团队成员列表</h3>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="openAddMemberDialog">添加成员</el-button>
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="list">列表</el-radio-button>
            <el-radio-button label="grid">网格</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="members-list">
        <el-table :data="teamMembers" style="width: 100%">
          <el-table-column label="成员" width="200">
            <template #default="{ row }">
              <div class="member-info">
                <el-avatar :size="32" :src="row.avatar" class="member-avatar">
                  {{ row.realName?.charAt(0) }}
                </el-avatar>
                <div class="member-details">
                  <div class="member-name">{{ row.realName }}</div>
                  <div class="member-username">@{{ row.userName }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="角色" width="120">
            <template #default="{ row }">
              <el-tag :type="getRoleTagType(row.roleType)" size="small">
                {{ getRoleLabel(row.roleType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="加入时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.joinTime) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'info'" size="small">
                {{ row.status === 'ACTIVE' ? '活跃' : '非活跃' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="所属团队" width="150">
            <template #default="{ row }">
              {{ row.teamName }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewMemberDetail(row)">详情</el-button>
              <el-button type="text" size="small" @click="removeMember(row)" style="color: #f56c6c">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 网格视图 -->
      <div v-else class="members-grid">
        <div v-for="member in teamMembers" :key="member.id" class="member-card">
          <div class="member-card-header">
            <el-avatar :size="48" :src="member.avatar" class="member-avatar">
              {{ member.realName?.charAt(0) }}
            </el-avatar>
            <div class="member-info">
              <div class="member-name">{{ member.realName }}</div>
              <div class="member-username">@{{ member.userName }}</div>
            </div>
          </div>
          <div class="member-card-body">
            <div class="member-role">
              <el-tag :type="getRoleTagType(member.roleType)" size="small">
                {{ getRoleLabel(member.roleType) }}
              </el-tag>
            </div>
            <div class="member-meta">
              <div>加入时间：{{ formatDate(member.joinTime) }}</div>
              <div>团队：{{ member.teamName }}</div>
            </div>
          </div>
          <div class="member-card-actions">
            <el-button type="text" size="small" @click="viewMemberDetail(member)">详情</el-button>
            <el-button type="text" size="small" @click="removeMember(member)" style="color: #f56c6c">移除</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 团队分配对话框 -->
    <TeamAssignDialog
      :visible="teamAssignDialogVisible"
      :biz-type="'CUSTOMER'"
      :biz-id="entityId"
      @update:visible="teamAssignDialogVisible = $event"
      @success="handleTeamAssignSuccess"
    />

    <!-- 添加成员对话框 -->
    <el-dialog
      title="添加团队成员"
      v-model="addMemberDialogVisible"
      width="600px"
      :before-close="handleAddMemberDialogClose"
    >
      <div class="add-member-form">
        <el-form :model="addMemberForm" label-width="80px">
          <el-form-item label="选择用户">
            <el-select
              v-model="addMemberForm.userIds"
              multiple
              filterable
              placeholder="请选择要添加的用户"
              style="width: 100%"
              @focus="loadAvailableUsers"
            >
              <el-option
                v-for="user in availableUsers"
                :key="user.userId"
                :label="`${user.realName} (@${user.userName})`"
                :value="user.userId"
              >
                <div class="user-option">
                  <el-avatar :size="24" :src="user.avatar" class="user-avatar">
                    {{ user.realName?.charAt(0) }}
                  </el-avatar>
                  <span class="user-name">{{ user.realName }}</span>
                  <span class="user-username">@{{ user.userName }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="addMemberForm.roleType" placeholder="请选择角色">
              <el-option label="负责人" value="OWNER"></el-option>
              <el-option label="协作者" value="COLLABORATOR"></el-option>
              <el-option label="只读" value="READER"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addMemberDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddMembers" :loading="addMemberLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 成员详情对话框 -->
    <el-dialog
      title="成员详情"
      v-model="memberDetailDialogVisible"
      width="500px"
    >
      <div v-if="selectedMember" class="member-detail">
        <div class="member-detail-header">
          <el-avatar :size="64" :src="selectedMember.avatar" class="member-avatar">
            {{ selectedMember.realName?.charAt(0) }}
          </el-avatar>
          <div class="member-info">
            <h3>{{ selectedMember.realName }}</h3>
            <p>@{{ selectedMember.userName }}</p>
          </div>
        </div>
        <div class="member-detail-body">
          <div class="detail-item">
            <label>角色：</label>
            <el-tag :type="getRoleTagType(selectedMember.roleType)" size="small">
              {{ getRoleLabel(selectedMember.roleType) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>加入时间：</label>
            <span>{{ formatDate(selectedMember.joinTime) }}</span>
          </div>
          <div class="detail-item">
            <label>状态：</label>
            <el-tag :type="selectedMember.status === 'ACTIVE' ? 'success' : 'info'" size="small">
              {{ selectedMember.status === 'ACTIVE' ? '活跃' : '非活跃' }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>所属团队：</label>
            <span>{{ selectedMember.teamName }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import TeamAssignDialog from '@/components/TeamAssignDialog.vue'
import {
  getTeamByBiz,
  getTeamMembersByBiz,
  getTeamMembersByTeamId,
  unassignTeamFromBiz,
  batchAddTeamMembers,
  removeTeamMember,
  getAvailableUsers
} from '@/api/team-relation'

// Props
const props = defineProps({
  entityData: {
    type: Object,
    required: true
  }
})

// 计算属性获取entityId
const entityId = computed(() => {
  return props.entityData?.id || props.entityData?.customerId || null
})

// Store
const userStore = useUserStore()

// 响应式数据
const teamInfo = ref(null)
const teamMembers = ref([])
const viewMode = ref('list')
const teamAssignDialogVisible = ref(false)
const addMemberDialogVisible = ref(false)
const memberDetailDialogVisible = ref(false)
const selectedMember = ref(null)
const availableUsers = ref([])
const addMemberLoading = ref(false)

// 添加成员表单
const addMemberForm = reactive({
  userIds: [],
  roleType: 'COLLABORATOR'
})

// 权限检查
const hasPermission = (permission) => {
  return userStore.permissions.includes(permission)
}

// 方法
const openTeamAssignDialog = () => {
  teamAssignDialogVisible.value = true
}

const openAddMemberDialog = () => {
  addMemberDialogVisible.value = true
  addMemberForm.userIds = []
  addMemberForm.roleType = 'COLLABORATOR'
}

const handleAddMemberDialogClose = () => {
  addMemberDialogVisible.value = false
  addMemberForm.userIds = []
  addMemberForm.roleType = 'COLLABORATOR'
}

const viewMemberDetail = (member) => {
  selectedMember.value = member
  memberDetailDialogVisible.value = true
}

const loadAvailableUsers = async () => {
  try {
    const response = await getAvailableUsers()
    availableUsers.value = response.data || []
  } catch (error) {
    console.error('加载可用用户失败:', error)
    ElMessage.error('加载可用用户失败')
  }
}

const removeMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除成员 "${member.realName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await removeTeamMember(member.id)
    ElMessage.success('移除成员成功')
    await loadTeamMembers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error)
      ElMessage.error('移除成员失败')
    }
  }
}

const handleAddMembers = async () => {
  if (!addMemberForm.userIds.length) {
    ElMessage.warning('请选择要添加的用户')
    return
  }

  if (!addMemberForm.roleType) {
    ElMessage.warning('请选择角色')
    return
  }

  addMemberLoading.value = true
  try {
    await batchAddTeamMembers(
      teamInfo.value.teamId,
      addMemberForm.userIds,
      addMemberForm.roleType
    )
    ElMessage.success('添加成员成功')
    addMemberDialogVisible.value = false
    await loadTeamMembers()
  } catch (error) {
    console.error('添加成员失败:', error)
    ElMessage.error('添加成员失败')
  } finally {
    addMemberLoading.value = false
  }
}

const unassignTeam = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消团队分配吗？取消后将无法查看团队成员信息。',
      '确认取消分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await unassignTeamFromBiz(entityId.value, 'CUSTOMER')
    ElMessage.success('取消团队分配成功')
    await loadAllData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消团队分配失败:', error)
      ElMessage.error('取消团队分配失败')
    }
  }
}

const handleTeamAssignSuccess = () => {
  teamAssignDialogVisible.value = false
  loadAllData()
}

// 辅助方法
const getRoleTagType = (roleType) => {
  const typeMap = {
    'OWNER': 'danger',
    'COLLABORATOR': 'primary',
    'READER': 'info'
  }
  return typeMap[roleType] || 'info'
}

const getRoleLabel = (roleType) => {
  const labelMap = {
    'OWNER': '负责人',
    'COLLABORATOR': '协作者',
    'READER': '只读'
  }
  return labelMap[roleType] || roleType
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 数据加载方法（优化版本，使用缓存）
let teamInfoCache = null
let teamMembersCache = null
let lastLoadTime = 0
const CACHE_DURATION = 30000 // 30秒缓存

const loadTeamInfo = async () => {
  const now = Date.now()
  if (teamInfoCache && (now - lastLoadTime) < CACHE_DURATION) {
    teamInfo.value = teamInfoCache
    return
  }

  try {
    const response = await getTeamByBiz(entityId.value, 'CUSTOMER')
    teamInfo.value = response.data
    teamInfoCache = response.data
    lastLoadTime = now
  } catch (error) {
    console.error('加载团队信息失败:', error)
    teamInfo.value = null
    teamInfoCache = null
  }
}

const loadTeamMembers = async () => {
  if (!teamInfo.value?.teamId) {
    teamMembers.value = []
    return
  }

  const now = Date.now()
  if (teamMembersCache && (now - lastLoadTime) < CACHE_DURATION) {
    teamMembers.value = teamMembersCache
    return
  }

  try {
    const response = await getTeamMembersByBiz(entityId.value, 'CUSTOMER')
    teamMembers.value = response.data || []
    teamMembersCache = response.data || []
  } catch (error) {
    console.error('加载团队成员失败:', error)
    teamMembers.value = []
    teamMembersCache = []
  }
}

const loadAllData = async () => {
  // 清除缓存
  teamInfoCache = null
  teamMembersCache = null
  lastLoadTime = 0
  
  await loadTeamInfo()
  await loadTeamMembers()
}

// 生命周期
onMounted(() => {
  if (entityId.value) {
    loadAllData()
  }
})

// 监听 entityId 变化
watch(
  () => entityId.value,
  (newId) => {
    if (newId) {
      loadAllData()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.customer-team-management {
  padding: 20px;
}

.team-assignment-section {
  margin-bottom: 30px;
}

.team-assignment-section h3 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.team-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.team-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.team-leader {
  color: #606266;
  font-size: 14px;
}

.team-meta {
  margin-bottom: 15px;
  color: #909399;
  font-size: 12px;
}

.team-actions {
  display: flex;
  gap: 10px;
}

.no-team {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.team-members-section h3 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.member-details {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-weight: 600;
  color: #303133;
}

.member-username {
  font-size: 12px;
  color: #909399;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.member-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  background: #fff;
  transition: box-shadow 0.2s;
}

.member-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.member-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
}

.member-card-body {
  margin-bottom: 15px;
}

.member-role {
  margin-bottom: 10px;
}

.member-meta {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

.member-card-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.add-member-form {
  padding: 20px 0;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-name {
  font-weight: 600;
}

.user-username {
  color: #909399;
  font-size: 12px;
}

.member-detail {
  padding: 20px 0;
}

.member-detail-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.member-detail-header h3 {
  margin: 0;
  color: #303133;
}

.member-detail-header p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.member-detail-body {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
}
</style>