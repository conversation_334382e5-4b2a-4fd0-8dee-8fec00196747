-- CRM无密码认证系统快速部署脚本
-- 适用于已有CRM系统的快速升级
-- 日期：2025-06-30
-- 版本：v1.0 - 最小化快速部署

-- ==============================================
-- 快速创建核心表（最小化版本）
-- ==============================================

-- 1. 用户注册表（核心表）
CREATE TABLE IF NOT EXISTS `crm_user_registration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `phone_number` varchar(20) NOT NULL,
  `registration_type` varchar(20) NOT NULL DEFAULT 'sms',
  `verification_code` varchar(10) DEFAULT NULL,
  `verification_expire_time` datetime DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `user_id` bigint(20) DEFAULT NULL,
  `status` varchar(20) DEFAULT 'pending',
  `user_type` varchar(20) DEFAULT 'customer',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_type` (`phone_number`, `registration_type`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='用户注册记录表';

-- 2. 用户认证方式表（核心表）
CREATE TABLE IF NOT EXISTS `crm_user_auth_methods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `auth_type` varchar(20) NOT NULL,
  `auth_identifier` varchar(100) NOT NULL,
  `is_primary` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `last_used_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_auth_type` (`user_id`, `auth_type`),
  UNIQUE KEY `uk_auth_identifier` (`auth_type`, `auth_identifier`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB COMMENT='用户认证方式表';

-- 3. 客户线索表（可选，如果不存在类似表）
CREATE TABLE IF NOT EXISTS `crm_customer_leads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL,
  `phone_number` varchar(20) NOT NULL,
  `source_type` varchar(50) DEFAULT 'website',
  `status` varchar(20) DEFAULT 'new',
  `assigned_to` bigint(20) DEFAULT NULL,
  `access_token` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='客户线索表';

-- ==============================================
-- 快速数据迁移（完全无入侵版本）
-- ==============================================

-- 🔒 重要说明：以下数据迁移完全不修改现有数据
-- 只是为现有用户建立新的认证映射关系，原有登录方式完全保留

-- 为现有用户创建手机号认证方式记录（仅建立映射关系）
INSERT IGNORE INTO crm_user_auth_methods (user_id, auth_type, auth_identifier, is_primary, is_active)
SELECT 
    user_id,
    'phone' as auth_type,
    phonenumber as auth_identifier,
    0 as is_primary,  -- 设为非主要方式，保持原有登录为主
    1 as is_active
FROM sys_user 
WHERE phonenumber IS NOT NULL 
  AND phonenumber != ''
  AND del_flag = '0';

-- 如果存在企业微信表，则添加企业微信认证记录
INSERT IGNORE INTO crm_user_auth_methods (user_id, auth_type, auth_identifier, is_primary, is_active)
SELECT 
    user_id,
    'wechat' as auth_type,
    wecom_user_id as auth_identifier,
    0 as is_primary,  -- 设为非主要方式，保持原有登录为主
    1 as is_active
FROM crm_thirdparty_wechat 
WHERE wecom_user_id IS NOT NULL;

-- 🛡️ 安全检查：确认没有修改任何现有表结构
SELECT 
    'sys_user表结构未被修改' as safety_check,
    COUNT(*) as total_users,
    COUNT(CASE WHEN password IS NOT NULL THEN 1 END) as users_with_password,
    '✅ 所有密码完整保留' as password_status
FROM sys_user 
WHERE del_flag = '0';

-- ==============================================
-- 基础配置插入
-- ==============================================

-- 插入基础配置
INSERT IGNORE INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES 
('无密码认证-短信验证码长度', 'passwordless.sms.code.length', '6', 'N', 'admin', NOW(), '验证码长度'),
('无密码认证-验证码过期时间', 'passwordless.sms.code.expire', '300', 'N', 'admin', NOW(), '过期时间（秒）'),
('无密码认证-JWT密钥', 'passwordless.jwt.secret', 'CRM_PASSWORDLESS_SECRET_2025', 'Y', 'admin', NOW(), 'JWT密钥');

-- ==============================================
-- 基础索引优化
-- ==============================================

-- 添加必要的索引
CREATE INDEX IF NOT EXISTS idx_registration_phone_status ON crm_user_registration(phone_number, status);
CREATE INDEX IF NOT EXISTS idx_auth_methods_identifier ON crm_user_auth_methods(auth_identifier);

-- ==============================================
-- 简单验证脚本
-- ==============================================

-- 验证表创建结果
SELECT 
    'crm_user_registration' as table_name,
    COUNT(*) as record_count,
    'OK' as status
FROM crm_user_registration
UNION ALL
SELECT 
    'crm_user_auth_methods' as table_name,
    COUNT(*) as record_count,
    'OK' as status
FROM crm_user_auth_methods
UNION ALL
SELECT 
    'crm_customer_leads' as table_name,
    COUNT(*) as record_count,
    'OK' as status
FROM crm_customer_leads;

-- ==============================================
-- 快速部署完成提示
-- ==============================================

SELECT 
    '✅ CRM无密码认证系统快速部署完成!' as message,
    NOW() as deployment_time,
    '🔧 请配置短信服务和企业微信参数' as next_step_1,
    '🚀 然后启动应用进行测试' as next_step_2;

-- ==============================================
-- 可选：创建测试数据（开发环境）
-- ==============================================

-- 注释掉的测试数据，需要时可以取消注释
/*
-- 插入测试注册记录
INSERT INTO crm_user_registration (phone_number, registration_type, user_type, status)
VALUES 
('13800138000', 'sms', 'customer', 'pending'),
('13800138001', 'sms', 'employee', 'completed');

-- 插入测试线索
INSERT INTO crm_customer_leads (phone_number, source_type, status)
VALUES 
('13800138000', 'website', 'new'),
('13800138002', '3d_upload', 'new');
*/
