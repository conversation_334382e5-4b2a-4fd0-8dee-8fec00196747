package com.ruoyi.crm.controller;


import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.service.ICrmContactFollowService;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.crm.BaseTestCase;

/**
 * 联系人管理第四阶段前端功能增强集成测试
 * 测试筛选功能、关注功能、批量操作等前端交互功能
 * 
 * <AUTHOR>
 * @date 2024-12-28
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("联系人管理第四阶段集成测试")
class ContactManagementPhase4IntegrationTest extends BaseTestCase {



    private static final Logger logger = LoggerFactory.getLogger(ContactManagementPhase4IntegrationTest.class);

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmContactsService crmContactsService;

    @Autowired
    private ICrmContactFollowService crmContactFollowService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    private CrmContacts testContact1;
    private CrmContacts testContact2;
    private CrmContacts testContact3;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .alwaysDo(print())
                .build();

        // 创建测试联系人数据
        createTestContacts();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        cleanupTestContacts();
    }

    @Nested
    @DisplayName("筛选功能集成测试")
    class FilterIntegrationTests {

        @Test
        @DisplayName("测试全部联系人筛选")
        void testFilterAll() throws Exception {
            MvcResult result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "1")
                    .param("pageSize", "10")
                    .param("filterType", "all"))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);

            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertTrue(response.getTotal() >= 3, "应该查询到至少3条测试数据");

            logger.info("全部联系人筛选测试通过，查询到 {} 条记录", response.getTotal());
        }

        @Test
        @DisplayName("测试我负责的联系人筛选")
        void testFilterMine() throws Exception {
            MvcResult result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "1")
                    .param("pageSize", "10")
                    .param("filterType", "mine"))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);

            assertNotNull(response);
            assertEquals(200, response.getCode());

            logger.warn("我负责的联系人筛选测试通过，查询到 {} 条记录", response.getTotal());
        }

        @Test
        @DisplayName("测试下属负责的联系人筛选")
        void testFilterSubordinate() throws Exception {
            MvcResult result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "1")
                    .param("pageSize", "10")
                    .param("filterType", "subordinate"))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);

            assertNotNull(response);
            assertEquals(200, response.getCode());

            logger.info("下属负责的联系人筛选测试通过，查询到 {} 条记录", response.getTotal());
        }

        @Test
        @DisplayName("测试我关注的联系人筛选")
        void testFilterFollowing() throws Exception {
            // 先关注一个联系人
            mockMvc.perform(post("/front/crm/contacts/follow/{contactId}", testContact1.getId()))
                    .andExpect(status().isOk());

            MvcResult result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "1")
                    .param("pageSize", "10")
                    .param("filterType", "following"))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);

            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertTrue(response.getTotal() >= 1, "应该至少有一条关注的联系人");

            logger.info("我关注的联系人筛选测试通过，查询到 {} 条记录", response.getTotal());
        }
    }

    @Nested
    @DisplayName("关注功能集成测试")
    class FollowIntegrationTests {

        @Test
        @DisplayName("关注联系人功能测试")
        void testFollowContact() throws Exception {
            // 1. 关注联系人
            MvcResult followResult = mockMvc.perform(post("/front/crm/contacts/follow/{contactId}", testContact1.getId()))
                    .andExpect(status().isOk())
                    .andReturn();

            String followResponse = followResult.getResponse().getContentAsString();
            AjaxResult followResult_parsed = objectMapper.readValue(followResponse, AjaxResult.class);
            
            assertTrue(followResult_parsed.isSuccess(), "关注应该成功");
            logger.info("关注联系人成功");

            // 2. 查询关注状态
            MvcResult statusResult = mockMvc.perform(get("/front/crm/contacts/follow/status/{contactId}", testContact1.getId()))
                    .andExpect(status().isOk())
                    .andReturn();

            String statusResponse = statusResult.getResponse().getContentAsString();
            AjaxResult statusResult_parsed = objectMapper.readValue(statusResponse, AjaxResult.class);
            
            assertTrue(statusResult_parsed.isSuccess(), "查询关注状态应该成功");
            assertTrue((Boolean) statusResult_parsed.get("data"), "应该显示为已关注状态");
            logger.info("查询关注状态成功，当前状态：已关注");
        }

        @Test
        @DisplayName("取消关注联系人功能测试")
        void testUnfollowContact() throws Exception {
            // 1. 先关注联系人
            mockMvc.perform(post("/front/crm/contacts/follow/{contactId}", testContact2.getId()))
                    .andExpect(status().isOk());

            // 2. 取消关注
            MvcResult unfollowResult = mockMvc.perform(delete("/front/crm/contacts/follow/{contactId}", testContact2.getId()))
                    .andExpect(status().isOk())
                    .andReturn();

            String unfollowResponse = unfollowResult.getResponse().getContentAsString();
            AjaxResult unfollowResult_parsed = objectMapper.readValue(unfollowResponse, AjaxResult.class);
            
            assertTrue(unfollowResult_parsed.isSuccess(), "取消关注应该成功");
            logger.info("取消关注联系人成功");

            // 3. 验证关注状态
            MvcResult statusResult = mockMvc.perform(get("/front/crm/contacts/follow/status/{contactId}", testContact2.getId()))
                    .andExpect(status().isOk())
                    .andReturn();

            String statusResponse = statusResult.getResponse().getContentAsString();
            AjaxResult statusResult_parsed = objectMapper.readValue(statusResponse, AjaxResult.class);
            
            assertFalse((Boolean) statusResult_parsed.get("data"), "应该显示为未关注状态");
            logger.info("验证取消关注状态成功，当前状态：未关注");
        }

        @Test
        @DisplayName("批量关注联系人功能测试")
        void testBatchFollowContacts() throws Exception {
            List<Long> contactIds = Arrays.asList(testContact1.getId(), testContact2.getId());

            MvcResult batchResult = mockMvc.perform(post("/front/crm/contacts/follow/batch")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(contactIds)))
                    .andExpect(status().isOk())
                    .andReturn();

            String batchResponse = batchResult.getResponse().getContentAsString();
            AjaxResult batchResult_parsed = objectMapper.readValue(batchResponse, AjaxResult.class);
            
            assertTrue(batchResult_parsed.isSuccess(), "批量关注应该成功");
            logger.info("批量关注联系人成功，关注了 {} 个联系人", contactIds.size());

            // 验证关注状态
            for (Long contactId : contactIds) {
                MvcResult statusResult = mockMvc.perform(get("/front/crm/contacts/follow/status/{contactId}", contactId))
                        .andExpect(status().isOk())
                        .andReturn();

                String statusResponse = statusResult.getResponse().getContentAsString();
                AjaxResult statusResult_parsed = objectMapper.readValue(statusResponse, AjaxResult.class);
                
                assertTrue((Boolean) statusResult_parsed.get("data"), "联系人 " + contactId + " 应该显示为已关注");
            }
        }

        @Test
        @DisplayName("批量取消关注联系人功能测试")
        void testBatchUnfollowContacts() throws Exception {
            List<Long> contactIds = Arrays.asList(testContact1.getId(), testContact2.getId());

            // 先批量关注
            mockMvc.perform(post("/front/crm/contacts/follow/batch")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(contactIds)))
                    .andExpect(status().isOk());

            // 批量取消关注
            MvcResult batchResult = mockMvc.perform(delete("/front/crm/contacts/follow/batch")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(contactIds)))
                    .andExpect(status().isOk())
                    .andReturn();

            String batchResponse = batchResult.getResponse().getContentAsString();
            AjaxResult batchResult_parsed = objectMapper.readValue(batchResponse, AjaxResult.class);
            
            assertTrue(batchResult_parsed.isSuccess(), "批量取消关注应该成功");
            logger.info("批量取消关注联系人成功，取消关注了 {} 个联系人", contactIds.size());

            // 验证关注状态
            for (Long contactId : contactIds) {
                MvcResult statusResult = mockMvc.perform(get("/front/crm/contacts/follow/status/{contactId}", contactId))
                        .andExpect(status().isOk())
                        .andReturn();

                String statusResponse = statusResult.getResponse().getContentAsString();
                AjaxResult statusResult_parsed = objectMapper.readValue(statusResponse, AjaxResult.class);
                
                assertFalse((Boolean) statusResult_parsed.get("data"), "联系人 " + contactId + " 应该显示为未关注");
            }
        }
    }

    @Nested
    @DisplayName("用户体验优化测试")
    class UserExperienceTests {

        @Test
        @DisplayName("搜索功能响应性测试")
        void testSearchResponsiveness() throws Exception {
            // 测试搜索关键词匹配
            MvcResult result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "1")
                    .param("pageSize", "10")
                    .param("searchKeyword", "测试联系人1"))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);

            assertNotNull(response);
            assertEquals(200, response.getCode());
            
            // 验证搜索结果包含期望的联系人
            List<?> rows = response.getRows();
            assertNotNull(rows);
            assertTrue(rows.size() >= 1, "搜索应该找到匹配的联系人");

            logger.info("搜索功能测试通过，找到 {} 条匹配记录", rows.size());
        }

        @Test
        @DisplayName("分页功能测试")
        void testPaginationFunction() throws Exception {
            // 测试第一页
            MvcResult page1Result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "1")
                    .param("pageSize", "2"))
                    .andExpect(status().isOk())
                    .andReturn();

            String page1Response = page1Result.getResponse().getContentAsString();
            TableDataInfo page1Data = objectMapper.readValue(page1Response, TableDataInfo.class);

            // 测试第二页
            MvcResult page2Result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "2")
                    .param("pageSize", "2"))
                    .andExpect(status().isOk())
                    .andReturn();

            String page2Response = page2Result.getResponse().getContentAsString();
            TableDataInfo page2Data = objectMapper.readValue(page2Response, TableDataInfo.class);

            // 验证分页结果
            assertEquals(200, page1Data.getCode());
            assertEquals(200, page2Data.getCode());
            assertTrue(page1Data.getTotal() >= 3, "总记录数应该大于等于3");
            assertTrue(page1Data.getRows().size() <= 2, "第一页应该最多显示2条记录");

            logger.info("分页功能测试通过，总记录数: {}, 第一页记录数: {}, 第二页记录数: {}", 
                       page1Data.getTotal(), page1Data.getRows().size(), page2Data.getRows().size());
        }

        @Test
        @DisplayName("排序功能测试")
        void testSortingFunction() throws Exception {
            // 测试按创建时间降序排序
            MvcResult result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "1")
                    .param("pageSize", "10")
                    .param("orderByColumn", "createTime")
                    .param("isAsc", "false"))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);

            assertNotNull(response);
            assertEquals(200, response.getCode());
            assertTrue(response.getTotal() >= 3, "应该有足够的数据进行排序测试");

            logger.info("排序功能测试通过，按创建时间降序排列，共 {} 条记录", response.getTotal());
        }
    }

    @Nested
    @DisplayName("异常处理和边界测试")
    class ExceptionAndBoundaryTests {

        @Test
        @DisplayName("关注不存在的联系人")
        void testFollowNonExistentContact() throws Exception {
            Long nonExistentId = 999999L;

            MvcResult result = mockMvc.perform(post("/front/crm/contacts/follow/{contactId}", nonExistentId))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);

            assertFalse(response.isSuccess(), "关注不存在的联系人应该失败");
            logger.info("关注不存在联系人的异常处理测试通过");
        }

        @Test
        @DisplayName("重复关注同一联系人")
        void testDuplicateFollow() throws Exception {
            // 第一次关注
            mockMvc.perform(post("/front/crm/contacts/follow/{contactId}", testContact3.getId()))
                    .andExpect(status().isOk());

            // 第二次关注同一联系人
            MvcResult result = mockMvc.perform(post("/front/crm/contacts/follow/{contactId}", testContact3.getId()))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            AjaxResult response = objectMapper.readValue(responseContent, AjaxResult.class);

            // 系统应该优雅处理重复关注
            logger.info("重复关注处理测试完成，响应: {}", response.get("msg"));
        }

        @Test
        @DisplayName("空筛选条件处理")
        void testEmptyFilterHandling() throws Exception {
            MvcResult result = mockMvc.perform(get("/front/crm/contacts/list")
                    .param("pageNum", "1")
                    .param("pageSize", "10")
                    .param("filterType", "")
                    .param("searchKeyword", ""))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            TableDataInfo response = objectMapper.readValue(responseContent, TableDataInfo.class);

            assertEquals(200, response.getCode());
            logger.info("空筛选条件处理测试通过");
        }
    }

    /**
     * 创建测试联系人数据
     */
    private void createTestContacts() {
        testContact1 = new CrmContacts();
        testContact1.setName("测试联系人1");
        testContact1.setMobile("13900139001");
        testContact1.setEmail("<EMAIL>");
        testContact1.setPosition("产品经理");
        testContact1.setResponsiblePersonId("1");
        testContact1.setDelFlag("0");
        testContact1.setCreateTime(new Date());
        crmContactsService.insertCrmContacts(testContact1);

        testContact2 = new CrmContacts();
        testContact2.setName("测试联系人2");
        testContact2.setMobile("13900139002");
        testContact2.setEmail("<EMAIL>");
        testContact2.setPosition("技术经理");
        testContact2.setResponsiblePersonId("1");
        testContact2.setDelFlag("0");
        testContact2.setCreateTime(new Date());
        crmContactsService.insertCrmContacts(testContact2);

        testContact3 = new CrmContacts();
        testContact3.setName("测试联系人3");
        testContact3.setMobile("13900139003");
        testContact3.setEmail("<EMAIL>");
        testContact3.setPosition("销售经理");
        testContact3.setResponsiblePersonId("2");
        testContact3.setDelFlag("0");
        testContact3.setCreateTime(new Date());
        crmContactsService.insertCrmContacts(testContact3);

        logger.info("创建测试联系人数据完成");
    }

    /**
     * 清理测试联系人数据
     */
    private void cleanupTestContacts() {
        try {
            if (testContact1 != null && testContact1.getId() != null) {
                crmContactsService.deleteCrmContactsById(testContact1.getId());
            }
            if (testContact2 != null && testContact2.getId() != null) {
                crmContactsService.deleteCrmContactsById(testContact2.getId());
            }
            if (testContact3 != null && testContact3.getId() != null) {
                crmContactsService.deleteCrmContactsById(testContact3.getId());
            }
            logger.info("清理测试联系人数据完成");
        } catch (Exception e) {
            logger.warn("清理测试数据时发生异常", e);
        }
    }
}