-- =====================================================
-- 联系人字段统一迁移脚本
-- 为 crm_business_contacts 表添加缺失的扩展字段
-- 执行日期: 2025-06-27
-- =====================================================

-- 为 crm_business_contacts 表添加扩展字段
ALTER TABLE `crm_business_contacts` 
ADD COLUMN `birthday` date NULL DEFAULT NULL COMMENT '生日' AFTER `gender`,
ADD COLUMN `department` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '部门' AFTER `birthday`,
ADD COLUMN `decision_role` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '决策角色' AFTER `department`,
ADD COLUMN `contact_level` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系人级别' AFTER `decision_role`,
ADD COLUMN `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态（0有效 1无效）' AFTER `contact_level`;

-- 添加索引以提升查询性能
CREATE INDEX `idx_department` ON `crm_business_contacts` (`department`);
CREATE INDEX `idx_decision_role` ON `crm_business_contacts` (`decision_role`);
CREATE INDEX `idx_contact_level` ON `crm_business_contacts` (`contact_level`);
CREATE INDEX `idx_status` ON `crm_business_contacts` (`status`);

-- 验证字段添加结果
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'crm41' AND TABLE_NAME = 'crm_business_contacts' 
-- ORDER BY ORDINAL_POSITION;
