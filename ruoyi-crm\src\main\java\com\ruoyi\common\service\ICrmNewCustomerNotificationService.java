package com.ruoyi.common.service;

import java.util.List;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmNewCustomerNotification;

/**
 * CRM新客户通知服务接口
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
public interface ICrmNewCustomerNotificationService {
    
    /**
     * 创建新客户通知
     * 
     * @param customerId 客户ID
     * @param customerName 客户名称
     * @param customerPhone 客户电话
     * @param orderId 关联订单ID（可选）
     * @param orderNo 订单编号（可选）
     * @return 通知创建结果
     */
    public NotificationResult createNewCustomerNotification(Long customerId, String customerName, 
                                                           String customerPhone, Long orderId, String orderNo);
    
    /**
     * 创建未分配订单通知
     * 
     * @param orderId 订单ID
     * @param orderNo 订单编号
     * @param customerName 客户名称
     * @return 通知创建结果
     */
    public NotificationResult createUnassignedOrderNotification(Long orderId, String orderNo, String customerName);
    
    /**
     * 处理通知
     * 
     * @param notificationId 通知ID
     * @param processedBy 处理人ID
     * @param processResult 处理结果
     * @return 处理结果
     */
    public boolean processNotification(Long notificationId, Long processedBy, String processResult);
    
    /**
     * 批量处理通知
     * 
     * @param notificationIds 通知ID列表
     * @param processedBy 处理人ID
     * @param processResult 处理结果
     * @return 处理结果
     */
    public BatchProcessResult batchProcessNotifications(List<Long> notificationIds, Long processedBy, String processResult);
    
    /**
     * 分配通知给管理员
     * 
     * @param notificationId 通知ID
     * @param assignedTo 分配给的管理员ID
     * @param assignedBy 分配人ID
     * @return 分配结果
     */
    public boolean assignNotification(Long notificationId, Long assignedTo, Long assignedBy);
    
    /**
     * 获取待处理通知列表
     * 
     * @param assignedTo 分配给的用户ID（可选）
     * @return 待处理通知列表
     */
    public List<CrmNewCustomerNotification> getPendingNotifications(Long assignedTo);
    
    /**
     * 获取超时未处理通知
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时通知列表
     */
    public List<CrmNewCustomerNotification> getTimeoutNotifications(Integer timeoutMinutes);
    
    /**
     * 分页查询通知列表
     * 
     * @param notification 查询条件
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public TableDataInfo selectNotificationPage(CrmNewCustomerNotification notification, Integer pageNum, Integer pageSize);
    
    /**
     * 发送企业微信通知
     * 
     * @param notificationId 通知ID
     * @return 发送结果
     */
    public boolean sendWechatNotification(Long notificationId);
    
    /**
     * 发送邮件通知
     * 
     * @param notificationId 通知ID
     * @return 发送结果
     */
    public boolean sendEmailNotification(Long notificationId);
    
    /**
     * 批量发送企业微信通知
     * 
     * @return 发送结果
     */
    public BatchSendResult batchSendWechatNotifications();
    
    /**
     * 批量发送邮件通知
     * 
     * @return 发送结果
     */
    public BatchSendResult batchSendEmailNotifications();
    
    /**
     * 获取通知统计信息
     * 
     * @param assignedTo 分配给的用户ID（可选）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    public NotificationStatistics getNotificationStatistics(Long assignedTo, String startDate, String endDate);
    
    /**
     * 获取通知处理效率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 效率统计
     */
    public List<NotificationEfficiency> getNotificationEfficiency(String startDate, String endDate);
    
    /**
     * 清理过期通知
     * 
     * @param expireDays 过期天数
     * @return 清理数量
     */
    public int cleanExpiredNotifications(Integer expireDays);
    
    /**
     * 自动分配通知给管理员
     * 
     * @param notificationId 通知ID
     * @return 分配结果
     */
    public boolean autoAssignNotification(Long notificationId);
    
    /**
     * 通知创建结果
     */
    public static class NotificationResult {
        private boolean success;
        private String message;
        private Long notificationId;
        private String notificationType;
        
        public static NotificationResult success(Long notificationId, String notificationType) {
            NotificationResult result = new NotificationResult();
            result.setSuccess(true);
            result.setMessage("通知创建成功");
            result.setNotificationId(notificationId);
            result.setNotificationType(notificationType);
            return result;
        }
        
        public static NotificationResult failure(String message) {
            NotificationResult result = new NotificationResult();
            result.setSuccess(false);
            result.setMessage(message);
            return result;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public Long getNotificationId() { return notificationId; }
        public void setNotificationId(Long notificationId) { this.notificationId = notificationId; }
        
        public String getNotificationType() { return notificationType; }
        public void setNotificationType(String notificationType) { this.notificationType = notificationType; }
    }
    
    /**
     * 批量处理结果
     */
    public static class BatchProcessResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private String summary;
        
        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
    }
    
    /**
     * 批量发送结果
     */
    public static class BatchSendResult {
        private int totalCount;
        private int sentCount;
        private int failedCount;
        private List<String> failureReasons;
        
        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSentCount() { return sentCount; }
        public void setSentCount(int sentCount) { this.sentCount = sentCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public List<String> getFailureReasons() { return failureReasons; }
        public void setFailureReasons(List<String> failureReasons) { this.failureReasons = failureReasons; }
    }
    
    /**
     * 通知统计信息
     */
    public static class NotificationStatistics {
        private int totalNotifications;
        private int pendingNotifications;
        private int processingNotifications;
        private int completedNotifications;
        private int cancelledNotifications;
        private double avgProcessTime;
        private double completionRate;
        
        // Getters and Setters
        public int getTotalNotifications() { return totalNotifications; }
        public void setTotalNotifications(int totalNotifications) { this.totalNotifications = totalNotifications; }
        
        public int getPendingNotifications() { return pendingNotifications; }
        public void setPendingNotifications(int pendingNotifications) { this.pendingNotifications = pendingNotifications; }
        
        public int getProcessingNotifications() { return processingNotifications; }
        public void setProcessingNotifications(int processingNotifications) { this.processingNotifications = processingNotifications; }
        
        public int getCompletedNotifications() { return completedNotifications; }
        public void setCompletedNotifications(int completedNotifications) { this.completedNotifications = completedNotifications; }
        
        public int getCancelledNotifications() { return cancelledNotifications; }
        public void setCancelledNotifications(int cancelledNotifications) { this.cancelledNotifications = cancelledNotifications; }
        
        public double getAvgProcessTime() { return avgProcessTime; }
        public void setAvgProcessTime(double avgProcessTime) { this.avgProcessTime = avgProcessTime; }
        
        public double getCompletionRate() { return completionRate; }
        public void setCompletionRate(double completionRate) { this.completionRate = completionRate; }
    }
    
    /**
     * 通知处理效率
     */
    public static class NotificationEfficiency {
        private String date;
        private int totalNotifications;
        private int processedNotifications;
        private double avgProcessTime;
        private double processRate;
        
        // Getters and Setters
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        
        public int getTotalNotifications() { return totalNotifications; }
        public void setTotalNotifications(int totalNotifications) { this.totalNotifications = totalNotifications; }
        
        public int getProcessedNotifications() { return processedNotifications; }
        public void setProcessedNotifications(int processedNotifications) { this.processedNotifications = processedNotifications; }
        
        public double getAvgProcessTime() { return avgProcessTime; }
        public void setAvgProcessTime(double avgProcessTime) { this.avgProcessTime = avgProcessTime; }
        
        public double getProcessRate() { return processRate; }
        public void setProcessRate(double processRate) { this.processRate = processRate; }
    }
}
