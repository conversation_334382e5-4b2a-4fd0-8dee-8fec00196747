<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>极简多负责人方案 - 基于现有架构</title>
    
    <!-- Mermaid.js for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- Prism.js for code highlighting -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #27ae60;
            border-bottom: 3px solid #27ae60;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .insight-box {
            background-color: #e8f5e8;
            border: 2px solid #27ae60;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .question-box {
            background-color: #fff3cd;
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .mermaid-diagram {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        .code-section {
            margin: 20px 0;
        }
        .code-title {
            background-color: #495057;
            color: white;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            margin: 0;
            font-weight: bold;
            font-size: 14px;
        }
        pre[class*="language-"] {
            margin: 0 !important;
            border-radius: 0 0 5px 5px !important;
            font-size: 13px !important;
        }
        .scenario-example {
            background-color: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 15px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .option-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 极简多负责人方案 - 基于现有架构</h1>
        
        <div class="insight-box">
            <h3>💡 核心理念：最小改动，最大效果</h3>
            <p><strong>你的思路完全正确！</strong>通过现有的客户-联系人关系，自然实现多负责人，无需新增复杂表结构。</p>
            <ul>
                <li>✅ <strong>客户表：</strong>去掉 responsible_person_id 字段</li>
                <li>✅ <strong>联系人表：</strong>保留/新增 responsible_person_id 字段</li>
                <li>✅ <strong>关系表：</strong>利用现有的 crm_customer_contact_relation</li>
                <li>✅ <strong>操作日志：</strong>使用现有的 crm_pool_operation_log</li>
            </ul>
        </div>

        <h2>🤔 需要确认的关键问题</h2>

        <div class="question-box">
            <h4>❓ 联系人-负责人关系类型</h4>
            <p><strong>请确认您想要的设计：</strong></p>
            
            <div class="option-card">
                <h5>方案A：一对一关系（推荐）</h5>
                <p>一个联系人只能有一个负责人</p>
                <ul>
                    <li>✅ 简单直接，联系人表直接加 responsible_person_id 字段</li>
                    <li>✅ 权责清晰，每个联系人都有明确的负责人</li>
                    <li>✅ 实现简单，改动最小</li>
                </ul>
            </div>

            <div class="option-card">
                <h5>方案B：多对多关系</h5>
                <p>一个联系人可以有多个负责人（需要新增关系表）</p>
                <ul>
                    <li>❌ 增加复杂度，需要新增 contact_responsible_relation 表</li>
                    <li>❌ 权责模糊，不清楚谁是主要负责人</li>
                    <li>❌ 业务场景不常见，过度设计</li>
                </ul>
            </div>
        </div>

        <h2>🏗️ 推荐架构：方案A（极简版）</h2>

        <div class="mermaid-diagram">
            <div class="mermaid">
erDiagram
    CrmCustomer {
        Long id PK
        String customerName
        String customerIndustry
        String customerLevel
        String status
        String delFlag
        comment "移除 responsible_person_id 字段"
    }
    
    CrmContacts {
        Long id PK
        Long responsiblePersonId FK "负责人ID"
        String name
        String department
        String position
        String mobile
        String email
        String status
        String delFlag
    }
    
    CrmCustomerContactRelation {
        Long id PK
        Long customerId FK
        Long contactId FK
        String relationType "主要联系人/决策人等"
        Integer isPrimary "是否主联系人"
        Date startDate
        String status
        comment "现有表,无需修改"
    }
    
    CrmPoolOperationLog {
        Long id PK
        Long customerId FK
        String operationType "CLAIM/RETURN"
        Long operatorId FK
        Long fromUserId FK
        Long toUserId FK
        String reason
        Date operationTime
        comment "现有表,无需修改"
    }
    
    CrmCustomer ||--o{ CrmCustomerContactRelation : "has contacts"
    CrmContacts ||--o{ CrmCustomerContactRelation : "belongs to"
    CrmContacts ||--|| User : "has responsible person"
            </div>
        </div>

        <h2>📊 业务场景验证</h2>

        <div class="scenario-example">
            <h4>🏢 A公司玩具制造商</h4>
            <p><strong>实现方式：</strong></p>
            <ul>
                <li><strong>客户：</strong>A公司（无负责人字段）</li>
                <li><strong>联系人1：</strong>王五-研发部（responsible_person_id = 张三ID）</li>
                <li><strong>联系人2：</strong>赵六-市场部（responsible_person_id = 李四ID）</li>
                <li><strong>关系：</strong>crm_customer_contact_relation 记录 A公司 ↔ 王五、赵六</li>
            </ul>
            <p><strong>查询A公司的负责人：</strong>通过客户→联系人→负责人的关联查询</p>
        </div>

        <h2>🔧 具体实现方案</h2>

        <h3>1. 数据库改动</h3>

        <div class="code-section">
            <div class="code-title">联系人表增加负责人字段</div>
            <pre><code class="language-sql">-- 联系人表增加负责人字段
ALTER TABLE crm_contacts 
ADD COLUMN responsible_person_id BIGINT COMMENT '负责人ID',
ADD INDEX idx_responsible_person (responsible_person_id);

-- 可选：删除客户表的负责人字段（保留用于兼容性也可以）
-- ALTER TABLE crm_customer DROP COLUMN responsible_person_id;</code></pre>
        </div>

        <h3>2. 联系人认领逻辑</h3>

        <div class="code-section">
            <div class="code-title">联系人认领（公海的核心逻辑）</div>
            <pre><code class="language-java">@Override
@Transactional
public int claimContacts(List&lt;Long&gt; contactIds) {
    // 1. 参数验证
    if (contactIds == null || contactIds.isEmpty()) {
        throw new ServiceException("请选择要认领的联系人");
    }
    
    Long userId = SecurityUtils.getUserId();
    
    // 2. 检查联系人是否已有负责人
    List&lt;CrmContacts&gt; contacts = contactMapper.selectByIds(contactIds);
    List&lt;Long&gt; alreadyOwnedContacts = contacts.stream()
        .filter(contact -&gt; contact.getResponsiblePersonId() != null)
        .map(CrmContacts::getId)
        .collect(Collectors.toList());
    
    if (!alreadyOwnedContacts.isEmpty()) {
        throw new ServiceException("部分联系人已有负责人，请重新选择");
    }
    
    // 3. 检查认领限制
    if (!checkClaimLimit(userId, contactIds.size())) {
        throw new ServiceException("超出认领限制");
    }
    
    // 4. 批量更新联系人负责人
    int result = contactMapper.batchUpdateResponsiblePerson(contactIds, userId);
    
    // 5. 记录操作日志（基于联系人关联的客户）
    recordContactClaimLog(contacts, userId);
    
    return result;
}</code></pre>
        </div>

        <h3>3. 客户维度的查询逻辑</h3>

        <div class="code-section">
            <div class="code-title">查询客户的所有负责人</div>
            <pre><code class="language-java">/**
 * 获取客户的所有负责人（通过联系人间接获取）
 */
@Override
public List&lt;Long&gt; getCustomerResponsiblePersons(Long customerId) {
    return contactMapper.selectResponsiblePersonsByCustomerId(customerId);
}

/**
 * 获取用户负责的客户（通过联系人间接获取）
 */
@Override
public List&lt;CrmCustomer&gt; selectMyCustomers(Long userId) {
    return customerMapper.selectCustomersByContactResponsible(userId);
}

/**
 * 获取用户负责的联系人
 */
@Override
public List&lt;CrmContacts&gt; selectMyContacts(Long userId) {
    CrmContacts query = new CrmContacts();
    query.setResponsiblePersonId(userId);
    return contactMapper.selectList(query);
}</code></pre>
        </div>

        <h3>4. 公海查询逻辑</h3>

        <div class="code-section">
            <div class="code-title">公海联系人查询</div>
            <pre><code class="language-java">/**
 * 查询公海联系人（无负责人的联系人）
 */
@Override
public List&lt;CrmContacts&gt; selectPoolContacts() {
    CrmContacts query = new CrmContacts();
    query.setResponsiblePersonId(null); // 查询无负责人的联系人
    return contactMapper.selectPoolContacts(query);
}

/**
 * 查询公海客户（所有联系人都无负责人的客户）
 */
@Override
public List&lt;CrmCustomer&gt; selectPoolCustomers() {
    return customerMapper.selectCustomersWithoutAnyResponsible();
}</code></pre>
        </div>

        <h2>📈 数据迁移策略</h2>

        <div class="code-section">
            <div class="code-title">现有数据迁移</div>
            <pre><code class="language-sql">-- 将客户的负责人迁移到联系人
UPDATE crm_contacts c
INNER JOIN crm_customer_contact_relation ccr ON c.id = ccr.contact_id
INNER JOIN crm_customer cust ON ccr.customer_id = cust.id
SET c.responsible_person_id = cust.responsible_person_id
WHERE c.responsible_person_id IS NULL
AND cust.responsible_person_id IS NOT NULL
AND ccr.status = 'active';

-- 对于没有联系人的客户，创建默认联系人
INSERT INTO crm_contacts (name, responsible_person_id, create_time)
SELECT 
    CONCAT(customer_name, '-默认联系人'), 
    responsible_person_id, 
    NOW()
FROM crm_customer 
WHERE responsible_person_id IS NOT NULL
AND id NOT IN (
    SELECT DISTINCT customer_id 
    FROM crm_customer_contact_relation 
    WHERE status = 'active'
);

-- 为新创建的联系人建立客户关系
INSERT INTO crm_customer_contact_relation 
    (customer_id, contact_id, relation_type, is_primary, start_date, status)
SELECT 
    c.id,
    ct.id,
    '主要联系人',
    1,
    NOW(),
    'active'
FROM crm_customer c
INNER JOIN crm_contacts ct ON ct.name = CONCAT(c.customer_name, '-默认联系人')
WHERE c.responsible_person_id IS NOT NULL;</code></pre>
        </div>

        <h2>✨ 方案优势</h2>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>方面</th>
                    <th>现有设计</th>
                    <th>极简多负责人方案</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>表结构</strong></td>
                    <td>客户表有responsible_person_id</td>
                    <td>只需联系人表有responsible_person_id</td>
                </tr>
                <tr>
                    <td><strong>多负责人支持</strong></td>
                    <td>❌ 不支持</td>
                    <td>✅ 通过多联系人自然支持</td>
                </tr>
                <tr>
                    <td><strong>实现复杂度</strong></td>
                    <td>简单</td>
                    <td>简单（最小改动）</td>
                </tr>
                <tr>
                    <td><strong>业务灵活性</strong></td>
                    <td>低</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td><strong>数据一致性</strong></td>
                    <td>好</td>
                    <td>好</td>
                </tr>
                <tr>
                    <td><strong>查询性能</strong></td>
                    <td>好</td>
                    <td>需要关联查询，稍复杂</td>
                </tr>
            </tbody>
        </table>

        <h2>⚡ 实施计划</h2>

        <div class="insight-box">
            <h4>📅 3-4天极简实施</h4>
            <ol>
                <li><strong>第1天：</strong>联系人表增加负责人字段，数据迁移</li>
                <li><strong>第2天：</strong>修改业务逻辑，联系人认领和查询</li>
                <li><strong>第3天：</strong>修复集成测试，确保功能正常</li>
                <li><strong>第4天：</strong>完善操作日志和异常处理</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #e8f5e8; border-radius: 5px;">
            <h3>🎯 确认问题</h3>
            <p><strong>1. 联系人-负责人关系：</strong>一对一就够了吗？</p>
            <p><strong>2. 这个极简方案符合您的思路吗？</strong></p>
            <p style="color: #27ae60; font-weight: bold;">如果确认，我们立即开始实施！ 🚀</p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#333333',
                fill: '#ececff',
                fontSize: 12
            }
        });

        // Auto highlight code blocks
        document.addEventListener('DOMContentLoaded', function() {
            Prism.highlightAll();
        });
    </script>
</body>
</html>