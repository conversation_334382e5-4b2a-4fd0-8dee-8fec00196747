package com.ruoyi.crm.domain.vo;

import java.util.List;
import java.util.Map;
import com.ruoyi.common.domain.entity.CrmTeam;
import com.ruoyi.common.domain.entity.CrmTeamMember;

/**
 * 团队业绩统计VO
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class TeamPerformanceVO {
    
    /** 团队基本信息 */
    private CrmTeam teamInfo;
    
    /** 团队成员列表 */
    private List<CrmTeamMember> members;
    
    /** 团队管理的总联系人数 */
    private Integer totalContacts;
    
    /** 按业务类型统计 */
    private List<Map<String, Object>> businessTypeStats;
    
    /** 按成员统计 */
    private Map<Long, Integer> memberStats;
    
    /** 商机统计 */
    private Map<String, Object> opportunityStats;
    
    /** 合同统计 */
    private Map<String, Object> contractStats;
    
    /** 统计时间范围 */
    private String dateRange;

    public CrmTeam getTeamInfo() {
        return teamInfo;
    }

    public void setTeamInfo(CrmTeam teamInfo) {
        this.teamInfo = teamInfo;
    }

    public List<CrmTeamMember> getMembers() {
        return members;
    }

    public void setMembers(List<CrmTeamMember> members) {
        this.members = members;
    }

    public Integer getTotalContacts() {
        return totalContacts;
    }

    public void setTotalContacts(Integer totalContacts) {
        this.totalContacts = totalContacts;
    }

    public List<Map<String, Object>> getBusinessTypeStats() {
        return businessTypeStats;
    }

    public void setBusinessTypeStats(List<Map<String, Object>> businessTypeStats) {
        this.businessTypeStats = businessTypeStats;
    }

    public Map<Long, Integer> getMemberStats() {
        return memberStats;
    }

    public void setMemberStats(Map<Long, Integer> memberStats) {
        this.memberStats = memberStats;
    }

    public Map<String, Object> getOpportunityStats() {
        return opportunityStats;
    }

    public void setOpportunityStats(Map<String, Object> opportunityStats) {
        this.opportunityStats = opportunityStats;
    }

    public Map<String, Object> getContractStats() {
        return contractStats;
    }

    public void setContractStats(Map<String, Object> contractStats) {
        this.contractStats = contractStats;
    }

    public String getDateRange() {
        return dateRange;
    }

    public void setDateRange(String dateRange) {
        this.dateRange = dateRange;
    }

    @Override
    public String toString() {
        return "TeamPerformanceVO{" +
                "teamInfo=" + teamInfo +
                ", members=" + members +
                ", totalContacts=" + totalContacts +
                ", businessTypeStats=" + businessTypeStats +
                ", memberStats=" + memberStats +
                ", opportunityStats=" + opportunityStats +
                ", contractStats=" + contractStats +
                ", dateRange='" + dateRange + '\'' +
                '}';
    }
}