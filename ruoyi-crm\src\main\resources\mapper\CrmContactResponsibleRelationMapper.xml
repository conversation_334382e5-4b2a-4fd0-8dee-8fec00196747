<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmContactResponsibleRelationMapper">
    
    <resultMap type="CrmContactResponsibleRelation" id="CrmContactResponsibleRelationResult">
        <result property="id"                     column="id"                     />
        <result property="contactId"              column="contact_id"             />
        <result property="responsiblePersonId"    column="responsible_person_id"  />
        <result property="businessType"           column="business_type"          />
        <result property="relationStatus"         column="relation_status"        />
        <result property="startDate"              column="start_date"             />
        <result property="endDate"                column="end_date"               />
        <result property="teamId"                 column="team_id"                />
        <result property="assignType"             column="assign_type"            />
        <result property="assignBy"               column="assign_by"              />
        <result property="assignTime"             column="assign_time"            />
        <result property="createBy"               column="create_by"              />
        <result property="createTime"             column="create_time"            />
        <result property="updateBy"               column="update_by"              />
        <result property="updateTime"             column="update_time"            />
        <result property="remark"                 column="remark"                 />
        <!-- 关联查询字段 -->
        <result property="contactName"            column="contact_name"           />
        <result property="responsiblePersonName"  column="responsible_person_name"/>
        <result property="teamName"               column="team_name"              />
        <result property="assignByName"           column="assign_by_name"         />
        <result property="contactMobile"          column="contact_mobile"         />
        <result property="contactEmail"           column="contact_email"          />
        <result property="contactCompany"         column="contact_company"        />
    </resultMap>

    <sql id="selectCrmContactResponsibleRelationVo">
        select r.id, r.contact_id, r.responsible_person_id, r.business_type, r.relation_status, 
               r.start_date, r.end_date, r.team_id, r.assign_type, r.assign_by, r.assign_time,
               r.create_by, r.create_time, r.update_by, r.update_time, r.remark,
               c.name as contact_name, c.mobile as contact_mobile, c.email as contact_email,
               c.company as contact_company,
               u.nick_name as responsible_person_name,
               t.team_name as team_name,
               u2.nick_name as assign_by_name
        from crm_contact_responsible_relation r
        left join crm_contacts c on r.contact_id = c.id
        left join sys_user u on r.responsible_person_id = u.user_id
        left join crm_teams t on r.team_id = t.id
        left join sys_user u2 on r.assign_by = u2.user_id
    </sql>

    <select id="selectCrmContactResponsibleRelationList" parameterType="CrmContactResponsibleRelation" resultMap="CrmContactResponsibleRelationResult">
        <include refid="selectCrmContactResponsibleRelationVo"/>
        <where>  
            <if test="contactId != null">and r.contact_id = #{contactId}</if>
            <if test="responsiblePersonId != null">and r.responsible_person_id = #{responsiblePersonId}</if>
            <if test="businessType != null and businessType != ''">and r.business_type = #{businessType}</if>
            <if test="relationStatus != null and relationStatus != ''">and r.relation_status = #{relationStatus}</if>
            <if test="teamId != null">and r.team_id = #{teamId}</if>
            <if test="assignType != null and assignType != ''">and r.assign_type = #{assignType}</if>
            <if test="assignBy != null">and r.assign_by = #{assignBy}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(r.start_date,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(r.start_date,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by r.start_date desc
    </select>
    
    <select id="selectCrmContactResponsibleRelationById" parameterType="Long" resultMap="CrmContactResponsibleRelationResult">
        <include refid="selectCrmContactResponsibleRelationVo"/>
        where r.id = #{id}
    </select>

    <select id="selectByContactId" parameterType="Long" resultMap="CrmContactResponsibleRelationResult">
        <include refid="selectCrmContactResponsibleRelationVo"/>
        where r.contact_id = #{contactId}
        order by r.start_date desc
    </select>

    <select id="selectByResponsiblePersonId" parameterType="Long" resultMap="CrmContactResponsibleRelationResult">
        <include refid="selectCrmContactResponsibleRelationVo"/>
        where r.responsible_person_id = #{responsiblePersonId}
        order by r.start_date desc
    </select>

    <select id="selectByTeamId" parameterType="Long" resultMap="CrmContactResponsibleRelationResult">
        <include refid="selectCrmContactResponsibleRelationVo"/>
        where r.team_id = #{teamId}
        order by r.start_date desc
    </select>

    <select id="checkRelationExists" resultType="int">
        select count(1) from crm_contact_responsible_relation
        where contact_id = #{contactId} 
          and responsible_person_id = #{responsiblePersonId}
          and business_type = #{businessType}
    </select>

    <select id="checkActiveRelationExists" resultType="int">
        select count(1) from crm_contact_responsible_relation
        where contact_id = #{contactId} 
          and responsible_person_id = #{responsiblePersonId}
          and business_type = #{businessType}
          and relation_status = 'ACTIVE'
    </select>

    <select id="selectActiveResponsibleByContactAndBusiness" resultMap="CrmContactResponsibleRelationResult">
        <include refid="selectCrmContactResponsibleRelationVo"/>
        where r.contact_id = #{contactId}
          and r.business_type = #{businessType}
          and r.relation_status = 'ACTIVE'
        limit 1
    </select>

    <select id="selectUnassignedContactIds" resultType="Long">
        select c.id from crm_contacts c
        where c.del_flag = '0'
          and not exists (
            select 1 from crm_contact_responsible_relation r
            where r.contact_id = c.id
              and r.relation_status = 'ACTIVE'
              <if test="businessType != null and businessType != ''">
                and r.business_type = #{businessType}
              </if>
          )
    </select>

    <select id="selectTeamClaimableContactIds" resultType="Long">
        select c.id from crm_contacts c
        where c.del_flag = '0'
          and not exists (
            select 1 from crm_contact_responsible_relation r
            inner join crm_team_member tm on r.responsible_person_id = tm.user_id
            where r.contact_id = c.id
              and r.relation_status = 'ACTIVE'
              and tm.team_id = #{teamId}
              and tm.status = '0'
              <if test="businessType != null and businessType != ''">
                and r.business_type = #{businessType}
              </if>
          )
    </select>

    <select id="countTeamContacts" resultType="int">
        select count(distinct r.contact_id) 
        from crm_contact_responsible_relation r
        where r.team_id = #{teamId} and r.relation_status = 'ACTIVE'
    </select>

    <select id="countByBusinessTypeAndTeam" resultType="map">
        select 
            r.business_type as businessType,
            count(distinct r.contact_id) as contactCount,
            count(distinct r.responsible_person_id) as memberCount
        from crm_contact_responsible_relation r
        where r.team_id = #{teamId} 
          and r.relation_status = 'ACTIVE'
          <if test="startDate != null">
            and r.start_date &gt;= #{startDate}
          </if>
          <if test="endDate != null">
            and r.start_date &lt;= #{endDate}
          </if>
        group by r.business_type
    </select>

    <select id="countByMemberAndDateRange" resultType="map">
        select 
            r.responsible_person_id as userId,
            u.nick_name as userName,
            count(distinct r.contact_id) as contactCount,
            group_concat(distinct r.business_type) as businessTypes
        from crm_contact_responsible_relation r
        left join sys_user u on r.responsible_person_id = u.user_id
        where r.team_id = #{teamId} 
          and r.relation_status = 'ACTIVE'
          <if test="startDate != null">
            and r.start_date &gt;= #{startDate}
          </if>
          <if test="endDate != null">
            and r.start_date &lt;= #{endDate}
          </if>
        group by r.responsible_person_id, u.nick_name
    </select>

    <select id="selectResponsibleByContactIds" resultMap="CrmContactResponsibleRelationResult">
        <include refid="selectCrmContactResponsibleRelationVo"/>
        where r.contact_id in
        <foreach item="contactId" collection="contactIds" open="(" separator="," close=")">
            #{contactId}
        </foreach>
        and r.relation_status = 'ACTIVE'
        <if test="businessType != null and businessType != ''">
            and r.business_type = #{businessType}
        </if>
        order by r.contact_id, r.start_date desc
    </select>

    <select id="countContactsByResponsible" resultType="map">
        select 
            count(distinct r.contact_id) as totalContacts,
            count(distinct case when r.business_type = 'PACKAGING' then r.contact_id end) as packagingContacts,
            count(distinct case when r.business_type = 'PROTOTYPE' then r.contact_id end) as prototypeContacts,
            count(distinct case when r.business_type = '3D_PRINTING' then r.contact_id end) as printingContacts,
            count(distinct case when r.business_type = 'GENERAL' then r.contact_id end) as generalContacts
        from crm_contact_responsible_relation r
        where r.responsible_person_id = #{responsiblePersonId} 
          and r.relation_status = 'ACTIVE'
    </select>

    <insert id="insertCrmContactResponsibleRelation" parameterType="CrmContactResponsibleRelation" useGeneratedKeys="true" keyProperty="id">
        insert into crm_contact_responsible_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contactId != null">contact_id,</if>
            <if test="responsiblePersonId != null">responsible_person_id,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="relationStatus != null and relationStatus != ''">relation_status,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="teamId != null">team_id,</if>
            <if test="assignType != null and assignType != ''">assign_type,</if>
            <if test="assignBy != null">assign_by,</if>
            <if test="assignTime != null">assign_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contactId != null">#{contactId},</if>
            <if test="responsiblePersonId != null">#{responsiblePersonId},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="relationStatus != null and relationStatus != ''">#{relationStatus},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="assignType != null and assignType != ''">#{assignType},</if>
            <if test="assignBy != null">#{assignBy},</if>
            <if test="assignTime != null">#{assignTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="list">
        insert into crm_contact_responsible_relation
        (contact_id, responsible_person_id, business_type, relation_status, start_date, 
         team_id, assign_type, assign_by, assign_time, create_by, create_time, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.contactId}, #{item.responsiblePersonId}, #{item.businessType}, #{item.relationStatus}, 
             #{item.startDate}, #{item.teamId}, #{item.assignType}, #{item.assignBy}, #{item.assignTime},
             #{item.createBy}, #{item.createTime}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateCrmContactResponsibleRelation" parameterType="CrmContactResponsibleRelation">
        update crm_contact_responsible_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="responsiblePersonId != null">responsible_person_id = #{responsiblePersonId},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="relationStatus != null and relationStatus != ''">relation_status = #{relationStatus},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="assignType != null and assignType != ''">assign_type = #{assignType},</if>
            <if test="assignBy != null">assign_by = #{assignBy},</if>
            <if test="assignTime != null">assign_time = #{assignTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateRelationStatus">
        update crm_contact_responsible_relation
        set relation_status = #{newStatus},
            end_date = case when #{newStatus} != 'ACTIVE' then now() else null end,
            update_time = now()
        where contact_id = #{contactId}
          and responsible_person_id = #{responsiblePersonId}
          <if test="businessType != null and businessType != ''">
            and business_type = #{businessType}
          </if>
          and relation_status = #{oldStatus}
    </update>

    <update id="batchUpdateRelationStatus">
        update crm_contact_responsible_relation
        set relation_status = #{newStatus},
            end_date = #{endDate},
            update_by = #{updateBy},
            update_time = now()
        where contact_id in
        <foreach item="contactId" collection="contactIds" open="(" separator="," close=")">
            #{contactId}
        </foreach>
        and responsible_person_id = #{responsiblePersonId}
        and relation_status = #{oldStatus}
    </update>

    <delete id="deleteCrmContactResponsibleRelationById" parameterType="Long">
        delete from crm_contact_responsible_relation where id = #{id}
    </delete>

    <delete id="deleteCrmContactResponsibleRelationByIds" parameterType="String">
        delete from crm_contact_responsible_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 公海联系人多维度查询 -->
    <select id="selectPoolContacts" resultType="com.ruoyi.common.domain.entity.CrmContacts">
        select c.id, c.responsible_person_id, c.name, c.mobile, c.phone, c.telephone, c.email, 
               c.position, c.department, c.decision_role, c.contact_level, c.is_key_decision_maker, 
               c.direct_superior, c.address, c.detailed_address, c.next_contact_time, c.selected_date, 
               c.gender, c.birthday, c.status, c.remarks, c.del_flag, c.create_by, c.create_time, 
               c.update_by, c.update_time
        from crm_business_contacts c
        where c.del_flag = '0'
          <choose>
            <when test="queryType == 'PERSONAL'">
              and not exists (
                select 1 from crm_contact_responsible_relation r
                where r.contact_id = c.id
                  and r.relation_status = 'ACTIVE'
                  <if test="businessType != null and businessType != ''">
                    and r.business_type = #{businessType}
                  </if>
              )
            </when>
            <when test="queryType == 'TEAM'">
              and not exists (
                select 1 from crm_contact_responsible_relation r
                inner join crm_team_member tm on r.responsible_person_id = tm.user_id
                where r.contact_id = c.id
                  and r.relation_status = 'ACTIVE'
                  and tm.team_id = #{teamId}
                  and tm.status = '0'
                  <if test="businessType != null and businessType != ''">
                    and r.business_type = #{businessType}
                  </if>
              )
            </when>
            <when test="queryType == 'BUSINESS_TYPE'">
              and not exists (
                select 1 from crm_contact_responsible_relation r
                where r.contact_id = c.id
                  and r.relation_status = 'ACTIVE'
                  and r.business_type = #{businessType}
              )
            </when>
            <otherwise>
              and not exists (
                select 1 from crm_contact_responsible_relation r
                where r.contact_id = c.id
                  and r.relation_status = 'ACTIVE'
              )
            </otherwise>
          </choose>
        order by c.create_time desc
    </select>

</mapper>