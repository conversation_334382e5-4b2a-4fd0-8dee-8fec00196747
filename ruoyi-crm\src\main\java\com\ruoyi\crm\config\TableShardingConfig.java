package com.ruoyi.crm.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 表分片配置类 - 暂时注释掉，开发阶段不使用分表
 * 提供动态表名管理功能
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
// 开发阶段暂时注释掉分表功能
// @Configuration
public class TableShardingConfig {
    
    // @Autowired
    // private TableShardingManager tableShardingManager;
    
    /**
     * 表分片管理器 - 开发阶段暂时注释
     */
    // @Bean
    // public TableShardingManager tableShardingManager() {
    //     return new TableShardingManager();
    // }
    
    /**
     * 获取当前日期的分片表名 - 开发阶段暂时注释
     */
    // public String getCurrentShardingTableName(String baseTableName) {
    //     LocalDate now = LocalDate.now();
    //     
    //     try {
    //         switch (baseTableName) {
    //             case "crm_business_leads":
    //                 String leadsTableName = tableShardingManager.getLeadsTableName(now);
    //                 log.debug("获取分片表名: {} -> {}", baseTableName, leadsTableName);
    //                 return leadsTableName;
    //                 
    //             case "crm_business_customers":
    //                 String customersTableName = tableShardingManager.getCustomersTableName(now);
    //                 log.debug("获取分片表名: {} -> {}", baseTableName, customersTableName);
    //                 return customersTableName;
    //                 
    //             case "crm_lead_operation_log":
    //                 String leadLogTableName = tableShardingManager.getOperationLogTableName("lead", now);
    //                 log.debug("获取分片表名: {} -> {}", baseTableName, leadLogTableName);
    //                 return leadLogTableName;
    //                 
    //             case "crm_customer_operation_log":
    //                 String customerLogTableName = tableShardingManager.getOperationLogTableName("customer", now);
    //                 log.debug("获取分片表名: {} -> {}", baseTableName, customerLogTableName);
    //                 return customerLogTableName;
    //                 
    //             default:
    //                 return baseTableName;
    //         }
    //     } catch (Exception e) {
    //         log.error("获取分片表名失败: {}", baseTableName, e);
    //         return baseTableName; // 出错时返回原表名
    //     }
    // }
    
    /**
     * 手动指定表名的工具类
     */
    public static class TableNameContext {
        private static final ThreadLocal<Map<String, String>> TABLE_NAME_CONTEXT = new ThreadLocal<>();
        
        public static void setTableName(String logicalTableName, String actualTableName) {
            Map<String, String> tableNameMap = TABLE_NAME_CONTEXT.get();
            if (tableNameMap == null) {
                tableNameMap = new HashMap<>();
                TABLE_NAME_CONTEXT.set(tableNameMap);
            }
            tableNameMap.put(logicalTableName, actualTableName);
        }
        
        public static String getTableName(String logicalTableName) {
            Map<String, String> tableNameMap = TABLE_NAME_CONTEXT.get();
            return tableNameMap != null ? tableNameMap.get(logicalTableName) : null;
        }
        
        public static void clear() {
            TABLE_NAME_CONTEXT.remove();
        }
    }
} 