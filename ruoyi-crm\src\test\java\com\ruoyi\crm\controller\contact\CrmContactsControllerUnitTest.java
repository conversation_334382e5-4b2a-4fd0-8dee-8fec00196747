package com.ruoyi.crm.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmContacts;
import com.ruoyi.common.service.ICrmContactsService;
import com.ruoyi.crm.utils.TestWebContextUtils;

/**
 * CrmContactsController 纯单元测试
 * 不依赖Spring上下文，只使用Mockito
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("联系人控制器纯单元测试")
class CrmContactsControllerUnitTest {

    private static final Logger logger = LoggerFactory.getLogger(CrmContactsControllerUnitTest.class);

    @Mock
    private ICrmContactsService crmContactsService;

    @InjectMocks
    private CrmContactsController crmContactsController;

    private CrmContacts testContact;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testContact = new CrmContacts();
        testContact.setId(1L);
        testContact.setName("测试联系人");
        testContact.setMobile("13800138000");
        testContact.setPhone("010-12345678");
        testContact.setEmail("<EMAIL>");
        testContact.setPosition("产品经理");
        testContact.setIsKeyDecisionMaker("1");
        testContact.setResponsiblePersonId("1");
        testContact.setGender("M");
        testContact.setAddress("北京市朝阳区");
        testContact.setDetailedAddress("朝阳区望京街道");
        testContact.setRemarks("重要联系人");
        testContact.setDelFlag("0");
        testContact.setCreateTime(new Date());
        testContact.setUpdateTime(new Date());
    }

    @Nested
    @DisplayName("联系人查询测试")
    class QueryContactTests {

        @Nested
        @DisplayName("分页查询测试")
        class PaginationQueryTests {
            
            @BeforeEach
            void setUpWebContext() {
                // 只在分页查询测试中设置 Web 上下文
                TestWebContextUtils.setupDefaultWebContext();
            }

            @AfterEach
            void tearDownWebContext() {
                TestWebContextUtils.cleanupWebContext();
            }

            @Test
            @DisplayName("获取联系人列表")
            void testGetContactsList() {
                // ✅ 需要分页，所以需要 Web 上下文
                List<CrmContacts> contactList = Arrays.asList(testContact);
                when(crmContactsService.selectCrmContactsList(any(CrmContacts.class))).thenReturn(contactList);

                // Act
                TableDataInfo result = crmContactsController.getContactsList(new CrmContacts(), null);

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                assertEquals(1, result.getTotal());
                assertNotNull(result.getRows());
                verify(crmContactsService).selectCrmContactsList(any(CrmContacts.class));
            }

            @Test
            @DisplayName("获取联系人列表 - 带筛选条件")
            void testGetContactsList_WithFilter() {
                // Arrange
                CrmContacts filterContact = new CrmContacts();
                filterContact.setPosition("经理");
                
                List<CrmContacts> contactList = Arrays.asList(testContact);
                when(crmContactsService.selectCrmContactsList(any(CrmContacts.class))).thenReturn(contactList);

                // Act
                TableDataInfo result = crmContactsController.getContactsList(filterContact, null);

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                assertTrue(result.getTotal() >= 0);
                verify(crmContactsService).selectCrmContactsList(filterContact);
            }

            @Test
            @DisplayName("获取联系人列表 - 空结果")
            void testGetContactsList_EmptyResult() {
                // Arrange
                when(crmContactsService.selectCrmContactsList(any(CrmContacts.class))).thenReturn(Arrays.asList());

                // Act
                TableDataInfo result = crmContactsController.getContactsList(new CrmContacts(), null);

                // Assert
                assertNotNull(result);
                assertEquals(200, result.getCode());
                assertEquals(0, result.getTotal());
                assertNotNull(result.getRows());
                assertTrue(result.getRows().isEmpty());
                verify(crmContactsService).selectCrmContactsList(any(CrmContacts.class));
            }
        }

        @Nested
        @DisplayName("单个查询测试")
        class SingleQueryTests {
            
            @Test
            @DisplayName("获取联系人详细信息")
            void testGetInfo() {
                // ✅ 不需要分页，所以不设置 Web 上下文
                // Arrange
                when(crmContactsService.selectCrmContactsById(1L)).thenReturn(testContact);

                // Act
                AjaxResult result = crmContactsController.getInfo(1L);

                // Assert
                assertNotNull(result);
                assertTrue(result.isSuccess());
                assertEquals(testContact, result.get("data"));
                verify(crmContactsService).selectCrmContactsById(1L);
            }

            @Test
            @DisplayName("获取联系人详细信息 - 联系人不存在")
            void testGetInfo_ContactNotFound() {
                // Arrange
                when(crmContactsService.selectCrmContactsById(999L)).thenReturn(null);

                // Act
                AjaxResult result = crmContactsController.getInfo(999L);

                // Assert
                assertNotNull(result);
                assertTrue(result.isSuccess());
                assertNull(result.get("data"));
                verify(crmContactsService).selectCrmContactsById(999L);
            }

            @Test
            @DisplayName("获取联系人详细信息 - 服务异常")
            void testGetInfo_ServiceException() {
                // Arrange
                when(crmContactsService.selectCrmContactsById(1L))
                        .thenThrow(new RuntimeException("数据库连接失败"));

                // Act & Assert
                RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                    crmContactsController.getInfo(1L);
                });
                
                assertEquals("数据库连接失败", exception.getMessage());
                verify(crmContactsService).selectCrmContactsById(1L);
            }
        }
    }

    @Nested
    @DisplayName("联系人CRUD测试")
    class CrudContactTests {

        @Test
        @DisplayName("新增联系人 - 成功")
        void testAdd_Success() {
            // Arrange
            when(crmContactsService.insertCrmContacts(any(CrmContacts.class))).thenReturn(1);

            // Act
            AjaxResult result = crmContactsController.addWithRecord(testContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmContactsService).insertCrmContacts(testContact);
        }

        @Test
        @DisplayName("新增联系人 - 失败")
        void testAdd_Failed() {
            // Arrange
            when(crmContactsService.insertCrmContacts(any(CrmContacts.class))).thenReturn(0);

            // Act
            AjaxResult result = crmContactsController.addWithRecord(testContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            verify(crmContactsService).insertCrmContacts(testContact);
        }

        @Test
        @DisplayName("新增联系人 - 异常")
        void testAdd_Exception() {
            // Arrange
            when(crmContactsService.insertCrmContacts(any(CrmContacts.class)))
                    .thenThrow(new RuntimeException("数据库连接失败"));

            // Act
            AjaxResult result = crmContactsController.addWithRecord(testContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("数据库连接失败", result.get("msg"));
            verify(crmContactsService).insertCrmContacts(testContact);
        }

        @Test
        @DisplayName("新增联系人 - 空对象")
        void testAdd_NullContact() {
            // Arrange - Service层会返回0（因为有null检查）
            // 不需要mock，因为真实Service会处理null并返回0

            // Act
            AjaxResult result = crmContactsController.addWithRecord(null);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            // 验证Service方法被调用，但不验证Mapper
            verify(crmContactsService).insertCrmContacts(null);
        }

        @Test
        @DisplayName("修改联系人 - 成功")
        void testEdit_Success() {
            // Arrange
            testContact.setName("修改后的联系人");
            testContact.setPosition("高级产品经理");
            when(crmContactsService.updateCrmContacts(any(CrmContacts.class))).thenReturn(1);

            // Act
            AjaxResult result = crmContactsController.editWithRecord(testContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmContactsService).updateCrmContacts(testContact);
        }

        @Test
        @DisplayName("修改联系人 - 失败")
        void testEdit_Failed() {
            // Arrange
            when(crmContactsService.updateCrmContacts(any(CrmContacts.class))).thenReturn(0);

            // Act
            AjaxResult result = crmContactsController.editWithRecord(testContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            verify(crmContactsService).updateCrmContacts(testContact);
        }

        @Test
        @DisplayName("修改联系人 - 异常")
        void testEdit_Exception() {
            // Arrange
            when(crmContactsService.updateCrmContacts(any(CrmContacts.class)))
                    .thenThrow(new RuntimeException("更新失败"));

            // Act
            AjaxResult result = crmContactsController.editWithRecord(testContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("更新失败", result.get("msg"));
            verify(crmContactsService).updateCrmContacts(testContact);
        }

        @Test
        @DisplayName("删除联系人 - 成功")
        void testRemove_Success() {
            // Arrange
            Long[] ids = {1L, 2L};
            when(crmContactsService.deleteCrmContactsByIds(ids)).thenReturn(2);

            // Act
            AjaxResult result = crmContactsController.remove(ids);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmContactsService).deleteCrmContactsByIds(ids);
        }

        @Test
        @DisplayName("删除联系人 - 部分失败")
        void testRemove_PartialSuccess() {
            // Arrange
            Long[] ids = {1L, 999L};
            when(crmContactsService.deleteCrmContactsByIds(ids)).thenReturn(1);

            // Act
            AjaxResult result = crmContactsController.remove(ids);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmContactsService).deleteCrmContactsByIds(ids);
        }

        @Test
        @DisplayName("删除联系人 - 异常")
        void testRemove_Exception() {
            // Arrange
            Long[] ids = {1L, 2L};
            when(crmContactsService.deleteCrmContactsByIds(ids))
                    .thenThrow(new RuntimeException("删除失败"));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                crmContactsController.remove(ids);
            });
            
            assertEquals("删除失败", exception.getMessage());
            verify(crmContactsService).deleteCrmContactsByIds(ids);
        }

        @Test
        @DisplayName("删除联系人 - 空数组")
        void testRemove_EmptyArray() {
            // Arrange
            Long[] ids = {};
            when(crmContactsService.deleteCrmContactsByIds(ids)).thenReturn(0);

            // Act
            AjaxResult result = crmContactsController.remove(ids);

            // Assert
            assertNotNull(result);
            logger.warn("result: {}", result);
            // 空数组删除返回0行，根据BaseController.toAjax逻辑应该返回error
            assertTrue(result.isError());
            verify(crmContactsService).deleteCrmContactsByIds(ids);
        }
    }

    @Nested
    @DisplayName("导出功能测试")
    class ExportTests {

        @Test
        @DisplayName("导出联系人")
        void testExport_Success() {
            // Arrange
            List<CrmContacts> contactList = Arrays.asList(testContact);
            when(crmContactsService.selectCrmContactsList(any(CrmContacts.class))).thenReturn(contactList);

            // Act
            AjaxResult result = crmContactsController.export(new CrmContacts());

            // Assert
            assertNotNull(result);
            // 导出功能会返回文件名或成功信息
            verify(crmContactsService).selectCrmContactsList(any(CrmContacts.class));
        }

        @Test
        @DisplayName("导出联系人 - 带筛选条件")
        void testExport_WithFilter() {
            // Arrange
            CrmContacts filterContact = new CrmContacts();
     
            
            List<CrmContacts> contactList = Arrays.asList(testContact);
            when(crmContactsService.selectCrmContactsList(any(CrmContacts.class))).thenReturn(contactList);

            // Act
            AjaxResult result = crmContactsController.export(filterContact);

            // Assert
            assertNotNull(result);
            verify(crmContactsService).selectCrmContactsList(filterContact);
        }

        @Test
        @DisplayName("导出联系人 - 空数据")
        void testExport_EmptyData() {
            // Arrange
            when(crmContactsService.selectCrmContactsList(any(CrmContacts.class))).thenReturn(Arrays.asList());

            // Act
            AjaxResult result = crmContactsController.export(new CrmContacts());

            // Assert
            assertNotNull(result);
            verify(crmContactsService).selectCrmContactsList(any(CrmContacts.class));
        }

        @Test
        @DisplayName("导出联系人 - 服务异常")
        void testExport_ServiceException() {
            // Arrange
            when(crmContactsService.selectCrmContactsList(any(CrmContacts.class)))
                    .thenThrow(new RuntimeException("查询数据失败"));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                crmContactsController.export(new CrmContacts());
            });
            
            assertEquals("查询数据失败", exception.getMessage());
            verify(crmContactsService).selectCrmContactsList(any(CrmContacts.class));
        }
    }

    @Nested
    @DisplayName("参数验证测试")
    class ParameterValidationTests {

        @Test
        @DisplayName("ID参数验证 - null值")
        void testIdParameter_Null() {
            // Arrange
            when(crmContactsService.selectCrmContactsById(null)).thenReturn(null);

            // Act
            AjaxResult result = crmContactsController.getInfo(null);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNull(result.get("data"));
            verify(crmContactsService).selectCrmContactsById(null);
        }

        @Test
        @DisplayName("ID参数验证 - 负数")
        void testIdParameter_Negative() {
            // Arrange
            when(crmContactsService.selectCrmContactsById(-1L)).thenReturn(null);

            // Act
            AjaxResult result = crmContactsController.getInfo(-1L);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNull(result.get("data"));
            verify(crmContactsService).selectCrmContactsById(-1L);
        }

        @Test
        @DisplayName("联系人对象验证 - 必填字段为空")
        void testContactObject_RequiredFieldsEmpty() {
            // Arrange
            CrmContacts emptyContact = new CrmContacts();
            // 只设置ID，其他必填字段为空
            emptyContact.setId(1L);
            
            when(crmContactsService.insertCrmContacts(any(CrmContacts.class))).thenReturn(1);

            // Act
            AjaxResult result = crmContactsController.addWithRecord(emptyContact);

            // Assert
            assertNotNull(result);
            // 根据实际业务逻辑，这里可能成功也可能失败
            verify(crmContactsService).insertCrmContacts(emptyContact);
        }

        @Test
        @DisplayName("联系人对象验证 - 特殊字符")
        void testContactObject_SpecialCharacters() {
            // Arrange
            CrmContacts specialContact = new CrmContacts();
            specialContact.setName("张三\n李四");
            specialContact.setEmail("test@测试.com");
            specialContact.setRemarks("备注\t包含特殊字符&<>\"'");
            
            when(crmContactsService.insertCrmContacts(any(CrmContacts.class))).thenReturn(1);

            // Act
            AjaxResult result = crmContactsController.addWithRecord(specialContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmContactsService).insertCrmContacts(specialContact);
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryTests {

        @Test
        @DisplayName("处理极大ID值")
        void testHandleMaxId() {
            // Arrange
            Long maxId = Long.MAX_VALUE;
            when(crmContactsService.selectCrmContactsById(maxId)).thenReturn(null);

            // Act
            AjaxResult result = crmContactsController.getInfo(maxId);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNull(result.get("data"));
            verify(crmContactsService).selectCrmContactsById(maxId);
        }

        @Test
        @DisplayName("处理大批量删除")
        void testHandleLargeBatchDelete() {
            // Arrange
            Long[] largeIds = new Long[1000];
            for (int i = 0; i < 1000; i++) {
                largeIds[i] = (long) (i + 1);
            }
            when(crmContactsService.deleteCrmContactsByIds(largeIds)).thenReturn(1000);

            // Act
            AjaxResult result = crmContactsController.remove(largeIds);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmContactsService).deleteCrmContactsByIds(largeIds);
        }

        @Test
        @DisplayName("处理长文本字段")
        void testHandleLongTextFields() {
            // Arrange
            CrmContacts longTextContact = new CrmContacts();
            longTextContact.setName("A".repeat(100)); // 长姓名
            longTextContact.setRemarks("B".repeat(1000)); // 长备注
            longTextContact.setDetailedAddress("C".repeat(500)); // 长地址
            
            when(crmContactsService.insertCrmContacts(any(CrmContacts.class))).thenReturn(1);

            // Act
            AjaxResult result = crmContactsController.addWithRecord(longTextContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(crmContactsService).insertCrmContacts(longTextContact);
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("处理运行时异常")
        void testHandleRuntimeException() {
            // Arrange
            when(crmContactsService.selectCrmContactsById(1L))
                    .thenThrow(new RuntimeException("意外的运行时异常"));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                crmContactsController.getInfo(1L);
            });
            
            assertEquals("意外的运行时异常", exception.getMessage());
            verify(crmContactsService).selectCrmContactsById(1L);
        }

        @Test
        @DisplayName("处理空指针异常")
        void testHandleNullPointerException() {
            // Arrange
            when(crmContactsService.insertCrmContacts(any(CrmContacts.class)))
                    .thenThrow(new NullPointerException("空指针异常"));

            // Act
            AjaxResult result = crmContactsController.addWithRecord(testContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("空指针异常", result.get("msg"));
            verify(crmContactsService).insertCrmContacts(testContact);
        }

        @Test
        @DisplayName("处理SQL异常")
        void testHandleSQLException() {
            // Arrange
            when(crmContactsService.updateCrmContacts(any(CrmContacts.class)))
                    .thenThrow(new RuntimeException("SQL语法错误"));

            // Act
            AjaxResult result = crmContactsController.editWithRecord(testContact);

            // Assert
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("SQL语法错误", result.get("msg"));
            verify(crmContactsService).updateCrmContacts(testContact);
        }

        @Test
        @DisplayName("验证异常处理策略差异")
        void testExceptionHandlingStrategy() {
            // 1. 有try-catch的方法：异常被捕获，返回错误结果
            // Arrange
            when(crmContactsService.insertCrmContacts(any(CrmContacts.class)))
                    .thenThrow(new RuntimeException("服务异常"));

            // Act
            AjaxResult result = crmContactsController.addWithRecord(testContact);

            // Assert - 异常被捕获，返回错误结果
            assertNotNull(result);
            assertTrue(result.isError());
            assertEquals("服务异常", result.get("msg"));

            // 2. 没有try-catch的方法：异常直接抛出
            // Arrange
            when(crmContactsService.selectCrmContactsById(1L))
                    .thenThrow(new RuntimeException("查询异常"));

            // Act & Assert - 异常直接抛出
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                crmContactsController.getInfo(1L);
            });
            
            assertEquals("查询异常", exception.getMessage());
            
            verify(crmContactsService).insertCrmContacts(testContact);
            verify(crmContactsService).selectCrmContactsById(1L);
        }
    }
} 