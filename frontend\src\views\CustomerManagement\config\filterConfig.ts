import type { CommonFilterConfig } from '@/types';

// 筛选类型选项
const filterOptions = [
    { label: '全部客户', value: 'all' },
    { label: '我负责的', value: 'mine' },
    { label: '下属负责的', value: 'subordinate' },
    { label: '我关注的客户', value: 'following' },
    { label: '未分配的', value: 'unassigned' }
];

// 客户筛选配置
export const customerFilterConfig: CommonFilterConfig = {
    search: {
        placeholder: '客户名称/手机/电话/邮箱',
        width: '240px',
        icon: 'Search',
        debounceTime: 300
    },
    filter: {
        label: '显示：',
        options: filterOptions,
        buttonStyle: true,
        size: 'default'
    }
}; 