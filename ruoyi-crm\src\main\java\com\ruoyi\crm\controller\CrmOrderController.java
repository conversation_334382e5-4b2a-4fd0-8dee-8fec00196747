package com.ruoyi.crm.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.dto.CrmOrderAssignmentDTO;
import com.ruoyi.common.domain.dto.CrmOrderDTO;
import com.ruoyi.common.domain.dto.CrmOrderQueryDTO;
import com.ruoyi.common.domain.entity.CrmOrder;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.service.ICrmOrderService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 3D打印订单控制器
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Api(tags = "CRM订单管理")
@RestController
@RequestMapping("/crm/order")
public class CrmOrderController extends BaseController {
    @Autowired
    private ICrmOrderService crmOrderService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('crm:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmOrder crmOrder) {
        startPage();
        List<CrmOrder> list = crmOrderService.selectCrmOrderList(crmOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('crm:order:export')")
    @Log(title = "订单管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(CrmOrder crmOrder) {
        List<CrmOrder> list = crmOrderService.selectCrmOrderList(crmOrder);
        ExcelUtil<CrmOrder> util = new ExcelUtil<CrmOrder>(CrmOrder.class);
        return util.exportExcel(list, "订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(crmOrderService.selectCrmOrderById(id));
    }

    /**
     * 根据订单编号获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('crm:order:query')")
    @GetMapping(value = "/orderNo/{orderNo}")
    public AjaxResult getInfoByOrderNo(@PathVariable("orderNo") String orderNo) {
        return success(crmOrderService.selectCrmOrderByOrderNo(orderNo));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('crm:order:add')")
    @Log(title = "订单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmOrder crmOrder) {
        return toAjax(crmOrderService.insertCrmOrder(crmOrder));
    }

    /**
     * 创建3D打印订单
     */
    @Anonymous
    @Log(title = "3D打印订单", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createPrintOrder(
            @RequestParam("quoteData") String quoteData,
            @RequestParam("customerInfo") String customerInfo,
            @RequestParam(value = "files", required = false) MultipartFile[] files) {
        try {
            CrmOrder order = crmOrderService.createPrintOrder(quoteData, customerInfo, files);
            
            // 构建返回数据
            AjaxResult result = AjaxResult.success("订单创建成功");
            result.put("orderId", order.getId());
            result.put("orderNo", order.getOrderNo());
            result.put("status", order.getStatus());
            result.put("createTime", order.getCreateTime());
            
            if (order.getFileUrls() != null && !order.getFileUrls().isEmpty()) {
                result.put("fileUrls", order.getFileUrls());
            }
            
            return result;
        } catch (Exception e) {
            logger.error("创建3D打印订单失败", e);
            return AjaxResult.error("订单创建失败: " + e.getMessage());
        }
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('crm:order:edit')")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmOrder crmOrder) {
        return toAjax(crmOrderService.updateCrmOrder(crmOrder));
    }

    /**
     * 更新订单状态
     */
    @PreAuthorize("@ss.hasPermi('crm:order:edit')")
    @Log(title = "订单状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}")
    public AjaxResult updateStatus(@PathVariable Long id, @RequestParam String status) {
        Long operatorId = SecurityUtils.getUserId();
        return toAjax(crmOrderService.updateOrderStatus(id, status, operatorId));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('crm:order:remove')")
    @Log(title = "订单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(crmOrderService.deleteCrmOrderByIds(ids));
    }

    // ==================== 新增的订单管理API ====================

    /**
     * 分页查询订单列表
     */
    @ApiOperation("分页查询订单列表")
    @PreAuthorize("@ss.hasPermi('crm:order:list')")
    @PostMapping("/page")
    public TableDataInfo page(@RequestBody CrmOrderQueryDTO queryDTO) {
        return crmOrderService.selectCrmOrderPage(queryDTO);
    }

    /**
     * 获取订单详细信息（包含关联信息）
     */
    @ApiOperation("获取订单详细信息")
    @PreAuthorize("@ss.hasPermi('crm:order:query')")
    @GetMapping(value = "/detail/{id}")
    public AjaxResult getOrderDetail(@ApiParam("订单ID") @PathVariable("id") Long id) {
        CrmOrderDTO orderDTO = crmOrderService.getCrmOrderDetail(id);
        return success(orderDTO);
    }

    /**
     * 创建订单（包含订单项）
     */
    @ApiOperation("创建订单")
    @PreAuthorize("@ss.hasPermi('crm:order:add')")
    @Log(title = "订单创建", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createOrder(@RequestBody CrmOrderDTO orderDTO) {
        return toAjax(crmOrderService.createCrmOrder(orderDTO));
    }

    /**
     * 批量更新订单状态
     */
    @ApiOperation("批量更新订单状态")
    @PreAuthorize("@ss.hasPermi('crm:order:edit')")
    @Log(title = "批量订单状态更新", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/status")
    public AjaxResult batchUpdateStatus(
            @ApiParam("订单ID列表") @RequestParam List<Long> ids,
            @ApiParam("新状态") @RequestParam String status) {
        Long operatorId = SecurityUtils.getUserId();
        return toAjax(crmOrderService.batchUpdateOrderStatus(ids, status, operatorId));
    }

    /**
     * 分配订单
     */
    @ApiOperation("分配订单")
    @PreAuthorize("@ss.hasPermi('crm:order:assign')")
    @Log(title = "订单分配", businessType = BusinessType.UPDATE)
    @PostMapping("/assign")
    public AjaxResult assignOrder(@RequestBody CrmOrderAssignmentDTO assignmentDTO) {
        // 设置操作人信息
        assignmentDTO.setOperatorId(SecurityUtils.getUserId());
        assignmentDTO.setOperatorName(SecurityUtils.getUsername());
        assignmentDTO.setIpAddress(request.getRemoteAddr());
        assignmentDTO.setUserAgent(request.getHeader("User-Agent"));

        return toAjax(crmOrderService.assignOrder(assignmentDTO));
    }

    /**
     * 批量分配订单
     */
    @ApiOperation("批量分配订单")
    @PreAuthorize("@ss.hasPermi('crm:order:assign')")
    @Log(title = "批量订单分配", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/assign")
    public AjaxResult batchAssignOrders(@RequestBody CrmOrderAssignmentDTO assignmentDTO) {
        // 设置操作人信息
        assignmentDTO.setOperatorId(SecurityUtils.getUserId());
        assignmentDTO.setOperatorName(SecurityUtils.getUsername());
        assignmentDTO.setIpAddress(request.getRemoteAddr());
        assignmentDTO.setUserAgent(request.getHeader("User-Agent"));

        return toAjax(crmOrderService.batchAssignOrders(assignmentDTO));
    }

    /**
     * 转移订单
     */
    @ApiOperation("转移订单")
    @PreAuthorize("@ss.hasPermi('crm:order:transfer')")
    @Log(title = "订单转移", businessType = BusinessType.UPDATE)
    @PostMapping("/transfer")
    public AjaxResult transferOrder(@RequestBody CrmOrderAssignmentDTO assignmentDTO) {
        // 设置操作人信息
        assignmentDTO.setOperatorId(SecurityUtils.getUserId());
        assignmentDTO.setOperatorName(SecurityUtils.getUsername());

        return toAjax(crmOrderService.transferOrder(assignmentDTO));
    }

    /**
     * 抢单
     */
    @ApiOperation("抢单")
    @PreAuthorize("@ss.hasPermi('crm:order:grab')")
    @Log(title = "订单抢单", businessType = BusinessType.UPDATE)
    @PostMapping("/grab/{orderId}")
    public AjaxResult grabOrder(@ApiParam("订单ID") @PathVariable Long orderId) {
        Long userId = SecurityUtils.getUserId();
        return toAjax(crmOrderService.grabOrder(orderId, userId));
    }

    /**
     * 回收订单
     */
    @ApiOperation("回收订单")
    @PreAuthorize("@ss.hasPermi('crm:order:reclaim')")
    @Log(title = "订单回收", businessType = BusinessType.UPDATE)
    @PostMapping("/reclaim")
    public AjaxResult reclaimOrder(
            @ApiParam("订单ID") @RequestParam Long orderId,
            @ApiParam("回收原因") @RequestParam String reason) {
        Long operatorId = SecurityUtils.getUserId();
        return toAjax(crmOrderService.reclaimOrder(orderId, operatorId, reason));
    }

    /**
     * 获取我的订单列表
     */
    @ApiOperation("获取我的订单列表")
    @PreAuthorize("@ss.hasPermi('crm:order:list')")
    @PostMapping("/my")
    public TableDataInfo getMyOrders(@RequestBody CrmOrderQueryDTO queryDTO) {
        Long userId = SecurityUtils.getUserId();
        return crmOrderService.getMyOrders(userId, queryDTO);
    }

    /**
     * 获取未分配订单列表
     */
    @ApiOperation("获取未分配订单列表")
    @PreAuthorize("@ss.hasPermi('crm:order:list')")
    @PostMapping("/unassigned")
    public TableDataInfo getUnassignedOrders(@RequestBody CrmOrderQueryDTO queryDTO) {
        return crmOrderService.getUnassignedOrders(queryDTO);
    }

    /**
     * 获取部门订单列表
     */
    @ApiOperation("获取部门订单列表")
    @PreAuthorize("@ss.hasPermi('crm:order:list')")
    @PostMapping("/dept/{deptId}")
    public TableDataInfo getDeptOrders(
            @ApiParam("部门ID") @PathVariable Long deptId,
            @RequestBody CrmOrderQueryDTO queryDTO) {
        return crmOrderService.getDeptOrders(deptId, queryDTO);
    }

    /**
     * 检查订单是否可以分配
     */
    @ApiOperation("检查订单是否可以分配")
    @PreAuthorize("@ss.hasPermi('crm:order:list')")
    @GetMapping("/check/assign")
    public AjaxResult checkCanAssign(
            @ApiParam("订单ID") @RequestParam Long orderId,
            @ApiParam("用户ID") @RequestParam Long userId) {
        boolean canAssign = crmOrderService.canAssignOrder(orderId, userId);
        return success(canAssign);
    }

    /**
     * 检查用户是否可以抢单
     */
    @ApiOperation("检查用户是否可以抢单")
    @PreAuthorize("@ss.hasPermi('crm:order:list')")
    @GetMapping("/check/grab/{orderId}")
    public AjaxResult checkCanGrab(@ApiParam("订单ID") @PathVariable Long orderId) {
        Long userId = SecurityUtils.getUserId();
        boolean canGrab = crmOrderService.canGrabOrder(orderId, userId);
        return success(canGrab);
    }
}