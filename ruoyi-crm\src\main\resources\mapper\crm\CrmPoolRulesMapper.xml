<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmPoolRulesMapper">
    
    <resultMap type="com.ruoyi.common.domain.entity.CrmPoolRules" id="CrmPoolRulesResult">
        <result property="id"    column="id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="daysLimit"    column="days_limit"    />
        <result property="enabled"    column="enabled"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <insert id="insert" parameterType="com.ruoyi.common.domain.entity.CrmPoolRules" useGeneratedKeys="true" keyProperty="id">
        insert into crm_pool_rules
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null">rule_name,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="daysLimit != null">days_limit,</if>
            <if test="enabled != null">enabled,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null">#{ruleName},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="daysLimit != null">#{daysLimit},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.common.domain.entity.CrmPoolRules">
        update crm_pool_rules
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="ruleType != null">rule_type = #{ruleType},</if>
            <if test="daysLimit != null">days_limit = #{daysLimit},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectList" parameterType="com.ruoyi.common.domain.entity.CrmPoolRules" resultMap="CrmPoolRulesResult">
        select * from crm_pool_rules
        <where>
            <if test="ruleName != null and ruleName != ''">and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleType != null and ruleType != ''">and rule_type = #{ruleType}</if>
            <if test="enabled != null">and enabled = #{enabled}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectById" parameterType="Long" resultMap="CrmPoolRulesResult">
        select * from crm_pool_rules where id = #{id}
    </select>

    <delete id="deleteById" parameterType="Long">
        delete from crm_pool_rules where id = #{id}
    </delete>

</mapper>