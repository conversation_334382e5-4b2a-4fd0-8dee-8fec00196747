package com.ruoyi.crm.domain.vo;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 团队分配请求VO
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class TeamAssignRequest {
    
    /** 团队ID */
    @NotNull(message = "团队ID不能为空")
    private Long teamId;
    
    /** 被分配的用户ID */
    @NotNull(message = "被分配用户ID不能为空")
    private Long assignToUserId;
    
    /** 联系人ID列表 */
    @NotEmpty(message = "联系人ID列表不能为空")
    private List<Long> contactIds;
    
    /** 业务类型 */
    private String businessType;
    
    /** 备注 */
    private String remark;

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getAssignToUserId() {
        return assignToUserId;
    }

    public void setAssignToUserId(Long assignToUserId) {
        this.assignToUserId = assignToUserId;
    }

    public List<Long> getContactIds() {
        return contactIds;
    }

    public void setContactIds(List<Long> contactIds) {
        this.contactIds = contactIds;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TeamAssignRequest{" +
                "teamId=" + teamId +
                ", assignToUserId=" + assignToUserId +
                ", contactIds=" + contactIds +
                ", businessType='" + businessType + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}