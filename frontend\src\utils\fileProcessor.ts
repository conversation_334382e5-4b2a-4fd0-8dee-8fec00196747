import * as THREE from 'three'
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader'
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { PLYLoader } from 'three/examples/jsm/loaders/PLYLoader'
import { ColladaLoader } from 'three/examples/jsm/loaders/ColladaLoader'

// 支持的文件类型
export const SUPPORTED_3D_FORMATS = ['stl', 'obj', 'fbx', 'gltf', 'glb', 'ply', 'dae']

// 文件格式信息
export const FORMAT_INFO = {
  stl: { name: 'STL', description: '立体光刻文件格式，广泛用于3D打印' },
  obj: { name: 'OBJ', description: 'Wavefront OBJ 3D模型格式' },
  fbx: { name: 'FBX', description: 'Autodesk FBX 3D模型格式' },
  gltf: { name: 'glTF', description: 'GL传输格式，Web 3D标准' },
  glb: { name: 'GLB', description: 'glTF二进制格式' },
  ply: { name: 'PLY', description: 'Stanford多边形文件格式' },
  dae: { name: 'COLLADA', description: 'COLLADA数字资产交换格式' }
}

interface ProcessedFile {
  url: string
  thumbnail: string
  isSupported: boolean
  message?: string
}

export class FileProcessor {
  static async process(file: File): Promise<ProcessedFile> {
    const extension = file.name.split('.').pop()?.toLowerCase()
    
    // 特殊处理STEP格式
    if (extension === 'step' || extension === 'stp') {
      return {
        url: URL.createObjectURL(file),
        thumbnail: '',
        isSupported: false,
        message: 'STEP/STP是CAD专用格式，暂不支持3D预览。请转换为STL、OBJ等格式后重新上传，或联系客服协助处理。'
      }
    }
    
    if (!extension || !SUPPORTED_3D_FORMATS.includes(extension)) {
      return {
        url: URL.createObjectURL(file),
        thumbnail: '',
        isSupported: false,
        message: `不支持的文件格式。请使用以下格式之一：${SUPPORTED_3D_FORMATS.join(', ')}`
      }
    }

    try {
      const thumbnail = await FileProcessor.generateThumbnail(file, extension)
      return {
        url: URL.createObjectURL(file),
        thumbnail,
        isSupported: true
      }
    } catch (error) {
      console.error('生成缩略图失败:', error)
      return {
        url: URL.createObjectURL(file),
        thumbnail: '',
        isSupported: false,
        message: '生成缩略图失败，请检查文件是否损坏'
      }
    }
  }

  static async extractModelInfo(file: File, extension: string): Promise<{
    dimensions?: string
    volume?: string
    surfaceArea?: string
  }> {
    try {
      let geometry: THREE.BufferGeometry | null = null
      
      switch (extension.toLowerCase()) {
        case 'stl':
          geometry = await FileProcessor.loadSTL(file)
          break
        case 'obj':
          geometry = await FileProcessor.loadOBJ(file)
          break
        case 'ply':
          geometry = await FileProcessor.loadPLY(file)
          break
        case 'gltf':
        case 'glb':
          geometry = await FileProcessor.loadGLTF(file)
          break
        case 'fbx':
          geometry = await FileProcessor.loadFBX(file)
          break
        case 'dae':
          geometry = await FileProcessor.loadCollada(file)
          break
        default:
          return {}
      }
      
      if (geometry) {
        const box = new THREE.Box3().setFromObject(new THREE.Mesh(geometry))
        const size = box.getSize(new THREE.Vector3())
        
        return {
          dimensions: `${size.x.toFixed(2)} x ${size.y.toFixed(2)} x ${size.z.toFixed(2)}`,
          volume: (size.x * size.y * size.z).toFixed(2),
          surfaceArea: FileProcessor.calculateSurfaceArea(geometry).toFixed(2)
        }
      }
    } catch (error) {
      console.error(`提取${extension}文件信息失败:`, error)
    }
    
    return {}
  }

  private static async loadSTL(file: File): Promise<THREE.BufferGeometry> {
    return new Promise((resolve, reject) => {
      const loader = new STLLoader()
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const geometry = loader.parse(e.target?.result as ArrayBuffer)
          resolve(geometry)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsArrayBuffer(file)
    })
  }

  private static calculateSurfaceArea(geometry: THREE.BufferGeometry): number {
    const position = geometry.attributes.position
    const index = geometry.index
    let area = 0

    if (index) {
      for (let i = 0; i < index.count; i += 3) {
        const a = index.getX(i)
        const b = index.getX(i + 1)
        const c = index.getX(i + 2)
        
        const vA = new THREE.Vector3().fromBufferAttribute(position, a)
        const vB = new THREE.Vector3().fromBufferAttribute(position, b)
        const vC = new THREE.Vector3().fromBufferAttribute(position, c)
        
        area += FileProcessor.triangleArea(vA, vB, vC)
      }
    } else {
      for (let i = 0; i < position.count; i += 3) {
        const vA = new THREE.Vector3().fromBufferAttribute(position, i)
        const vB = new THREE.Vector3().fromBufferAttribute(position, i + 1)
        const vC = new THREE.Vector3().fromBufferAttribute(position, i + 2)
        
        area += FileProcessor.triangleArea(vA, vB, vC)
      }
    }

    return area
  }

  private static triangleArea(a: THREE.Vector3, b: THREE.Vector3, c: THREE.Vector3): number {
    const ab = new THREE.Vector3().subVectors(b, a)
    const ac = new THREE.Vector3().subVectors(c, a)
    const cross = new THREE.Vector3().crossVectors(ab, ac)
    return cross.length() / 2
  }

  // OBJ文件加载器
  private static async loadOBJ(file: File): Promise<THREE.BufferGeometry | null> {
    return new Promise((resolve, reject) => {
      const loader = new OBJLoader()
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const object = loader.parse(e.target?.result as string)
          // 从Group中提取第一个几何体
          let geometry: THREE.BufferGeometry | null = null
          object.traverse((child) => {
            if (child instanceof THREE.Mesh && child.geometry) {
              geometry = child.geometry
            }
          })
          resolve(geometry)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsText(file)
    })
  }

  // PLY文件加载器
  private static async loadPLY(file: File): Promise<THREE.BufferGeometry> {
    return new Promise((resolve, reject) => {
      const loader = new PLYLoader()
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const geometry = loader.parse(e.target?.result as ArrayBuffer)
          resolve(geometry)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsArrayBuffer(file)
    })
  }

  // GLTF文件加载器
  private static async loadGLTF(file: File): Promise<THREE.BufferGeometry | null> {
    return new Promise((resolve, reject) => {
      const loader = new GLTFLoader()
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer
          const url = URL.createObjectURL(new Blob([arrayBuffer]))
          loader.load(url, (gltf) => {
            // 从场景中提取第一个几何体
            let geometry: THREE.BufferGeometry | null = null
            gltf.scene.traverse((child) => {
              if (child instanceof THREE.Mesh && child.geometry) {
                geometry = child.geometry
              }
            })
            URL.revokeObjectURL(url)
            resolve(geometry)
          }, undefined, reject)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsArrayBuffer(file)
    })
  }

  // FBX文件加载器
  private static async loadFBX(file: File): Promise<THREE.BufferGeometry | null> {
    return new Promise((resolve, reject) => {
      const loader = new FBXLoader()
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer
          const object = loader.parse(arrayBuffer, '')
          // 从Group中提取第一个几何体
          let geometry: THREE.BufferGeometry | null = null
          object.traverse((child) => {
            if (child instanceof THREE.Mesh && child.geometry) {
              geometry = child.geometry
            }
          })
          resolve(geometry)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsArrayBuffer(file)
    })
  }

  // Collada文件加载器
  private static async loadCollada(file: File): Promise<THREE.BufferGeometry | null> {
    return new Promise((resolve, reject) => {
      const loader = new ColladaLoader()
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const text = e.target?.result as string
          const collada = loader.parse(text, '')
          // 从场景中提取第一个几何体
          let geometry: THREE.BufferGeometry | null = null
          collada.scene.traverse((child) => {
            if (child instanceof THREE.Mesh && child.geometry) {
              geometry = child.geometry
            }
          })
          resolve(geometry)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsText(file)
    })
  }

  private static async generateThumbnail(file: File, extension: string): Promise<string> {
    // 创建Three.js场景
    const scene = new THREE.Scene()
    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000)
    const renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(200, 200)
    
    // 添加灯光
    const light = new THREE.DirectionalLight(0xffffff, 1)
    light.position.set(0, 1, 1)
    scene.add(light)
    scene.add(new THREE.AmbientLight(0x404040))

    // 根据文件类型加载模型
    let object: THREE.Object3D | null = null
    
    try {
      switch (extension.toLowerCase()) {
        case 'stl':
          const stlGeometry = await FileProcessor.loadSTL(file)
          object = new THREE.Mesh(stlGeometry, new THREE.MeshPhongMaterial({ color: 0xcccccc }))
          break
        case 'obj':
          const objGeometry = await FileProcessor.loadOBJ(file)
          if (objGeometry) {
            object = new THREE.Mesh(objGeometry, new THREE.MeshPhongMaterial({ color: 0xcccccc }))
          }
          break
        case 'ply':
          const plyGeometry = await FileProcessor.loadPLY(file)
          object = new THREE.Mesh(plyGeometry, new THREE.MeshPhongMaterial({ color: 0xcccccc }))
          break
        case 'gltf':
        case 'glb':
          const gltfGeometry = await FileProcessor.loadGLTF(file)
          if (gltfGeometry) {
            object = new THREE.Mesh(gltfGeometry, new THREE.MeshPhongMaterial({ color: 0xcccccc }))
          }
          break
        case 'fbx':
          const fbxGeometry = await FileProcessor.loadFBX(file)
          if (fbxGeometry) {
            object = new THREE.Mesh(fbxGeometry, new THREE.MeshPhongMaterial({ color: 0xcccccc }))
          }
          break
        case 'dae':
          const colladaGeometry = await FileProcessor.loadCollada(file)
          if (colladaGeometry) {
            object = new THREE.Mesh(colladaGeometry, new THREE.MeshPhongMaterial({ color: 0xcccccc }))
          }
          break
        default:
          throw new Error(`不支持的文件格式: ${extension}`)
      }
      
      if (!object) {
        throw new Error('模型加载失败')
      }
      
      // 自动调整相机位置以适应模型大小
      const box = new THREE.Box3().setFromObject(object)
      const center = box.getCenter(new THREE.Vector3())
      const size = box.getSize(new THREE.Vector3())
      const maxDim = Math.max(size.x, size.y, size.z)
      camera.position.set(center.x + maxDim, center.y + maxDim, center.z + maxDim)
      camera.lookAt(center)
      
      scene.add(object)
      
      // 渲染缩略图
      renderer.render(scene, camera)
      return renderer.domElement.toDataURL('image/png')
    } finally {
      // 清理资源
      renderer.dispose()
    }
  }
}
