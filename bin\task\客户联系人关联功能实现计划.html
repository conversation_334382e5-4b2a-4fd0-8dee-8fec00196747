<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户-联系人关联功能实现计划</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 5px;
        }
        h3 {
            color: #2980b9;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.exists { background: #d4edda; color: #155724; }
        .status.missing { background: #f8d7da; color: #721c24; }
        .status.partial { background: #fff3cd; color: #856404; }
        
        .task-group {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .task-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .task-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .task-desc {
            color: #6c757d;
            font-size: 14px;
        }
        .priority-high { border-left: 4px solid #e74c3c; }
        .priority-medium { border-left: 4px solid #f39c12; }
        .priority-low { border-left: 4px solid #27ae60; }
        
        .code-path {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .timeline {
            border-left: 2px solid #3498db;
            padding-left: 20px;
            margin: 20px 0;
        }
        .timeline-item {
            margin-bottom: 20px;
            position: relative;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -27px;
            top: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #3498db;
        }
        
        .api-list {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning::before {
            content: "⚠️ ";
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 客户-联系人关联功能实现计划</h1>
        
        <div class="warning">
            <strong>核心问题：</strong>联系人在抽屉中无法创建拜访计划，因为缺少客户信息。需要完善客户-联系人关联功能。
        </div>

        <h2>📊 现状分析</h2>
        
        <h3>已有功能 <span class="status exists">✅ 存在</span></h3>
        <ul>
            <li><strong>客户管理界面：</strong>有完整的联系人Tab <span class="code-path">CustomerContactsTab.vue</span></li>
            <li><strong>数据库设计：</strong>关联表 <span class="code-path">CrmCustomerContactRelation</span> 设计完善</li>
            <li><strong>后端服务：</strong><span class="code-path">ICrmCustomerContactRelationService</span> 接口完整</li>
            <li><strong>基础API：</strong>有根据客户ID查询联系人的接口</li>
        </ul>

        <h3>缺失功能 <span class="status missing">❌ 缺失</span></h3>
        <ul>
            <li><strong>控制器层：</strong>没有客户联系人关联的Controller</li>
            <li><strong>前端API：</strong>缺少关联管理的API接口定义</li>
            <li><strong>联系人界面：</strong>客户关联功能不完善（只有文本输入框）</li>
            <li><strong>数据传递：</strong>联系人数据中customerId字段可能为空</li>
        </ul>

        <h2>🎯 实施计划</h2>

        <div class="timeline">
            <div class="timeline-item">
                <h3>阶段一：后端API完善 (1-2天)</h3>
                <div class="task-group">
                    <div class="task-item priority-high">
                        <div class="task-title">1.1 创建客户联系人关联控制器</div>
                        <div class="task-desc">
                            文件：<span class="code-path">CrmCustomerContactRelationController.java</span><br>
                            功能：提供关联、取消关联、查询关联关系等REST API
                        </div>
                    </div>
                    <div class="task-item priority-high">
                        <div class="task-title">1.2 完善联系人控制器</div>
                        <div class="task-desc">
                            修改：<span class="code-path">CrmContactsController.java</span><br>
                            添加：根据联系人ID获取关联客户信息的接口
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <h3>阶段二：前端API接口 (0.5天)</h3>
                <div class="task-group">
                    <div class="task-item priority-high">
                        <div class="task-title">2.1 创建客户联系人关联API</div>
                        <div class="task-desc">
                            文件：<span class="code-path">frontend/src/api/crm/customer-contact-relation.js</span><br>
                            功能：封装所有关联管理的API调用
                        </div>
                    </div>
                    <div class="task-item priority-medium">
                        <div class="task-title">2.2 完善现有API</div>
                        <div class="task-desc">
                            修改：<span class="code-path">frontend/src/api/crm/contacts.js</span><br>
                            添加：获取联系人关联客户的API
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <h3>阶段三：客户管理界面功能实现 (1天)</h3>
                <div class="task-group">
                    <div class="task-item priority-high">
                        <div class="task-title">3.1 完善CustomerContactsTab</div>
                        <div class="task-desc">
                            文件：<span class="code-path">CustomerContactsTab.vue</span><br>
                            实现：所有TODO标注的API调用功能
                        </div>
                    </div>
                    <div class="task-item priority-medium">
                        <div class="task-title">3.2 优化用户交互</div>
                        <div class="task-desc">
                            功能：搜索联系人、批量关联、关系类型管理等
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <h3>阶段四：联系人管理界面优化 (1天)</h3>
                <div class="task-group">
                    <div class="task-item priority-high">
                        <div class="task-title">4.1 改进客户关联功能</div>
                        <div class="task-desc">
                            文件：<span class="code-path">ContactDetailsTab.vue</span><br>
                            改进：将客户名称文本输入改为客户选择器
                        </div>
                    </div>
                    <div class="task-item priority-high">
                        <div class="task-title">4.2 添加客户关联Tab</div>
                        <div class="task-desc">
                            新建：<span class="code-path">ContactCustomerRelationTab.vue</span><br>
                            功能：管理联系人与多个客户的关联关系
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <h3>阶段五：数据修复和测试 (1天)</h3>
                <div class="task-group">
                    <div class="task-item priority-high">
                        <div class="task-title">5.1 数据一致性检查</div>
                        <div class="task-desc">
                            确保现有联系人数据正确关联到客户
                        </div>
                    </div>
                    <div class="task-item priority-medium">
                        <div class="task-title">5.2 功能测试</div>
                        <div class="task-desc">
                            测试：关联、取消关联、拜访计划创建等完整流程
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h2>🛠️ 需要创建的API接口</h2>
        
        <div class="api-list">
            <h4>客户联系人关联管理API：</h4>
            <table>
                <tr>
                    <th>API</th>
                    <th>方法</th>
                    <th>功能</th>
                    <th>优先级</th>
                </tr>
                <tr>
                    <td>POST /crm/customer-contact-relation</td>
                    <td>创建关联</td>
                    <td>建立客户与联系人的关联关系</td>
                    <td class="status missing">高</td>
                </tr>
                <tr>
                    <td>DELETE /crm/customer-contact-relation/{id}</td>
                    <td>取消关联</td>
                    <td>删除客户与联系人的关联关系</td>
                    <td class="status missing">高</td>
                </tr>
                <tr>
                    <td>GET /crm/customer-contact-relation/customer/{customerId}</td>
                    <td>查询客户联系人</td>
                    <td>获取客户的所有关联联系人</td>
                    <td class="status missing">高</td>
                </tr>
                <tr>
                    <td>GET /crm/customer-contact-relation/contact/{contactId}</td>
                    <td>查询联系人客户</td>
                    <td>获取联系人的所有关联客户</td>
                    <td class="status missing">高</td>
                </tr>
                <tr>
                    <td>PUT /crm/customer-contact-relation/primary</td>
                    <td>设置主要联系人</td>
                    <td>设置客户的主要联系人</td>
                    <td class="status missing">中</td>
                </tr>
                <tr>
                    <td>POST /crm/customer-contact-relation/batch</td>
                    <td>批量关联</td>
                    <td>批量建立关联关系</td>
                    <td class="status missing">中</td>
                </tr>
            </table>
        </div>

        <h2>📁 需要修改的文件清单</h2>

        <h3>后端文件</h3>
        <ul>
            <li><span class="code-path">ruoyi-crm/src/main/java/com/ruoyi/crm/controller/CrmCustomerContactRelationController.java</span> <span class="status missing">新建</span></li>
            <li><span class="code-path">ruoyi-crm/src/main/java/com/ruoyi/crm/controller/CrmContactsController.java</span> <span class="status partial">修改</span></li>
        </ul>

        <h3>前端文件</h3>
        <ul>
            <li><span class="code-path">frontend/src/api/crm/customer-contact-relation.js</span> <span class="status missing">新建</span></li>
            <li><span class="code-path">frontend/src/api/crm/contacts.js</span> <span class="status partial">修改</span></li>
            <li><span class="code-path">frontend/src/views/CustomerManagement/tabs/CustomerContactsTab.vue</span> <span class="status partial">修改</span></li>
            <li><span class="code-path">frontend/src/views/ContactManagement/tabs/ContactDetailsTab.vue</span> <span class="status partial">修改</span></li>
            <li><span class="code-path">frontend/src/views/ContactManagement/tabs/ContactCustomerRelationTab.vue</span> <span class="status missing">新建</span></li>
            <li><span class="code-path">frontend/src/views/ContactManagement/config/index.ts</span> <span class="status partial">修改</span></li>
        </ul>

        <h2>🎯 预期效果</h2>
        
        <div class="task-group">
            <h4>用户体验改进：</h4>
            <ul>
                <li>✅ 联系人可以正确关联到客户，解决拜访计划创建问题</li>
                <li>✅ 客户界面可以方便地管理关联联系人</li>
                <li>✅ 联系人界面可以选择和管理关联客户</li>
                <li>✅ 支持一个联系人关联多个客户的复杂业务场景</li>
            </ul>
        </div>

        <div class="task-group">
            <h4>技术架构改进：</h4>
            <ul>
                <li>✅ 完善的关联关系管理API</li>
                <li>✅ 数据一致性保证</li>
                <li>✅ 灵活的关系类型支持</li>
                <li>✅ 良好的前后端分离架构</li>
            </ul>
        </div>

        <h2>⏱️ 时间预估</h2>
        
        <table>
            <tr>
                <th>阶段</th>
                <th>时间</th>
                <th>主要工作</th>
                <th>关键里程碑</th>
            </tr>
            <tr>
                <td>后端API完善</td>
                <td>1-2天</td>
                <td>Controller + API开发</td>
                <td>API测试通过</td>
            </tr>
            <tr>
                <td>前端API接口</td>
                <td>0.5天</td>
                <td>API封装</td>
                <td>接口调用正常</td>
            </tr>
            <tr>
                <td>客户界面实现</td>
                <td>1天</td>
                <td>CustomerContactsTab完善</td>
                <td>客户管理联系人功能完整</td>
            </tr>
            <tr>
                <td>联系人界面优化</td>
                <td>1天</td>
                <td>客户关联功能改进</td>
                <td>联系人可选择客户</td>
            </tr>
            <tr>
                <td>测试和修复</td>
                <td>1天</td>
                <td>集成测试</td>
                <td>拜访计划创建正常</td>
            </tr>
        </table>

        <p><strong>总计：4.5-5.5天</strong></p>

        <div class="warning">
            <strong>注意事项：</strong><br>
            1. 需要考虑现有数据的迁移和修复<br>
            2. 确保向后兼容性，不破坏现有功能<br>
            3. 关联关系的删除需要谨慎处理，避免数据孤岛<br>
            4. 需要添加相应的权限控制
        </div>
    </div>
</body>
</html>