-- =============================================
-- 联系人负责人数据迁移脚本
-- 将现有的单一负责人数据迁移到多对多关系表中
-- 创建时间: 2025-07-29
-- =============================================

-- 1. 迁移前的数据统计
SELECT '迁移前数据统计' as '阶段';
SELECT 
    COUNT(*) as '总联系人数',
    COUNT(CASE WHEN responsible_person_id IS NOT NULL AND responsible_person_id != '' THEN 1 END) as '有负责人的联系人数',
    COUNT(CASE WHEN responsible_person_id IS NULL OR responsible_person_id = '' THEN 1 END) as '无负责人的联系人数'
FROM crm_contacts 
WHERE del_flag = '0';

-- 2. 迁移现有联系人负责人数据到关系表
INSERT INTO crm_contact_responsible_relation 
    (contact_id, responsible_person_id, business_type, relation_status, start_date, assign_type, remark, create_by, create_time)
SELECT 
    c.id AS contact_id,
    CAST(c.responsible_person_id AS UNSIGNED) AS responsible_person_id,
    'GENERAL' AS business_type,  -- 默认通用业务类型
    'ACTIVE' AS relation_status,
    IFNULL(c.create_time, NOW()) AS start_date,
    'MIGRATED' AS assign_type,
    CONCAT('数据迁移：原负责人ID=', c.responsible_person_id) AS remark,
    'system' AS create_by,
    NOW() AS create_time
FROM crm_contacts c
WHERE c.responsible_person_id IS NOT NULL 
  AND c.responsible_person_id != ''
  AND c.responsible_person_id REGEXP '^[0-9]+$'  -- 确保是数字
  AND c.del_flag = '0'
  AND NOT EXISTS (
    -- 避免重复迁移
    SELECT 1 FROM crm_contact_responsible_relation r 
    WHERE r.contact_id = c.id 
      AND r.responsible_person_id = CAST(c.responsible_person_id AS UNSIGNED)
      AND r.business_type = 'GENERAL'
  );

-- 3. 更新团队关联信息
-- 根据负责人所属的团队，更新关系表中的team_id
UPDATE crm_contact_responsible_relation r
INNER JOIN crm_team_member tm ON r.responsible_person_id = tm.user_id
SET r.team_id = tm.team_id,
    r.assign_time = r.start_date,
    r.assign_by = tm.user_id,
    r.update_by = 'system',
    r.update_time = NOW(),
    r.remark = CONCAT(r.remark, ' | 团队分配：团队ID=', tm.team_id)
WHERE tm.status = '0' 
  AND tm.role_type IN ('owner', 'admin', 'member')
  AND r.assign_type = 'MIGRATED'
  AND r.team_id IS NULL;

-- 4. 处理团队关联表中已存在的联系人关系
-- 如果团队关联表中已经有联系人关系，更新对应的team_id
UPDATE crm_contact_responsible_relation r
INNER JOIN crm_team_relation tr ON r.contact_id = tr.relation_id
INNER JOIN crm_teams t ON tr.team_id = t.id
SET r.team_id = tr.team_id,
    r.remark = CONCAT(r.remark, ' | 已有团队关联：', t.team_name)
WHERE tr.relation_type = 'CONTACT'
  AND r.team_id IS NULL
  AND r.assign_type = 'MIGRATED';

-- 5. 为没有团队信息的负责人创建团队关联记录（如果需要）
INSERT INTO crm_team_relation (team_id, relation_type, relation_id, create_by, create_time, remark)
SELECT DISTINCT 
    r.team_id,
    'CONTACT' as relation_type,
    r.contact_id as relation_id,
    'system' as create_by,
    NOW() as create_time,
    '数据迁移：自动创建团队-联系人关联' as remark
FROM crm_contact_responsible_relation r
WHERE r.team_id IS NOT NULL
  AND r.assign_type = 'MIGRATED'
  AND NOT EXISTS (
    SELECT 1 FROM crm_team_relation tr 
    WHERE tr.team_id = r.team_id 
      AND tr.relation_type = 'CONTACT' 
      AND tr.relation_id = r.contact_id
  );

-- 6. 迁移后的数据验证
SELECT '迁移后数据验证' as '阶段';

-- 验证迁移的记录数
SELECT 
    COUNT(*) as '迁移的关系记录数',
    COUNT(CASE WHEN team_id IS NOT NULL THEN 1 END) as '有团队信息的记录数',
    COUNT(CASE WHEN team_id IS NULL THEN 1 END) as '无团队信息的记录数'
FROM crm_contact_responsible_relation 
WHERE assign_type = 'MIGRATED';

-- 验证每个业务员的联系人数量
SELECT 
    r.responsible_person_id as '业务员ID',
    u.nick_name as '业务员姓名',
    COUNT(*) as '负责联系人数量',
    GROUP_CONCAT(DISTINCT t.team_name) as '所属团队'
FROM crm_contact_responsible_relation r
LEFT JOIN sys_user u ON r.responsible_person_id = u.user_id
LEFT JOIN crm_teams t ON r.team_id = t.id
WHERE r.assign_type = 'MIGRATED' AND r.relation_status = 'ACTIVE'
GROUP BY r.responsible_person_id, u.nick_name
ORDER BY COUNT(*) DESC;

-- 验证是否还有未迁移的负责人数据
SELECT 
    COUNT(*) as '仍有负责人但未迁移的联系人数'
FROM crm_contacts c
WHERE c.responsible_person_id IS NOT NULL 
  AND c.responsible_person_id != ''
  AND c.del_flag = '0'
  AND NOT EXISTS (
    SELECT 1 FROM crm_contact_responsible_relation r 
    WHERE r.contact_id = c.id 
      AND r.relation_status = 'ACTIVE'
  );

-- 7. 创建数据一致性检查视图（可选）
CREATE OR REPLACE VIEW v_contact_responsible_check AS
SELECT 
    c.id as contact_id,
    c.name as contact_name,
    c.responsible_person_id as old_responsible_id,
    GROUP_CONCAT(DISTINCT r.responsible_person_id) as new_responsible_ids,
    GROUP_CONCAT(DISTINCT u.nick_name) as responsible_names,
    GROUP_CONCAT(DISTINCT r.business_type) as business_types,
    COUNT(r.id) as relation_count
FROM crm_contacts c
LEFT JOIN crm_contact_responsible_relation r ON c.id = r.contact_id AND r.relation_status = 'ACTIVE'
LEFT JOIN sys_user u ON r.responsible_person_id = u.user_id
WHERE c.del_flag = '0'
GROUP BY c.id, c.name, c.responsible_person_id;

-- 显示检查结果的前10条
SELECT * FROM v_contact_responsible_check LIMIT 10;

SELECT '数据迁移完成' as '状态', NOW() as '完成时间';