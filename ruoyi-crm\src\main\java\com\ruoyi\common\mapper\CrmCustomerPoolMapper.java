package com.ruoyi.common.mapper;

import com.ruoyi.common.domain.entity.CrmCustomerPool;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户公海Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface CrmCustomerPoolMapper {
    
    /**
     * 查询公海客户列表
     * 
     * @param crmCustomerPool 查询条件
     * @return 公海客户列表
     */
    List<CrmCustomerPool> selectPoolCustomerList(CrmCustomerPool crmCustomerPool);
    
    /**
     * 批量更新客户公海状态
     * 
     * @param customerIds 客户ID列表
     * @param status 状态
     * @param claimBy 认领人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("customerIds") List<Long> customerIds, 
                         @Param("status") String status,
                         @Param("claimBy") String claimBy);
    
    /**
     * 查询用户今日认领数量
     * 
     * @param userId 用户ID
     * @return 认领数量
     */
    int selectTodayClaimCount(@Param("userId") Long userId);
    
    /**
     * 查询用户当前持有客户数
     * 
     * @param userId 用户ID
     * @return 客户数量
     */
    int selectUserCustomerCount(@Param("userId") Long userId);
    
    /**
     * 更新在公海天数
     */
    void updateDaysInPool();
}