# 3D打印订单CRM集成 - 数据库表操作详情

## 概述
本文档详细描述了3D打印订单从提交到完成整个流程中涉及的数据库表操作，包括状态流转和数据变化。

## 数据流转步骤

### 步骤1: 处理客户信息

#### 表: `crm_customer`

**操作类型**: 查询 → 插入（如果不存在）

**查询条件**:
```sql
SELECT * FROM crm_customer WHERE mobile = '客户手机号'
```

**插入操作**（如果客户不存在）:
```sql
INSERT INTO crm_customer (
    customer_name,          -- 公司名称
    mobile,                 -- 联系电话
    email,                  -- 联系邮箱
    customer_address,       -- 客户地址
    primary_contact,        -- 主要联系人
    remarks,                -- 备注
    customer_source,        -- 客户来源: "3D打印报价系统"
    customer_level,         -- 客户等级: "A级客户"
    status,                 -- 状态: "1" (正常)
    responsible_person_id,  -- 负责人ID
    create_by,              -- 创建者
    create_time             -- 创建时间
) VALUES (...)
```

**状态变化**:
- 新客户: `无记录` → `正常状态(1)`
- 现有客户: 保持原状态不变

---

### 步骤2: 处理联系人信息

#### 表: `crm_contacts`

**操作类型**: 查询 → 插入（如果不存在）

**查询条件**:
```sql
SELECT * FROM crm_contacts WHERE mobile = '联系人手机号'
```

**插入操作**（如果联系人不存在）:
```sql
INSERT INTO crm_contacts (
    name,                   -- 联系人姓名
    mobile,                 -- 联系电话
    email,                  -- 联系邮箱
    position,               -- 职位: "主要联系人"
    remarks,                -- 备注: "通过3D打印报价系统创建 - 询价单号: XXX"
    status,                 -- 状态: "1" (正常)
    create_by,              -- 创建者
    create_time             -- 创建时间
) VALUES (...)
```

**状态变化**:
- 新联系人: `无记录` → `正常状态(1)`
- 现有联系人: 保持原状态不变

---

### 步骤3: 创建商机

#### 表: `crm_opportunity`

**操作类型**: 插入（总是创建新商机）

**插入操作**:
```sql
INSERT INTO crm_opportunity (
    opportunity_name,       -- 商机名称: "3D打印服务 - 询价单号"
    customer_id,            -- 客户ID（关联crm_customer.id）
    customer_name,          -- 客户名称
    opportunity_stage,      -- 商机阶段: "proposal" (方案阶段)
    opportunity_amount,     -- 商机金额
    win_rate,               -- 成功率: 70%
    expected_close_date,    -- 预期成交日期: 当前时间+7天
    remarks,                -- 备注: 包含来源、询价单号、模型数量、订单金额
    opportunity_source,     -- 商机来源: "3D打印报价系统"
    status,                 -- 状态: "1" (正常)
    manager_id,             -- 负责人ID
    create_by,              -- 创建者
    create_time             -- 创建时间
) VALUES (...)
```

**状态变化**:
- 商机状态: `无记录` → `proposal阶段，正常状态(1)`

---

### 步骤4: 创建订单

#### 表: `crm_order`

**操作类型**: 插入（总是创建新订单）

**插入操作**:
```sql
INSERT INTO crm_order (
    order_no,               -- 订单号: "ORD" + 时间戳
    quote_no,               -- 询价单号
    customer_id,            -- 客户ID（关联crm_customer.id）
    total_amount,           -- 订单总金额
    status,                 -- 订单状态: "pending" (待处理)
    remarks,                -- 订单详情: 包含模型信息、材料、数量、价格等
    delivery_address,       -- 配送地址
    create_by,              -- 创建者
    create_time             -- 创建时间
) VALUES (...)
```

**状态变化**:
- 订单状态: `无记录` → `pending状态（待处理）`

---

## 表关系图

```
crm_customer (客户表)
    ├── id (主键)
    └── 关联到 → crm_contacts.customer_id (隐式关联)
    └── 关联到 → crm_opportunity.customer_id
    └── 关联到 → crm_order.customer_id

crm_contacts (联系人表)
    ├── id (主键)
    └── 关联到客户 (通过手机号匹配)

crm_opportunity (商机表)
    ├── id (主键)
    ├── customer_id → crm_customer.id
    └── 关联到 → crm_order (通过业务逻辑)

crm_order (订单表)
    ├── id (主键)
    ├── customer_id → crm_customer.id
    └── 关联商机 (通过业务逻辑)
```

## 数据包结构

### 输入数据包 (ThreeDPrintingOrderCreateDTO)
```json
{
  "quoteNo": "询价单号",
  "totalAmount": "订单总金额",
  "customerInfo": {
    "companyName": "公司名称",
    "contactName": "联系人姓名",
    "contactPhone": "联系电话",
    "contactEmail": "联系邮箱",
    "deliveryAddress": "配送地址",
    "remark": "备注"
  },
  "items": [
    {
      "modelName": "模型名称",
      "material": "材料类型",
      "quantity": "数量",
      "unitPrice": "单价",
      "totalPrice": "总价"
    }
  ],
  "sprayOptions": ["喷漆选项"],
  "insertOptions": ["镶嵌选项"]
}
```

### 输出数据包 (ThreeDPrintingOrderResultVO)
```json
{
  "success": true,
  "orderId": "订单ID",
  "orderNo": "订单号",
  "customerId": "客户ID",
  "isNewCustomer": "是否新客户",
  "contactId": "联系人ID",
  "isNewContact": "是否新联系人",
  "opportunityId": "商机ID"
}
```

## 日志记录级别

### INFO级别日志
- 主要业务流程节点
- 成功创建的记录信息
- 使用现有记录的信息

### DEBUG级别日志
- 详细的数据包内容
- 即将插入的数据详情
- 数据库操作前的准备信息

### ERROR级别日志
- 数据库插入失败
- 业务异常情况
- 系统错误信息

## 事务处理

整个流程在一个事务中执行，确保数据一致性：
- 如果任何步骤失败，所有操作都会回滚
- 使用 `@Transactional(rollbackFor = Exception.class)` 注解
- 保证客户、联系人、商机、订单的创建要么全部成功，要么全部失败

## 状态流转总结

1. **客户状态**: 不存在 → 创建(正常状态) 或 使用现有客户
2. **联系人状态**: 不存在 → 创建(正常状态) 或 使用现有联系人  
3. **商机状态**: 无 → 创建(proposal阶段，正常状态)
4. **订单状态**: 无 → 创建(pending状态)

整个流程确保了从3D打印报价到CRM系统的完整数据链路，实现了业务数据的有效管理和跟踪。