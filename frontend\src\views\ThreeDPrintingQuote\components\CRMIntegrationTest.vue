<template>
  <div class="test-panel">
    
    <div class="test-section">
      <h4>🔍 客户检查测试</h4>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-input
            v-model="testMobile"
            placeholder="输入手机号测试客户是否存在"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="testCustomerExists" :loading="testing">
            检查客户
          </el-button>
        </el-col>
      </el-row>
      <div v-if="customerTestResult" class="test-result">
        <el-alert
          :title="customerTestResult.title"
          :type="customerTestResult.type"
          :description="customerTestResult.description"
          show-icon
          :closable="false"
        />
      </div>
    </div>
    
    <el-divider />
    
    <div class="test-section">
      <h4>👥 联系人检查测试</h4>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="testCustomerId"
            placeholder="客户ID"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-input
            v-model="testContactMobile"
            placeholder="联系人手机号"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="testContactExists" :loading="testing">
            检查联系人
          </el-button>
        </el-col>
      </el-row>
      <div v-if="contactTestResult" class="test-result">
        <el-alert
          :title="contactTestResult.title"
          :type="contactTestResult.type"
          :description="contactTestResult.description"
          show-icon
          :closable="false"
        />
      </div>
    </div>
    
    <el-divider />
    
    <div class="test-section">
      <h4>🚀 完整流程测试</h4>
      <el-form :model="testForm" label-width="120px" size="small">
        <el-form-item label="公司名称">
          <el-input v-model="testForm.companyName" placeholder="测试公司" />
        </el-form-item>
        <el-form-item label="联系人姓名">
          <el-input v-model="testForm.contactName" placeholder="张三" />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input v-model="testForm.contactPhone" placeholder="13800138000" />
        </el-form-item>
        <el-form-item label="电子邮箱">
          <el-input v-model="testForm.contactEmail" placeholder="<EMAIL>" />
        </el-form-item>
        <el-form-item label="收货地址">
          <el-input v-model="testForm.deliveryAddress" placeholder="北京市朝阳区xxx" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="testForm.remark" placeholder="测试订单" />
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="testFullIntegration" :loading="testing">
            🧪 测试完整CRM集成
          </el-button>
          <el-button @click="resetTestForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="integrationTestResult" class="test-result">
        <el-alert
          :title="integrationTestResult.title"
          :type="integrationTestResult.type"
          :description="integrationTestResult.description"
          show-icon
          :closable="false"
        />
        
        <div v-if="integrationTestResult.details" class="test-details">
          <h5>详细结果：</h5>
          <ul>
            <li v-for="detail in integrationTestResult.details" :key="detail">{{ detail }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  checkCustomerExists, 
  checkContactExists, 
  createOrderWithCRMIntegration,
  type ThreeDPrintingOrderCreateRequest 
} from '@/api/crm/3dPrintingIntegration';
import type { CustomerFormData } from './CustomerInfoForm.vue';

// 测试状态
const testing = ref(false);

// 客户检查测试
const testMobile = ref('');
const customerTestResult = ref<any>(null);

// 联系人检查测试
const testCustomerId = ref('');
const testContactMobile = ref('');
const contactTestResult = ref<any>(null);

// 完整流程测试
const testForm = reactive<CustomerFormData>({
  companyName: '测试科技有限公司',
  contactName: '张测试',
  contactPhone: '13800138001',
  contactEmail: '<EMAIL>',
  deliveryAddress: '北京市朝阳区测试街道123号',
  remark: '这是一个CRM集成测试订单'
});

const integrationTestResult = ref<any>(null);

// 测试客户是否存在
const testCustomerExists = async () => {
  if (!testMobile.value.trim()) {
    ElMessage.warning('请输入手机号');
    return;
  }
  
  testing.value = true;
  try {
    const result = await checkCustomerExists(testMobile.value.trim());
    
    if (result.code === 200) {
      customerTestResult.value = {
        title: result.data.exists ? '✅ 客户存在' : '❌ 客户不存在',
        type: result.data.exists ? 'success' : 'info',
        description: result.data.exists 
          ? `客户ID: ${result.data.customerId}，客户名称: ${result.data.customerName || '未知'}`
          : '该手机号未找到对应的客户记录'
      };
    } else {
      customerTestResult.value = {
        title: '❌ 检查失败',
        type: 'error',
        description: result.msg || '检查客户存在性时发生错误'
      };
    }
  } catch (error) {
    console.error('测试客户存在性失败:', error);
    customerTestResult.value = {
      title: '❌ 检查失败',
      type: 'error',
      description: '网络错误或接口异常'
    };
  } finally {
    testing.value = false;
  }
};

// 测试联系人是否存在
const testContactExists = async () => {
  if (!testCustomerId.value.trim() || !testContactMobile.value.trim()) {
    ElMessage.warning('请输入客户ID和联系人手机号');
    return;
  }
  
  testing.value = true;
  try {
    const result = await checkContactExists(testCustomerId.value.trim(), testContactMobile.value.trim());
    
    if (result.code === 200) {
      contactTestResult.value = {
        title: result.data.exists ? '✅ 联系人存在' : '❌ 联系人不存在',
        type: result.data.exists ? 'success' : 'info',
        description: result.data.exists 
          ? `联系人ID: ${result.data.contactId}`
          : '该客户下未找到对应手机号的联系人记录'
      };
    } else {
      contactTestResult.value = {
        title: '❌ 检查失败',
        type: 'error',
        description: result.msg || '检查联系人存在性时发生错误'
      };
    }
  } catch (error) {
    console.error('测试联系人存在性失败:', error);
    contactTestResult.value = {
      title: '❌ 检查失败',
      type: 'error',
      description: '网络错误或接口异常'
    };
  } finally {
    testing.value = false;
  }
};

// 测试完整CRM集成流程
const testFullIntegration = async () => {
  testing.value = true;
  integrationTestResult.value = null;
  
  try {
    const orderCreateRequest: ThreeDPrintingOrderCreateRequest = {
      quoteNo: 'TEST' + Date.now(),
      customerInfo: testForm,
      items: [
        {
          modelName: '测试模型1.stl',
          modelInfo: {
            dimensions: '100×50×20mm',
            volume: '100000mm³',
            surfaceArea: '10000mm²'
          },
          material: '测试材料',
          materialId: 'test-material-1',
          quantity: 1,
          unitPrice: 99.99,
          totalPrice: 99.99,
          processOptions: ['喷漆']
        },
        {
          modelName: '测试模型2.stl',
          modelInfo: {
            dimensions: '80×60×30mm',
            volume: '144000mm³',
            surfaceArea: '12000mm²'
          },
          material: '测试材料',
          materialId: 'test-material-2',
          quantity: 2,
          unitPrice: 50.00,
          totalPrice: 100.00,
          processOptions: ['丝印']
        }
      ],
      totalAmount: 199.99,
      sprayOptions: ['喷漆'],
      insertOptions: ['镶钢丝牙套']
    };
    
    const response = await createOrderWithCRMIntegration(orderCreateRequest);
    
    if (response.code === 200) {
      const result = response.data;
      
      const details = [
        `✅ 订单号: ${result.orderNo}`,
        `✅ 订单ID: ${result.orderId}`,
        `✅ 客户ID: ${result.customerId} (${result.isNewCustomer ? '新建' : '现有'})`,
        result.contactId ? `✅ 联系人ID: ${result.contactId} (${result.isNewContact ? '新建' : '现有'})` : '⚠️ 联系人创建失败',
        `✅ 商机ID: ${result.opportunityId}`,
        `📋 询价单号: ${orderCreateRequest.quoteNo}`,
        `💰 订单金额: ¥${orderCreateRequest.totalAmount}`
      ];
      
      integrationTestResult.value = {
        title: '🎉 3D打印CRM集成测试成功！',
        type: 'success',
        description: '所有记录已通过后端事务性处理成功创建，数据一致性得到保证',
        details
      };
      
      ElMessage.success('后端事务性CRM集成测试完成！请在CRM后台查看数据');
    } else {
      integrationTestResult.value = {
        title: '❌ CRM集成测试失败',
        type: 'error',
        description: response.msg || '未知错误'
      };
    }
    
  } catch (error) {
    console.error('CRM集成测试失败:', error);
    integrationTestResult.value = {
      title: '❌ CRM集成测试失败',
      type: 'error',
      description: error instanceof Error ? error.message : '网络错误或服务器异常'
    };
  } finally {
    testing.value = false;
  }
};

// 重置测试表单
const resetTestForm = () => {
  Object.assign(testForm, {
    companyName: '测试科技有限公司',
    contactName: '张测试',
    contactPhone: '13800138001',
    contactEmail: '<EMAIL>',
    deliveryAddress: '北京市朝阳区测试街道123号',
    remark: '这是一个CRM集成测试订单'
  });
  integrationTestResult.value = null;
};
</script>

<style scoped>
.test-panel {
  padding: 20px;
  max-width: 100%;
}

.test-section {
  margin-bottom: 20px;
}

.test-section h4 {
  color: #409eff;
  font-size: 16px;
  margin-bottom: 15px;
}

.test-result {
  margin-top: 15px;
}

.test-details {
  margin-top: 15px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 5px;
}

.test-details h5 {
  margin-bottom: 10px;
  color: #303133;
}

.test-details ul {
  margin: 0;
  padding-left: 20px;
}

.test-details li {
  margin-bottom: 5px;
  color: #606266;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-alert) {
  margin-bottom: 10px;
}
</style>