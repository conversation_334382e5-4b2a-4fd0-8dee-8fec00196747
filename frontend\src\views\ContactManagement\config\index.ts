
import { markRaw } from 'vue';
import ContactActivityTab from '../tabs/ContactActivityTab.vue';
import ContactAttachmentTab from '../tabs/ContactAttachmentTab.vue';
import ContactDetailsTab from '../tabs/ContactDetailsTab.vue';
import ContactHeaderTab from '../tabs/ContactHeaderTab.vue';
import ContactLogTab from '../tabs/ContactLogTab.vue';
import ContactTeamTab from '../tabs/ContactTeamTab.vue';
import ContactVisitPlanTab from '../tabs/ContactVisitPlanTab.vue';
import ContactCustomerRelationTab from '../tabs/ContactCustomerRelationTab.vue';
// 表格列配置接口
export interface TableColumn {
    type?: 'selection' | 'index' | 'expand';
    prop?: string;
    label?: string;
    width?: number | string;
    sortable?: boolean;
    fixed?: boolean | 'left' | 'right';
    link?: boolean;
}

// 表格列配置
export const tableColumns: TableColumn[] = [
    { type: 'selection', width: 55 },
    { prop: 'name', link: true, label: '姓名', width: 120, sortable: true },
    { prop: 'position', label: '职位', sortable: true },
    { prop: 'phone', label: '电话', width: 140, sortable: true },
    { prop: 'email', label: '邮箱', width: 200, sortable: true },
    { prop: 'customerName', label: '所属客户', sortable: true },
    { prop: 'isFollowing', label: '关注状态', width: 100, sortable: false },
    { prop: 'createTime', label: '创建时间', width: 180, sortable: true }
];

// 新建联系人表单配置
export const newContactFormConfig = {
    layout: {
        labelPosition: 'right',
        labelWidth: '100px',
        size: 'default',
        gutter: 20
    },
    fields: [
        {
            field: 'name',
            label: '联系人姓名',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            maxLength: 50,
            showWordLimit: true,
            placeholder: '请输入联系人姓名',
            prefixIcon: 'User',
            rules: [
                { required: true, message: '请输入联系人姓名', trigger: 'blur' },
                { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
            ]
        },
        {
            field: 'position',
            label: '职位',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请输入职位',
            prefixIcon: 'Briefcase'
        },
        {
            field: 'customerId',
            label: '所属客户',
            type: 'select',
            colSpan: 12,
            required: true,
            clearable: true,
            filterable: true,
            placeholder: '请选择所属客户',
            options: [] // 需要动态获取客户列表
        },
        {
            field: 'phone',
            label: '联系电话',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请输入联系电话',
            prefixIcon: 'Phone',
            rules: [
                { required: true, message: '请输入联系电话', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
            ]
        },
        {
            field: 'email',
            label: '电子邮箱',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入电子邮箱',
            prefixIcon: 'Message',
            rules: [
                { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
            ]
        },
        {
            field: 'remarks',
            label: '备注',
            type: 'textarea',
            colSpan: 24,
            clearable: true,
            placeholder: '请输入备注信息',
            maxLength: 500,
            showWordLimit: true,
            rows: 4
        }
    ]
};



// 抽屉配置
export const drawerConfig = {
    width: '800px',
    title: '联系人详情',
    showClose: true,
    direction: 'rtl',
    headerComponent: markRaw(ContactHeaderTab),
    headerFields: [
        { label: '客户名称', field: 'customerName' },
        { label: '手机号码', field: 'phone' },
        { label: '职务', field: 'position' },
        { label: '创建时间', field: 'createTime' }
    ],
    actions: [
        {
            label: '编辑',
            type: 'primary',
            icon: 'Edit',
            handler: (data: any) => {
                console.log('编辑联系人', data);
            }
        },
        {
            label: '转移',
            icon: 'Share',
            handler: (data: any) => {
                console.log('转移联系人', data);
            }
        },
        {
            label: '打印',
            icon: 'Printer',
            handler: (data: any) => {
                console.log('打印联系人', data);
            }
        },
        {
            label: '删除',
            type: 'danger',
            icon: 'Delete',
            handler: (data: any) => {
                console.log('删除联系人', data);
            }
        }
    ],
    menuItems: [
        {
            key: 'details',
            label: '详细资料',
            icon: 'Document',
            component: markRaw(ContactDetailsTab)
        },
        {
            key: 'activity',
            label: '活动记录',
            icon: 'Timer',
            component: markRaw(ContactActivityTab)
        },
        {
            key: 'attachments',
            label: '附件',
            icon: 'Paperclip',
            component: markRaw(ContactAttachmentTab),
            badge: true
        },
        //拜访计划
        {
            key: 'visitPlan',
            label: '拜访计划',
            icon: 'Calendar',
            component: markRaw(ContactVisitPlanTab)
        },
        // 客户关联
        {
            key: 'customerRelation',
            label: '客户关联',
            icon: 'OfficeBuilding',
            component: markRaw(ContactCustomerRelationTab),
            badge: true
        },
        // 团队成员
        {
            key: 'team',
            label: '团队成员',
            icon: 'UserFilled',
            component: markRaw(ContactTeamTab)
        },
        {
            key: 'logs',
            label: '操作日志',
            icon: 'List',
            component: markRaw(ContactLogTab),
        }
    ]
};

// 导航配置
export const navConfig = {
    title: '联系人管理',
    menuItems: [
        {
            key: 'contacts',
            label: '联系人',
            icon: 'User'
        },
        {
            key: 'followup',
            label: '跟进记录',
            icon: 'ChatRound'
        }
    ]
};
