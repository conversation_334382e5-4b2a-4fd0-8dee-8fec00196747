package com.ruoyi.crm.controller.customer;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmClaimLimits;
import com.ruoyi.common.domain.entity.CrmCustomer;
import com.ruoyi.common.domain.entity.CrmPoolRules;
import com.ruoyi.common.mapper.CrmClaimLimitsMapper;
import com.ruoyi.common.mapper.CrmCustomerMapper;
import com.ruoyi.common.mapper.CrmCustomerPoolMapper;
import com.ruoyi.common.mapper.CrmPoolRulesMapper;
import com.ruoyi.crm.BaseTestCase;
import com.ruoyi.crm.service.ICrmCustomerPoolService;

/**
 * 客户公海管理全面集成测试
 * 
 * 测试覆盖：
 * 1. 基础功能测试
 * 2. 边界条件测试
 * 3. 异常场景测试
 * 4. 权限控制测试
 * 5. 并发操作测试
 * 6. 业务规则验证测试
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CrmCustomerPoolControllerIntegrationTest extends BaseTestCase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ICrmCustomerPoolService customerPoolService;

    @Autowired
    private CrmCustomerMapper customerMapper;

    @Autowired
    private CrmCustomerPoolMapper customerPoolMapper;

    @Autowired
    private CrmPoolRulesMapper poolRulesMapper;

    @Autowired
    private CrmClaimLimitsMapper claimLimitsMapper;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    // 测试数据
    private CrmCustomer testCustomer1;
    private CrmCustomer testCustomer2;
    private CrmCustomer testCustomer3;
    private CrmPoolRules testRule;
    private CrmClaimLimits testLimit;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
        
        // 初始化测试数据
        initTestData();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        cleanTestData();
    }

    // ==================== 基础功能测试 ====================

    @Test
    @Order(1)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("获取公海客户列表 - 正常情况")
    void testGetPoolCustomers_Normal() throws Exception {
        // 先将客户放入公海
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "测试放入公海");

        mockMvc.perform(get("/crm/customer/pool")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").isNumber());
    }

    @Test
    @Order(2)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("获取我的客户列表 - 正常情况")
    void testGetOwnCustomers_Normal() throws Exception {
        mockMvc.perform(get("/crm/customer/own")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray());
    }

    @Test
    @Order(3)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("认领客户 - 正常情况")
    void testClaimCustomer_Normal() throws Exception {
        // 先将客户放入公海
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "测试放入公海");

        mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("认领成功"));
    }

    @Test
    @Order(4)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("放回公海 - 正常情况")
    void testReturnToPool_Normal() throws Exception {
        mockMvc.perform(post("/crm/customer/{id}/return", testCustomer2.getId())
                .param("reason", "MANUAL")
                .param("remark", "测试放入公海")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("放入公海成功"));
    }

    // ==================== 边界条件测试 ====================

    @Test
    @Order(10)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("分页边界测试 - 第一页")
    void testPagination_FirstPage() throws Exception {
        mockMvc.perform(get("/crm/customer/pool")
                .param("pageNum", "1")
                .param("pageSize", "1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(11)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("分页边界测试 - 超大页码")
    void testPagination_LargePage() throws Exception {
        mockMvc.perform(get("/crm/customer/pool")
                .param("pageNum", "99999")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isEmpty());
    }

    @Test
    @Order(12)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("分页边界测试 - 最大页面大小")
    void testPagination_MaxPageSize() throws Exception {
        mockMvc.perform(get("/crm/customer/pool")
                .param("pageNum", "1")
                .param("pageSize", "1000")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(13)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("批量操作边界测试 - 单个客户")
    void testBatchOperation_SingleCustomer() throws Exception {
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "测试");

        String requestBody = "[" + testCustomer1.getId() + "]";
        mockMvc.perform(post("/crm/customer/batch/claim")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Order(14)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("批量操作边界测试 - 大量客户")
    void testBatchOperation_ManyCustomers() throws Exception {
        // 创建100个测试客户
        List<Long> customerIds = createManyTestCustomers(100);
        customerPoolService.returnToPool(customerIds, "MANUAL", "批量测试");

        String requestBody = objectMapper.writeValueAsString(customerIds);
        mockMvc.perform(post("/crm/customer/batch/claim")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 异常场景测试 ====================

    @Test
    @Order(20)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("异常测试 - 认领不存在的客户")
    void testClaimCustomer_NotExists() throws Exception {
        mockMvc.perform(post("/crm/customer/{id}/claim", 99999L)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @Order(21)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("异常测试 - 认领已认领的客户")
    void testClaimCustomer_AlreadyClaimed() throws Exception {
        // 第一次认领
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "测试");
        customerPoolService.claimCustomers(Arrays.asList(testCustomer1.getId()));

        // 第二次认领应该失败
        mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    @Order(22)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("异常测试 - 空的批量操作")
    void testBatchOperation_EmptyList() throws Exception {
        String requestBody = "[]";
        mockMvc.perform(post("/crm/customer/batch/claim")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value(containsString("请选择")));
    }

    @Test
    @Order(23)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("异常测试 - 无效的JSON数据")
    void testBatchOperation_InvalidJson() throws Exception {
        String invalidJson = "{invalid json}";
        mockMvc.perform(post("/crm/customer/batch/claim")
                .content(invalidJson)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @Order(24)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("异常测试 - 超长备注信息")
    void testReturnToPool_LongRemark() throws Exception {
        String longRemark = "a".repeat(1000); // 1000字符的备注

        mockMvc.perform(post("/crm/customer/{id}/return", testCustomer2.getId())
                .param("reason", "MANUAL")
                .param("remark", longRemark)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // ==================== 权限控制测试 ====================

    @Test
    @Order(30)
    @DisplayName("权限测试 - 未登录用户")
    void testUnauthorized_NoLogin() throws Exception {
        mockMvc.perform(get("/crm/customer/pool")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @Order(31)
    @WithMockUser(username = "user", authorities = {})
    @DisplayName("权限测试 - 无权限用户")
    void testForbidden_NoPermission() throws Exception {
        mockMvc.perform(get("/crm/customer/pool")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @Test
    @Order(32)
    @WithMockUser(username = "user", authorities = {"crm:customer:list"})
    @DisplayName("权限测试 - 只读权限用户尝试修改")
    void testForbidden_ReadOnlyUser() throws Exception {
        mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    // ==================== 业务规则验证测试 ====================

    @Test
    @Order(40)
    @WithMockUser(username = "limitedUser", authorities = {"crm:customer:edit"})
    @DisplayName("业务规则测试 - 认领限制验证")
    void testClaimLimit_Validation() throws Exception {
        // 设置严格的认领限制
        CrmClaimLimits strictLimit = new CrmClaimLimits();
        strictLimit.setMaxClaimDaily(1);
        strictLimit.setMaxClaimTotal(1);
        strictLimit.setEnabled(true);
        claimLimitsMapper.insertCrmClaimLimits(strictLimit);

        // 准备公海客户
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId(), testCustomer2.getId()), "MANUAL", "测试");

        // 第一次认领应该成功
        mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        // 第二次认领应该失败（超出限制）
        mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer2.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value(containsString("限制")));
    }

    @Test
    @Order(41)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("业务规则测试 - 自动回收规则")
    void testAutoReturnRule() throws Exception {
        // 创建自动回收规则
        CrmPoolRules autoRule = new CrmPoolRules();
        autoRule.setRuleName("测试自动回收");
        autoRule.setRuleType("NO_FOLLOW");
        autoRule.setDaysLimit(0); // 立即回收
        autoRule.setEnabled(true);
        poolRulesMapper.insert(autoRule);

        // 执行自动回收
        int count = customerPoolService.autoReturnToPool();
        assertTrue(count >= 0, "自动回收应该返回非负数");
    }

    // ==================== 并发操作测试 ====================

    @Test
    @Order(50)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("并发测试 - 多用户同时认领同一客户")
    void testConcurrentClaim_SameCustomer() throws Exception {
        // 将客户放入公海
        customerPoolService.returnToPool(Arrays.asList(testCustomer1.getId()), "MANUAL", "并发测试");

        ExecutorService executor = Executors.newFixedThreadPool(5);
        List<CompletableFuture<MvcResult>> futures = Arrays.asList(
            CompletableFuture.supplyAsync(() -> {
                try {
                    return mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                            .contentType(MediaType.APPLICATION_JSON))
                            .andReturn();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, executor),
            CompletableFuture.supplyAsync(() -> {
                try {
                    return mockMvc.perform(post("/crm/customer/{id}/claim", testCustomer1.getId())
                            .contentType(MediaType.APPLICATION_JSON))
                            .andReturn();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, executor)
        );

        // 等待所有请求完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 验证只有一个请求成功
        long successCount = futures.stream()
                .mapToInt(f -> {
                    try {
                        String response = f.get().getResponse().getContentAsString();
                        return response.contains("\"code\":200") ? 1 : 0;
                    } catch (Exception e) {
                        return 0;
                    }
                })
                .sum();

        assertEquals(1, successCount, "并发认领同一客户，只应该有一个成功");
        executor.shutdown();
    }

    @Test
    @Order(51)
    @WithMockUser(username = "admin", authorities = {"crm:customer:edit"})
    @DisplayName("并发测试 - 大量批量操作")
    void testConcurrentBatchOperations() throws Exception {
        // 创建多个客户并放入公海
        List<Long> customerIds = createManyTestCustomers(50);
        customerPoolService.returnToPool(customerIds, "MANUAL", "并发测试");

        ExecutorService executor = Executors.newFixedThreadPool(10);
        List<CompletableFuture<Void>> futures = Arrays.asList(
            CompletableFuture.runAsync(() -> {
                try {
                    String requestBody = objectMapper.writeValueAsString(customerIds.subList(0, 25));
                    mockMvc.perform(post("/crm/customer/batch/claim")
                            .content(requestBody)
                            .contentType(MediaType.APPLICATION_JSON));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, executor),
            CompletableFuture.runAsync(() -> {
                try {
                    String requestBody = objectMapper.writeValueAsString(customerIds.subList(25, 50));
                    mockMvc.perform(post("/crm/customer/batch/claim")
                            .content(requestBody)
                            .contentType(MediaType.APPLICATION_JSON));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, executor)
        );

        // 等待所有操作完成
        assertDoesNotThrow(() -> CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join());
        executor.shutdown();
    }

    // ==================== 性能测试 ====================

    @Test
    @Order(60)
    @WithMockUser(username = "admin", authorities = {"crm:customer:list"})
    @DisplayName("性能测试 - 大数据量查询")
    void testPerformance_LargeDataQuery() throws Exception {
        // 创建大量测试数据
        List<Long> customerIds = createManyTestCustomers(1000);
        customerPoolService.returnToPool(customerIds, "MANUAL", "性能测试");

        long startTime = System.currentTimeMillis();

        mockMvc.perform(get("/crm/customer/pool")
                .param("pageNum", "1")
                .param("pageSize", "100")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertTrue(duration < 5000, "大数据量查询应该在5秒内完成，实际用时: " + duration + "ms");
    }

    // ==================== 辅助方法 ====================

    private void initTestData() {
        // 创建测试客户
        testCustomer1 = createTestCustomer("测试客户1", "13800138001", "<EMAIL>");
        testCustomer2 = createTestCustomer("测试客户2", "13800138002", "<EMAIL>");
        testCustomer3 = createTestCustomer("测试客户3", "13800138003", "<EMAIL>");

        customerMapper.insertCrmCustomer(testCustomer1);
        customerMapper.insertCrmCustomer(testCustomer2);
        customerMapper.insertCrmCustomer(testCustomer3);
    }

    private void cleanTestData() {
        // 清理测试数据（事务回滚会自动处理）
    }

    private CrmCustomer createTestCustomer(String name, String mobile, String email) {
        CrmCustomer customer = new CrmCustomer();
        customer.setCustomerName(name);
        customer.setMobile(mobile);
        customer.setEmail(email);
        customer.setResponsiblePersonId("1");
        customer.setCustomerIndustry("测试行业");
        customer.setCustomerLevel("A级");
        customer.setCustomerSource("测试来源");
        customer.setStatus("1");
        customer.setDelFlag("0");
        customer.setCreateTime(new Date());
        customer.setUpdateTime(new Date());
        return customer;
    }

    private List<Long> createManyTestCustomers(int count) {
        List<Long> customerIds = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            CrmCustomer customer = createTestCustomer("批量测试客户" + i, "138000000" + String.format("%02d", i), "batch" + i + "@test.com");
            customerMapper.insertCrmCustomer(customer);
            customerIds.add(customer.getId());
        }
        return customerIds;
    }
}