<template>
  <div class="contact-customer-relation-tab">
    <!-- 统计卡片 -->
    <div class="statistics-row">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic title="关联客户数" :value="customers.length" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="主要关系数" :value="primaryRelations.length" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="活跃关系数" :value="activeRelations.length" />
        </el-col>
      </el-row>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="openLinkCustomerDialog">
        <el-icon><Link /></el-icon>
        关联客户
      </el-button>
      <div class="toolbar-right">
        <el-radio-group v-model="filterStatus" @change="filterCustomers">
          <el-radio-button value="">全部</el-radio-button>
          <el-radio-button value="primary">主要关系</el-radio-button>
          <el-radio-button value="active">活跃关系</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 客户关系列表 -->
    <el-table
      v-loading="loading"
      :data="filteredRelatedCustomers"
      style="width: 100%"
      @row-click="handleRowClick"
    >
      <el-table-column prop="customerName" label="客户名称" min-width="200">
        <template #default="{ row }">
          <div class="customer-info">
            <el-avatar :size="32" :src="row.avatar">
              {{ row.customerName?.charAt(0) || '客' }}
            </el-avatar>
            <div class="customer-details">
              <div class="customer-name">{{ row.customerName }}</div>
              <div class="customer-industry">{{ row.industry || '未知行业' }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="relationType" label="关系类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getRelationTypeTagType(row.relationType)">
            {{ row.relationType || '次要联系人' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="isPrimary" label="主要关系" width="100">
        <template #default="{ row }">
          <el-tag :type="row.isPrimary ? 'success' : 'info'" size="small">
            <el-icon>
              <StarFilled v-if="row.isPrimary" />
              <Star v-else />
            </el-icon>
            {{ row.isPrimary ? '主要' : '次要' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="relationStatus" label="关系状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.relationStatus === 'active' ? 'success' : 'danger'">
            {{ row.relationStatus === 'active' ? '活跃' : '无效' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="startDate" label="关系开始时间" width="120">
        <template #default="{ row }">
          {{ formatDate(row.startDate) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="lastContactTime" label="最后联系" width="120">
        <template #default="{ row }">
          {{ formatDate(row.lastContactTime) || '未联系' }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button-group size="small">
            <el-button 
              v-if="!row.isPrimary" 
              @click.stop="handleSetPrimary(row)"
            >
              设为主要
            </el-button>
            <el-button @click.stop="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              type="danger" 
              @click.stop="handleUnlink(row)"
            >
              取消关联
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 关联客户对话框 -->
    <el-dialog 
      v-model="showLinkCustomerDialog" 
      title="关联客户" 
      width="600px"
      @close="closeLinkCustomerDialog"
    >
      <div class="link-customer-content">
        <el-input
          v-model="customerSearchKeyword"
          placeholder="搜索客户名称"
          size="default"
          clearable
          @input="searchCustomers"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <div class="available-customers" v-loading="searchLoading">
          <div v-if="paginatedCustomers.length === 0" class="empty-result">
            <el-empty description="暂无可关联的客户" />
          </div>

          <div v-else class="customers-selection">
            <!-- 客户列表表格 -->
            <el-table 
              :data="paginatedCustomers" 
              style="width: 100%" 
              height="300"
              @row-click="handleCustomerRowClick"
              highlight-current-row
            >
              <el-table-column width="50">
                <template #default="{ row }">
                  <el-radio 
                    v-model="selectedCustomerId" 
                    :value="row.id" 
                    @click.stop
                  />
                </template>
              </el-table-column>
              
              <el-table-column width="60">
                <template #default="{ row }">
                  <el-avatar :size="40" :src="row.avatar">
                    {{ row.customerName?.charAt(0) || '客' }}
                  </el-avatar>
                </template>
              </el-table-column>
              
              <el-table-column prop="customerName" label="客户名称" min-width="120">
                <template #default="{ row }">
                  <div class="customer-name">{{ row.customerName }}</div>
                </template>
              </el-table-column>
              
              <el-table-column prop="industry" label="行业" width="100">
                <template #default="{ row }">
                  <span class="industry-text">{{ row.industry || '未知' }}</span>
                </template>
              </el-table-column>
              
              <el-table-column prop="customerLevel" label="等级" width="80">
                <template #default="{ row }">
                  <el-tag size="small" :type="getCustomerLevelTagType(row.customerLevel)">
                    {{ row.customerLevel || '未分级' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 分页组件 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[5, 10, 20, 50]"
                :small="true"
                :total="filteredCustomers.length"
                layout="total, sizes, prev, pager, next"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>

        <!-- 关系设置 -->
        <div class="relation-settings" v-if="selectedCustomerId">
          <el-divider>关系设置</el-divider>
          <el-form :model="relationForm" label-width="100px">
            <el-form-item label="关系类型">
              <el-select v-model="relationForm.relationType" style="width: 100%">
                <el-option label="主要联系人" value="主要联系人" />
                <el-option label="次要联系人" value="次要联系人" />
                <el-option label="决策人" value="决策人" />
                <el-option label="财务联系人" value="财务联系人" />
                <el-option label="技术联系人" value="技术联系人" />
                <el-option label="运营联系人" value="运营联系人" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否主要">
              <el-switch v-model="relationForm.isPrimary" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input 
                v-model="relationForm.remarks" 
                type="textarea" 
                :rows="2" 
                placeholder="请输入关系备注"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeLinkCustomerDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleLinkCustomer" 
            :loading="linkCustomerLoading"
            :disabled="!selectedCustomerId"
          >
            确认关联
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { 
  Link,
  Search,
  Star, 
  StarFilled
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
    getCustomersByContactId, 
    linkCustomerContact, 
    unlinkCustomerContact, 
    setPrimaryContact
} from '@/api/crm/customer-contact-relation';
import { searchCustomers as searchCustomersApi } from '@/api/crm/customers';

interface CustomerRelation {
  id: number;
  customerId: number;
  customerName: string;
  relationType: string;
  isPrimary: boolean;
  relationStatus: string;
  startDate: string;
  lastContactTime?: string;
  avatar?: string;
  industry?: string;
  relationId?: number;
  remarks?: string;
}

interface Props {
  entityData: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'update:entity', value: Record<string, any>): void;
  (e: 'update-count', count: number): void;
}>();

// 状态
const loading = ref(false);
const customers = ref<CustomerRelation[]>([]);
const filterStatus = ref('');

// 关联客户相关
const showLinkCustomerDialog = ref(false);
const linkCustomerLoading = ref(false);
const searchLoading = ref(false);
const customerSearchKeyword = ref('');
const availableCustomers = ref<any[]>([]);
const selectedCustomerId = ref<number | null>(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const filteredCustomers = ref<any[]>([]);

// 关系表单
const relationForm = reactive({
  relationType: '次要联系人',
  isPrimary: false,
  remarks: ''
});

// 计算属性
const primaryRelations = computed(() => 
  customers.value.filter(c => c.isPrimary)
);

const activeRelations = computed(() => 
  customers.value.filter(c => c.relationStatus === 'active')
);

const filteredRelatedCustomers = computed(() => {
  let result = customers.value;
  
  if (filterStatus.value) {
    if (filterStatus.value === 'primary') {
      result = result.filter(c => c.isPrimary);
    } else if (filterStatus.value === 'active') {
      result = result.filter(c => c.relationStatus === 'active');
    }
  }
  
  return result;
});

// 分页后的客户列表
const paginatedCustomers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredCustomers.value.slice(start, end);
});

// 获取关系类型标签类型
const getRelationTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    '主要联系人': 'danger',
    '决策人': 'danger',
    '次要联系人': 'success',
    '财务联系人': 'warning',
    '技术联系人': 'info',
    '运营联系人': 'info'
  };
  return typeMap[type] || 'info';
};

// 获取客户等级标签类型
const getCustomerLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    'A级': 'danger',
    'B级': 'warning', 
    'C级': 'info',
    'D级': '',
    '重要客户': 'danger',
    '普通客户': 'success',
    '潜在客户': 'info'
  };
  return typeMap[level] || 'info';
};

// 格式化日期
const formatDate = (dateStr: string | undefined) => {
  if (!dateStr) return '-';
  return new Date(dateStr).toLocaleDateString('zh-CN');
};

// 处理筛选
const filterCustomers = () => {
  // 筛选逻辑在computed中处理
};

// 分页事件处理
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置到第一页
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理客户行点击
const handleCustomerRowClick = (row: any) => {
  selectedCustomerId.value = row.id;
};

// 处理行点击
const handleRowClick = (row: CustomerRelation) => {
  // 可以添加行点击处理逻辑
  console.log('点击客户关系:', row);
};

// 设置主要关系
const handleSetPrimary = async (row: CustomerRelation) => {
  if (!props.entityData?.id) return;
  
  try {
    const response = await setPrimaryContact({
      customerId: row.customerId,
      contactId: props.entityData.id
    });
    
    if (response.code === 200) {
      ElMessage.success('设置主要关系成功');
      await loadCustomers();
    } else {
      ElMessage.error(response.msg || '设置主要关系失败');
    }
  } catch (error) {
    console.error('设置主要关系失败:', error);
    ElMessage.error('设置主要关系失败');
  }
};

// 编辑关系
const handleEdit = (row: CustomerRelation) => {
  ElMessage.info('编辑关系功能开发中');
};

// 取消关联
const handleUnlink = async (row: CustomerRelation) => {
  if (!props.entityData?.id) return;
  
  try {
    await ElMessageBox.confirm(
      `确定要取消与客户 "${row.customerName}" 的关联吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await unlinkCustomerContact({
      customerId: row.customerId,
      contactId: props.entityData.id
    });
    
    if (response.code === 200) {
      ElMessage.success('取消关联成功');
      await loadCustomers();
    } else {
      ElMessage.error(response.msg || '取消关联失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消关联失败:', error);
      ElMessage.error('取消关联失败');
    }
  }
};

// 显示关联客户对话框
const openLinkCustomerDialog = async () => {
  showLinkCustomerDialog.value = true;
  // 对话框打开时立即加载所有客户
  await loadAllAvailableCustomers();
};

// 加载所有可用客户
const loadAllAvailableCustomers = async () => {
  searchLoading.value = true;
  try {
    // 获取已关联的客户ID列表，用于排除
    const linkedCustomerIds = customers.value.map(c => c.customerId);
    
    // 获取所有客户（不需要关键词）
    const response = await searchCustomersApi('');
    
    if (response.code === 200) {
      // 过滤掉已关联的客户
      const mappedCustomers = (response.rows || response.data || [])
        .filter((customer: any) => !linkedCustomerIds.includes(customer.id))
        .map((customer: any) => ({
          id: customer.id,
          customerName: customer.customerName || customer.name,
          industry: customer.industry || customer.customerIndustry,
          customerLevel: customer.customerLevel,
          avatar: customer.avatar
        }));
      
      availableCustomers.value = mappedCustomers;
      filteredCustomers.value = mappedCustomers;
      currentPage.value = 1; // 重置分页
    } else {
      availableCustomers.value = [];
      filteredCustomers.value = [];
    }
  } catch (error) {
    console.error('加载客户失败:', error);
    availableCustomers.value = [];
    filteredCustomers.value = [];
  } finally {
    searchLoading.value = false;
  }
};

// 搜索客户
const searchCustomers = async () => {
  // 如果搜索框为空，显示所有客户
  if (!customerSearchKeyword.value.trim()) {
    filteredCustomers.value = availableCustomers.value;
    currentPage.value = 1;
    return;
  }
  
  // 在本地数据中进行搜索
  const keyword = customerSearchKeyword.value.toLowerCase();
  filteredCustomers.value = availableCustomers.value.filter(customer => 
    customer.customerName.toLowerCase().includes(keyword) ||
    (customer.industry && customer.industry.toLowerCase().includes(keyword)) ||
    (customer.customerLevel && customer.customerLevel.toLowerCase().includes(keyword))
  );
  currentPage.value = 1; // 重置到第一页
};

// 关联客户
const handleLinkCustomer = async () => {
  if (!props.entityData?.id || !selectedCustomerId.value) return;
  
  linkCustomerLoading.value = true;
  try {
    const response = await linkCustomerContact({
      customerId: selectedCustomerId.value,
      contactId: props.entityData.id,
      relationType: relationForm.relationType,
      isPrimary: relationForm.isPrimary ? '1' : '0'
    });
    
    if (response.code === 200) {
      ElMessage.success('关联客户成功');
      closeLinkCustomerDialog();
      await loadCustomers();
    } else {
      ElMessage.error(response.msg || '关联客户失败');
    }
  } catch (error) {
    console.error('关联客户失败:', error);
    ElMessage.error('关联客户失败');
  } finally {
    linkCustomerLoading.value = false;
  }
};

// 关闭关联客户对话框
const closeLinkCustomerDialog = () => {
  showLinkCustomerDialog.value = false;
  customerSearchKeyword.value = '';
  availableCustomers.value = [];
  filteredCustomers.value = [];
  selectedCustomerId.value = null;
  currentPage.value = 1;
  pageSize.value = 10;
  Object.assign(relationForm, {
    relationType: '次要联系人',
    isPrimary: false,
    remarks: ''
  });
};

// 加载关联客户
const loadCustomers = async () => {
  if (!props.entityData?.id) return;
  
  loading.value = true;
  try {
    const response = await getCustomersByContactId(props.entityData.id);
    if (response.code === 200) {
      customers.value = (response.data || []).map((customer: any) => ({
        id: customer.id,
        customerId: customer.customerId || customer.id,
        customerName: customer.customerName || customer.name,
        relationType: customer.relationType || '次要联系人',
        isPrimary: customer.isPrimary === 1 || customer.isPrimary === true,
        relationStatus: customer.relationStatus || customer.status || 'active',
        startDate: customer.startDate || customer.createTime,
        lastContactTime: customer.lastContactTime,
        avatar: customer.avatar,
        industry: customer.industry,
        relationId: customer.relationId,
        remarks: customer.remarks
      }));
      
      // 通知父组件更新计数
      emit('update-count', customers.value.length);
    } else {
      customers.value = [];
    }
  } catch (error) {
    console.error('加载关联客户失败:', error);
    customers.value = [];
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  if (props.entityData) {
    loadCustomers();
  }
});
</script>

<style scoped lang="scss">
.contact-customer-relation-tab {
  padding: 20px;

  .statistics-row {
    margin-bottom: 20px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .toolbar-right {
    display: flex;
    gap: 12px;
  }

  .customer-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .customer-details {
      .customer-name {
        font-weight: 500;
        color: #303133;
      }

      .customer-industry {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .link-customer-content {
    .el-input {
      margin-bottom: 20px;
    }

    .available-customers {
      max-height: 300px;
      overflow-y: auto;
      margin-bottom: 20px;

      .empty-result {
        text-align: center;
        padding: 40px 0;
      }

      .customers-selection {
        .customer-name {
          font-weight: 500;
          color: #303133;
        }
        
        .industry-text {
          font-size: 12px;
          color: #666;
        }
        
        .pagination-wrapper {
          margin-top: 16px;
          display: flex;
          justify-content: center;
        }
      }
    }

    .relation-settings {
      .el-form {
        margin-top: 16px;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

:deep(.el-button-group .el-button) {
  margin-left: 0;
}

// 对话框中的表格样式
:deep(.available-customers .el-table) {
  .el-table__header th {
    background-color: #fafafa;
    font-weight: 500;
  }
  
  .el-table__row {
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  .el-radio {
    .el-radio__input {
      .el-radio__inner {
        width: 16px;
        height: 16px;
        
        &:after {
          width: 6px;
          height: 6px;
        }
      }
    }
  }
}

:deep(.el-statistic__number) {
  font-weight: bold;
  color: var(--el-color-primary);
}

@media (max-width: 768px) {
  .contact-customer-relation-tab {
    padding: 12px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: center;
  }
}
</style>