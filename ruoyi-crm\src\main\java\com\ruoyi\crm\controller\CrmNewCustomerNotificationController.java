package com.ruoyi.crm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.entity.CrmNewCustomerNotification;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.service.ICrmNewCustomerNotificationService;
import com.ruoyi.common.service.ICrmNewCustomerNotificationService.BatchProcessResult;
import com.ruoyi.common.service.ICrmNewCustomerNotificationService.BatchSendResult;
import com.ruoyi.common.service.ICrmNewCustomerNotificationService.NotificationResult;
import com.ruoyi.common.service.ICrmNewCustomerNotificationService.NotificationStatistics;
import com.ruoyi.common.utils.SecurityUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * CRM新客户通知控制器
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Api(tags = "CRM新客户通知")
@RestController
@RequestMapping("/crm/notification")
public class CrmNewCustomerNotificationController extends BaseController {

    @Autowired
    private ICrmNewCustomerNotificationService notificationService;

    /**
     * 查询通知列表
     */
    @ApiOperation("查询通知列表")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/list")
    public TableDataInfo list(CrmNewCustomerNotification notification) {
        return notificationService.selectNotificationPage(notification, null, null);
    }

    /**
     * 分页查询通知列表
     */
    @ApiOperation("分页查询通知列表")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @PostMapping("/page")
    public TableDataInfo page(@RequestBody CrmNewCustomerNotification notification,
                              @RequestParam(defaultValue = "1") Integer pageNum,
                              @RequestParam(defaultValue = "10") Integer pageSize) {
        return notificationService.selectNotificationPage(notification, pageNum, pageSize);
    }

    /**
     * 创建新客户通知
     */
    @ApiOperation("创建新客户通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:add')")
    @Log(title = "新客户通知", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createNotification(
            @ApiParam("客户ID") @RequestParam Long customerId,
            @ApiParam("客户名称") @RequestParam String customerName,
            @ApiParam("客户电话") @RequestParam String customerPhone,
            @ApiParam("订单ID") @RequestParam(required = false) Long orderId,
            @ApiParam("订单编号") @RequestParam(required = false) String orderNo) {
        NotificationResult result = notificationService.createNewCustomerNotification(
                customerId, customerName, customerPhone, orderId, orderNo);
        return result.isSuccess() ? success(result) : error(result.getMessage());
    }

    /**
     * 创建未分配订单通知
     */
    @ApiOperation("创建未分配订单通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:add')")
    @Log(title = "未分配订单通知", businessType = BusinessType.INSERT)
    @PostMapping("/create/unassigned")
    public AjaxResult createUnassignedOrderNotification(
            @ApiParam("订单ID") @RequestParam Long orderId,
            @ApiParam("订单编号") @RequestParam String orderNo,
            @ApiParam("客户名称") @RequestParam String customerName) {
        NotificationResult result = notificationService.createUnassignedOrderNotification(orderId, orderNo, customerName);
        return result.isSuccess() ? success(result) : error(result.getMessage());
    }

    /**
     * 处理通知
     */
    @ApiOperation("处理通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:edit')")
    @Log(title = "处理通知", businessType = BusinessType.UPDATE)
    @PutMapping("/process/{id}")
    public AjaxResult processNotification(
            @ApiParam("通知ID") @PathVariable Long id,
            @ApiParam("处理结果") @RequestParam String processResult) {
        Long processedBy = SecurityUtils.getUserId();
        boolean result = notificationService.processNotification(id, processedBy, processResult);
        return toAjax(result ? 1 : 0);
    }

    /**
     * 批量处理通知
     */
    @ApiOperation("批量处理通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:edit')")
    @Log(title = "批量处理通知", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/process")
    public AjaxResult batchProcessNotifications(
            @ApiParam("通知ID列表") @RequestParam List<Long> notificationIds,
            @ApiParam("处理结果") @RequestParam String processResult) {
        Long processedBy = SecurityUtils.getUserId();
        BatchProcessResult result = notificationService.batchProcessNotifications(notificationIds, processedBy, processResult);
        return success(result);
    }

    /**
     * 分配通知
     */
    @ApiOperation("分配通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:assign')")
    @Log(title = "分配通知", businessType = BusinessType.UPDATE)
    @PutMapping("/assign/{id}")
    public AjaxResult assignNotification(
            @ApiParam("通知ID") @PathVariable Long id,
            @ApiParam("分配给的用户ID") @RequestParam Long assignedTo) {
        Long assignedBy = SecurityUtils.getUserId();
        boolean result = notificationService.assignNotification(id, assignedTo, assignedBy);
        return toAjax(result ? 1 : 0);
    }

    /**
     * 自动分配通知
     */
    @ApiOperation("自动分配通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:assign')")
    @Log(title = "自动分配通知", businessType = BusinessType.UPDATE)
    @PutMapping("/auto/assign/{id}")
    public AjaxResult autoAssignNotification(@ApiParam("通知ID") @PathVariable Long id) {
        boolean result = notificationService.autoAssignNotification(id);
        return toAjax(result ? 1 : 0);
    }

    /**
     * 获取待处理通知
     */
    @ApiOperation("获取待处理通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/pending")
    public AjaxResult getPendingNotifications(@RequestParam(required = false) Long assignedTo) {
        List<CrmNewCustomerNotification> notifications = notificationService.getPendingNotifications(assignedTo);
        return success(notifications);
    }

    /**
     * 获取我的待处理通知
     */
    @ApiOperation("获取我的待处理通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/my/pending")
    public AjaxResult getMyPendingNotifications() {
        Long userId = SecurityUtils.getUserId();
        List<CrmNewCustomerNotification> notifications = notificationService.getPendingNotifications(userId);
        return success(notifications);
    }

    /**
     * 获取超时通知
     */
    @ApiOperation("获取超时通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/timeout")
    public AjaxResult getTimeoutNotifications(@RequestParam(defaultValue = "60") Integer timeoutMinutes) {
        List<CrmNewCustomerNotification> notifications = notificationService.getTimeoutNotifications(timeoutMinutes);
        return success(notifications);
    }

    /**
     * 发送企业微信通知
     */
    @ApiOperation("发送企业微信通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:send')")
    @Log(title = "发送企业微信通知", businessType = BusinessType.OTHER)
    @PostMapping("/send/wechat/{id}")
    public AjaxResult sendWechatNotification(@ApiParam("通知ID") @PathVariable Long id) {
        boolean result = notificationService.sendWechatNotification(id);
        return toAjax(result ? 1 : 0);
    }

    /**
     * 发送邮件通知
     */
    @ApiOperation("发送邮件通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:send')")
    @Log(title = "发送邮件通知", businessType = BusinessType.OTHER)
    @PostMapping("/send/email/{id}")
    public AjaxResult sendEmailNotification(@ApiParam("通知ID") @PathVariable Long id) {
        boolean result = notificationService.sendEmailNotification(id);
        return toAjax(result ? 1 : 0);
    }

    /**
     * 批量发送企业微信通知
     */
    @ApiOperation("批量发送企业微信通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:send')")
    @Log(title = "批量发送企业微信通知", businessType = BusinessType.OTHER)
    @PostMapping("/batch/send/wechat")
    public AjaxResult batchSendWechatNotifications() {
        BatchSendResult result = notificationService.batchSendWechatNotifications();
        return success(result);
    }

    /**
     * 批量发送邮件通知
     */
    @ApiOperation("批量发送邮件通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:send')")
    @Log(title = "批量发送邮件通知", businessType = BusinessType.OTHER)
    @PostMapping("/batch/send/email")
    public AjaxResult batchSendEmailNotifications() {
        BatchSendResult result = notificationService.batchSendEmailNotifications();
        return success(result);
    }

    /**
     * 获取通知统计
     */
    @ApiOperation("获取通知统计")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/statistics")
    public AjaxResult getNotificationStatistics(
            @RequestParam(required = false) Long assignedTo,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        NotificationStatistics statistics = notificationService.getNotificationStatistics(assignedTo, startDate, endDate);
        return success(statistics);
    }

    /**
     * 获取我的通知统计
     */
    @ApiOperation("获取我的通知统计")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/my/statistics")
    public AjaxResult getMyNotificationStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getUserId();
        NotificationStatistics statistics = notificationService.getNotificationStatistics(userId, startDate, endDate);
        return success(statistics);
    }

    /**
     * 获取通知处理效率
     */
    @ApiOperation("获取通知处理效率")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/efficiency")
    public AjaxResult getNotificationEfficiency(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        List<ICrmNewCustomerNotificationService.NotificationEfficiency> efficiency = 
                notificationService.getNotificationEfficiency(startDate, endDate);
        return success(efficiency);
    }

    /**
     * 清理过期通知
     */
    @ApiOperation("清理过期通知")
    @PreAuthorize("@ss.hasPermi('crm:notification:remove')")
    @Log(title = "清理过期通知", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean/expired")
    public AjaxResult cleanExpiredNotifications(@RequestParam(defaultValue = "30") Integer expireDays) {
        int cleanedCount = notificationService.cleanExpiredNotifications(expireDays);
        return success("已清理 " + cleanedCount + " 条过期通知");
    }

    /**
     * 通知配置
     */
    @ApiOperation("获取通知配置")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/config")
    public AjaxResult getNotificationConfig() {
        AjaxResult result = success();
        result.put("wechatEnabled", true);
        result.put("emailEnabled", true);
        result.put("autoAssignEnabled", true);
        result.put("timeoutMinutes", 60);
        result.put("expireDays", 30);
        result.put("batchSize", 100);
        
        return result;
    }

    /**
     * 通知模板
     */
    @ApiOperation("获取通知模板")
    @PreAuthorize("@ss.hasPermi('crm:notification:list')")
    @GetMapping("/template")
    public AjaxResult getNotificationTemplate(@RequestParam String type) {
        AjaxResult result = success();
        
        switch (type.toUpperCase()) {
            case "NEW_CUSTOMER":
                result.put("title", "新客户通知");
                result.put("content", "发现新客户：{customerName}，电话：{customerPhone}，请及时跟进。");
                break;
            case "UNASSIGNED_ORDER":
                result.put("title", "未分配订单通知");
                result.put("content", "订单 {orderNo} 尚未分配，客户：{customerName}，请及时处理。");
                break;
            default:
                result.put("title", "系统通知");
                result.put("content", "您有新的待处理事项，请及时查看。");
                break;
        }
        
        return result;
    }
}
