<template>
    <!-- 联系人管理主容器 -->
    <el-container class="contact-management">
        <!-- 主内容区域容器 -->
        <el-container class="main-container">
            <!-- 页面头部，包含标题和操作按钮 -->
            <el-header class="header">
                <h1>联系人管理</h1>
                <div class="header-actions">
                    <el-button
                        type="primary"
                        plain
                        size="small"
                        :disabled="selectedContacts.length === 0"
                        @click="handleBatchFollow"
                        class="action-btn"
                    >
                        <el-icon><Star /></el-icon>
                        批量关注
                    </el-button>
                    <el-button
                        type="primary"
                        plain
                        size="small"
                        :disabled="selectedContacts.length === 0"
                        @click="handleBatchUnfollow"
                        class="action-btn"
                    >
                        <el-icon><StarFilled /></el-icon>
                        批量取消关注
                    </el-button>
                    <!-- 团队分配按钮 -->
                    <TeamAssignButton
                        v-if="selectedContacts.length > 0"
                        :biz-ids="selectedContacts.map(c => c.id)"
                        :biz-names="selectedContacts.map(c => c.name)"
                        biz-type="CONTACT"
                        button-type="warning"
                        size="small"
                        text="分配团队"
                        @success="handleTeamAssignSuccess"
                    />
                    <!-- 管理员批量分配负责人按钮 -->
                    <el-button
                        v-if="selectedContacts.length > 0 && hasAdminPermission"
                        type="info"
                        plain
                        size="small"
                        @click="openBatchAssignDialog"
                        class="action-btn"
                    >
                        <el-icon><User /></el-icon>
                        批量分配负责人
                    </el-button>
                    <el-button
                        type="primary"
                        size="small"
                        @click="openContactDialog"
                        class="action-btn primary-btn"
                    >
                        <el-icon><Plus /></el-icon>
                        新建联系人
                    </el-button>
                </div>
            </el-header>

            <el-main class="main-content">
                <!-- 搜索和筛选组件 -->
                <common-filter
                    v-model:searchValue="searchInput"
                    v-model:filterValue="filterType"
                    :config="contactFilterConfig"
                    @search="handleSearch"
                    @filter="handleFilterChange"
                    class="filter-section"
                />

                <!-- 联系人数据表格 -->
                <div class="table-container">
                    <el-table 
                        ref="contactTable" 
                        :data="contacts" 
                        border 
                        sortable 
                        tooltip-effect="dark"
                        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333' }"
                        :height="'100%'"
                        :max-height="'100%'"
                        class="contacts-table"
                        @selection-change="handleSelectionChange">
                    <template v-for="col in tableColumns" :key="col.prop">
                        <el-table-column v-bind="col">
                            <template #default="scope" v-if="col.link">
                                <el-button link type="primary" class="link-button" @click="openDrawer(scope.row)">
                                    {{ scope.row.name }}
                                </el-button>
                            </template>
                            <template #default="scope" v-else-if="col.prop === 'isFollowing'">
                                <el-tag :type="scope.row.isFollowing ? 'success' : 'info'" size="small">
                                    <el-icon style="margin-right: 4px;">
                                        <StarFilled v-if="scope.row.isFollowing" />
                                        <Star v-else />
                                    </el-icon>
                                    {{ scope.row.isFollowing ? '已关注' : '未关注' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                    </template>
                    <!-- 表格操作列 -->
                    <el-table-column label="操作" width="220" fixed="right">
                        <template #default="scope">
                            <el-button
                                type="primary"
                                link
                          
                                @click="openDrawer(scope.row)"
                            >
                                <el-icon><Edit /></el-icon>
                                详情
                            </el-button>
                            <el-button
                                :type="scope.row.isFollowing ? 'warning' : 'success'"
                                link

                                @click="handleToggleFollow(scope.row)"
                            >
                                <el-icon>
                                    <StarFilled v-if="scope.row.isFollowing" />
                                    <Star v-else />
                                </el-icon>
                                {{ scope.row.isFollowing ? '取关' : '关注' }}
                            </el-button>
                            <!-- 团队分配按钮 -->
                            <TeamAssignButton
                                :biz-id="scope.row.id"
                                :biz-name="scope.row.name"
                                biz-type="CONTACT"
                                button-link
                                size="small"
                                text="分配"
                                show-current-team
                                @success="handleTeamAssignSuccess"
                            />
                            <el-button
                                type="danger"
                                link

                                @click="handleDelete(scope.row)"
                            >
                                <el-icon><Delete /></el-icon>
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                                    </el-table>
                </div>
                
                <!-- 分页组件 -->
                <div class="pagination-section">
                    <pagination v-show="totalContacts > 0" 
                        :total="totalContacts" 
                        :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize"
                        @pagination="handlePagination" />
                </div>
            </el-main>
        </el-container>

        <!-- 联系人详情抽屉组件 -->
        <common-drawer 
            v-model="drawerVisible" 
            entity-type="contact" 
            model-name="联系人" 
            :entity-data="currentContact" 
            :drawer-config="localDrawerConfig"
            :header-component="ContactHeaderTab" 
            :header-props="{ userOptions }"
            :actions="drawerActions"
            @update:entity-data="handleContactUpdate"
            @toggle-follow="handleDrawerToggleFollow" 
        />

        <!-- 新建联系人对话框组件 -->
        <common-form-dialog 
            v-model="contactDialogVisible" 
            title="新建联系人" 
            sub-title="请填写联系人信息" 
            :form-config="newContactFormConfig"
            :initial-data="newContact" 
            @cancel="cancelContact" 
            @submit="handleContactSubmit" 
            @close="handleContactClose" />

        <!-- 批量分配负责人对话框 -->
        <el-dialog
            v-model="batchAssignDialogVisible"
            title="批量分配负责人"
            width="500px"
            :before-close="() => batchAssignDialogVisible = false"
        >
            <div class="batch-assign-content">
                <el-alert
                    :title="`即将为 ${selectedContacts.length} 个联系人分配负责人`"
                    type="info"
                    :closable="false"
                    show-icon
                    style="margin-bottom: 20px;"
                />
                
                <el-form :model="batchAssignForm" label-width="100px">
                    <el-form-item label="新负责人" required>
                        <el-select
                            v-model="batchAssignForm.responsiblePersonId"
                            placeholder="请选择新负责人"
                            style="width: 100%"
                            filterable
                        >
                            <el-option
                                v-for="user in userOptions"
                                :key="user.userId"
                                :label="user.nickName || user.userName"
                                :value="String(user.userId)"
                            />
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="分配原因">
                        <el-input
                            v-model="batchAssignForm.reason"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入分配原因（可选）"
                            maxlength="200"
                            show-word-limit
                        />
                    </el-form-item>
                </el-form>
            </div>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="batchAssignDialogVisible = false">取消</el-button>
                    <el-button
                        type="primary"
                        @click="handleBatchAssignResponsible"
                        :loading="batchAssignLoading"
                    >
                        确定分配
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </el-container>
</template>

<script setup lang="ts">
// 导入所需的组件和类型
import { listUser as getUserList } from '@/api/system/user';
import { listTeam } from '@/api/crm/team';
import { searchCustomers } from '@/api/crm/customers';
import { assignTeamToBiz } from '@/api/team-relation';
import type { Action } from '@/components/CommonDrawer/index.vue';
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import CommonFilter from '@/components/CommonFilter/index.vue';
import CommonFormDialog from '@/components/CommonFormDialog/index.vue';
import type { TableButton } from '@/components/TableOperations/index.vue';
import TeamAssignButton from '@/components/TeamAssignButton.vue';
import { FilterType } from '@/types';
import { ContactEntity, DEFAULT_CONTACT } from '@/views/ContactManagement/types';
import { Delete, Edit, Plus, Star, StarFilled, User } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, onMounted, reactive, ref } from 'vue';
import {
    addContact,
    batchFollowContacts,
    batchUnfollowContacts,
    deleteContact,
    followContact,
    getContact,
    getCustomerInfo,
    getFollowStatus,
    listContacts,
    unfollowContact,
    updateContact,
    batchAssignResponsible,
    type ContactForm
} from './api';
import { drawerConfig as baseDrawerConfig, tableColumns } from './config';
import { contactFilterConfig } from './config/filterConfig';
import { newContactFormConfig as defaultFormConfig } from './config/formConfig';
import ContactHeaderTab from './tabs/ContactHeaderTab.vue';

// 查询参数状态定义
const queryParams = reactive({
    pageNum: 1,          // 当前页码
    pageSize: 10,        // 每页显示条数
    searchKeyword: '',   // 搜索关键词
    filterType: 'all' as FilterType  // 筛选类型
});

// 组件状态定义
const filterType = ref<FilterType>('all');           // 当前筛选类型
const searchInput = ref('');                         // 搜索输入值
const totalContacts = ref(0);                        // 联系人总数
const contacts = ref<ContactEntity[]>([]);           // 联系人列表数据
const contactDialogVisible = ref(false);             // 新建联系人对话框显示状态
const newContact = ref<ContactEntity>({ ...DEFAULT_CONTACT });  // 新建联系人表单数据
const drawerVisible = ref(false);                    // 详情抽屉显示状态
const currentContact = ref<ContactEntity>({ ...DEFAULT_CONTACT });  // 当前选中的联系人数据
const newContactFormConfig = ref(defaultFormConfig); // 新建联系人表单配置
const loading = ref(false);                          // 加载状态
const selectedContacts = ref<ContactEntity[]>([]);   // 选中的联系人列表
const userOptions = ref<any[]>([]);                  // 用户选项列表（用于负责人选择）
const batchAssignDialogVisible = ref(false);        // 批量分配对话框显示状态
const batchAssignForm = ref({                        // 批量分配表单
    responsiblePersonId: '',
    reason: ''
});
const batchAssignLoading = ref(false);               // 批量分配加载状态

// 表格操作按钮配置
const tableButtons: TableButton[] = [
    {
        label: '编辑',
        type: 'primary',
        link: true,
        icon: 'Edit',
        handler: (row: ContactEntity) => openDrawer(row)
    },
    {
        label: '关注',
        type: 'warning',
        link: true,
        icon: 'Star',
        handler: (row: ContactEntity) => handleToggleFollow(row)
    },
    {
        label: '删除',
        type: 'danger',
        link: true,
        icon: 'Delete',
        handler: (row: ContactEntity) => handleDelete(row)
    }
];

// 抽屉操作按钮配置（使用计算属性动态生成）
const drawerActions = computed<Action[]>(() => {
    const actions = [
        {
            label: '编辑',
            icon: 'Edit',
            handler: (data: ContactEntity) => {
                openDrawer(data);
            }
        },
        {
            label: currentContact.value.isFollowing ? '取消关注' : '关注',
            icon: currentContact.value.isFollowing ? 'StarFilled' : 'Star',
            type: (currentContact.value.isFollowing ? 'warning' : 'success') as 'warning' | 'success',
            handler: (data: ContactEntity) => {
                handleToggleFollow(data);
            }
        },
        {
            label: '分配团队',
            icon: 'UserFilled',
            type: 'primary' as 'primary',
            handler: (data: ContactEntity) => {
                handleAssignTeam(data);
            }
        },
        {
            label: '打印',
            icon: 'Printer',
            handler: (data: ContactEntity) => {
                console.log('打印', data);
            }
        },
        {
            label: '删除',
            type: 'danger' as 'danger',
            icon: 'Delete',
            handler: (data: ContactEntity) => {
                handleDelete(data);
            }
        }
    ];
    

    
    return actions;
});

// 权限检查
const hasAdminPermission = computed(() => {
    // 这里应该检查用户是否有管理员权限
    // 可以从用户store中获取角色信息
    return true; // 临时设为true，实际应该根据用户角色判断
});

// 抽屉配置
const localDrawerConfig = reactive({
    ...baseDrawerConfig
});

// 组件挂载时初始化数据
onMounted(() => {
    getList();
    loadFormOptions();
});

/**
 * 加载表单选项数据
 */
const loadFormOptions = async () => {
    try {
        // 并行加载所有选项数据
        const [userResponse, teamResponse, customerResponse] = await Promise.allSettled([
            getUserList(),
            listTeam({ status: '0' }),
            searchCustomers('')
        ]);

        // 处理用户列表
        if (userResponse.status === 'fulfilled' && userResponse.value.code === 200) {
            const userOptions = (userResponse.value.rows || []).map((user: any) => ({
                label: user.nickName || user.userName,
                value: String(user.userId)
            }));
            
            // 更新负责人选项
            const responsibleField = newContactFormConfig.value.fields.find(f => f.field === 'responsiblePersonId');
            if (responsibleField) {
                responsibleField.options = userOptions;
            }
        }

        // 处理团队列表
        if (teamResponse.status === 'fulfilled' && teamResponse.value.code === 200) {
            const teamOptions = (teamResponse.value.rows || []).map((team: any) => ({
                label: team.teamName,
                value: team.id || team.teamId
            }));
            
            // 更新团队选项
            const teamField = newContactFormConfig.value.fields.find(f => f.field === 'teamId');
            if (teamField) {
                teamField.options = teamOptions;
            }
        }

        // 处理客户列表
        if (customerResponse.status === 'fulfilled' && customerResponse.value.code === 200) {
            // 后端返回分页数据，需要取 rows 字段
            const customerData = customerResponse.value.rows || customerResponse.value.data || [];
            const customerOptions = customerData.map((customer: any) => ({
                label: customer.customerName || customer.name,
                value: customer.id
            }));
            
            // 更新客户选项
            const customerField = newContactFormConfig.value.fields.find(f => f.field === 'customerId');
            if (customerField) {
                customerField.options = customerOptions;
            }
        }
    } catch (error) {
        console.error('加载表单选项失败:', error);
    }
};

/**
 * 获取联系人列表数据
 */
const getList = async () => {
    try {
        loading.value = true;
        const response = await listContacts(queryParams);
                        const { code, msg, rows = [], total = 0 } = response;

        if (code === 200) {
            // 处理联系人数据，包含关注状态
            contacts.value = await Promise.all((rows || []).map(async (item: any) => {
                // 查询关注状态
                let isFollowing = false;
                try {
                    const followResponse = await getFollowStatus(item.id);
                    isFollowing = followResponse.data || false;
                } catch (error) {
                    console.warn('获取关注状态失败:', error);
                }
                
                return {
                    ...item,
                    delFlag: item.delFlag || '0',
                    customerName: item.customerName || '',
                    isFollowing
                };
            }));
            totalContacts.value = total;
        } else {
            ElMessage.error(msg || '获取联系人列表失败');
        }
    } catch (error) {
        console.error('获取联系人列表失败:', error);
        ElMessage.error('获取联系人列表失败');
    } finally {
        loading.value = false;
    }
};

/**
 * 处理表格选择变化
 * @param val 选中的联系人数组
 */
const handleSelectionChange = (val: ContactEntity[]): void => {
    selectedContacts.value = val;
    console.log('选中的联系人:', val);
};

/**
 * 打开新建联系人对话框
 */
const openContactDialog = (): void => {
    // 重置表单数据，包含所有新字段的默认值
    newContact.value = { 
        ...DEFAULT_CONTACT,
        gender: '',
        mobile: '',
        telephone: '',
        birthday: '',
        address: '',
        detailedAddress: '',
        department: '',
        decisionRole: '',
        contactLevel: '',
        nextContactTime: '',
        status: '0',
        isKeyDecisionMaker: '0',
        directSuperior: ''
    };
    contactDialogVisible.value = true;
};

/**
 * 取消新建联系人
 */
const cancelContact = (): void => {
    contactDialogVisible.value = false;
    newContact.value = { ...DEFAULT_CONTACT };
};

/**
 * 处理联系人表单提交
 * @param formData 表单数据
 */
const handleContactSubmit = async (formData: any): Promise<void> => {
    try {
        console.log('📝 提交的表单数据:', formData);
        
        // 构造提交数据，映射所有字段
        const contactData: ContactForm = {
            name: formData.name || '',
            position: formData.position || '',
            phone: formData.mobile || formData.phone || '', // 优先使用mobile字段
            mobile: formData.mobile || '',  // 确保mobile字段存在
            email: formData.email || '',
            customerId: formData.customerId || 0,
            responsiblePerson: formData.responsiblePersonId ? Number(formData.responsiblePersonId) : 0,
            responsiblePersonId: formData.responsiblePersonId || '1', // 确保后端字段存在，默认设为1
            remarks: formData.remarks || '',
            
            // 扩展字段
            gender: formData.gender || '',
            telephone: formData.telephone || '',
            birthday: formData.birthday || '',
            address: formData.address || '',
            detailedAddress: formData.detailedAddress || '',
            department: formData.department || '',
            decisionRole: formData.decisionRole || '',
            contactLevel: formData.contactLevel || '',
            nextContactTime: formData.nextContactTime || '',
            status: formData.status || '0',
            isKeyDecisionMaker: formData.isKeyDecisionMaker || '0',
            directSuperior: formData.directSuperior || ''
        };

        console.log('🚀 构造的API数据:', contactData);

        const response = await addContact(contactData);
        if (response.code === 200) {
            ElMessage.success('新建联系人成功');
            
            // 如果选择了团队，自动分配团队
            if (formData.teamId && response.data?.id) {
                try {
                    await assignTeamToBiz(formData.teamId, response.data.id, 'CONTACT');
                    ElMessage.success('联系人创建成功并已分配团队');
                } catch (error) {
                    console.error('分配团队失败:', error);
                    ElMessage.warning('联系人创建成功，但团队分配失败');
                }
            }
            
            // 关闭对话框并刷新列表
            contactDialogVisible.value = false;
            getList();
        } else {
            ElMessage.error(response.msg || '新建联系人失败');
        }
    } catch (error) {
        console.error('新建联系人失败:', error);
        ElMessage.error('新建联系人失败');
    }
};

/**
 * 处理联系人对话框关闭
 */
const handleContactClose = (): void => {
    newContact.value = { ...DEFAULT_CONTACT };
};

/**
 * 处理团队分配成功
 */
const handleTeamAssignSuccess = (): void => {
    ElMessage.success('团队分配成功');
    // 可以在这里刷新列表或更新相关状态
    getList();
};

/**
 * 处理单个联系人的团队分配
 */
const handleAssignTeam = (contact: ContactEntity): void => {
    // 这里可以打开团队分配对话框
    // 或者使用其他方式处理单个联系人的团队分配
    console.log('分配团队给联系人:', contact);
    ElMessage.info('团队分配功能开发中...');
};

/**
 * 打开联系人详情抽屉
 * @param row 联系人数据行
 */
const openDrawer = async (row: ContactEntity): Promise<void> => {
    try {
        // 并行加载多个接口数据
        const [contactResponse, followResponse, customerResponse, userResponse] = await Promise.allSettled([
            getContact(row.id),
            getFollowStatus(row.id),
            row.customerId ? getCustomerInfo(row.customerId) : Promise.resolve({ code: 200, data: null }),
            getUserList()
        ]);

        // 处理联系人详情
        if (contactResponse.status === 'fulfilled' && contactResponse.value.code === 200 && contactResponse.value.data) {
            const contactData = contactResponse.value.data;
            console.log('🔍 后端返回的原始联系人数据:', contactData);
            
            currentContact.value = {
                // 基础字段
                id: contactData.id,
                name: contactData.name,
                position: contactData.position,
                phone: contactData.phone,
                email: contactData.email,
                customerId: contactData.customerId,
                responsiblePersonId: contactData.responsiblePersonId,
                createTime: contactData.createdAt || contactData.createTime,
                createBy: contactData.createBy,
                updateTime: contactData.updateTime,
                updateBy: contactData.updateBy,
                delFlag: contactData.delFlag || '0',
                customerName: contactData.customerName || '',
                
                // 🔧 修复：添加遗漏的字段
                gender: contactData.gender || '', // 性别
                mobile: contactData.mobile || contactData.phone || '', // 手机号（后端可能用phone字段）
                birthday: contactData.birthday || '', // 生日
                address: contactData.address || '', // 地址
                telephone: contactData.telephone || '', // 固话
                department: contactData.department || '', // 部门
                decisionRole: contactData.decisionRole || '', // 决策角色
                contactLevel: contactData.contactLevel || '', // 联系人级别
                nextContactTime: contactData.nextContactTime || '', // 下次联系时间
                status: contactData.status || '0', // 状态
                remarks: contactData.remarks || '' // 备注
            };
            
            console.log('🔧 修复后的currentContact数据:', currentContact.value);

            // 处理关注状态
            if (followResponse.status === 'fulfilled' && followResponse.value.code === 200) {
                currentContact.value.isFollowing = followResponse.value.data || false;
            }

            // 处理客户信息
            if (customerResponse.status === 'fulfilled' && customerResponse.value.code === 200 && customerResponse.value.data) {
                currentContact.value.customerInfo = customerResponse.value.data;
            }

            console.log('🔍 当前联系人数据:', JSON.stringify(userResponse, null, 2) );
            // 处理用户列表（用于负责人选择）
            if (userResponse.status === 'fulfilled' && userResponse.value.code === 200) {
                userOptions.value = userResponse.value.rows || [];
            }

            drawerVisible.value = true;
        } else {
            const errorMsg = contactResponse.status === 'fulfilled' ? contactResponse.value.msg : '获取联系人详情失败';
            ElMessage.error(errorMsg || '获取联系人详情失败');
        }
    } catch (error) {
        console.error('获取联系人详情失败:', error);
        ElMessage.error('获取联系人详情失败');
    }
};

/**
 * 更新联系人信息
 * @param newData 更新后的联系人数据
 */
const handleContactUpdate = async (newData: ContactEntity): Promise<void> => {
    try {
        console.log('=== 父组件接收到更新数据 ===');
        console.log('新数据:', newData);
        console.log('当前联系人数据:', currentContact.value);

        // 构造符合后端 CrmContacts 实体类的数据格式
        const updateData: ContactForm = {
            id: newData.id,
            name: newData.name || '',
            position: newData.position || '',
            phone: newData.mobile || newData.phone || '', // 优先使用mobile字段，兼容phone字段
            email: newData.email || '',
            customerId: newData.customerId || 0,
            responsiblePerson: newData.responsiblePersonId ? Number(newData.responsiblePersonId) : 0,
            remarks: newData.remarks || '',
            
            // 扩展字段支持
            gender: newData.gender || '',
            birthday: newData.birthday || '',
            telephone: newData.telephone || '',
            address: newData.address || '',
            department: newData.department || '',
            decisionRole: newData.decisionRole || '',
            contactLevel: newData.contactLevel || '',
            nextContactTime: newData.nextContactTime || '',
            status: newData.status || '0',
            customerName: newData.customerName || ''
        };

        console.log('构造的API数据:', updateData);

        const response = await updateContact(updateData);
        console.log('API响应:', response);
        
        if (response.code === 200) {
            ElMessage.success('更新联系人成功');
            // 立即更新当前联系人数据
            Object.assign(currentContact.value, newData);
            console.log('更新后的当前联系人数据:', currentContact.value);
            
            // 刷新列表数据
            await getList();
            console.log('列表数据已刷新');
        } else {
            ElMessage.error(response.msg || '更新联系人失败');
        }
        
        console.log('=== 父组件更新处理完成 ===');
    } catch (error) {
        console.error('更新联系人失败:', error);
        ElMessage.error('更新联系人失败');
    }
};

/**
 * 删除联系人
 * @param row 要删除的联系人数据行
 */
const handleDelete = async (row: ContactEntity): Promise<void> => {
    try {
        await ElMessageBox.confirm('确认删除该联系人吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        const response = await deleteContact(row.id);
        if (response.code === 200) {
            ElMessage.success('删除成功');
            getList();
        } else {
            ElMessage.error(response.msg || '删除联系人失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除联系人失败:', error);
            ElMessage.error('删除联系人失败');
        }
    }
};

/**
 * 切换关注状态
 * @param row 联系人数据行
 */
const handleToggleFollow = async (row: ContactEntity): Promise<void> => {
    try {
        // 先查询当前关注状态
        const statusResponse = await getFollowStatus(row.id);
        const isCurrentlyFollowing = statusResponse.data || false;

        if (isCurrentlyFollowing) {
            // 取消关注
            const response = await unfollowContact(row.id);
            if (response.code === 200) {
                ElMessage.success('取消关注成功');
                row.isFollowing = false;
                // 如果是当前抽屉中的联系人，也要更新currentContact的状态
                if (currentContact.value.id === row.id) {
                    currentContact.value.isFollowing = false;
                }
                getList(); // 刷新列表
            } else {
                ElMessage.error(response.msg || '取消关注失败');
            }
        } else {
            // 关注
            const response = await followContact(row.id);
            if (response.code === 200) {
                ElMessage.success('关注成功');
                row.isFollowing = true;
                // 如果是当前抽屉中的联系人，也要更新currentContact的状态
                if (currentContact.value.id === row.id) {
                    currentContact.value.isFollowing = true;
                }
                getList(); // 刷新列表
            } else {
                ElMessage.error(response.msg || '关注失败');
            }
        }
    } catch (error) {
        console.error('切换关注状态失败:', error);
        ElMessage.error('操作失败');
    }
};

/**
 * 处理搜索
 * @param value 搜索关键词
 */
const handleSearch = (value: string): void => {
    queryParams.pageNum = 1; // 重置到第一页
    queryParams.searchKeyword = value;
    getList();
};

/**
 * 处理筛选变化
 * @param value 筛选类型
 */
const handleFilterChange = (value: FilterType): void => {
    queryParams.pageNum = 1; // 重置到第一页
    queryParams.filterType = value;
    getList();
};

/**
 * 处理分页变化
 * @param val 分页参数对象
 */
const handlePagination = (val: { page: number; limit: number }) => {
    queryParams.pageNum = val.page;
    queryParams.pageSize = val.limit;
    getList();
};

/**
 * 批量关注联系人
 */
const handleBatchFollow = async (): Promise<void> => {
    if (selectedContacts.value.length === 0) {
        ElMessage.warning('请选择要关注的联系人');
        return;
    }

    try {
        const contactIds = selectedContacts.value.map(contact => contact.id);
        const response = await batchFollowContacts(contactIds);
        
        if (response.code === 200) {
            ElMessage.success('批量关注成功');
            getList(); // 刷新列表
        } else {
            ElMessage.error(response.msg || '批量关注失败');
        }
    } catch (error) {
        console.error('批量关注失败:', error);
        ElMessage.error('批量关注失败');
    }
};

/**
 * 处理抽屉中的关注状态切换
 */
const handleDrawerToggleFollow = async (): Promise<void> => {
    await handleToggleFollow(currentContact.value);
    // 更新当前联系人的关注状态
    const updatedContact = contacts.value.find(c => c.id === currentContact.value.id);
    if (updatedContact) {
        currentContact.value.isFollowing = updatedContact.isFollowing;
    }
};

/**
 * 批量取消关注联系人
 */
const handleBatchUnfollow = async (): Promise<void> => {
    if (selectedContacts.value.length === 0) {
        ElMessage.warning('请选择要取消关注的联系人');
        return;
    }

    try {
        const contactIds = selectedContacts.value.map(contact => contact.id);
        const response = await batchUnfollowContacts(contactIds);
        
        if (response.code === 200) {
            ElMessage.success('批量取消关注成功');
            getList(); // 刷新列表
        } else {
            ElMessage.error(response.msg || '批量取消关注失败');
        }
    } catch (error) {
        console.error('批量取消关注失败:', error);
        ElMessage.error('批量取消关注失败');
    }
};

/**
 * 打开批量分配负责人对话框
 */
const openBatchAssignDialog = (): void => {
    if (selectedContacts.value.length === 0) {
        ElMessage.warning('请选择要分配的联系人');
        return;
    }
    
    batchAssignForm.value = {
        responsiblePersonId: '',
        reason: ''
    };
    batchAssignDialogVisible.value = true;
};

/**
 * 处理批量分配负责人
 */
const handleBatchAssignResponsible = async (): Promise<void> => {
    if (!batchAssignForm.value.responsiblePersonId) {
        ElMessage.warning('请选择负责人');
        return;
    }
    
    batchAssignLoading.value = true;
    try {
        const contactIds = selectedContacts.value.map(contact => contact.id);
        const response = await batchAssignResponsible({
            contactIds,
            responsiblePersonId: batchAssignForm.value.responsiblePersonId,
            reason: batchAssignForm.value.reason
        });
        
        if (response.code === 200) {
            ElMessage.success(`成功分配 ${contactIds.length} 个联系人`);
            batchAssignDialogVisible.value = false;
            getList(); // 刷新列表
        } else {
            ElMessage.error(response.msg || '批量分配失败');
        }
    } catch (error) {
        console.error('批量分配失败:', error);
        ElMessage.error('批量分配失败');
    } finally {
        batchAssignLoading.value = false;
    }
};
</script>

<!-- 样式定义 -->
<style scoped>
/* 联系人管理容器样式 */
.contact-management {
    background-color: #fff;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 主容器样式 */
.main-container {
    flex: 1;
    padding: 0 20px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    flex-shrink: 0;
}

.header h1 {
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin: 0;
}

/* 头部操作区域样式 */
.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 400;
    font-size: var(--ep-font-size-base);
    transition: all 0.2s ease;
}

.action-btn .el-icon {
    margin-right: 5px;
    font-size: var(--ep-font-size-base);
}

.primary-btn {
    font-weight: 500;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 20px 16px;
    overflow: hidden;
    min-height: 0;
}

/* 筛选区域样式 */
.filter-section {
    flex-shrink: 0;
    margin-bottom: 16px;
}

/* 表格容器样式 */
.table-container {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    min-height: 0;
}

/* 表格样式 */
.contacts-table {
    flex: 1;
    border-radius: 8px;
}

/* 表格列对齐 */
:deep(.el-table .cell) {
    text-align: left;
}

/* 表格头部对齐 */
:deep(.el-table th .cell) {
    text-align: left;
}

/* 分页区域样式 */
.pagination-section {
    flex-shrink: 0;
    display: flex;
    /* justify-content: center; */
    padding: 12px 0;
    /* background-color: #fff; */
    border-top: 1px solid #f0f2f5;
}

/* 链接按钮样式 */
.link-button {
    padding: 0;
    height: auto;
    font-weight: normal;
}

.link-button:hover {
    text-decoration: underline;
}

/* 表格行高调整 */
:deep(.el-table td) {
    padding: 12px 0;
}

:deep(.el-table th) {
    padding: 14px 0;
    background-color: #fafafa !important;
}

/* 表格操作按钮样式 */
:deep(.el-table .el-button) {
    font-size: var(--ep-font-size-base);
}

:deep(.el-table .el-button .el-icon) {
    font-size: var(--ep-font-size-base);
}

/* 表格边框优化 */
:deep(.el-table--border) {
    border: 1px solid #ebeef5;
}

:deep(.el-table--border td) {
    border-right: 1px solid #f0f2f5;
}

:deep(.el-table--border th) {
    border-right: 1px solid #f0f2f5;
}
</style>
