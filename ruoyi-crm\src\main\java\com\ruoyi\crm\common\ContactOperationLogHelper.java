package com.ruoyi.crm.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.domain.entity.CrmContactOperationLog;
import com.ruoyi.common.service.ICrmContactOperationLogService;
import com.ruoyi.common.utils.SecurityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 联系人操作日志帮助类
 * 
 * <AUTHOR>
 */
@Component
public class ContactOperationLogHelper {

    @Autowired
    private ICrmContactOperationLogService operationLogService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 记录联系人活动操作日志
     * 
     * @param contactId 联系人ID
     * @param operationType 操作类型
     * @param operationContent 操作内容
     * @param operationDetails 操作详情
     */
    public void recordActivityLog(Long contactId, String operationType, String operationContent, String operationDetails) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String currentUserName = SecurityUtils.getUsername();
            
            // 将操作详情转换为JSON格式
            String jsonDetails = convertToJsonDetails(operationDetails);
            
            CrmContactOperationLog log = new CrmContactOperationLog(
                contactId, 
                "活动记录", // businessType
                operationType, 
                operationContent, 
                jsonDetails,
                currentUserId, 
                currentUserName
            );
            
            operationLogService.insertCrmContactOperationLog(log);
        } catch (Exception e) {
            // 记录日志失败不应该影响业务操作，只记录错误
            System.err.println("记录联系人操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 记录联系人活动操作日志（结构化详情）
     */
    public void recordActivityLogWithDetails(Long contactId, String operationType, String operationContent, Map<String, Object> detailsMap) {
        try {
            System.out.println("=== 开始记录联系人操作日志 ===");
            System.out.println("联系人ID: " + contactId);
            System.out.println("操作类型: " + operationType);
            System.out.println("操作内容: " + operationContent);
            
            Long currentUserId = SecurityUtils.getUserId();
            String currentUserName = SecurityUtils.getUsername();
            
            System.out.println("当前用户ID: " + currentUserId);
            System.out.println("当前用户名: " + currentUserName);
            
            // 将详情Map转换为JSON格式
            String jsonDetails = objectMapper.writeValueAsString(detailsMap);
            System.out.println("JSON详情: " + jsonDetails);
            
            CrmContactOperationLog log = new CrmContactOperationLog(
                contactId, 
                "活动记录", // businessType
                operationType, 
                operationContent, 
                jsonDetails,
                currentUserId, 
                currentUserName
            );
            
            System.out.println("准备插入操作日志...");
            int result = operationLogService.insertCrmContactOperationLog(log);
            System.out.println("插入结果: " + result);
            System.out.println("=== 联系人操作日志记录完成 ===");
        } catch (Exception e) {
            // 记录日志失败不应该影响业务操作，只记录错误
            System.err.println("记录联系人操作日志失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 将操作详情转换为JSON格式
     */
    private String convertToJsonDetails(String details) {
        try {
            Map<String, Object> detailsMap = new HashMap<>();
            detailsMap.put("description", details);
            detailsMap.put("timestamp", System.currentTimeMillis());
            return objectMapper.writeValueAsString(detailsMap);
        } catch (Exception e) {
            // 如果转换失败，返回一个简单的JSON
            return "{\"description\":\"" + (details != null ? details.replace("\"", "\\\"") : "") + "\"}";
        }
    }

    /**
     * 记录跟进记录操作
     */
    public void recordFollowupLog(Long contactId, String operationType, String followupType, String content) {
        String operationContent = String.format("%s了%s跟进记录", getOperationLabel(operationType), followupType);
        
        // 创建结构化的JSON详情
        Map<String, Object> details = new HashMap<>();
        details.put("operationType", operationType);
        details.put("followupType", followupType);
        details.put("content", content != null ? content : "");
        details.put("timestamp", System.currentTimeMillis());
        
        recordActivityLogWithDetails(contactId, operationType, operationContent, details);
    }

    /**
     * 记录拜访计划操作
     */
    public void recordVisitPlanLog(Long contactId, String operationType, String planName, String details) {
        String operationContent = String.format("%s拜访计划: %s", getOperationLabel(operationType), planName);
        
        // 创建结构化的JSON详情
        Map<String, Object> detailsMap = new HashMap<>();
        detailsMap.put("operationType", operationType);
        detailsMap.put("planName", planName);
        detailsMap.put("details", details != null ? details : "");
        detailsMap.put("timestamp", System.currentTimeMillis());
        
        recordActivityLogWithDetails(contactId, operationType, operationContent, detailsMap);
    }

    /**
     * 记录通话记录操作
     */
    public void recordCallLog(Long contactId, String operationType, String callType, String duration) {
        String operationContent = String.format("%s了%s通话记录", getOperationLabel(operationType), callType);
        
        // 创建结构化的JSON详情
        Map<String, Object> detailsMap = new HashMap<>();
        detailsMap.put("operationType", operationType);
        detailsMap.put("callType", callType);
        detailsMap.put("duration", duration != null ? duration : "");
        detailsMap.put("timestamp", System.currentTimeMillis());
        
        recordActivityLogWithDetails(contactId, operationType, operationContent, detailsMap);
    }

    /**
     * 记录客户关联操作
     */
    public void recordCustomerRelationLog(Long contactId, String operationType, String customerName, String relationType) {
        String operationContent = String.format("%s客户关联: %s", getOperationLabel(operationType), customerName);
        
        // 创建结构化的JSON详情
        Map<String, Object> detailsMap = new HashMap<>();
        detailsMap.put("operationType", operationType);
        detailsMap.put("customerName", customerName);
        detailsMap.put("relationType", relationType != null ? relationType : "");
        detailsMap.put("timestamp", System.currentTimeMillis());
        
        recordActivityLogWithDetails(contactId, operationType, operationContent, detailsMap);
    }

    /**
     * 记录客户关联操作（通过客户ID获取客户名称）
     */
    public void recordCustomerRelationLogById(Long contactId, String operationType, Long customerId, String relationType) {
        try {
            // 这里可以注入客户服务来获取客户名称，但为了避免循环依赖，直接使用客户ID
            String customerInfo = customerId != null ? "客户ID: " + customerId : "未知客户";
            String operationContent = String.format("%s客户关联: %s", getOperationLabel(operationType), customerInfo);
            
            // 创建结构化的JSON详情
            Map<String, Object> detailsMap = new HashMap<>();
            detailsMap.put("operationType", operationType);
            detailsMap.put("customerId", customerId);
            detailsMap.put("relationType", relationType != null ? relationType : "");
            detailsMap.put("timestamp", System.currentTimeMillis());
            
            recordActivityLogWithDetails(contactId, operationType, operationContent, detailsMap);
        } catch (Exception e) {
            // 记录基本信息，避免因获取客户信息失败而导致整个日志记录失败
            String operationContent = String.format("%s客户关联", getOperationLabel(operationType));
            Map<String, Object> detailsMap = new HashMap<>();
            detailsMap.put("operationType", operationType);
            detailsMap.put("customerId", customerId);
            detailsMap.put("relationType", relationType != null ? relationType : "");
            detailsMap.put("error", "获取客户信息失败");
            detailsMap.put("timestamp", System.currentTimeMillis());
            
            recordActivityLogWithDetails(contactId, operationType, operationContent, detailsMap);
        }
    }

    /**
     * 获取操作标签
     */
    private String getOperationLabel(String operationType) {
        switch (operationType) {
            case "add_followup":
            case "add_visit_plan":
            case "add_call_record":
                return "添加";
            case "update_followup":
            case "update_visit_plan":
            case "update_call_record":
                return "更新";
            case "delete_followup":
            case "delete_visit_plan":
            case "delete_call_record":
                return "删除";
            case "postpone_visit_plan":
                return "延期";
            case "cancel_visit_plan":
                return "取消";
            case "complete_visit_plan":
                return "完成";
            case "link_customer":
                return "关联";
            case "unlink_customer":
                return "取消关联";
            case "set_primary_customer":
                return "设置主要";
            default:
                return "操作";
        }
    }
}