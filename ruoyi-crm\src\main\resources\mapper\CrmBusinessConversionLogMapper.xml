<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.mapper.CrmBusinessConversionLogMapper">
    
    <resultMap type="CrmBusinessConversionLog" id="CrmBusinessConversionLogResult">
        <result property="id"    column="id"    />
        <result property="conversionType"    column="conversion_type"    />
        <result property="sourceType"    column="source_type"    />
        <result property="sourceId"    column="source_id"    />
        <result property="sourceName"    column="source_name"    />
        <result property="targetType"    column="target_type"    />
        <result property="targetId"    column="target_id"    />
        <result property="targetName"    column="target_name"    />
        <result property="conversionData"    column="conversion_data"    />
        <result property="conversionAmount"    column="conversion_amount"    />
        <result property="successFlag"    column="success_flag"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="conversionTime"    column="conversion_time"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectCrmBusinessConversionLogVo">
        select id, conversion_type, source_type, source_id, source_name, target_type, target_id, target_name,
               conversion_data, conversion_amount, success_flag, operator_id, operator_name, conversion_time, remarks
        from crm_business_conversion_log
    </sql>

    <select id="selectCrmBusinessConversionLogList" parameterType="CrmBusinessConversionLog" resultMap="CrmBusinessConversionLogResult">
        <include refid="selectCrmBusinessConversionLogVo"/>
        <where>  
            <if test="conversionType != null  and conversionType != ''"> and conversion_type = #{conversionType}</if>
            <if test="sourceType != null  and sourceType != ''"> and source_type = #{sourceType}</if>
            <if test="sourceId != null "> and source_id = #{sourceId}</if>
            <if test="targetType != null  and targetType != ''"> and target_type = #{targetType}</if>
            <if test="targetId != null "> and target_id = #{targetId}</if>
            <if test="successFlag != null "> and success_flag = #{successFlag}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="startTime != null and startTime != ''"> and conversion_time &gt;= #{startTime}</if>
            <if test="endTime != null and endTime != ''"> and conversion_time &lt;= #{endTime}</if>
            <if test="minAmount != null"> and conversion_amount &gt;= #{minAmount}</if>
            <if test="maxAmount != null"> and conversion_amount &lt;= #{maxAmount}</if>
        </where>
        order by conversion_time desc
    </select>
    
    <select id="selectCrmBusinessConversionLogById" parameterType="Long" resultMap="CrmBusinessConversionLogResult">
        <include refid="selectCrmBusinessConversionLogVo"/>
        where id = #{id}
    </select>

    <select id="selectCrmBusinessConversionLogByType" parameterType="String" resultMap="CrmBusinessConversionLogResult">
        <include refid="selectCrmBusinessConversionLogVo"/>
        where conversion_type = #{conversionType}
        order by conversion_time desc
    </select>

    <select id="selectCrmBusinessConversionLogBySource" resultMap="CrmBusinessConversionLogResult">
        <include refid="selectCrmBusinessConversionLogVo"/>
        where source_type = #{sourceType} and source_id = #{sourceId}
        order by conversion_time desc
    </select>

    <select id="selectCrmBusinessConversionLogByTarget" resultMap="CrmBusinessConversionLogResult">
        <include refid="selectCrmBusinessConversionLogVo"/>
        where target_type = #{targetType} and target_id = #{targetId}
        order by conversion_time desc
    </select>

    <select id="selectCrmBusinessConversionLogByOperator" parameterType="Long" resultMap="CrmBusinessConversionLogResult">
        <include refid="selectCrmBusinessConversionLogVo"/>
        where operator_id = #{operatorId}
        order by conversion_time desc
    </select>

    <select id="selectConversionSuccessRate" resultMap="CrmBusinessConversionLogResult">
        select conversion_type, 
               count(*) as total_count,
               sum(success_flag) as success_count,
               round(sum(success_flag) * 100.0 / count(*), 2) as success_rate
        from crm_business_conversion_log
        where 1=1
        <if test="conversionType != null and conversionType != ''">and conversion_type = #{conversionType}</if>
        <if test="startTime != null and startTime != ''">and conversion_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and conversion_time &lt;= #{endTime}</if>
        group by conversion_type
        order by success_rate desc
    </select>

    <select id="selectConversionAmountStats" resultMap="CrmBusinessConversionLogResult">
        select conversion_type,
               count(*) as conversion_count,
               sum(conversion_amount) as total_amount,
               avg(conversion_amount) as avg_amount,
               max(conversion_amount) as max_amount,
               min(conversion_amount) as min_amount
        from crm_business_conversion_log
        where success_flag = 1
        <if test="conversionType != null and conversionType != ''">and conversion_type = #{conversionType}</if>
        <if test="startTime != null and startTime != ''">and conversion_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and conversion_time &lt;= #{endTime}</if>
        group by conversion_type
        order by total_amount desc
    </select>

    <select id="selectUserConversionStats" resultMap="CrmBusinessConversionLogResult">
        select operator_id, operator_name, conversion_type,
               count(*) as conversion_count,
               sum(success_flag) as success_count,
               sum(conversion_amount) as total_amount,
               round(sum(success_flag) * 100.0 / count(*), 2) as success_rate
        from crm_business_conversion_log
        where 1=1
        <if test="operatorId != null">and operator_id = #{operatorId}</if>
        <if test="startTime != null and startTime != ''">and conversion_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and conversion_time &lt;= #{endTime}</if>
        group by operator_id, operator_name, conversion_type
        order by success_rate desc, total_amount desc
    </select>

    <select id="selectConversionFunnelData" resultMap="CrmBusinessConversionLogResult">
        select 
            sum(case when conversion_type = 'LEAD_TO_CUSTOMER' then 1 else 0 end) as lead_to_customer_count,
            sum(case when conversion_type = 'LEAD_TO_OPPORTUNITY' then 1 else 0 end) as lead_to_opportunity_count,
            sum(case when conversion_type = 'OPPORTUNITY_TO_ORDER' then 1 else 0 end) as opportunity_to_order_count,
            sum(case when conversion_type = 'ORDER_TO_CONTRACT' then 1 else 0 end) as order_to_contract_count
        from crm_business_conversion_log
        where success_flag = 1
        <if test="startTime != null and startTime != ''">and conversion_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and conversion_time &lt;= #{endTime}</if>
    </select>

    <select id="selectConversionTrendData" resultMap="CrmBusinessConversionLogResult">
        select 
            <choose>
                <when test="groupBy == 'DAY'">date(conversion_time) as conversion_date,</when>
                <when test="groupBy == 'WEEK'">date_format(conversion_time, '%Y-%u') as conversion_date,</when>
                <when test="groupBy == 'MONTH'">date_format(conversion_time, '%Y-%m') as conversion_date,</when>
                <otherwise>date(conversion_time) as conversion_date,</otherwise>
            </choose>
            conversion_type,
            count(*) as conversion_count,
            sum(success_flag) as success_count,
            sum(conversion_amount) as total_amount
        from crm_business_conversion_log
        where 1=1
        <if test="conversionType != null and conversionType != ''">and conversion_type = #{conversionType}</if>
        <if test="startTime != null and startTime != ''">and conversion_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and conversion_time &lt;= #{endTime}</if>
        group by conversion_date, conversion_type
        order by conversion_date desc
    </select>

    <select id="selectConversionPathAnalysis" resultMap="CrmBusinessConversionLogResult">
        select source_type, target_type, count(*) as conversion_count,
               sum(success_flag) as success_count,
               round(sum(success_flag) * 100.0 / count(*), 2) as success_rate
        from crm_business_conversion_log
        where 1=1
        <if test="sourceType != null and sourceType != ''">and source_type = #{sourceType}</if>
        <if test="targetType != null and targetType != ''">and target_type = #{targetType}</if>
        <if test="startTime != null and startTime != ''">and conversion_time &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''">and conversion_time &lt;= #{endTime}</if>
        group by source_type, target_type
        order by conversion_count desc
    </select>
        
    <insert id="insertCrmBusinessConversionLog" parameterType="CrmBusinessConversionLog" useGeneratedKeys="true" keyProperty="id">
        insert into crm_business_conversion_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="conversionType != null">conversion_type,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="sourceName != null">source_name,</if>
            <if test="targetType != null">target_type,</if>
            <if test="targetId != null">target_id,</if>
            <if test="targetName != null">target_name,</if>
            <if test="conversionData != null">conversion_data,</if>
            <if test="conversionAmount != null">conversion_amount,</if>
            <if test="successFlag != null">success_flag,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="conversionTime != null">conversion_time,</if>
            <if test="remarks != null">remarks,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="conversionType != null">#{conversionType},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="sourceName != null">#{sourceName},</if>
            <if test="targetType != null">#{targetType},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="targetName != null">#{targetName},</if>
            <if test="conversionData != null">#{conversionData},</if>
            <if test="conversionAmount != null">#{conversionAmount},</if>
            <if test="successFlag != null">#{successFlag},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="conversionTime != null">#{conversionTime},</if>
            <if test="remarks != null">#{remarks},</if>
         </trim>
    </insert>

    <update id="updateCrmBusinessConversionLog" parameterType="CrmBusinessConversionLog">
        update crm_business_conversion_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="conversionType != null">conversion_type = #{conversionType},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="sourceName != null">source_name = #{sourceName},</if>
            <if test="targetType != null">target_type = #{targetType},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="targetName != null">target_name = #{targetName},</if>
            <if test="conversionData != null">conversion_data = #{conversionData},</if>
            <if test="conversionAmount != null">conversion_amount = #{conversionAmount},</if>
            <if test="successFlag != null">success_flag = #{successFlag},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="conversionTime != null">conversion_time = #{conversionTime},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmBusinessConversionLogById" parameterType="Long">
        delete from crm_business_conversion_log where id = #{id}
    </delete>

    <delete id="deleteCrmBusinessConversionLogByIds" parameterType="String">
        delete from crm_business_conversion_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCrmBusinessConversionLogBySource">
        delete from crm_business_conversion_log where source_type = #{sourceType} and source_id = #{sourceId}
    </delete>

    <insert id="batchInsertCrmBusinessConversionLog" parameterType="java.util.List">
        insert into crm_business_conversion_log(conversion_type, source_type, source_id, source_name, target_type, target_id, target_name,
                                                 conversion_data, conversion_amount, success_flag, operator_id, operator_name, conversion_time, remarks)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.conversionType}, #{item.sourceType}, #{item.sourceId}, #{item.sourceName}, #{item.targetType}, #{item.targetId}, #{item.targetName},
             #{item.conversionData}, #{item.conversionAmount}, #{item.successFlag}, #{item.operatorId}, #{item.operatorName}, #{item.conversionTime}, #{item.remarks})
        </foreach>
    </insert>

</mapper>
