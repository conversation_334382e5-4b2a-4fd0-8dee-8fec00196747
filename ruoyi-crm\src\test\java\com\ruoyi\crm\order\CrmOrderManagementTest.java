package com.ruoyi.crm.order;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.domain.entity.CrmOrder;
import com.ruoyi.common.domain.entity.CrmOrderAssignmentLog;
import com.ruoyi.common.domain.entity.CrmBusinessConversionLog;
import com.ruoyi.common.domain.entity.CrmNewCustomerNotification;
import com.ruoyi.common.mapper.CrmOrderMapper;
import com.ruoyi.common.mapper.CrmOrderAssignmentLogMapper;
import com.ruoyi.common.mapper.CrmBusinessConversionLogMapper;
import com.ruoyi.common.mapper.CrmNewCustomerNotificationMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * CRM订单管理模块测试类
 * 
 * <AUTHOR>
 * @date 2025-02-02
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CrmOrderManagementTest {

    @Autowired
    private CrmOrderMapper crmOrderMapper;

    @Autowired
    private CrmOrderAssignmentLogMapper crmOrderAssignmentLogMapper;

    @Autowired
    private CrmBusinessConversionLogMapper crmBusinessConversionLogMapper;

    @Autowired
    private CrmNewCustomerNotificationMapper crmNewCustomerNotificationMapper;

    @Test
    public void testCrmOrderExtendedFields() {
        log.info("=== 测试CRM订单扩展字段 ===");
        
        // 查询现有订单
        List<CrmOrder> orders = crmOrderMapper.selectCrmOrderList(new CrmOrder());
        log.info("查询到订单数量: {}", orders.size());
        
        if (!orders.isEmpty()) {
            CrmOrder order = orders.get(0);
            log.info("订单信息: ID={}, 订单号={}, 客户名称={}", 
                    order.getId(), order.getOrderNo(), order.getCustomerName());
            
            // 测试新增字段
            order.setOrderSource("3D_PRINTING");
            order.setOrderType("3D_PRINTING");
            order.setPriorityLevel("NORMAL");
            order.setCustomerType("EXISTING");
            order.setAssignmentStatus("ASSIGNED");
            order.setCurrency("CNY");
            order.setPaidAmount(BigDecimal.ZERO);
            order.setOrderDate(new Date());
            
            // 更新订单
            int result = crmOrderMapper.updateCrmOrder(order);
            log.info("更新订单结果: {}", result);
        }
    }

    @Test
    public void testOrderAssignmentLog() {
        log.info("=== 测试订单分配历史 ===");
        
        // 创建分配历史记录
        CrmOrderAssignmentLog assignmentLog = new CrmOrderAssignmentLog();
        assignmentLog.setOrderId(1L);
        assignmentLog.setActionType(CrmOrderAssignmentLog.ACTION_TYPE_ASSIGN);
        assignmentLog.setToUserId(1L);
        assignmentLog.setToUserName("管理员");
        assignmentLog.setOperatorId(1L);
        assignmentLog.setOperatorName("系统管理员");
        assignmentLog.setReason("测试分配");
        assignmentLog.setOperationTime(new Date());
        assignmentLog.setIpAddress("127.0.0.1");
        
        // 插入记录
        int result = crmOrderAssignmentLogMapper.insertCrmOrderAssignmentLog(assignmentLog);
        log.info("插入分配历史结果: {}, ID: {}", result, assignmentLog.getId());
        
        // 查询分配历史
        List<CrmOrderAssignmentLog> logs = crmOrderAssignmentLogMapper.selectCrmOrderAssignmentLogByOrderId(1L);
        log.info("查询到分配历史数量: {}", logs.size());
        
        // 统计今日抢单次数
        int grabCount = crmOrderAssignmentLogMapper.countTodayGrabsByUserId(1L);
        log.info("今日抢单次数: {}", grabCount);
    }

    @Test
    public void testBusinessConversionLog() {
        log.info("=== 测试业务转化日志 ===");
        
        // 创建转化日志记录
        CrmBusinessConversionLog conversionLog = new CrmBusinessConversionLog();
        conversionLog.setConversionType(CrmBusinessConversionLog.CONVERSION_TYPE_OPPORTUNITY_TO_ORDER);
        conversionLog.setSourceType(CrmBusinessConversionLog.ENTITY_TYPE_OPPORTUNITY);
        conversionLog.setSourceId(1L);
        conversionLog.setSourceName("测试商机");
        conversionLog.setTargetType(CrmBusinessConversionLog.ENTITY_TYPE_ORDER);
        conversionLog.setTargetId(1L);
        conversionLog.setTargetName("测试订单");
        conversionLog.setConversionAmount(new BigDecimal("10000.00"));
        conversionLog.setSuccessFlag(CrmBusinessConversionLog.SUCCESS_FLAG_SUCCESS);
        conversionLog.setOperatorId(1L);
        conversionLog.setOperatorName("测试用户");
        conversionLog.setConversionTime(new Date());
        conversionLog.setRemarks("测试转化");
        
        // 插入记录
        int result = crmBusinessConversionLogMapper.insertCrmBusinessConversionLog(conversionLog);
        log.info("插入转化日志结果: {}, ID: {}", result, conversionLog.getId());
        
        // 查询转化日志
        List<CrmBusinessConversionLog> logs = crmBusinessConversionLogMapper.selectCrmBusinessConversionLogByType(
                CrmBusinessConversionLog.CONVERSION_TYPE_OPPORTUNITY_TO_ORDER);
        log.info("查询到转化日志数量: {}", logs.size());
    }

    @Test
    public void testNewCustomerNotification() {
        log.info("=== 测试新客户通知 ===");
        
        // 创建新客户通知
        CrmNewCustomerNotification notification = new CrmNewCustomerNotification();
        notification.setCustomerId(1L);
        notification.setCustomerName("测试新客户");
        notification.setCustomerPhone("***********");
        notification.setCustomerSource("3D_PRINTING");
        notification.setOrderId(1L);
        notification.setOrderNo("ORDER-************");
        notification.setOrderAmount(new BigDecimal("5000.00"));
        notification.setNotificationType(CrmNewCustomerNotification.NOTIFICATION_TYPE_NEW_CUSTOMER);
        notification.setNotificationStatus(CrmNewCustomerNotification.STATUS_PENDING);
        notification.setPriorityLevel(CrmNewCustomerNotification.PRIORITY_NORMAL);
        notification.setNotificationChannels("[\"SYSTEM\",\"WECHAT\"]");
        notification.setWechatSent(CrmNewCustomerNotification.SENT_NO);
        notification.setEmailSent(CrmNewCustomerNotification.SENT_NO);
        notification.setCreateBy("system");
        notification.setDelFlag("0");
        
        // 插入记录
        int result = crmNewCustomerNotificationMapper.insertCrmNewCustomerNotification(notification);
        log.info("插入新客户通知结果: {}, ID: {}", result, notification.getId());
        
        // 查询待处理通知
        List<CrmNewCustomerNotification> pendingNotifications = 
                crmNewCustomerNotificationMapper.selectPendingNotifications();
        log.info("查询到待处理通知数量: {}", pendingNotifications.size());
        
        // 更新通知状态
        if (notification.getId() != null) {
            int updateResult = crmNewCustomerNotificationMapper.updateNotificationStatus(
                    notification.getId(), 
                    CrmNewCustomerNotification.STATUS_COMPLETED, 
                    1L, 
                    "测试处理完成");
            log.info("更新通知状态结果: {}", updateResult);
        }
    }

    @Test
    public void testOrderQueryWithNewFields() {
        log.info("=== 测试订单查询新字段 ===");
        
        // 创建查询条件
        CrmOrder queryOrder = new CrmOrder();
        queryOrder.setOrderSource("3D_PRINTING");
        queryOrder.setAssignmentStatus("ASSIGNED");
        
        // 查询订单
        List<CrmOrder> orders = crmOrderMapper.selectCrmOrderList(queryOrder);
        log.info("根据新字段查询到订单数量: {}", orders.size());
        
        for (CrmOrder order : orders) {
            log.info("订单详情: 编号={}, 来源={}, 分配状态={}, 优先级={}", 
                    order.getOrderNo(), order.getOrderSource(), 
                    order.getAssignmentStatus(), order.getPriorityLevel());
        }
    }

    @Test
    public void testDatabaseConnection() {
        log.info("=== 测试数据库连接 ===");
        
        try {
            // 简单查询测试连接
            List<CrmOrder> orders = crmOrderMapper.selectCrmOrderList(new CrmOrder());
            log.info("数据库连接正常，查询到 {} 条订单记录", orders.size());
            
            // 测试新表是否存在
            List<CrmOrderAssignmentLog> assignmentLogs = 
                    crmOrderAssignmentLogMapper.selectCrmOrderAssignmentLogList(new CrmOrderAssignmentLog());
            log.info("订单分配历史表正常，查询到 {} 条记录", assignmentLogs.size());
            
            List<CrmBusinessConversionLog> conversionLogs = 
                    crmBusinessConversionLogMapper.selectCrmBusinessConversionLogList(new CrmBusinessConversionLog());
            log.info("业务转化日志表正常，查询到 {} 条记录", conversionLogs.size());
            
            List<CrmNewCustomerNotification> notifications = 
                    crmNewCustomerNotificationMapper.selectCrmNewCustomerNotificationList(new CrmNewCustomerNotification());
            log.info("新客户通知表正常，查询到 {} 条记录", notifications.size());
            
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            throw e;
        }
    }
}
